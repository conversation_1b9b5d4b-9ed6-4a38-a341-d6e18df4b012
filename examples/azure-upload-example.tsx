/**
 * Azure File Upload Example
 *
 * This example demonstrates how to use the AzureFileUpload component
 * in a real-world scenario with proper error handling and user feedback.
 */

"use client";

import React, { useState } from "react";
import { AzureFileUpload } from "@workspace/ui/components/azure-file-upload";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { CheckCircle, AlertCircle, FileText, Download } from "lucide-react";
import {
  AzureBlobConfig,
  UploadOptions,
  UploadResult,
  UploadError,
} from "shared";

interface UploadedFile {
  id: string;
  name: string;
  url: string;
  size: number;
  uploadedAt: Date;
  status: "success" | "error";
}

export function AzureUploadExample() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [lastError, setLastError] = useState<string | null>(null);

  // Azure configuration - in a real app, this would come from environment variables
  const azureConfig: AzureBlobConfig = {
    connectionString:
      process.env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING || "",
    containerName:
      process.env.NEXT_PUBLIC_AZURE_STORAGE_CONTAINER_NAME || "documents",
    accountName: process.env.NEXT_PUBLIC_AZURE_STORAGE_ACCOUNT_NAME,
  };

  // Upload options
  const uploadOptions: UploadOptions = {
    maxFileSize: 25 * 1024 * 1024, // 25MB
    allowedMimeTypes: ["application/pdf"],
    generateUniqueFilename: true,
    filenamePrefix: "medical-doc",
    metadata: {
      uploadedBy: "example-user",
      category: "medical-records",
      timestamp: new Date().toISOString(),
    },
    accessTier: "Hot",
  };

  const handleUploadSuccess = (result: UploadResult) => {
    console.log("Upload successful:", result);
    setIsUploading(false);
    setLastError(null);

    const newFile: UploadedFile = {
      id: result.blobName,
      name: result.blobName,
      url: result.url,
      size: result.size,
      uploadedAt: result.uploadedAt,
      status: "success",
    };

    setUploadedFiles((prev) => [newFile, ...prev]);
  };

  const handleUploadError = (error: UploadError) => {
    console.error("Upload failed:", error);
    setIsUploading(false);
    setLastError(error.error);
  };

  const handleProgress = (progress: any) => {
    console.log(`Upload progress: ${progress.percentage}%`);
  };

  const handleFileSelect = (file: File) => {
    console.log("File selected:", file.name);
    setIsUploading(true);
    setLastError(null);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  const clearError = () => {
    setLastError(null);
  };

  const clearAllFiles = () => {
    setUploadedFiles([]);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Azure Blob Storage Upload Example
        </h1>
        <p className="text-gray-600">
          Upload PDF documents to Azure Blob Storage with real-time progress
          tracking
        </p>
      </div>

      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Upload Document
          </CardTitle>
          <CardDescription>
            Select or drag and drop a PDF file to upload to Azure Blob Storage.
            Maximum file size: 25MB.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AzureFileUpload
            config={azureConfig}
            options={uploadOptions}
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
            onProgress={handleProgress}
            onFileSelect={handleFileSelect}
            disabled={isUploading}
            placeholder="Drop your PDF document here or click to browse"
            showProgress={true}
            enableDragDrop={true}
            className="min-h-[200px]"
          />

          {/* Error Display */}
          {lastError && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>{lastError}</span>
                <Button variant="ghost" size="sm" onClick={clearError}>
                  Dismiss
                </Button>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Uploaded Files Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Uploaded Files ({uploadedFiles.length})
              </CardTitle>
              <CardDescription>
                Files successfully uploaded to Azure Blob Storage
              </CardDescription>
            </div>
            {uploadedFiles.length > 0 && (
              <Button variant="outline" size="sm" onClick={clearAllFiles}>
                Clear All
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {uploadedFiles.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No files uploaded yet</p>
              <p className="text-sm">Upload a PDF file to see it listed here</p>
            </div>
          ) : (
            <div className="space-y-3">
              {uploadedFiles.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <FileText className="h-8 w-8 text-blue-500" />
                    <div>
                      <p className="font-medium text-gray-900">{file.name}</p>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <span>{formatFileSize(file.size)}</span>
                        <span>•</span>
                        <span>{formatDate(file.uploadedAt)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-800"
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Uploaded
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(file.url, "_blank")}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuration Info */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration</CardTitle>
          <CardDescription>
            Current Azure Blob Storage configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Container:</span>{" "}
              <code className="bg-gray-100 px-2 py-1 rounded">
                {azureConfig.containerName}
              </code>
            </div>
            <div>
              <span className="font-medium">Max File Size:</span>{" "}
              <code className="bg-gray-100 px-2 py-1 rounded">
                {formatFileSize(uploadOptions.maxFileSize || 0)}
              </code>
            </div>
            <div>
              <span className="font-medium">Allowed Types:</span>{" "}
              <code className="bg-gray-100 px-2 py-1 rounded">
                {uploadOptions.allowedMimeTypes?.join(", ")}
              </code>
            </div>
            <div>
              <span className="font-medium">Access Tier:</span>{" "}
              <code className="bg-gray-100 px-2 py-1 rounded">
                {uploadOptions.accessTier}
              </code>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default AzureUploadExample;
