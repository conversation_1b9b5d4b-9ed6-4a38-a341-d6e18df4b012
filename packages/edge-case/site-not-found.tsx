import dynamic from "next/dynamic";
import React from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import Link from "next/link";
import NoSiteSvg from "@/components/illustrations/no-site";
import { ArrowRight, Home } from "lucide-react";

const TITLE = "Organization Not Found";
const MESSAGE =
  "We couldn't find a healthcare organization at this address. The organization might not be registered yet or the URL might be incorrect.";

export const SiteNotFound = () => {
  return (
    <div className="flex min-h-[60vh] flex-col items-center justify-center bg-background px-4">
      <div className="w-full max-w-xl mx-auto space-y-8">
        <NoSiteSvg className="mx-auto" />
        <h2 className="font-heading text-2xl text-center font-bold text-foreground sm:text-3xl">
          {TITLE}
        </h2>
        <p className="text-muted-foreground text-center">{MESSAGE}</p>
        <div className="mt-8 flex flex-row justify-center gap-4">
          <Button
            asChild
            className="text-sm font-medium transition-all hover:shadow-sm"
          >
            <Link
              href="/sign-up"
              className="flex items-center justify-center gap-2"
            >
              Register Your Organization
              <ArrowRight className="h-3.5 w-3.5" />
            </Link>
          </Button>
          <Button asChild variant="outline" className="text-sm font-medium">
            <Link href="/" className="flex items-center justify-center gap-2">
              <Home className="h-3.5 w-3.5" />
              Return to Homepage
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
};
