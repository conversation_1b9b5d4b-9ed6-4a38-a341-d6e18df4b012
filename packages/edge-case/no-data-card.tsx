import { Card, CardContent } from "@workspace/ui/components/card";
import { But<PERSON> } from "@workspace/ui/components/button";
import Link from "next/link";
import { PlusCircle } from "lucide-react";
import NoDataSvg from "../illustrations/no-data";

export const NoDataCard = ({ href, doctype }: any) => {
  return (
    <Card className="flex h-[80vh] shrink-0 items-center justify-center rounded-md border border-dashed p-6 shadow-none">
      <CardContent className="flex flex-col items-center justify-center p-12 text-center space-y-6">
        <NoDataSvg />
        <h3 className="text-xl mb-2 font-bold">No {doctype} found</h3>
        <p className="text-muted-foreground">
          There are currently no {doctype} records in the system. You can add
          one to get started.
        </p>
        {href && (
          <Button className="btn-animate" asChild>
            <Link href={href}>
              <PlusCircle size={16} className="mr-2" />
              Add new {doctype}
            </Link>
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
