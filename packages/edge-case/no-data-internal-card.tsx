import { Icon } from "@/icon";

export const NoDataInternalCard = ({ description, title, icon }: any) => {
  return (
    <div className="text-center py-16 text-muted-foreground bg-muted/20 rounded-xl border border-dashed flex flex-col items-center justify-center">
      <div className="flex flex-col items-center justify-center py-10 px-4 text-center">
        <div className="h-12 w-12 rounded-full bg-background flex items-center justify-center mb-3 shadow-lg">
          <Icon name={icon} className="h-6 w-6 text-primary" />
        </div>
        <h3 className="text-lg font-medium mb-1 text-foreground">{title}</h3>
        <p className="text-muted-foreground text-sm max-w-sm">{description}</p>
      </div>
    </div>
  );
};
