import { Card, CardContent } from "@workspace/ui/components/card";
import BookingSvg from "../illustrations/booking";

export const NoAppointmentCard = ({ slug }: any) => {
  let description = `There are currently no ${slug} appointments in the system.`;

  if (slug === "all" || !slug) {
    description = "There are currently no appointments in the system.";
  }

  return (
    <Card className="flex h-[60vh] shrink-0 items-center justify-center rounded-md border border-dashed p-6 shadow-none">
      <CardContent className="flex flex-col items-center justify-center p-12 text-center space-y-6">
        <BookingSvg />
        <h3 className="text-xl mb-2 font-bold">No appointments found</h3>
        <p className="text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
};
