/**
 * Azure Blob Storage Service
 *
 * This service provides methods for uploading files to Azure Blob Storage
 * with proper validation, progress tracking, and error handling.
 */

import {
  BlobServiceClient,
  ContainerClient,
  BlockBlobClient,
} from "@azure/storage-blob";
import {
  AzureBlobConfig,
  UploadOptions,
  UploadProgress,
  UploadResponse,
  UploadResult,
  UploadError,
  FileValidationResult,
  AzureBlobService,
  AzureErrorType,
  AzureError,
} from "shared";

export class AzureBlobStorageService implements AzureBlobService {
  private blobServiceClient: BlobServiceClient;
  private containerClient: ContainerClient;
  private config: AzureBlobConfig;

  constructor(config: AzureBlobConfig) {
    this.config = config;

    try {
      // Initialize Azure Blob Service Client
      this.blobServiceClient = BlobServiceClient.fromConnectionString(
        config.connectionString,
      );
      this.containerClient = this.blobServiceClient.getContainerClient(
        config.containerName,
      );
    } catch (error) {
      throw new Error(`Failed to initialize Azure Blob Storage: ${error}`);
    }
  }

  /**
   * Upload a file to Azure Blob Storage
   */
  async uploadFile(
    file: File,
    options: UploadOptions = {},
    onProgress?: (progress: UploadProgress) => void,
  ): Promise<UploadResponse> {
    try {
      // Validate file first
      const validation = this.validateFile(file, options);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error || "File validation failed",
          code: AzureErrorType.VALIDATION_ERROR,
        };
      }

      // Generate unique filename
      const blobName =
        options.generateUniqueFilename !== false
          ? this.generateUniqueFilename(file.name, options.filenamePrefix)
          : file.name;

      // Get blob client
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);

      // Prepare upload options
      const uploadOptions = {
        blobHTTPHeaders: {
          blobContentType: file.type,
        },
        metadata: options.metadata,
        tier: options.accessTier,
        onProgress: onProgress
          ? (ev: any) => {
              const progress: UploadProgress = {
                percentage: Math.round((ev.loadedBytes / file.size) * 100),
                bytesUploaded: ev.loadedBytes,
                totalBytes: file.size,
              };
              onProgress(progress);
            }
          : undefined,
      };

      // Upload the file
      const uploadResponse = await blockBlobClient.uploadData(
        file,
        uploadOptions,
      );

      // Return success result
      const result: UploadResult = {
        success: true,
        url: this.getBlobUrl(blobName),
        blobName,
        size: file.size,
        contentType: file.type,
        uploadedAt: new Date(),
        etag: uploadResponse.etag,
        metadata: options.metadata,
      };

      return result;
    } catch (error: any) {
      console.error("Azure upload error:", error);

      const azureError: UploadError = {
        success: false,
        error: this.getErrorMessage(error),
        code: this.getErrorType(error),
        details: error,
      };

      return azureError;
    }
  }

  /**
   * Validate a file before upload
   */
  validateFile(file: File, options: UploadOptions = {}): FileValidationResult {
    const errors: { size?: string; type?: string; name?: string } = {};
    let isValid = true;

    // Check file size
    const maxSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB default
    if (file.size > maxSize) {
      errors.size = `File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${this.formatFileSize(maxSize)})`;
      isValid = false;
    }

    // Check MIME type
    const allowedTypes = options.allowedMimeTypes || ["application/pdf"];
    if (!allowedTypes.includes(file.type)) {
      errors.type = `File type "${file.type}" is not allowed. Allowed types: ${allowedTypes.join(", ")}`;
      isValid = false;
    }

    // Check filename
    if (!file.name || file.name.trim() === "") {
      errors.name = "File name is required";
      isValid = false;
    }

    return {
      isValid,
      error: isValid ? undefined : "File validation failed",
      errors: Object.keys(errors).length > 0 ? errors : undefined,
    };
  }

  /**
   * Generate a unique filename
   */
  generateUniqueFilename(originalName: string, prefix?: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const extension = originalName.split(".").pop();
    const nameWithoutExtension = originalName.replace(/\.[^/.]+$/, "");

    const sanitizedName = nameWithoutExtension.replace(/[^a-zA-Z0-9-_]/g, "_");
    const prefixPart = prefix ? `${prefix}_` : "";

    return `${prefixPart}${sanitizedName}_${timestamp}_${randomString}.${extension}`;
  }

  /**
   * Get the public URL for a blob
   */
  getBlobUrl(blobName: string): string {
    if (this.config.accountName) {
      return `https://${this.config.accountName}.blob.core.windows.net/${this.config.containerName}/${blobName}`;
    }

    // Fallback: extract account name from connection string
    const accountNameMatch =
      this.config.connectionString.match(/AccountName=([^;]+)/);
    if (accountNameMatch) {
      const accountName = accountNameMatch[1];
      return `https://${accountName}.blob.core.windows.net/${this.config.containerName}/${blobName}`;
    }

    throw new Error("Unable to generate blob URL: account name not found");
  }

  /**
   * Delete a blob from storage
   */
  async deleteBlob(blobName: string): Promise<boolean> {
    try {
      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);
      await blockBlobClient.delete();
      return true;
    } catch (error) {
      console.error("Error deleting blob:", error);
      return false;
    }
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Get error message from Azure error
   */
  private getErrorMessage(error: any): string {
    if (error.message) return error.message;
    if (error.details?.message) return error.details.message;
    if (typeof error === "string") return error;
    return "An unknown error occurred during upload";
  }

  /**
   * Get error type from Azure error
   */
  private getErrorType(error: any): string {
    if (error.code) {
      switch (error.code) {
        case "AuthenticationFailed":
          return AzureErrorType.AUTHENTICATION_FAILED;
        case "ContainerNotFound":
          return AzureErrorType.CONTAINER_NOT_FOUND;
        case "RequestBodyTooLarge":
          return AzureErrorType.FILE_TOO_LARGE;
        default:
          return AzureErrorType.UPLOAD_FAILED;
      }
    }

    if (error.name === "NetworkError") {
      return AzureErrorType.NETWORK_ERROR;
    }

    return AzureErrorType.UNKNOWN_ERROR;
  }
}

/**
 * Factory function to create Azure Blob Storage service instance
 */
export function createAzureBlobService(
  config: AzureBlobConfig,
): AzureBlobStorageService {
  return new AzureBlobStorageService(config);
}

/**
 * Helper function to create config from environment variables
 */
export function createConfigFromEnv(): AzureBlobConfig {
  const connectionString =
    process.env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING;
  const containerName = process.env.NEXT_PUBLIC_AZURE_STORAGE_CONTAINER_NAME;

  if (!connectionString || !containerName) {
    throw new Error(
      "Azure Storage configuration missing. Please set NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING and NEXT_PUBLIC_AZURE_STORAGE_CONTAINER_NAME environment variables.",
    );
  }

  return {
    connectionString,
    containerName,
    accountName: process.env.NEXT_PUBLIC_AZURE_STORAGE_ACCOUNT_NAME,
    sasToken: process.env.AZURE_STORAGE_SAS_TOKEN,
  };
}
