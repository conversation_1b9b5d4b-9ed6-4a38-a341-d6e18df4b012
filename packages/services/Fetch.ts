import toast from "react-hot-toast";

function handleErrors(response: any) {
  return new Promise<void>(async (resolve, reject) => {
    if (!response.ok) {
      try {
        const res = await response.json();
        console.error("Error response:", res);
        toast.remove();
        toast.error(
          res.details || res.error || res.message || "Something went wrong",
        );
        return reject(response);
      } catch (jsonError) {
        console.error("Error parsing error response:", jsonError);
        toast.remove();
        toast.error("Failed to parse error response");
        return reject(response);
      }
    }

    if (response.status === 204) {
      return resolve();
    }

    try {
      const data = await response.json();
      return resolve(data);
    } catch (jsonError) {
      console.error("Error parsing response:", jsonError);
      return resolve();
    }
  });
}

function getHeaders() {
  // Get team-id from cookie
  const tenantId =
    document.cookie
      .split("; ")
      .find((row) => row.startsWith("tenant-id="))
      ?.split("=")[1] || "";

  const headers = {
    "Content-Type": "application/json",
    Accept: "application/json",
    "tenant-id": tenantId,
  };
  return headers;
}

class Fetch {
  static getJSON(url: string, opts = {}) {
    return fetch(url, {
      headers: getHeaders(),
      ...opts,
      credentials: "include",
    })
      .then((response) => {
        return handleErrors(response);
      })
      .catch((error) => {
        console.error("Fetch error for URL:", url, error);
        throw error;
      });
  }

  static postJSON(url: string, data = {}) {
    return fetch(url, {
      method: "POST",
      headers: getHeaders(),
      body: JSON.stringify(data),
      credentials: "include",
    }).then(handleErrors);
  }

  static updateJSON(url: string, data = {}) {
    return fetch(url, {
      method: "PUT",
      headers: getHeaders(),
      body: JSON.stringify(data),
      credentials: "include",
    }).then(handleErrors);
  }

  static postFormData(url: string, formData: any) {
    return fetch(url, {
      method: "POST",
      body: formData,
      credentials: "include",
    }).then(handleErrors);
  }

  static deleteJSON(url: string) {
    return fetch(url, {
      method: "DELETE",
      headers: getHeaders(),
      credentials: "include",
    }).then(handleErrors);
  }
}

export default Fetch;
