import Fetch from "./Fetch";

export class FlinkkClient {
  static create(doctype: string, payload: any) {
    return Fetch.postJSON(`/api/${doctype}/create`, payload);
  }

  static update(doctype: string, id: string, payload: any) {
    return Fetch.updateJSON(`/api/${doctype}/update/${id}`, payload);
  }

  static delete(doctype: string, id: string) {
    return Fetch.deleteJSON(`/api/${doctype}/delete/${id}`);
  }

  static deleteMany(doctype: string, ids: string[]) {
    return Fetch.postJSON(`/api/${doctype}/delete-many`, { ids });
  }
}

// Export Azure Blob Storage service
export * from "./azure-blob-service";
