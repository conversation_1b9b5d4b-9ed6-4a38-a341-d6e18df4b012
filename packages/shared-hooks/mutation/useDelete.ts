import { useMutation } from "@tanstack/react-query";
import { FlinkkClient } from "@workspace/services";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

export const useDelete = ({
  doctype,
  href,
}: {
  doctype: string;
  href?: string;
}) => {
  const router = useRouter();
  return useMutation({
    mutationFn: (id: string) => FlinkkClient.delete(doctype, id),
    onSuccess: () => {
      toast.success("Record deleted successfully");

      if (href) {
        return router.push(href);
      }

      router.refresh();
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.error ||
        error?.message ||
        "Failed to delete record";
      toast.error(errorMessage);
      console.error("Delete error:", error);
    },
  });
};
