import { useMutation } from "@tanstack/react-query";
import { FlinkkClient } from "@workspace/services";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";

export const useDeleteMany = ({ doctype }: { doctype: string }) => {
  const router = useRouter();
  return useMutation({
    mutationFn: (ids: string[]) => FlinkkClient.deleteMany(doctype, ids),
    onSuccess: () => {
      toast.remove();
      toast.success("Records deleted successfully");
      router.refresh();
    },
  });
};
