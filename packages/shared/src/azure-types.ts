/**
 * Azure Blob Storage Types and Interfaces
 *
 * This file contains all TypeScript types and interfaces for Azure Blob Storage integration
 */

/**
 * Configuration for Azure Blob Storage connection
 */
export interface AzureBlobConfig {
  /** Azure Storage Account connection string */
  connectionString: string;
  /** Container name where files will be uploaded */
  containerName: string;
  /** Optional SAS token for authentication */
  sasToken?: string;
  /** Optional account name for URL generation */
  accountName?: string;
}

/**
 * File upload configuration options
 */
export interface UploadOptions {
  /** Maximum file size in bytes (default: 10MB) */
  maxFileSize?: number;
  /** Allowed MIME types (default: ['application/pdf']) */
  allowedMimeTypes?: string[];
  /** Whether to generate unique filename (default: true) */
  generateUniqueFilename?: boolean;
  /** Custom filename prefix */
  filenamePrefix?: string;
  /** Custom metadata to attach to the blob */
  metadata?: Record<string, string>;
  /** Blob access tier (Hot, Cool, Archive) */
  accessTier?: "Hot" | "Cool" | "Archive";
}

/**
 * Upload progress information
 */
export interface UploadProgress {
  /** Percentage of upload completed (0-100) */
  percentage: number;
  /** Number of bytes uploaded */
  bytesUploaded: number;
  /** Total file size in bytes */
  totalBytes: number;
  /** Current upload speed in bytes per second */
  speed?: number;
  /** Estimated time remaining in seconds */
  estimatedTimeRemaining?: number;
}

/**
 * File validation result
 */
export interface FileValidationResult {
  /** Whether the file is valid */
  isValid: boolean;
  /** Error message if validation failed */
  error?: string;
  /** Specific validation errors */
  errors?: {
    size?: string;
    type?: string;
    name?: string;
  };
}

/**
 * Upload result after successful upload
 */
export interface UploadResult {
  /** Success status */
  success: true;
  /** URL of the uploaded file */
  url: string;
  /** Blob name in the container */
  blobName: string;
  /** File size in bytes */
  size: number;
  /** Content type of the uploaded file */
  contentType: string;
  /** Upload timestamp */
  uploadedAt: Date;
  /** ETag of the uploaded blob */
  etag?: string;
  /** Any metadata attached to the blob */
  metadata?: Record<string, string>;
}

/**
 * Upload error result
 */
export interface UploadError {
  /** Success status */
  success: false;
  /** Error message */
  error: string;
  /** Error code if available */
  code?: string;
  /** Additional error details */
  details?: any;
}

/**
 * Combined upload result type
 */
export type UploadResponse = UploadResult | UploadError;

/**
 * Upload state for component management
 */
export interface UploadState {
  /** Current upload status */
  status: "idle" | "validating" | "uploading" | "success" | "error";
  /** Upload progress information */
  progress?: UploadProgress;
  /** Upload result or error */
  result?: UploadResponse;
  /** Currently selected file */
  file?: File;
}

/**
 * Props for the Azure upload component
 */
export interface AzureUploadComponentProps {
  /** Azure blob configuration */
  config: AzureBlobConfig;
  /** Upload options */
  options?: UploadOptions;
  /** Callback when upload completes successfully */
  onUploadSuccess?: (result: UploadResult) => void;
  /** Callback when upload fails */
  onUploadError?: (error: UploadError) => void;
  /** Callback for upload progress updates */
  onProgress?: (progress: UploadProgress) => void;
  /** Callback when file is selected */
  onFileSelect?: (file: File) => void;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Custom CSS class name */
  className?: string;
  /** Custom placeholder text */
  placeholder?: string;
  /** Whether to show upload progress */
  showProgress?: boolean;
  /** Whether to enable drag and drop */
  enableDragDrop?: boolean;
}

/**
 * Azure Blob Storage service interface
 */
export interface AzureBlobService {
  /** Upload a file to Azure Blob Storage */
  uploadFile(
    file: File,
    options?: UploadOptions,
    onProgress?: (progress: UploadProgress) => void,
  ): Promise<UploadResponse>;

  /** Validate a file before upload */
  validateFile(file: File, options?: UploadOptions): FileValidationResult;

  /** Generate a unique filename */
  generateUniqueFilename(originalName: string, prefix?: string): string;

  /** Get the public URL for a blob */
  getBlobUrl(blobName: string): string;

  /** Delete a blob from storage */
  deleteBlob(blobName: string): Promise<boolean>;
}

/**
 * Environment variables for Azure configuration
 */
export interface AzureEnvironmentConfig {
  NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING: string;
  NEXT_PUBLIC_AZURE_STORAGE_CONTAINER_NAME: string;
  NEXT_PUBLIC_AZURE_STORAGE_ACCOUNT_NAME?: string;
  AZURE_STORAGE_SAS_TOKEN?: string;
}

/**
 * Error types that can occur during Azure operations
 */
export enum AzureErrorType {
  AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED",
  CONTAINER_NOT_FOUND = "CONTAINER_NOT_FOUND",
  FILE_TOO_LARGE = "FILE_TOO_LARGE",
  INVALID_FILE_TYPE = "INVALID_FILE_TYPE",
  NETWORK_ERROR = "NETWORK_ERROR",
  UPLOAD_FAILED = "UPLOAD_FAILED",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

/**
 * Azure-specific error with additional context
 */
export interface AzureError extends Error {
  type: AzureErrorType;
  code?: string;
  statusCode?: number;
  details?: any;
}
