"use client";

import React from "react";
import { cn } from "../lib/utils";
import { Check } from "lucide-react";

export interface StepperProps extends React.HTMLAttributes<HTMLDivElement> {
  steps: {
    id: string;
    label: string;
    description?: string;
    optional?: boolean;
  }[];
  activeStep: number;
  orientation?: "horizontal" | "vertical";
  onStepClick?: (step: number) => void;
}

export function Stepper({
  steps,
  activeStep,
  orientation = "horizontal",
  onStepClick,
  className,
  ...props
}: StepperProps) {
  const isVertical = orientation === "vertical";

  return (
    <div
      className={cn(
        "w-full",
        isVertical
          ? "flex flex-col space-y-2"
          : "flex items-center justify-between",
        className,
      )}
      {...props}
    >
      {steps.map((step, index) => {
        const isCompleted = index < activeStep;
        const isActive = index === activeStep;
        const isClickable =
          onStepClick && (isCompleted || index === activeStep + 1);

        return (
          <React.Fragment key={step.id}>
            <div
              className={cn(
                "flex",
                isVertical ? "flex-row items-start" : "flex-col items-center",
                isClickable && "cursor-pointer",
              )}
              onClick={() => isClickable && onStepClick(index)}
            >
              <div className="flex items-center">
                <div
                  className={cn(
                    "flex h-8 w-8 items-center justify-center rounded-full border-2",
                    isCompleted
                      ? "border-primary bg-primary text-primary-foreground"
                      : isActive
                        ? "border-primary text-primary"
                        : "border-muted-foreground text-muted-foreground",
                  )}
                >
                  {isCompleted ? (
                    <Check className="h-5 w-5" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                {isVertical && (
                  <div className="ml-4">
                    <div className="flex items-center">
                      <span
                        className={cn(
                          "text-sm font-medium",
                          isActive || isCompleted
                            ? "text-foreground"
                            : "text-muted-foreground",
                        )}
                      >
                        {step.label}
                      </span>
                      {step.optional && (
                        <span className="ml-2 text-xs text-muted-foreground">
                          (Optional)
                        </span>
                      )}
                    </div>
                    {step.description && (
                      <p className="mt-0.5 text-xs text-muted-foreground">
                        {step.description}
                      </p>
                    )}
                  </div>
                )}
              </div>
              {!isVertical && (
                <div className="mt-2 text-center">
                  <span
                    className={cn(
                      "text-sm font-medium",
                      isActive || isCompleted
                        ? "text-foreground"
                        : "text-muted-foreground",
                    )}
                  >
                    {step.label}
                  </span>
                  {step.optional && (
                    <div className="text-xs text-muted-foreground">
                      (Optional)
                    </div>
                  )}
                </div>
              )}
            </div>
            {index < steps.length - 1 && !isVertical && (
              <div
                className={cn(
                  "h-[2px] w-full flex-1 mx-2",
                  index < activeStep ? "bg-primary" : "bg-muted",
                )}
              />
            )}
            {index < steps.length - 1 && isVertical && (
              <div
                className={cn(
                  "ml-4 h-8 w-[2px]",
                  index < activeStep ? "bg-primary" : "bg-muted",
                )}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
}

export interface StepperContentProps
  extends React.HTMLAttributes<HTMLDivElement> {
  step: number;
  activeStep: number;
}

export function StepperContent({
  step,
  activeStep,
  children,
  className,
  ...props
}: StepperContentProps) {
  if (step !== activeStep) return null;

  return (
    <div className={cn("mt-6 rounded-lg border p-4", className)} {...props}>
      {children}
    </div>
  );
}

export interface StepperNavigationProps
  extends React.HTMLAttributes<HTMLDivElement> {
  activeStep: number;
  steps: number;
  onNext?: () => void;
  onBack?: () => void;
  nextLabel?: string;
  backLabel?: string;
  completeLabel?: string;
  disableNext?: boolean;
}

export function StepperNavigation({
  activeStep,
  steps,
  onNext,
  onBack,
  nextLabel = "Next",
  backLabel = "Back",
  completeLabel = "Complete",
  disableNext = false,
  className,
  ...props
}: StepperNavigationProps) {
  const isLastStep = activeStep === steps - 1;

  return (
    <div className={cn("mt-6 flex justify-between", className)} {...props}>
      <button
        type="button"
        onClick={onBack}
        disabled={activeStep === 0}
        className={cn(
          "px-4 py-2 text-sm font-medium rounded-md",
          activeStep === 0
            ? "text-muted-foreground cursor-not-allowed"
            : "text-primary hover:bg-primary/10",
        )}
      >
        {backLabel}
      </button>
      <button
        type="button"
        onClick={onNext}
        disabled={disableNext}
        className={cn(
          "px-4 py-2 text-sm font-medium rounded-md bg-primary text-primary-foreground hover:bg-primary/90",
          disableNext && "opacity-50 cursor-not-allowed",
        )}
      >
        {isLastStep ? completeLabel : nextLabel}
      </button>
    </div>
  );
}
