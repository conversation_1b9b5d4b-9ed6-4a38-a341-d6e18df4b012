# @workspace/data-table

A powerful and customizable data table component built on top of [@tanstack/react-table](https://tanstack.com/table/latest) for React applications.

## Features

- 📊 Flexible data display with sorting, filtering, and pagination
- 🔍 Advanced filtering capabilities with multiple filter types
- 📱 Responsive design with column pinning
- 🔗 URL state management for table state persistence
- 🎨 Customizable styling and theming
- ⚡ Performance optimized with debounced filtering
- 🧩 Modular components for custom layouts

## Installation

```bash
# If using npm
npm install @workspace/data-table

# If using yarn
yarn add @workspace/data-table

# If using pnpm
pnpm add @workspace/data-table
```

## Basic Usage

Here's a simple example of how to use the data table:

```tsx
import { useDataTable } from "@workspace/data-table/hooks/use-data-table";
import { DataTable } from "@workspace/data-table/component/data-table";
import { DataTableToolbar } from "@workspace/data-table/component/data-table-toolbar";
import { createColumnHelper } from "@tanstack/react-table";

// Define your data type
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: "active" | "inactive";
}

// Sample data
const data: User[] = [
  {
    id: "1",
    name: "<PERSON>e",
    email: "<EMAIL>",
    role: "Admin",
    status: "active",
  },
  // More data...
];

// Create column helper
const columnHelper = createColumnHelper<User>();

// Define columns
const columns = [
  columnHelper.accessor("name", {
    header: "Name",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("email", {
    header: "Email",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("role", {
    header: "Role",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("status", {
    header: "Status",
    cell: (info) => info.getValue(),
  }),
];

function UsersTable() {
  // Set up the table
  const { table } = useDataTable({
    data,
    columns,
    pageCount: Math.ceil(data.length / 10),
  });

  return (
    <div>
      <DataTable table={table} doctype="User">
        <DataTableToolbar table={table} />
      </DataTable>
    </div>
  );
}
```

## Advanced Integration

### Server-Side Data Fetching

For server-side data fetching, you can use the table state to build your API requests:

```tsx
import { useDataTable } from "@workspace/data-table/hooks/use-data-table";
import { DataTable } from "@workspace/data-table/component/data-table";
import { DataTableToolbar } from "@workspace/data-table/component/data-table-toolbar";
import { useState, useEffect } from "react";

function UsersTableWithFetch() {
  const [data, setData] = useState([]);
  const [pageCount, setPageCount] = useState(0);
  const [loading, setLoading] = useState(false);

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
  });

  useEffect(() => {
    async function fetchData() {
      setLoading(true);

      const { pagination, sorting, columnFilters } = table.getState();

      // Build your API request using the table state
      const response = await fetch("/api/users", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          page: pagination.pageIndex + 1,
          pageSize: pagination.pageSize,
          sort: sorting.map((s) => ({ id: s.id, desc: s.desc })),
          filters: columnFilters,
        }),
      });

      const result = await response.json();

      setData(result.data);
      setPageCount(result.pageCount);
      setLoading(false);
    }

    fetchData();
  }, [
    table.getState().pagination,
    table.getState().sorting,
    table.getState().columnFilters,
  ]);

  return (
    <div>
      <DataTable table={table} doctype="User">
        <DataTableToolbar table={table} />
      </DataTable>
      {loading && <div>Loading...</div>}
    </div>
  );
}
```

### Custom Filtering

You can customize the filtering experience by using the filter components:

```tsx
import { DataTableFacetedFilter } from "@workspace/data-table/component/data-table-faceted-filter";
import { DataTableDateFilter } from "@workspace/data-table/component/data-table-date-filter";

// Inside your component
const statusOptions = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

return (
  <DataTable table={table} doctype="User">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <DataTableFacetedFilter
          column={table.getColumn("status")}
          title="Status"
          options={statusOptions}
        />
        <DataTableDateFilter
          column={table.getColumn("createdAt")}
          title="Created Date"
        />
      </div>
      <DataTableToolbar table={table} />
    </div>
  </DataTable>
);
```

### Row Actions

You can add actions to each row:

```tsx
import { Button } from '@workspace/ui/components/button';

// In your columns definition
columnHelper.display({
  id: 'actions',
  cell: ({ row }) => (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleEdit(row.original)}
      >
        Edit
      </Button>
      <Button
        variant="destructive"
        size="sm"
        onClick={() => handleDelete(row.original)}
      >
        Delete
      </Button>
    </div>
  ),
}),
```

### Row Selection

Enable row selection and add bulk actions:

```tsx
import { DataTableActionBar } from "@workspace/data-table/component/data-table-action-bar";

function UsersTableWithSelection() {
  const { table } = useDataTable({
    data,
    columns,
    pageCount: Math.ceil(data.length / 10),
  });

  const handleBulkDelete = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows;
    // Process selected rows
    console.log(selectedRows.map((row) => row.original));
  };

  return (
    <DataTable
      table={table}
      doctype="User"
      actionBar={
        <DataTableActionBar>
          <Button variant="destructive" onClick={handleBulkDelete}>
            Delete Selected
          </Button>
        </DataTableActionBar>
      }
    >
      <DataTableToolbar table={table} />
    </DataTable>
  );
}
```

## API Reference

### Components

#### `DataTable`

The main table component.

| Prop         | Type                   | Description                          |
| ------------ | ---------------------- | ------------------------------------ |
| `table`      | `TanstackTable<TData>` | The table instance from useDataTable |
| `doctype`    | `string`               | The document type for empty state    |
| `actionBar`  | `React.ReactNode`      | Optional action bar for bulk actions |
| `onRowClick` | `(row: TData) => void` | Optional handler for row clicks      |
| `addHref`    | `string`               | Optional link for adding new items   |
| `className`  | `string`               | Optional additional CSS classes      |

#### `useDataTable`

The hook for creating and managing the table instance.

| Prop                   | Type                  | Description                      |
| ---------------------- | --------------------- | -------------------------------- |
| `data`                 | `TData[]`             | The data to display in the table |
| `columns`              | `ColumnDef<TData>[]`  | The column definitions           |
| `pageCount`            | `number`              | The total number of pages        |
| `initialState`         | `Partial<TableState>` | Optional initial state           |
| `enableAdvancedFilter` | `boolean`             | Enable advanced filtering        |
| `history`              | `'push' \| 'replace'` | URL history behavior             |
| `debounceMs`           | `number`              | Debounce time for filters        |
| `throttleMs`           | `number`              | Throttle time for URL updates    |

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

ISC
