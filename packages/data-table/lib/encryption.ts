import { webcrypto } from "crypto";

export async function encryptRsa(
  data: string,
  publicKeyPem: string,
): Promise<string> {
  // Remove PEM headers and convert to binary
  const publicKeyDer = Buffer.from(
    publicKeyPem
      .replace("-----<PERSON><PERSON><PERSON> PUBLIC KEY-----", "")
      .replace("-----END PUBLIC KEY-----", "")
      .replace(/\s+/g, ""),
    "base64",
  );

  // Import the public key
  const publicKey = await webcrypto.subtle.importKey(
    "spki",
    publicKeyDer,
    {
      name: "RSA-OAEP",
      hash: "SHA-256",
    },
    true,
    ["encrypt"],
  );

  // Encrypt the data
  const encoded = new TextEncoder().encode(data);
  const encrypted = await webcrypto.subtle.encrypt(
    {
      name: "RSA-OAEP",
    },
    publicKey,
    encoded,
  );

  // Convert to base64
  return Buffer.from(encrypted).toString("base64");
}
