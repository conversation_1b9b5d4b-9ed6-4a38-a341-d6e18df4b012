import { webcrypto } from "crypto";
import { getAbdmSettings } from "@/app/api/abdm/_lib/utils";

export async function encryptAadhaar(aadhaar: string): Promise<string> {
  try {
    const abdmSettings = await getAbdmSettings();
    const publicKey = abdmSettings.public_key;
    if (!publicKey) {
      throw new Error("ABDM public key not found");
    }

    // Convert base64 public key to binary
    const keyData = Buffer.from(publicKey, "base64");

    // Import the key for RSA-OAEP encryption
    const cryptoKey = await webcrypto.subtle.importKey(
      "spki",
      keyData,
      {
        name: "RSA-<PERSON>A<PERSON>",
        hash: "SHA-1", // Using SHA-1 as specified
      },
      false,
      ["encrypt"],
    );

    // Encrypt the Aadhaar number
    const data = new TextEncoder().encode(aadhaar);
    const encrypted = await webcrypto.subtle.encrypt(
      {
        name: "RSA-OAEP",
      },
      cryptoKey,
      data,
    );

    // Convert to base64
    return Buffer.from(encrypted).toString("base64");
  } catch (error) {
    console.error("Encryption error:", error);
    throw new Error("Failed to encrypt Aadhaar number");
  }
}
