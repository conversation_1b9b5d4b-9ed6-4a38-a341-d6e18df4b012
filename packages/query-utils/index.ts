"use server";

import { prisma } from "@workspace/database/prisma";
import { cookies } from "next/headers";

export type BaseQueryInput = {
  page?: number;
  perPage?: number;
  sort?: Array<{ id: string; desc: boolean }>;
  filters?: any[];
  createdAt?: number[];
  tableFields?: string[];
};

export async function baseQuery(
  model: string,
  input: BaseQueryInput,
  whereConditions: any[] = [],
) {
  try {
    const tenantId = (await cookies())?.get("tenant-id")?.value;
    const userId = (await cookies())?.get("user-id")?.value;

    if (!userId) {
      throw new Error("User ID not found");
    }

    if (!tenantId) {
      throw new Error("Team ID not found");
    }

    // Ensure valid pagination values
    const page = Math.max(1, Number(input.page) || 1);
    const perPage = Math.max(1, Number(input.perPage) || 10);
    const skip = (page - 1) * perPage;

    // Build where conditions
    let whereConditionsWithBranch = [
      ...whereConditions,
      { organizationId: tenantId },
    ].filter(Boolean);

    const where = {
      AND: whereConditionsWithBranch,
    };

    // Build orderBy with proper handling of relation fields
    const orderBy = input.sort?.length
      ? input.sort.map((item) => {
          const [root, nested] = item.id.split(".");

          // Handle nested relation fields (e.g., "user.name")
          if (nested) {
            return {
              [root]: {
                [nested]: item.desc ? "desc" : "asc",
              },
            };
          }

          // Handle direct fields
          return {
            [item.id]: item.desc ? "desc" : "asc",
          };
        })
      : [{ createdAt: "desc" }];

    // Build select
    let select = input.tableFields?.length
      ? input.tableFields.reduce((acc: any, field: string) => {
          const parts = field.split(".");

          if (parts.length > 2) {
            // Handle nested relations (e.g., "patient.user.name")
            const [root, nested, nestedField] = parts;
            acc[root] = acc[root] || { select: {} };
            acc[root].select[nested] = {
              select: {
                [nestedField]: true,
              },
            };
          } else if (parts.length === 2) {
            const [root, nested] = parts;
            if (nested === "user.name") {
              // Special handling for user.name
              acc[root] = acc[root] || { select: {} };
              acc[root].select["user"] = {
                select: {
                  name: true,
                },
              };
            } else {
              acc[root] = acc[root] || { select: {} };
              acc[root].select[nested] = true;
            }
          } else {
            // Handle direct fields
            acc[parts[0]] = true;
          }
          return acc;
        }, {})
      : undefined;

    // Execute query with transaction
    let [data, total] = await prisma.$transaction([
      prisma[model].findMany({
        where,
        take: perPage,
        skip,
        orderBy,
        ...(select && Object.keys(select).length > 0 ? { select } : {}),
      }),
      prisma[model].count({ where }),
    ]);

    return {
      data,
      pageCount: Math.ceil(total / perPage),
    };
  } catch (error) {
    console.error(`Error in ${model} query:`, error);
    throw error; // Re-throw the error for proper error handling
  }
}
