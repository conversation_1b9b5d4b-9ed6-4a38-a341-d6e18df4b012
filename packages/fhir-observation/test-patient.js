// Test script for the FHIR Patient resource
const { generateResource, ResourceType } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateResource(ResourceType.PATIENT, {
      patientID: "ABHA123456789",
      patientFirstName: "<PERSON>",
      patientLastName: "Doe",
      patientGender: "male",
      patientBirthDate: "1980-01-01",
      patientPhone: "+91 **********",
      patientEmail: "<EMAIL>",
      patientAddress: "123 Main Street",
      patientCity: "Mumbai",
      patientState: "Maharashtra",
      patientPostalCode: "400001",
      patientCountry: "India"
    });

    console.log("Success! Generated Patient bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
