// Test script for the FHIR Organization resource
const { generateResource, ResourceType } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateResource(ResourceType.ORGANIZATION, {
      organizationID: "ORG123456",
      organizationName: "Aran Care Hospital",
      organizationPhone: "+91 22 12345678",
      organizationEmail: "<EMAIL>",
      organizationAddress: "456 Healthcare Avenue",
      organizationCity: "Bangalore",
      organizationState: "Karnataka",
      organizationPostalCode: "560001",
      organizationCountry: "India"
    });

    console.log("Success! Generated Organization bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
