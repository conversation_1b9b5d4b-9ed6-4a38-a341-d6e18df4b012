// Simple test script for the FHIR observation package
const { generateBundle } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateBundle("ENTRY_PRACTITIONER_STRATEGY", {
      practitionerName: "<PERSON>. <PERSON>",
      practitionerID: "12345",
    });

    console.log("Success! Generated bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
