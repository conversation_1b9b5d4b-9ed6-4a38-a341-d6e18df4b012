// Test script for the FHIR Observation Body Measurement resource
const { generateResource, ResourceType } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateResource(ResourceType.OBSERVATION_BODY_MEASUREMENT, {
      observationCode: "29463-7",
      observationDisplay: "Body weight",
      observationText: "Body weight",
      patientReference: "Patient/123",
      practitionerReference: "Practitioner/456",
      effectiveDateTime: "2023-06-15T10:30:00+05:30",
      valueQuantity: 70.5,
      valueUnit: "kg",
      valueSystem: "http://unitsofmeasure.org",
      valueCode: "kg"
    });

    console.log("Success! Generated Observation Body Measurement bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
