// Test script for the FHIR DiagnosticReport resource
const { generateResource, ResourceType } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateResource(ResourceType.DIAGNOSTIC_REPORT, {
      reportCode: "24331-1",
      reportDisplay: "Lipid panel - Serum or Plasma",
      reportText: "Lipid Profile Report",
      patientReference: "Patient/123",
      practitionerReference: "Practitioner/456",
      organizationReference: "Organization/789",
      effectiveDateTime: "2023-06-15T10:30:00+05:30",
      issuedDateTime: "2023-06-15T14:30:00+05:30",
      status: "final",
      category: "LAB",
      categoryDisplay: "Laboratory",
      conclusion: "Elevated LDL cholesterol levels",
      observationReference: "Observation/abc"
    });

    console.log("Success! Generated DiagnosticReport bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
