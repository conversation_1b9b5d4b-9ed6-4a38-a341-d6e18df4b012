// Test script for the FHIR Observation Vital Signs resource
const { generateResource, ResourceType } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateResource(ResourceType.OBSERVATION_VITAL_SIGNS, {
      observationCode: "8867-4",
      observationDisplay: "Heart rate",
      observationText: "Heart rate",
      patientReference: "Patient/123",
      practitionerReference: "Practitioner/456",
      effectiveDateTime: "2023-06-15T10:30:00+05:30",
      valueQuantity: 72,
      valueUnit: "beats/minute",
      valueSystem: "http://unitsofmeasure.org",
      valueCode: "/min"
    });

    console.log("Success! Generated Observation Vital Signs bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
