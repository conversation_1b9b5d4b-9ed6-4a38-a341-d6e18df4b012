// Test script for the FHIR Condition resource
const { generateResource, ResourceType } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateResource(ResourceType.CONDITION, {
      conditionCode: "44054006",
      conditionDisplay: "Diabetes mellitus type 2",
      conditionText: "Type 2 Diabetes Mellitus",
      patientReference: "Patient/123",
      practitionerReference: "Practitioner/456",
      recordedDate: "2023-06-15T10:30:00+05:30",
      onsetDate: "2023-01-01",
      clinicalStatus: "active",
      clinicalStatusDisplay: "Active",
      verificationStatus: "confirmed",
      verificationStatusDisplay: "Confirmed",
      category: "problem-list-item",
      categoryDisplay: "Problem List Item",
      severityCode: "6736007",
      severity: "Moderate"
    });

    console.log("Success! Generated Condition bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
