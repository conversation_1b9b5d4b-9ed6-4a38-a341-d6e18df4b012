// Test script for the FHIR MedicationRequest resource
const { generateResource, ResourceType } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateResource(ResourceType.MEDICATION_REQUEST, {
      medicationCode: "315246",
      medicationDisplay: "Metformin 500mg tablet",
      medicationText: "Metformin 500mg tablet",
      patientReference: "Patient/123",
      practitionerReference: "Practitioner/456",
      authoredOn: "2023-06-15T10:30:00+05:30",
      status: "active",
      intent: "order",
      dosageText: "Take 1 tablet twice daily with meals",
      dosageRouteCode: "26643006",
      dosageRoute: "Oral route",
      dosageMethodCode: "421521009",
      dosageMethod: "Swallow",
      dosageFrequency: 2,
      dosagePeriodValue: 1,
      dosagePeriodUnit: "d",
      dosageAsNeeded: false,
      dosageAsNeededCode: "266599000",
      dosageAsNeededReason: "Diabetes mellitus"
    });

    console.log("Success! Generated MedicationRequest bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
