// Test script for the FHIR OPConsultNote document bundle
const { generateResource, ResourceType } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateResource(ResourceType.OP_CONSULT_NOTE, {
      // Composition details
      compositionIdentifier: "645bb0c3-ff7e-4123-bef5-3852a4784813",
      compositionDate: new Date().toISOString(),
      compositionTitle: "Consultation Report",
      
      // Patient details
      patientID: "ABHA123456789",
      patientFirstName: "<PERSON>",
      patientLastName: "Doe",
      patientGender: "male",
      patientBirthDate: "1980-01-01",
      patientPhone: "+91 **********",
      patientEmail: "<EMAIL>",
      patientAddress: "123 Main Street",
      patientCity: "Mumbai",
      patientState: "Maharashtra",
      patientPostalCode: "400001",
      patientCountry: "India",
      
      // Practitioner details
      practitionerID: "21-1521-3828-3227",
      practitionerName: "<PERSON>. <PERSON>",
      practitionerQualification: "MD (Medicine)",
      
      // Organization details
      organizationID: "ORG123456",
      organizationName: "Aran Care Hospital",
      organizationPhone: "+91 22 12345678",
      organizationEmail: "<EMAIL>",
      organizationAddress: "456 Healthcare Avenue",
      organizationCity: "Bangalore",
      organizationState: "Karnataka",
      organizationPostalCode: "560001",
      organizationCountry: "India",
      
      // Encounter details
      encounterID: "ENC123456",
      encounterDate: new Date().toISOString(),
      encounterType: "Outpatient consultation",
      
      // Chief complaint (Condition)
      conditionCode: "44054006",
      conditionDisplay: "Diabetes mellitus type 2",
      conditionText: "Type 2 Diabetes Mellitus",
      conditionRecordedDate: new Date().toISOString(),
      conditionOnsetDate: "2023-01-01",
      conditionClinicalStatus: "active",
      conditionClinicalStatusDisplay: "Active",
      conditionVerificationStatus: "confirmed",
      conditionVerificationStatusDisplay: "Confirmed",
      conditionCategory: "problem-list-item",
      conditionCategoryDisplay: "Problem List Item",
      conditionSeverityCode: "6736007",
      conditionSeverity: "Moderate",
      
      // Allergy
      allergyCode: "91935009",
      allergyDisplay: "Allergy to penicillin",
      allergyText: "Penicillin allergy",
      allergyType: "allergy",
      allergyCriticality: "high",
      allergyOnsetDate: "2010-01-01",
      allergyCategory: "medication",
      
      // Medication
      medicationCode: "315246",
      medicationDisplay: "Metformin 500mg tablet",
      medicationText: "Metformin 500mg tablet",
      medicationAuthoredOn: new Date().toISOString(),
      medicationStatus: "active",
      medicationIntent: "order",
      dosageText: "Take 1 tablet twice daily with meals",
      dosageRouteCode: "26643006",
      dosageRoute: "Oral route",
      dosageMethodCode: "421521009",
      dosageMethod: "Swallow",
      dosageFrequency: 2,
      dosagePeriodValue: 1,
      dosagePeriodUnit: "d",
      dosageAsNeeded: false,
      dosageAsNeededCode: "266599000",
      dosageAsNeededReason: "Diabetes mellitus",
      
      // Observation (Vital Signs)
      observationCode: "8867-4",
      observationDisplay: "Heart rate",
      observationText: "Heart rate",
      observationEffectiveDateTime: new Date().toISOString(),
      observationValueQuantity: 72,
      observationValueUnit: "beats/minute",
      observationValueSystem: "http://unitsofmeasure.org",
      observationValueCode: "/min",
      
      // Diagnostic Report
      reportCode: "24331-1",
      reportDisplay: "Lipid panel - Serum or Plasma",
      reportText: "Lipid Profile Report",
      reportEffectiveDateTime: new Date().toISOString(),
      reportIssuedDateTime: new Date().toISOString(),
      reportStatus: "final",
      reportCategory: "LAB",
      reportCategoryDisplay: "Laboratory",
      reportConclusion: "Elevated LDL cholesterol levels",
      
      // Appointment (Follow-up)
      appointmentDescription: "Follow-up consultation for diabetes management",
      appointmentStart: "2023-07-15T09:00:00Z",
      appointmentEnd: "2023-07-15T09:30:00Z",
      appointmentCreated: new Date().toISOString(),
      
      // Document Reference
      documentTitle: "Previous Lab Results",
      documentDescription: "Previous laboratory test results",
      documentCreated: new Date().toISOString(),
      documentContentType: "application/pdf",
      documentUrl: "https://example.com/documents/lab-results.pdf"
    });

    console.log("Success! Generated OPConsultNote document bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
