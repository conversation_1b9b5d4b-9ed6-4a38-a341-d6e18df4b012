// Test script for the FHIR Observation Blood Pressure resource
const { generateResource, ResourceType } = require('./dist/index');

async function test() {
  try {
    const bundle = await generateResource(ResourceType.OBSERVATION_BLOOD_PRESSURE, {
      patientReference: "Patient/123",
      practitionerReference: "Practitioner/456",
      effectiveDateTime: "2023-06-15T10:30:00+05:30",
      systolicValue: 120,
      diastolicValue: 80,
      bodySiteCode: "368209003",
      bodySiteDisplay: "Right arm"
    });

    console.log("Success! Generated Observation Blood Pressure bundle:");
    console.log(bundle);
  } catch (error) {
    console.error("Error generating bundle:", error);
  }
}

test();
