import crypto from "crypto";
import { STRATEGY } from "../constants/report/op-consult";
import { InputEntity } from "../entities/entry-composition-op-consult-input";
import { EntryPatientStrategy } from "./entry-patient-strategy";
import { EntryPractitionerStrategy } from "./entry-practitioner-strategy";
import { EntryOrganizationStrategy } from "./entry-organization-strategy";
import { EntryConditionStrategy } from "./entry-condition-strategy";
import { EntryMedicationRequestStrategy } from "./entry-medication-request-strategy";
import { EntryObservationVitalSignsStrategy } from "./entry-observation-vital-signs-strategy";
import { EntryDiagnosticReportStrategy } from "./entry-diagnostic-report-strategy";

// Define transformations for specific fields
const transformations: Record<
  string,
  (value: string | number | boolean) => string
> = {
  // Add transformations as needed
};

/**
 * Generates a FHIR OPConsultNote document bundle
 *
 * @param promptInput - Input data for the OPConsultNote
 * @returns JSON string representation of the FHIR document bundle
 */
async function EntryCompositionOPConsultStrategy(
  promptInput: InputEntity,
): Promise<string> {
  try {
    // Generate UUIDs for all resources
    const resourceUUID = crypto.randomUUID();
    const compositionUUID = crypto.randomUUID();
    const patientUUID = crypto.randomUUID();
    const practitionerUUID = crypto.randomUUID();
    const organizationUUID = crypto.randomUUID();
    const encounterUUID = crypto.randomUUID();
    const conditionUUID = crypto.randomUUID();
    const allergyUUID = crypto.randomUUID();
    const medicationUUID = crypto.randomUUID();
    const observationUUID = crypto.randomUUID();
    const diagnosticReportUUID = crypto.randomUUID();
    const appointmentUUID = crypto.randomUUID();
    const documentReferenceUUID = crypto.randomUUID();

    // Create references
    const patientReference = `urn:uuid:${patientUUID}`;
    const practitionerReference = `urn:uuid:${practitionerUUID}`;
    const organizationReference = `urn:uuid:${organizationUUID}`;
    const encounterReference = `urn:uuid:${encounterUUID}`;
    const conditionReference = `urn:uuid:${conditionUUID}`;
    const allergyReference = `urn:uuid:${allergyUUID}`;
    const medicationReference = `urn:uuid:${medicationUUID}`;
    const observationReference = `urn:uuid:${observationUUID}`;
    const diagnosticReportReference = `urn:uuid:${diagnosticReportUUID}`;
    const appointmentReference = `urn:uuid:${appointmentUUID}`;
    const documentReference = `urn:uuid:${documentReferenceUUID}`;

    // Generate individual resources
    const patientResource = await EntryPatientStrategy({
      patientID: promptInput.patientID,
      patientFirstName: promptInput.patientFirstName,
      patientLastName: promptInput.patientLastName,
      patientGender: promptInput.patientGender,
      patientBirthDate: promptInput.patientBirthDate,
      patientPhone: promptInput.patientPhone,
      patientEmail: promptInput.patientEmail,
      patientAddress: promptInput.patientAddress,
      patientCity: promptInput.patientCity,
      patientState: promptInput.patientState,
      patientPostalCode: promptInput.patientPostalCode,
      patientCountry: promptInput.patientCountry,
    });

    const practitionerResource = await EntryPractitionerStrategy({
      practitionerID: promptInput.practitionerID,
      practitionerName: promptInput.practitionerName,
      practitionerQualification: promptInput.practitionerQualification,
    });

    const organizationResource = await EntryOrganizationStrategy({
      organizationID: promptInput.organizationID,
      organizationName: promptInput.organizationName,
      organizationPhone: promptInput.organizationPhone,
      organizationEmail: promptInput.organizationEmail,
      organizationAddress: promptInput.organizationAddress,
      organizationCity: promptInput.organizationCity,
      organizationState: promptInput.organizationState,
      organizationPostalCode: promptInput.organizationPostalCode,
      organizationCountry: promptInput.organizationCountry,
    });

    const conditionResource = await EntryConditionStrategy({
      conditionCode: promptInput.conditionCode,
      conditionDisplay: promptInput.conditionDisplay,
      conditionText: promptInput.conditionText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      recordedDate: promptInput.conditionRecordedDate,
      onsetDate: promptInput.conditionOnsetDate,
      clinicalStatus: promptInput.conditionClinicalStatus,
      clinicalStatusDisplay: promptInput.conditionClinicalStatusDisplay,
      verificationStatus: promptInput.conditionVerificationStatus,
      verificationStatusDisplay: promptInput.conditionVerificationStatusDisplay,
      category: promptInput.conditionCategory,
      categoryDisplay: promptInput.conditionCategoryDisplay,
      severityCode: promptInput.conditionSeverityCode,
      severity: promptInput.conditionSeverity,
    });

    const medicationResource = await EntryMedicationRequestStrategy({
      medicationCode: promptInput.medicationCode,
      medicationDisplay: promptInput.medicationDisplay,
      medicationText: promptInput.medicationText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      authoredOn: promptInput.medicationAuthoredOn,
      status: promptInput.medicationStatus,
      intent: promptInput.medicationIntent,
      dosageText: promptInput.dosageText,
      dosageRouteCode: promptInput.dosageRouteCode,
      dosageRoute: promptInput.dosageRoute,
      dosageMethodCode: promptInput.dosageMethodCode,
      dosageMethod: promptInput.dosageMethod,
      dosageFrequency: promptInput.dosageFrequency,
      dosagePeriodValue: promptInput.dosagePeriodValue,
      dosagePeriodUnit: promptInput.dosagePeriodUnit,
      dosageAsNeeded: promptInput.dosageAsNeeded,
      dosageAsNeededCode: promptInput.dosageAsNeededCode,
      dosageAsNeededReason: promptInput.dosageAsNeededReason,
    });

    const observationResource = await EntryObservationVitalSignsStrategy({
      observationCode: promptInput.observationCode,
      observationDisplay: promptInput.observationDisplay,
      observationText: promptInput.observationText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      effectiveDateTime: promptInput.observationEffectiveDateTime,
      valueQuantity: promptInput.observationValueQuantity,
      valueUnit: promptInput.observationValueUnit,
      valueSystem: promptInput.observationValueSystem,
      valueCode: promptInput.observationValueCode,
    });

    const diagnosticReportResource = await EntryDiagnosticReportStrategy({
      reportCode: promptInput.reportCode,
      reportDisplay: promptInput.reportDisplay,
      reportText: promptInput.reportText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      organizationReference: organizationReference,
      effectiveDateTime: promptInput.reportEffectiveDateTime,
      issuedDateTime: promptInput.reportIssuedDateTime,
      status: promptInput.reportStatus,
      category: promptInput.reportCategory,
      categoryDisplay: promptInput.reportCategoryDisplay,
      conclusion: promptInput.reportConclusion,
      observationReference: observationReference,
    });

    // Create the encounter resource
    const encounterResource = {
      resourceType: "Encounter",
      id: encounterUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter",
        ],
      },
      status: "finished",
      class: {
        system: "http://terminology.hl7.org/CodeSystem/v3-ActCode",
        code: "AMB",
        display: "ambulatory",
      },
      type: [
        {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "11429006",
              display: "Consultation",
            },
          ],
          text: promptInput.encounterType,
        },
      ],
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      participant: [
        {
          individual: {
            reference: practitionerReference,
            display: promptInput.practitionerName,
          },
        },
      ],
      period: {
        start: promptInput.encounterDate,
        end: promptInput.encounterDate,
      },
      serviceProvider: {
        reference: organizationReference,
        display: "Organization",
      },
    };

    // Create the allergy resource
    const allergyResource = {
      resourceType: "AllergyIntolerance",
      id: allergyUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/AllergyIntolerance",
        ],
      },
      clinicalStatus: {
        coding: [
          {
            system:
              "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical",
            code: "active",
            display: "Active",
          },
        ],
      },
      verificationStatus: {
        coding: [
          {
            system:
              "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification",
            code: "confirmed",
            display: "Confirmed",
          },
        ],
      },
      type: promptInput.allergyType,
      category: [promptInput.allergyCategory],
      criticality: promptInput.allergyCriticality,
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: promptInput.allergyCode,
            display: promptInput.allergyDisplay,
          },
        ],
        text: promptInput.allergyText,
      },
      patient: {
        reference: patientReference,
        display: "Patient",
      },
      onsetDateTime: promptInput.allergyOnsetDate,
      recordedDate: new Date().toISOString(),
      recorder: {
        reference: practitionerReference,
        display: promptInput.practitionerName,
      },
    };

    // Create the appointment resource
    const appointmentResource = {
      resourceType: "Appointment",
      id: appointmentUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Appointment",
        ],
      },
      status: "booked",
      description: promptInput.appointmentDescription,
      start: promptInput.appointmentStart,
      end: promptInput.appointmentEnd,
      created: promptInput.appointmentCreated,
      participant: [
        {
          actor: {
            reference: patientReference,
            display: "Patient",
          },
          status: "accepted",
        },
        {
          actor: {
            reference: practitionerReference,
            display: promptInput.practitionerName,
          },
          status: "accepted",
        },
      ],
    };

    // Create the document reference resource
    const documentReferenceResource = {
      resourceType: "DocumentReference",
      id: documentReferenceUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference",
        ],
      },
      status: "current",
      docStatus: "final",
      type: {
        coding: [
          {
            system: "http://loinc.org",
            code: "34108-1",
            display: "Outpatient Note",
          },
        ],
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      date: promptInput.documentCreated,
      author: [
        {
          reference: practitionerReference,
          display: promptInput.practitionerName,
        },
      ],
      custodian: {
        reference: organizationReference,
        display: "Organization",
      },
      description: promptInput.documentDescription,
      content: [
        {
          attachment: {
            contentType: promptInput.documentContentType,
            url: promptInput.documentUrl,
            title: promptInput.documentTitle,
            creation: promptInput.documentCreated,
          },
        },
      ],
      context: {
        encounter: [
          {
            reference: encounterReference,
            display: "Encounter",
          },
        ],
      },
    };

    // Add the UUIDs to the input
    const enhancedInput = {
      ...promptInput,
      resourceUUID,
      compositionUUID,
      patientUUID,
      practitionerUUID,
      organizationUUID,
      encounterUUID,
      conditionUUID,
      allergyUUID,
      medicationUUID,
      observationUUID,
      diagnosticReportUUID,
      appointmentUUID,
      documentReferenceUUID,
      bundleIdentifier: crypto.randomUUID(),
      patientReference,
      practitionerReference,
      organizationReference,
      encounterReference,
      chiefComplaintReference: conditionReference,
      allergyReference,
      medicationReference,
      observationReference,
      diagnosticReportReference,
      appointmentReference,
      documentReference,
    };

    // Convert to string for placeholder replacement
    let jsonString = JSON.stringify(STRATEGY);

    // Apply transformations and replace placeholders
    Object.keys(enhancedInput).forEach((key) => {
      const placeholder = `{${key}}`;

      // Get the value and apply transformation if available
      let value =
        enhancedInput[key as keyof typeof enhancedInput]?.toString() || "";
      if (transformations[key]) {
        value = transformations[key](
          enhancedInput[key as keyof typeof enhancedInput],
        );
      }

      // Replace all occurrences of the placeholder
      jsonString = jsonString.replace(new RegExp(placeholder, "g"), value);
    });

    // Parse the resulting JSON
    const result = JSON.parse(jsonString);

    // Replace the placeholder resources with the actual generated resources
    result.entry.forEach((entry: any, index: number) => {
      if (entry.fullUrl === patientReference) {
        result.entry[index].resource = JSON.parse(patientResource).resource;
      } else if (entry.fullUrl === practitionerReference) {
        result.entry[index].resource =
          JSON.parse(practitionerResource).resource;
      } else if (entry.fullUrl === organizationReference) {
        result.entry[index].resource =
          JSON.parse(organizationResource).resource;
      } else if (entry.fullUrl === conditionReference) {
        result.entry[index].resource = JSON.parse(conditionResource).resource;
      } else if (entry.fullUrl === medicationReference) {
        result.entry[index].resource = JSON.parse(medicationResource).resource;
      } else if (entry.fullUrl === observationReference) {
        result.entry[index].resource = JSON.parse(observationResource).resource;
      } else if (entry.fullUrl === diagnosticReportReference) {
        result.entry[index].resource = JSON.parse(
          diagnosticReportResource,
        ).resource;
      } else if (entry.resource?.resourceType === "Encounter") {
        result.entry[index].resource = encounterResource;
      } else if (entry.resource?.resourceType === "AllergyIntolerance") {
        result.entry[index].resource = allergyResource;
      } else if (entry.resource?.resourceType === "Appointment") {
        result.entry[index].resource = appointmentResource;
      } else if (entry.resource?.resourceType === "DocumentReference") {
        result.entry[index].resource = documentReferenceResource;
      }
    });

    // Return the formatted JSON string
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error(
      "Error generating FHIR OPConsultNote document bundle:",
      error,
    );
    throw error;
  }
}

export { EntryCompositionOPConsultStrategy };
