import crypto from "crypto";
import { EntryPatientStrategy } from "./entry-patient-strategy";
import { EntryPractitionerStrategy } from "./entry-practitioner-strategy";
import { EntryOrganizationStrategy } from "./entry-organization-strategy";
import { EntryDiagnosticReportStrategy } from "./entry-diagnostic-report-strategy";
import { EntryObservationVitalSignsStrategy } from "./entry-observation-vital-signs-strategy";

// Define the input entity interface
export interface DiagnosticReportInput {
  // Composition details
  compositionIdentifier: string;
  compositionDate: string;
  compositionTitle: string;

  // Patient details
  patientID: string;
  patientFirstName: string;
  patientLastName: string;
  patientGender: string;
  patientBirthDate: string;
  patientPhone: string;
  patientEmail: string;
  patientAddress: string;
  patientCity: string;
  patientState: string;
  patientPostalCode: string;
  patientCountry: string;

  // Practitioner details (author)
  practitionerID: string;
  practitionerName: string;
  practitionerQualification: string;

  // Organization details (custodian)
  organizationID: string;
  organizationName: string;
  organizationPhone: string;
  organizationEmail: string;
  organizationAddress: string;
  organizationCity: string;
  organizationState: string;
  organizationPostalCode: string;
  organizationCountry: string;

  // Encounter details
  encounterID: string;
  encounterDate: string;
  encounterType: string;

  // Diagnostic Report
  reportCode: string;
  reportDisplay: string;
  reportText: string;
  reportEffectiveDateTime: string;
  reportIssuedDateTime: string;
  reportStatus: string;
  reportCategory: string;
  reportCategoryDisplay: string;
  reportConclusion: string;

  // Observation (Lab Result)
  observationCode: string;
  observationDisplay: string;
  observationText: string;
  observationEffectiveDateTime: string;
  observationValueQuantity: number;
  observationValueUnit: string;
  observationValueSystem: string;
  observationValueCode: string;

  // Specimen
  specimenType: string;
  specimenTypeDisplay: string;
  specimenCollectionDate: string;
  specimenReceivedDate: string;
  specimenContainer: string;
  specimenContainerDisplay: string;

  // Document Reference
  documentTitle: string;
  documentDescription: string;
  documentCreated: string;
  documentContentType: string;
  documentUrl: string;
}

// Define transformations for specific fields
// const transformations: Record<string, (value: string | number | boolean) => string> = {
//   // Add transformations as needed
// };

/**
 * Generates a FHIR DiagnosticReport document bundle
 *
 * @param promptInput - Input data for the DiagnosticReport
 * @returns JSON string representation of the FHIR document bundle
 */
async function ReportDiagnosticStrategy(
  promptInput: DiagnosticReportInput,
): Promise<string> {
  try {
    // Validate input

    // Generate UUIDs for all resources
    const resourceUUID = crypto.randomUUID();
    const compositionUUID = crypto.randomUUID();
    const patientUUID = crypto.randomUUID();
    const practitionerUUID = crypto.randomUUID();
    const organizationUUID = crypto.randomUUID();
    const encounterUUID = crypto.randomUUID();
    const diagnosticReportUUID = crypto.randomUUID();
    const observationUUID = crypto.randomUUID();
    const specimenUUID = crypto.randomUUID();
    const documentReferenceUUID = crypto.randomUUID();

    // Create references
    const patientReference = `urn:uuid:${patientUUID}`;
    const practitionerReference = `urn:uuid:${practitionerUUID}`;
    const organizationReference = `urn:uuid:${organizationUUID}`;
    const encounterReference = `urn:uuid:${encounterUUID}`;
    const diagnosticReportReference = `urn:uuid:${diagnosticReportUUID}`;
    const observationReference = `urn:uuid:${observationUUID}`;
    const specimenReference = `urn:uuid:${specimenUUID}`;
    const documentReference = `urn:uuid:${documentReferenceUUID}`;

    // Generate individual resources
    const patientResource = await EntryPatientStrategy({
      patientID: promptInput.patientID,
      patientFirstName: promptInput.patientFirstName,
      patientLastName: promptInput.patientLastName,
      patientGender: promptInput.patientGender,
      patientBirthDate: promptInput.patientBirthDate,
      patientPhone: promptInput.patientPhone,
      patientEmail: promptInput.patientEmail,
      patientAddress: promptInput.patientAddress,
      patientCity: promptInput.patientCity,
      patientState: promptInput.patientState,
      patientPostalCode: promptInput.patientPostalCode,
      patientCountry: promptInput.patientCountry,
    });

    const practitionerResource = await EntryPractitionerStrategy({
      practitionerID: promptInput.practitionerID,
      practitionerName: promptInput.practitionerName,
      practitionerQualification: promptInput.practitionerQualification,
    });

    const organizationResource = await EntryOrganizationStrategy({
      organizationID: promptInput.organizationID,
      organizationName: promptInput.organizationName,
      organizationPhone: promptInput.organizationPhone,
      organizationEmail: promptInput.organizationEmail,
      organizationAddress: promptInput.organizationAddress,
      organizationCity: promptInput.organizationCity,
      organizationState: promptInput.organizationState,
      organizationPostalCode: promptInput.organizationPostalCode,
      organizationCountry: promptInput.organizationCountry,
    });

    const observationResource = await EntryObservationVitalSignsStrategy({
      observationCode: promptInput.observationCode,
      observationDisplay: promptInput.observationDisplay,
      observationText: promptInput.observationText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      effectiveDateTime: promptInput.observationEffectiveDateTime,
      valueQuantity: promptInput.observationValueQuantity,
      valueUnit: promptInput.observationValueUnit,
      valueSystem: promptInput.observationValueSystem,
      valueCode: promptInput.observationValueCode,
    });

    const diagnosticReportResource = await EntryDiagnosticReportStrategy({
      reportCode: promptInput.reportCode,
      reportDisplay: promptInput.reportDisplay,
      reportText: promptInput.reportText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      organizationReference: organizationReference,
      effectiveDateTime: promptInput.reportEffectiveDateTime,
      issuedDateTime: promptInput.reportIssuedDateTime,
      status: promptInput.reportStatus,
      category: promptInput.reportCategory,
      categoryDisplay: promptInput.reportCategoryDisplay,
      conclusion: promptInput.reportConclusion,
      observationReference: observationReference,
    });

    // Create the encounter resource
    const encounterResource = {
      resourceType: "Encounter",
      id: encounterUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter",
        ],
      },
      status: "finished",
      class: {
        system: "http://terminology.hl7.org/CodeSystem/v3-ActCode",
        code: "AMB",
        display: "ambulatory",
      },
      type: [
        {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "11429006",
              display: "Consultation",
            },
          ],
          text: promptInput.encounterType,
        },
      ],
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      participant: [
        {
          individual: {
            reference: practitionerReference,
            display: promptInput.practitionerName,
          },
        },
      ],
      period: {
        start: promptInput.encounterDate,
        end: promptInput.encounterDate,
      },
      serviceProvider: {
        reference: organizationReference,
        display: "Organization",
      },
    };

    // Create the specimen resource
    const specimenResource = {
      resourceType: "Specimen",
      id: specimenUUID,
      meta: {
        profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Specimen"],
      },
      status: "available",
      type: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: promptInput.specimenType,
            display: promptInput.specimenTypeDisplay,
          },
        ],
        text: promptInput.specimenTypeDisplay,
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      receivedTime: promptInput.specimenReceivedDate,
      collection: {
        collectedDateTime: promptInput.specimenCollectionDate,
        collector: {
          reference: practitionerReference,
          display: promptInput.practitionerName,
        },
      },
      container: [
        {
          type: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: promptInput.specimenContainer,
                display: promptInput.specimenContainerDisplay,
              },
            ],
            text: promptInput.specimenContainerDisplay,
          },
        },
      ],
    };

    // Create the document reference resource
    const documentReferenceResource = {
      resourceType: "DocumentReference",
      id: documentReferenceUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference",
        ],
      },
      status: "current",
      docStatus: "final",
      type: {
        coding: [
          {
            system: "http://loinc.org",
            code: "11502-2",
            display: "Laboratory report",
          },
        ],
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      date: promptInput.documentCreated,
      author: [
        {
          reference: practitionerReference,
          display: promptInput.practitionerName,
        },
      ],
      custodian: {
        reference: organizationReference,
        display: "Organization",
      },
      description: promptInput.documentDescription,
      content: [
        {
          attachment: {
            contentType: promptInput.documentContentType,
            data: promptInput.documentUrl,
            title: promptInput.documentTitle,
            creation: promptInput.documentCreated,
          },
        },
      ],
      context: {
        encounter: [
          {
            reference: encounterReference,
            display: "Encounter",
          },
        ],
        related: [
          {
            reference: diagnosticReportReference,
            display: "DiagnosticReport",
          },
        ],
      },
    };

    // Create the composition resource
    const compositionResource = {
      resourceType: "Composition",
      id: compositionUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DiagnosticReportRecord",
        ],
      },
      language: "en-IN",
      text: {
        status: "generated",
        div: `<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>${promptInput.compositionTitle}</div>`,
      },
      identifier: {
        system: "https://ndhm.in/phr",
        value: promptInput.compositionIdentifier,
      },
      status: "final",
      type: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "721981007",
            display: "Diagnostic studies report",
          },
        ],
        text: "Diagnostic Report",
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      encounter: {
        reference: encounterReference,
        display: "Encounter",
      },
      date: promptInput.compositionDate,
      author: [
        {
          reference: practitionerReference,
          display: promptInput.practitionerName,
        },
      ],
      title: promptInput.compositionTitle,
      custodian: {
        reference: organizationReference,
        display: "Organization",
      },
      section: [
        {
          title: "Diagnostic Reports",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "721981007",
                display: "Diagnostic studies report",
              },
            ],
          },
          entry: [
            {
              reference: diagnosticReportReference,
              display: "DiagnosticReport",
            },
          ],
        },
        {
          title: "Observations",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "364712009",
                display: "Clinical observation section",
              },
            ],
          },
          entry: [
            {
              reference: observationReference,
              display: "Observation",
            },
          ],
        },
        {
          title: "Specimens",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "123038009",
                display: "Specimen",
              },
            ],
          },
          entry: [
            {
              reference: specimenReference,
              display: "Specimen",
            },
          ],
        },
        {
          title: "Document Reference",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "371530004",
                display: "Clinical report",
              },
            ],
          },
          entry: [
            {
              reference: documentReference,
              display: "DocumentReference",
            },
          ],
        },
      ],
    };

    // Construct the bundle
    const bundle = {
      resourceType: "Bundle",
      id: resourceUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle",
        ],
        security: [
          {
            system: "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
            code: "V",
            display: "very restricted",
          },
        ],
      },
      identifier: {
        system: "http://hip.in",
        value: crypto.randomUUID(),
      },
      type: "document",
      timestamp: new Date().toISOString(),
      entry: [
        // Composition entry (always first in a document bundle)
        {
          fullUrl: `urn:uuid:${compositionUUID}`,
          resource: compositionResource,
        },
        // Patient entry
        {
          fullUrl: patientReference,
          resource: JSON.parse(patientResource).resource,
        },
        // Practitioner entry (author)
        {
          fullUrl: practitionerReference,
          resource: JSON.parse(practitionerResource).resource,
        },
        // Organization entry (custodian)
        {
          fullUrl: organizationReference,
          resource: JSON.parse(organizationResource).resource,
        },
        // Encounter entry
        {
          fullUrl: encounterReference,
          resource: encounterResource,
        },
        // Diagnostic Report entry
        {
          fullUrl: diagnosticReportReference,
          resource: JSON.parse(diagnosticReportResource).resource,
        },
        // Observation entry
        {
          fullUrl: observationReference,
          resource: JSON.parse(observationResource).resource,
        },
        // Specimen entry
        {
          fullUrl: specimenReference,
          resource: specimenResource,
        },
        // Document Reference entry
        {
          fullUrl: documentReference,
          resource: documentReferenceResource,
        },
      ],
    };

    // Return the formatted JSON string
    return JSON.stringify(bundle, null, 2);
  } catch (error) {
    console.error(
      "Error generating FHIR DiagnosticReport document bundle:",
      error,
    );
    throw error;
  }
}

export { ReportDiagnosticStrategy };
