import crypto from "crypto";
import { STRATEGY } from "../constants/entry/observation-body-measurement";
import { InputEntity } from "../entities/entry-observation-body-measurement-input";

// Define transformations for specific fields
const transformations: Record<string, (value: string | number) => string> = {
  observationCode: (value) => String(value).trim(),
  observationDisplay: (value) => String(value).trim(),
  observationText: (value) => String(value).trim(),
  patientReference: (value) => String(value).trim(),
  practitionerReference: (value) => String(value).trim(),
  effectiveDateTime: (value) => String(value).trim(),
  valueQuantity: (value) => String(value),
  valueUnit: (value) => String(value).trim(),
  valueSystem: (value) => String(value).trim(),
  valueCode: (value) => String(value).trim(),
  // Add more transformations as needed
};

// Validation function to ensure required fields are present

/**
 * Generates a FHIR Observation Body Measurement resource entry
 *
 * @param promptInput - Input data for the observation
 * @returns JSON string representation of the FHIR resource
 */
async function EntryObservationBodyMeasurementStrategy(
  promptInput: InputEntity,
): Promise<string> {
  try {
    // Validate input

    // Generate a new UUID for the resource
    const uuid = crypto.randomUUID();

    // Add the UUID to the input
    const enhancedInput = {
      ...promptInput,
      resourceUUID: uuid,
    };

    // Convert to string for placeholder replacement
    let jsonString = JSON.stringify(STRATEGY);

    // Apply transformations and replace placeholders
    Object.keys(enhancedInput).forEach((key) => {
      const placeholder = `{${key}}`;

      // Get the value and apply transformation if available
      let value =
        enhancedInput[
          key as keyof (InputEntity & { resourceUUID: string })
        ]?.toString() || "";
      if (transformations[key]) {
        value = transformations[key](
          enhancedInput[key as keyof (InputEntity & { resourceUUID: string })],
        );
      }

      // Replace all occurrences of the placeholder
      jsonString = jsonString.replace(new RegExp(placeholder, "g"), value);
    });

    // Parse and validate the resulting JSON
    const result = JSON.parse(jsonString);

    // Basic validation - ensure required fields are present in the result
    if (
      !result.resource?.code?.coding?.[0]?.code ||
      !result.resource?.subject?.reference
    ) {
      throw new Error("Generated FHIR resource is missing required fields");
    }

    // Return the formatted JSON string
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error(
      "Error generating FHIR Observation Body Measurement resource:",
      error,
    );
    throw error;
  }
}

export { EntryObservationBodyMeasurementStrategy };
