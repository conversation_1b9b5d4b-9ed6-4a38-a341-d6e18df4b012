import crypto from "crypto";
import { STRATEGY } from "../constants/entry/observation-blood-pressure";
import { InputEntity } from "../entities/entry-observation-blood-pressure-input";

// Define transformations for specific fields
const transformations: Record<string, (value: string | number) => string> = {
  patientReference: (value) => String(value).trim(),
  practitionerReference: (value) => String(value).trim(),
  effectiveDateTime: (value) => String(value).trim(),
  systolicValue: (value) => String(value),
  diastolicValue: (value) => String(value),
  bodySiteCode: (value) => String(value).trim(),
  bodySiteDisplay: (value) => String(value).trim(),
  // Add more transformations as needed
};

// Validation function to ensure required fields are present

/**
 * Generates a FHIR Observation Blood Pressure resource entry
 *
 * @param promptInput - Input data for the observation
 * @returns JSON string representation of the FHIR resource
 */
async function EntryObservationBloodPressureStrategy(
  promptInput: InputEntity,
): Promise<string> {
  try {
    // Validate input

    // Generate a new UUID for the resource
    const uuid = crypto.randomUUID();

    // Add the UUID to the input
    const enhancedInput = {
      ...promptInput,
      resourceUUID: uuid,
    };

    // Convert to string for placeholder replacement
    let jsonString = JSON.stringify(STRATEGY);

    // Apply transformations and replace placeholders
    Object.keys(enhancedInput).forEach((key) => {
      const placeholder = `{${key}}`;

      // Get the value and apply transformation if available
      let value =
        enhancedInput[
          key as keyof (InputEntity & { resourceUUID: string })
        ]?.toString() || "";
      if (transformations[key]) {
        value = transformations[key](
          enhancedInput[key as keyof (InputEntity & { resourceUUID: string })],
        );
      }

      // Replace all occurrences of the placeholder
      jsonString = jsonString.replace(new RegExp(placeholder, "g"), value);
    });

    // Parse and validate the resulting JSON
    const result = JSON.parse(jsonString);

    // Basic validation - ensure required fields are present in the result
    if (
      !result.resource?.subject?.reference ||
      !result.resource?.component?.[0]?.valueQuantity?.value
    ) {
      throw new Error("Generated FHIR resource is missing required fields");
    }

    // Return the formatted JSON string
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error(
      "Error generating FHIR Observation Blood Pressure resource:",
      error,
    );
    throw error;
  }
}

export { EntryObservationBloodPressureStrategy };
