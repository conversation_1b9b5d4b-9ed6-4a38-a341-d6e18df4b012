import crypto from "crypto";
import { STRATEGY } from "../constants/entry/organization";
import { InputEntity } from "../entities/entry-organization-input";

// Define transformations for specific fields
const transformations: Record<string, (value: string) => string> = {
  organizationID: (value) => value.trim(),
  organizationName: (value) => value.trim(),
  organizationPhone: (value) => value.trim(),
  organizationEmail: (value) => value.trim(),
  organizationAddress: (value) => value.trim(),
  organizationCity: (value) => value.trim(),
  organizationState: (value) => value.trim(),
  organizationPostalCode: (value) => value.trim(),
  organizationCountry: (value) => value.trim(),
  // Add more transformations as needed
};

// Validation function to ensure required fields are present
/**
 * Generates a FHIR Organization resource entry
 *
 * @param promptInput - Input data for the organization
 * @returns JSON string representation of the FHIR resource
 */
async function EntryOrganizationStrategy(
  promptInput: InputEntity,
): Promise<string> {
  try {
    // Validate input

    // Generate a new UUID for the resource
    const uuid = crypto.randomUUID();

    // Add the UUID to the input
    const enhancedInput = {
      ...promptInput,
      resourceUUID: uuid,
    };

    // Convert to string for placeholder replacement
    let jsonString = JSON.stringify(STRATEGY);

    // Apply transformations and replace placeholders
    Object.keys(enhancedInput).forEach((key) => {
      const placeholder = `{${key}}`;

      // Get the value and apply transformation if available
      let value =
        enhancedInput[
          key as keyof (InputEntity & { resourceUUID: string })
        ]?.toString() || "";
      if (transformations[key]) {
        value = transformations[key](value);
      }

      // Replace all occurrences of the placeholder
      jsonString = jsonString.replace(new RegExp(placeholder, "g"), value);
    });

    // Parse and validate the resulting JSON
    const result = JSON.parse(jsonString);

    // Basic validation - ensure required fields are present in the result
    if (!result.resource?.identifier?.[0]?.value || !result.resource?.name) {
      throw new Error("Generated FHIR resource is missing required fields");
    }

    // Return the formatted JSON string
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error("Error generating FHIR Organization resource:", error);
    throw error;
  }
}

export { EntryOrganizationStrategy };
