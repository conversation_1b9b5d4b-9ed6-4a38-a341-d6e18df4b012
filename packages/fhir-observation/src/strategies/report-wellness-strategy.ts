import crypto from "crypto";
import { EntryPatientStrategy } from "./entry-patient-strategy";
import { EntryPractitionerStrategy } from "./entry-practitioner-strategy";
import { EntryOrganizationStrategy } from "./entry-organization-strategy";
import { EntryObservationVitalSignsStrategy } from "./entry-observation-vital-signs-strategy";

// Define the input entity interface
export interface WellnessInput {
  // Composition details
  compositionIdentifier: string;
  compositionDate: string;
  compositionTitle: string;

  // Patient details
  patientID: string;
  patientFirstName: string;
  patientLastName: string;
  patientGender: string;
  patientBirthDate: string;
  patientPhone: string;
  patientEmail: string;
  patientAddress: string;
  patientCity: string;
  patientState: string;
  patientPostalCode: string;
  patientCountry: string;

  // Practitioner details (author)
  practitionerID: string;
  practitionerName: string;
  practitionerQualification: string;

  // Organization details (custodian)
  organizationID: string;
  organizationName: string;
  organizationPhone: string;
  organizationEmail: string;
  organizationAddress: string;
  organizationCity: string;
  organizationState: string;
  organizationPostalCode: string;
  organizationCountry: string;

  // Encounter details
  encounterID: string;
  encounterDate: string;
  encounterType: string;

  // Observation (Vital Signs) details
  observationCode: string;
  observationDisplay: string;
  observationText: string;
  observationEffectiveDateTime: string;
  observationValueQuantity: number;
  observationValueUnit: string;
  observationValueSystem: string;
  observationValueCode: string;

  // Document Reference
  documentTitle: string;
  documentDescription: string;
  documentCreated: string;
  documentContentType: string;
  documentUrl: string;
}

// Validation function to ensure required fields are present and provide defaults
/**
 * Generates a FHIR WellnessRecord document bundle
 *
 * @param promptInput - Input data for the WellnessRecord
 * @returns JSON string representation of the FHIR document bundle
 */
async function ReportWellnessStrategy(
  promptInput: WellnessInput,
): Promise<string> {
  try {
    // Validate input

    // Generate UUIDs for all resources
    const resourceUUID = crypto.randomUUID();
    const compositionUUID = crypto.randomUUID();
    const patientUUID = crypto.randomUUID();
    const practitionerUUID = crypto.randomUUID();
    const organizationUUID = crypto.randomUUID();
    const encounterUUID = crypto.randomUUID();
    const observationUUID = crypto.randomUUID();
    const documentReferenceUUID = crypto.randomUUID();

    // Create references
    const patientReference = `urn:uuid:${patientUUID}`;
    const practitionerReference = `urn:uuid:${practitionerUUID}`;
    const organizationReference = `urn:uuid:${organizationUUID}`;
    const encounterReference = `urn:uuid:${encounterUUID}`;
    const observationReference = `urn:uuid:${observationUUID}`;
    const documentReference = `urn:uuid:${documentReferenceUUID}`;

    // Generate individual resources
    const patientResource = await EntryPatientStrategy({
      patientID: promptInput.patientID,
      patientFirstName: promptInput.patientFirstName,
      patientLastName: promptInput.patientLastName,
      patientGender: promptInput.patientGender,
      patientBirthDate: promptInput.patientBirthDate,
      patientPhone: promptInput.patientPhone,
      patientEmail: promptInput.patientEmail,
      patientAddress: promptInput.patientAddress,
      patientCity: promptInput.patientCity,
      patientState: promptInput.patientState,
      patientPostalCode: promptInput.patientPostalCode,
      patientCountry: promptInput.patientCountry,
    });

    const practitionerResource = await EntryPractitionerStrategy({
      practitionerID: promptInput.practitionerID,
      practitionerName: promptInput.practitionerName,
      practitionerQualification: promptInput.practitionerQualification,
    });

    const organizationResource = await EntryOrganizationStrategy({
      organizationID: promptInput.organizationID,
      organizationName: promptInput.organizationName,
      organizationPhone: promptInput.organizationPhone,
      organizationEmail: promptInput.organizationEmail,
      organizationAddress: promptInput.organizationAddress,
      organizationCity: promptInput.organizationCity,
      organizationState: promptInput.organizationState,
      organizationPostalCode: promptInput.organizationPostalCode,
      organizationCountry: promptInput.organizationCountry,
    });

    const observationResource = await EntryObservationVitalSignsStrategy({
      observationCode: promptInput.observationCode,
      observationDisplay: promptInput.observationDisplay,
      observationText: promptInput.observationText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      effectiveDateTime: promptInput.observationEffectiveDateTime,
      valueQuantity: promptInput.observationValueQuantity,
      valueUnit: promptInput.observationValueUnit,
      valueSystem: promptInput.observationValueSystem,
      valueCode: promptInput.observationValueCode,
    });

    // Create the encounter resource
    const encounterResource = {
      resourceType: "Encounter",
      id: encounterUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter",
        ],
      },
      status: "finished",
      class: {
        system: "http://terminology.hl7.org/CodeSystem/v3-ActCode",
        code: "AMB",
        display: "ambulatory",
      },
      type: [
        {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "11429006",
              display: "Consultation",
            },
          ],
          text: promptInput.encounterType || "Wellness check",
        },
      ],
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      participant: [
        {
          individual: {
            reference: practitionerReference,
            display: promptInput.practitionerName,
          },
        },
      ],
      period: {
        start: promptInput.encounterDate,
        end: promptInput.encounterDate,
      },
      serviceProvider: {
        reference: organizationReference,
        display: "Organization",
      },
    };

    // Create the document reference resource only if documentUrl is provided
    let documentReferenceResource = null;
    if (promptInput.documentUrl) {
      documentReferenceResource = {
        resourceType: "DocumentReference",
        id: documentReferenceUUID,
        meta: {
          profile: [
            "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference",
          ],
        },
        status: "current",
        docStatus: "final",
        type: {
          coding: [
            {
              system: "http://loinc.org",
              code: "34117-2",
              display: "Health status assessment note",
            },
          ],
        },
        subject: {
          reference: patientReference,
          display: "Patient",
        },
        date: promptInput.documentCreated || new Date().toISOString(),
        author: [
          {
            reference: practitionerReference,
            display: promptInput.practitionerName,
          },
        ],
        custodian: {
          reference: organizationReference,
          display: "Organization",
        },
        description:
          promptInput.documentDescription || "Wellness record document",
        content: [
          {
            attachment: {
              contentType: promptInput.documentContentType || "application/pdf",
              data: promptInput.documentUrl,
              title: promptInput.documentTitle || "Wellness Record",
              creation: promptInput.documentCreated || new Date().toISOString(),
            },
          },
        ],
        context: {
          encounter: [
            {
              reference: encounterReference,
              display: "Encounter",
            },
          ],
          related: [
            {
              reference: observationReference,
              display: "Observation",
            },
          ],
        },
      };
    }

    // Create the composition resource
    const compositionResource = {
      resourceType: "Composition",
      id: compositionUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/WellnessRecord",
        ],
      },
      language: "en-IN",
      text: {
        status: "generated",
        div: `<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>${promptInput.compositionTitle}</div>`,
      },
      identifier: {
        system: "https://ndhm.in/phr",
        value: promptInput.compositionIdentifier,
      },
      status: "final",
      type: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "11429006",
            display: "Consultation note",
          },
        ],
        text: "Wellness Record",
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      encounter: {
        reference: encounterReference,
        display: "Encounter",
      },
      date: promptInput.compositionDate,
      author: [
        {
          reference: practitionerReference,
          display: promptInput.practitionerName,
        },
      ],
      title: promptInput.compositionTitle,
      custodian: {
        reference: organizationReference,
        display: "Organization",
      },
      section: [
        {
          title: "Vital Signs",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "8716-3",
                display: "Vital signs",
              },
            ],
          },
          entry: [
            {
              reference: observationReference,
              display: "Observation",
            },
          ],
        },
        {
          title: "Document Reference",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "371530004",
                display: "Clinical report",
              },
            ],
          },
          entry: [
            {
              reference: documentReference,
              display: "DocumentReference",
            },
          ],
        },
      ],
    };

    // Construct the bundle
    const bundle = {
      resourceType: "Bundle",
      id: resourceUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle",
        ],
        security: [
          {
            system: "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
            code: "V",
            display: "very restricted",
          },
        ],
      },
      identifier: {
        system: "http://hip.in",
        value: crypto.randomUUID(),
      },
      type: "document",
      timestamp: new Date().toISOString(),
      entry: [
        // Composition entry (always first in a document bundle)
        {
          fullUrl: `urn:uuid:${compositionUUID}`,
          resource: compositionResource,
        },
        // Patient entry
        {
          fullUrl: patientReference,
          resource: JSON.parse(patientResource).resource,
        },
        // Practitioner entry (author)
        {
          fullUrl: practitionerReference,
          resource: JSON.parse(practitionerResource).resource,
        },
        // Organization entry (custodian)
        {
          fullUrl: organizationReference,
          resource: JSON.parse(organizationResource).resource,
        },
        // Encounter entry
        {
          fullUrl: encounterReference,
          resource: encounterResource,
        },
        // Observation entry
        {
          fullUrl: observationReference,
          resource: JSON.parse(observationResource).resource,
        },
        // Document Reference entry (only if documentReferenceResource exists)
        ...(documentReferenceResource
          ? [
              {
                fullUrl: documentReference,
                resource: documentReferenceResource,
              },
            ]
          : []),
      ],
    };

    // Return the formatted JSON string
    return JSON.stringify(bundle, null, 2);
  } catch (error) {
    console.error(
      "Error generating FHIR WellnessRecord document bundle:",
      error,
    );
    throw error;
  }
}

export { ReportWellnessStrategy };
