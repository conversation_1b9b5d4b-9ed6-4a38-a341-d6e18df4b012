import crypto from "crypto";
import { STRATEGY } from "../constants/entry/practitioner";
import { InputEntity } from "../entities/entry-practitioner-input";

// Define transformations for specific fields
const transformations: Record<string, (value: string) => string> = {
  practitionerID: (value) => value.trim(), // Ensure no whitespace
  practitionerName: (value) => value.trim(), // Ensure no whitespace
  // Add more transformations as needed
};

/**
 * Generates a FHIR Practitioner resource entry
 *
 * @param promptInput - Input data for the practitioner
 * @returns JSON string representation of the FHIR resource
 */
async function EntryPractitionerStrategy(
  promptInput: InputEntity,
): Promise<string> {
  try {
    // Validate input

    // Generate a new UUID for the resource
    const uuid = crypto.randomUUID();

    // Add the UUID to the input
    const enhancedInput = {
      ...promptInput,
      resourceUUID: uuid,
    };

    // Convert to string for placeholder replacement
    let jsonString = JSON.stringify(STRATEGY);

    // Apply transformations and replace placeholders
    Object.keys(enhancedInput).forEach((key) => {
      const placeholder = `{${key}}`;

      // Get the value and apply transformation if available
      let value =
        enhancedInput[
          key as keyof (InputEntity & { resourceUUID: string })
        ]?.toString() || "";
      if (transformations[key]) {
        value = transformations[key](value);
      }

      // Replace all occurrences of the placeholder
      jsonString = jsonString.replace(new RegExp(placeholder, "g"), value);
    });

    // Parse and validate the resulting JSON
    const result = JSON.parse(jsonString);

    // Basic validation - ensure required fields are present in the result
    if (
      !result.resource?.identifier?.[0]?.value ||
      !result.resource?.name?.[0]?.text
    ) {
      throw new Error("Generated FHIR resource is missing required fields");
    }

    // Return the formatted JSON string
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error("Error generating FHIR Practitioner resource:", error);
    throw error;
  }
}

export { EntryPractitionerStrategy };
