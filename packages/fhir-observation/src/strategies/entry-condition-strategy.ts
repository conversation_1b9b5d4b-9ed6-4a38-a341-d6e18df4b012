import crypto from "crypto";
import { STRATEGY } from "../constants/entry/entry-condition";
import { InputEntity } from "../entities/entry-condition-input";

// Define transformations for specific fields
const transformations: Record<string, (value: string) => string> = {
  conditionCode: (value) => value.trim(),
  conditionDisplay: (value) => value.trim(),
  conditionText: (value) => value.trim(),
  patientReference: (value) => value.trim(),
  practitionerReference: (value) => value.trim(),
  recordedDate: (value) => value.trim(),
  onsetDate: (value) => value.trim(),
  clinicalStatus: (value) => value.trim(),
  clinicalStatusDisplay: (value) => value.trim(),
  verificationStatus: (value) => value.trim(),
  verificationStatusDisplay: (value) => value.trim(),
  category: (value) => value.trim(),
  categoryDisplay: (value) => value.trim(),
  severityCode: (value) => value.trim(),
  severity: (value) => value.trim(),
  // Add more transformations as needed
};

// Validation function to ensure required fields are present

/**
 * Generates a FHIR Condition resource entry
 *
 * @param promptInput - Input data for the condition
 * @returns JSON string representation of the FHIR resource
 */
async function EntryConditionStrategy(
  promptInput: InputEntity,
): Promise<string> {
  try {
    // Validate input

    // Generate a new UUID for the resource
    const uuid = crypto.randomUUID();

    // Add the UUID to the input
    const enhancedInput = {
      ...promptInput,
      resourceUUID: uuid,
    };

    // Convert to string for placeholder replacement
    let jsonString = JSON.stringify(STRATEGY);

    // Apply transformations and replace placeholders
    Object.keys(enhancedInput).forEach((key) => {
      const placeholder = `{${key}}`;

      // Get the value and apply transformation if available
      let value =
        enhancedInput[
          key as keyof (InputEntity & { resourceUUID: string })
        ]?.toString() || "";
      if (transformations[key]) {
        value = transformations[key](value);
      }

      // Replace all occurrences of the placeholder
      jsonString = jsonString.replace(new RegExp(placeholder, "g"), value);
    });

    // Parse and validate the resulting JSON
    const result = JSON.parse(jsonString);

    // Basic validation - ensure required fields are present in the result
    if (
      !result.resource?.code?.coding?.[0]?.code ||
      !result.resource?.subject?.reference
    ) {
      throw new Error("Generated FHIR resource is missing required fields");
    }

    // Return the formatted JSON string
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error("Error generating FHIR Condition resource:", error);
    throw error;
  }
}

export { EntryConditionStrategy };
