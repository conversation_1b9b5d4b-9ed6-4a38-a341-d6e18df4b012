import crypto from "crypto";
import { EntryPatientStrategy } from "./entry-patient-strategy";
import { EntryPractitionerStrategy } from "./entry-practitioner-strategy";
import { EntryOrganizationStrategy } from "./entry-organization-strategy";

// Define the input entity interface
export interface ImmunizationInput {
  // Composition details
  compositionIdentifier: string;
  compositionDate: string;
  compositionTitle: string;

  // Patient details
  patientID: string;
  patientFirstName: string;
  patientLastName: string;
  patientGender: string;
  patientBirthDate: string;
  patientPhone: string;
  patientEmail: string;
  patientAddress: string;
  patientCity: string;
  patientState: string;
  patientPostalCode: string;
  patientCountry: string;

  // Practitioner details (author)
  practitionerID: string;
  practitionerName: string;
  practitionerQualification: string;

  // Organization details (custodian)
  organizationID: string;
  organizationName: string;
  organizationPhone?: string;
  organizationEmail?: string;
  organizationAddress?: string;
  organizationCity?: string;
  organizationState?: string;
  organizationPostalCode?: string;
  organizationCountry?: string;

  // Encounter details
  encounterID: string;
  encounterDate: string;
  encounterType: string;

  // Immunization details
  immunizationStatus: string;
  immunizationVaccineCode: string;
  immunizationVaccineDisplay: string;
  immunizationVaccineText: string;
  immunizationOccurrenceDateTime: string;
  immunizationLotNumber: string;
  immunizationExpirationDate: string;
  immunizationSite: string;
  immunizationSiteDisplay: string;
  immunizationRoute: string;
  immunizationRouteDisplay: string;
  immunizationDoseQuantity: number;
  immunizationDoseUnit: string;
  immunizationPerformerFunction: string;
  immunizationPerformerFunctionDisplay: string;
  immunizationReasonCode: string;
  immunizationReasonDisplay: string;
  immunizationIsSubpotent: boolean;
  immunizationSubpotentReason: string;
  immunizationSubpotentReasonDisplay: string;
  immunizationEducation: string;
  immunizationProgramEligibility: string;
  immunizationProgramEligibilityDisplay: string;
  immunizationFundingSource: string;
  immunizationFundingSourceDisplay: string;
  immunizationManufacturer: string;

  // Protocol applied details for dose number
  protocolApplied?: Array<{
    series?: string;
    authority?: string;
    targetDisease?: string[];
    doseNumber?: string;
    seriesDoses?: string;
  }>;

  // Immunization Recommendation details
  recommendationDate: string;
  recommendationVaccineCode: string;
  recommendationVaccineDisplay: string;
  recommendationVaccineText: string;
  recommendationTargetDisease: string;
  recommendationTargetDiseaseDisplay: string;
  recommendationForecastStatus: string;
  recommendationForecastStatusDisplay: string;
  recommendationDateCriterion: string;
  recommendationDateCriterionValue: string;
  recommendationSupportingImmunization: string;
  recommendationSupportingPatientObservation: string;

  // Document Reference
  documentTitle: string;
  documentDescription: string;
  documentCreated: string;
  documentContentType: string;
  documentUrl: string;
}

// Validation function to ensure required fields are present and provide defaults

/**
 * Generates a FHIR ImmunizationRecord document bundle
 *
 * @param promptInput - Input data for the ImmunizationRecord
 * @returns JSON string representation of the FHIR document bundle
 */
async function ReportImmunizationStrategy(
  promptInput: ImmunizationInput,
): Promise<string> {
  try {
    // Validate input

    // Generate UUIDs for all resources
    const resourceUUID = crypto.randomUUID();
    const compositionUUID = crypto.randomUUID();
    const patientUUID = crypto.randomUUID();
    const practitionerUUID = crypto.randomUUID();
    const organizationUUID = crypto.randomUUID();
    const immunizationUUID = crypto.randomUUID();
    const immunizationRecommendationUUID = crypto.randomUUID();

    // Create references
    const patientReference = `urn:uuid:${patientUUID}`;
    const practitionerReference = `urn:uuid:${practitionerUUID}`;
    const organizationReference = `urn:uuid:${organizationUUID}`;
    const immunizationReference = `urn:uuid:${immunizationUUID}`;
    const immunizationRecommendationReference = `urn:uuid:${immunizationRecommendationUUID}`;

    // Generate individual resources
    const patientResource = await EntryPatientStrategy({
      patientID: promptInput.patientID,
      patientFirstName: promptInput.patientFirstName,
      patientLastName: promptInput.patientLastName,
      patientGender: promptInput.patientGender,
      patientBirthDate: promptInput.patientBirthDate,
      patientPhone: promptInput.patientPhone,
      patientEmail: promptInput.patientEmail,
      patientAddress: promptInput.patientAddress,
      patientCity: promptInput.patientCity,
      patientState: promptInput.patientState,
      patientPostalCode: promptInput.patientPostalCode,
      patientCountry: promptInput.patientCountry,
    });

    const practitionerResource = await EntryPractitionerStrategy({
      practitionerID: promptInput.practitionerID,
      practitionerName: promptInput.practitionerName,
      practitionerQualification: promptInput.practitionerQualification,
    });

    const organizationResource = await EntryOrganizationStrategy({
      organizationID: promptInput.organizationID,
      organizationName: promptInput.organizationName,
      organizationPhone: promptInput.organizationPhone || "",
      organizationEmail: promptInput.organizationEmail || "",
      organizationAddress: promptInput.organizationAddress || "",
      organizationCity: promptInput.organizationCity || "",
      organizationState: promptInput.organizationState || "",
      organizationPostalCode: promptInput.organizationPostalCode || "",
      organizationCountry: promptInput.organizationCountry || "",
    });

    // Note: Encounter resource removed to match example bundle structure

    // Create the immunization resource
    const immunizationResource = {
      resourceType: "Immunization",
      id: immunizationUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Immunization",
        ],
      },
      status: promptInput.immunizationStatus || "completed",
      vaccineCode: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: promptInput.immunizationVaccineCode,
            display: promptInput.immunizationVaccineDisplay,
          },
        ],
        text: promptInput.immunizationVaccineText,
      },
      patient: {
        reference: patientReference,
        display: "Patient",
      },
      occurrenceDateTime: promptInput.immunizationOccurrenceDateTime,
      primarySource: true,
      lotNumber: promptInput.immunizationLotNumber,
      manufacturer: promptInput.immunizationManufacturer
        ? {
            reference: `Organization/${promptInput.organizationID}`,
            display: promptInput.immunizationManufacturer,
          }
        : undefined,
      performer: promptInput.practitionerName
        ? [
            {
              function: {
                coding: [
                  {
                    system: "http://terminology.hl7.org/CodeSystem/v2-0443",
                    code: "AP",
                    display: "Administering Provider",
                  },
                ],
              },
              actor: {
                reference: practitionerReference,
                display: "Practitioner",
              },
            },
          ]
        : undefined,
      protocolApplied: promptInput.protocolApplied?.map((protocol) => ({
        series: protocol.series || "2-dose",
        targetDisease: protocol.targetDisease?.map((disease) => ({
          coding: [
            {
              system: "http://id.who.int/icd11/mms",
              code: "RA01.1",
              display: disease,
            },
          ],
        })),
        doseNumberPositiveInt: protocol.doseNumber
          ? parseInt(protocol.doseNumber)
          : 1,
      })),
    };

    // Create the immunization recommendation resource
    const immunizationRecommendationResource = {
      resourceType: "ImmunizationRecommendation",
      id: immunizationRecommendationUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/ImmunizationRecommendation",
        ],
      },
      patient: {
        reference: patientReference,
        display: "Patient",
      },
      date: promptInput.recommendationDate || new Date().toISOString(),
      authority: {
        reference: organizationReference,
        display: "Organization",
      },
      recommendation: [
        {
          vaccineCode: [
            {
              coding: [
                {
                  system: "http://snomed.info/sct",
                  code:
                    promptInput.recommendationVaccineCode ||
                    promptInput.immunizationVaccineCode,
                  display:
                    promptInput.recommendationVaccineDisplay ||
                    promptInput.immunizationVaccineDisplay,
                },
              ],
              text:
                promptInput.recommendationVaccineText ||
                promptInput.immunizationVaccineText,
            },
          ],
          targetDisease: promptInput.recommendationTargetDisease
            ? {
                coding: [
                  {
                    system: "http://snomed.info/sct",
                    code: promptInput.recommendationTargetDisease,
                    display: promptInput.recommendationTargetDiseaseDisplay,
                  },
                ],
              }
            : undefined,
          forecastStatus: {
            coding: [
              {
                system:
                  "http://terminology.hl7.org/CodeSystem/immunization-recommendation-status",
                code: promptInput.recommendationForecastStatus || "due",
                display:
                  promptInput.recommendationForecastStatusDisplay || "Due",
              },
            ],
          },
          dateCriterion: promptInput.recommendationDateCriterion
            ? [
                {
                  code: {
                    coding: [
                      {
                        system: "http://loinc.org",
                        code: "30980-7",
                        display: "Date vaccine due",
                      },
                    ],
                  },
                  value:
                    promptInput.recommendationDateCriterionValue ||
                    new Date(
                      new Date().setMonth(new Date().getMonth() + 6),
                    ).toISOString(),
                },
              ]
            : undefined,
          supportingImmunization:
            promptInput.recommendationSupportingImmunization
              ? [
                  {
                    reference: immunizationReference,
                    display: "Immunization",
                  },
                ]
              : undefined,
        },
      ],
    };

    // Create the composition resource
    const compositionResource = {
      resourceType: "Composition",
      id: compositionUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/ImmunizationRecord",
        ],
      },
      language: "en-IN",
      text: {
        status: "generated",
        div: `<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>${promptInput.compositionTitle}</div>`,
      },
      identifier: {
        system: "https://ndhm.in/phr",
        value: promptInput.compositionIdentifier,
      },
      status: "final",
      type: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "41000179103",
            display: "Immunization record",
          },
        ],
        text: "Immunization Record",
      },
      subject: {
        reference: patientReference,
        display: promptInput.patientFirstName || "Patient",
      },
      date: promptInput.compositionDate,
      author: [
        {
          reference: practitionerReference,
          display: promptInput.practitionerName || "Practitioner",
        },
      ],
      title: promptInput.compositionTitle || "Immunization record",
      custodian: {
        reference: organizationReference,
        display: promptInput.organizationName || "Organization",
      },
      section: [
        {
          title: "Immunization record",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "41000179103",
                display: "Immunization record",
              },
            ],
          },
          entry: [
            {
              reference: immunizationReference,
              type: "Immunization",
            },
            {
              reference: immunizationRecommendationReference,
              type: "ImmunizationRecommendation",
            },
          ],
        },
      ],
    };

    // Construct the bundle
    const bundle = {
      resourceType: "Bundle",
      id: resourceUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle",
        ],
        security: [
          {
            system: "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
            code: "V",
            display: "very restricted",
          },
        ],
      },
      identifier: {
        system: "http://hip.in",
        value: crypto.randomUUID(),
      },
      type: "document",
      timestamp: new Date().toISOString(),
      entry: [
        // Composition entry (always first in a document bundle)
        {
          fullUrl: `urn:uuid:${compositionUUID}`,
          resource: compositionResource,
        },
        // Practitioner entry (author) - second in example
        {
          fullUrl: practitionerReference,
          resource: JSON.parse(practitionerResource).resource,
        },
        // Organization entry (custodian) - third in example
        {
          fullUrl: organizationReference,
          resource: JSON.parse(organizationResource).resource,
        },
        // Patient entry - fourth in example
        {
          fullUrl: patientReference,
          resource: JSON.parse(patientResource).resource,
        },
        // Immunization entry
        {
          fullUrl: immunizationReference,
          resource: immunizationResource,
        },
        // Immunization Recommendation entry
        {
          fullUrl: immunizationRecommendationReference,
          resource: immunizationRecommendationResource,
        },
      ],
    };

    // Return the formatted JSON string
    return JSON.stringify(bundle, null, 2);
  } catch (error) {
    console.error(
      "Error generating FHIR ImmunizationRecord document bundle:",
      error,
    );
    throw error;
  }
}

export { ReportImmunizationStrategy };
