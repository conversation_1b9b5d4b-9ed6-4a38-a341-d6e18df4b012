import crypto from "crypto";
import { EntryPatientStrategy } from "./entry-patient-strategy";
import { EntryPractitionerStrategy } from "./entry-practitioner-strategy";
import { EntryOrganizationStrategy } from "./entry-organization-strategy";
import { EntryConditionStrategy } from "./entry-condition-strategy";
import { EntryMedicationRequestStrategy } from "./entry-medication-request-strategy";
import { EntryObservationVitalSignsStrategy } from "./entry-observation-vital-signs-strategy";
import { EntryDiagnosticReportStrategy } from "./entry-diagnostic-report-strategy";

// Define the input entity interface
export interface DischargeSummaryInput {
  // Composition details
  compositionIdentifier: string;
  compositionDate: string;
  compositionTitle: string;

  // Patient details
  patientID: string;
  patientFirstName: string;
  patientLastName: string;
  patientGender: string;
  patientBirthDate: string;
  patientPhone: string;
  patientEmail: string;
  patientAddress: string;
  patientCity: string;
  patientState: string;
  patientPostalCode: string;
  patientCountry: string;

  // Practitioner details (author)
  practitionerID: string;
  practitionerName: string;
  practitionerQualification: string;

  // Organization details (custodian)
  organizationID: string;
  organizationName: string;
  organizationPhone: string;
  organizationEmail: string;
  organizationAddress: string;
  organizationCity: string;
  organizationState: string;
  organizationPostalCode: string;
  organizationCountry: string;

  // Encounter details
  encounterID: string;
  encounterAdmissionDate: string;
  encounterDischargeDate: string;
  encounterType: string;
  encounterClass: string;
  encounterClassDisplay: string;

  // Admission details
  admissionDetails: string;

  // Chief complaint (Condition)
  conditionCode: string;
  conditionDisplay: string;
  conditionText: string;
  conditionRecordedDate: string;
  conditionOnsetDate: string;
  conditionClinicalStatus: string;
  conditionClinicalStatusDisplay: string;
  conditionVerificationStatus: string;
  conditionVerificationStatusDisplay: string;
  conditionCategory: string;
  conditionCategoryDisplay: string;
  conditionSeverityCode: string;
  conditionSeverity: string;

  // Procedure
  procedureCode: string;
  procedureDisplay: string;
  procedureText: string;
  procedureDate: string;
  procedureStatus: string;
  procedureCategory: string;
  procedureCategoryDisplay: string;
  procedureOutcome: string;

  // Medication
  medicationCode: string;
  medicationDisplay: string;
  medicationText: string;
  medicationAuthoredOn: string;
  medicationStatus: string;
  medicationIntent: string;
  dosageText: string;
  dosageRouteCode: string;
  dosageRoute: string;
  dosageMethodCode: string;
  dosageMethod: string;
  dosageFrequency: number;
  dosagePeriodValue: number;
  dosagePeriodUnit: string;
  dosageAsNeeded: boolean;
  dosageAsNeededCode: string;
  dosageAsNeededReason: string;

  // Observation (Vital Signs)
  observationCode: string;
  observationDisplay: string;
  observationText: string;
  observationEffectiveDateTime: string;
  observationValueQuantity: number;
  observationValueUnit: string;
  observationValueSystem: string;
  observationValueCode: string;

  // Diagnostic Report
  reportCode: string;
  reportDisplay: string;
  reportText: string;
  reportEffectiveDateTime: string;
  reportIssuedDateTime: string;
  reportStatus: string;
  reportCategory: string;
  reportCategoryDisplay: string;
  reportConclusion: string;

  // Discharge Instructions
  dischargeInstructions: string;

  // Appointment (Follow-up)
  appointmentDescription: string;
  appointmentStart: string;
  appointmentEnd: string;
  appointmentCreated: string;

  // Document Reference
  documentTitle: string;
  documentDescription: string;
  documentCreated: string;
  documentContentType: string;
  documentUrl: string;

  // New structured fields (optional)
  medicalHistory?: string;
  investigations?: string;
  proceduresPerformed?: string;
  medicationsList?: string;
  carePlan?: string;
}

/**
 * Generates a FHIR DischargeSummary document bundle
 *
 * @param promptInput - Input data for the DischargeSummary
 * @returns JSON string representation of the FHIR document bundle
 */
async function ReportDischargeSummaryStrategy(
  promptInput: DischargeSummaryInput,
): Promise<string> {
  try {
    // Validate input

    // Generate UUIDs for all resources
    const resourceUUID = crypto.randomUUID();
    const compositionUUID = crypto.randomUUID();
    const patientUUID = crypto.randomUUID();
    const practitionerUUID = crypto.randomUUID();
    const organizationUUID = crypto.randomUUID();
    const encounterUUID = crypto.randomUUID();
    const conditionUUID = crypto.randomUUID();
    const procedureUUID = crypto.randomUUID();
    const medicationUUID = crypto.randomUUID();
    const observationUUID = crypto.randomUUID();
    const diagnosticReportUUID = crypto.randomUUID();
    const appointmentUUID = crypto.randomUUID();
    const documentReferenceUUID = crypto.randomUUID();

    // Create references
    const patientReference = `urn:uuid:${patientUUID}`;
    const practitionerReference = `urn:uuid:${practitionerUUID}`;
    const organizationReference = `urn:uuid:${organizationUUID}`;
    const encounterReference = `urn:uuid:${encounterUUID}`;
    const conditionReference = `urn:uuid:${conditionUUID}`;
    const procedureReference = `urn:uuid:${procedureUUID}`;
    const medicationReference = `urn:uuid:${medicationUUID}`;
    const observationReference = `urn:uuid:${observationUUID}`;
    const diagnosticReportReference = `urn:uuid:${diagnosticReportUUID}`;
    const appointmentReference = `urn:uuid:${appointmentUUID}`;
    const documentReference = `urn:uuid:${documentReferenceUUID}`;

    // Generate individual resources
    const patientResource = await EntryPatientStrategy({
      patientID: promptInput.patientID,
      patientFirstName: promptInput.patientFirstName,
      patientLastName: promptInput.patientLastName,
      patientGender: promptInput.patientGender,
      patientBirthDate: promptInput.patientBirthDate,
      patientPhone: promptInput.patientPhone,
      patientEmail: promptInput.patientEmail,
      patientAddress: promptInput.patientAddress,
      patientCity: promptInput.patientCity,
      patientState: promptInput.patientState,
      patientPostalCode: promptInput.patientPostalCode,
      patientCountry: promptInput.patientCountry,
    });

    const practitionerResource = await EntryPractitionerStrategy({
      practitionerID: promptInput.practitionerID,
      practitionerName: promptInput.practitionerName,
      practitionerQualification: promptInput.practitionerQualification,
    });

    const organizationResource = await EntryOrganizationStrategy({
      organizationID: promptInput.organizationID,
      organizationName: promptInput.organizationName,
      organizationPhone: promptInput.organizationPhone,
      organizationEmail: promptInput.organizationEmail,
      organizationAddress: promptInput.organizationAddress,
      organizationCity: promptInput.organizationCity,
      organizationState: promptInput.organizationState,
      organizationPostalCode: promptInput.organizationPostalCode,
      organizationCountry: promptInput.organizationCountry,
    });

    const conditionResource = await EntryConditionStrategy({
      conditionCode: promptInput.conditionCode,
      conditionDisplay: promptInput.conditionDisplay,
      conditionText: promptInput.conditionText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      recordedDate: promptInput.conditionRecordedDate,
      onsetDate: promptInput.conditionOnsetDate,
      clinicalStatus: promptInput.conditionClinicalStatus,
      clinicalStatusDisplay: promptInput.conditionClinicalStatusDisplay,
      verificationStatus: promptInput.conditionVerificationStatus,
      verificationStatusDisplay: promptInput.conditionVerificationStatusDisplay,
      category: promptInput.conditionCategory,
      categoryDisplay: promptInput.conditionCategoryDisplay,
      severityCode: promptInput.conditionSeverityCode,
      severity: promptInput.conditionSeverity,
    });

    const medicationResource = await EntryMedicationRequestStrategy({
      medicationCode: promptInput.medicationCode,
      medicationDisplay: promptInput.medicationDisplay,
      medicationText: promptInput.medicationText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      authoredOn: promptInput.medicationAuthoredOn,
      status: promptInput.medicationStatus,
      intent: promptInput.medicationIntent,
      dosageText: promptInput.dosageText,
      dosageRouteCode: promptInput.dosageRouteCode,
      dosageRoute: promptInput.dosageRoute,
      dosageMethodCode: promptInput.dosageMethodCode,
      dosageMethod: promptInput.dosageMethod,
      dosageFrequency: promptInput.dosageFrequency,
      dosagePeriodValue: promptInput.dosagePeriodValue,
      dosagePeriodUnit: promptInput.dosagePeriodUnit,
      dosageAsNeeded: promptInput.dosageAsNeeded,
      dosageAsNeededCode: promptInput.dosageAsNeededCode,
      dosageAsNeededReason: promptInput.dosageAsNeededReason,
    });

    const observationResource = await EntryObservationVitalSignsStrategy({
      observationCode: promptInput.observationCode,
      observationDisplay: promptInput.observationDisplay,
      observationText: promptInput.observationText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      effectiveDateTime: promptInput.observationEffectiveDateTime,
      valueQuantity: promptInput.observationValueQuantity,
      valueUnit: promptInput.observationValueUnit,
      valueSystem: promptInput.observationValueSystem,
      valueCode: promptInput.observationValueCode,
    });

    const diagnosticReportResource = await EntryDiagnosticReportStrategy({
      reportCode: promptInput.reportCode,
      reportDisplay: promptInput.reportDisplay,
      reportText: promptInput.reportText,
      patientReference: patientReference,
      practitionerName: promptInput.practitionerName,
      practitionerReference: practitionerReference,
      organizationReference: organizationReference,
      effectiveDateTime: promptInput.reportEffectiveDateTime,
      issuedDateTime: promptInput.reportIssuedDateTime,
      status: promptInput.reportStatus,
      category: promptInput.reportCategory,
      categoryDisplay: promptInput.reportCategoryDisplay,
      conclusion: promptInput.reportConclusion,
      observationReference: observationReference,
    });

    // Create the encounter resource
    const encounterResource = {
      resourceType: "Encounter",
      id: encounterUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter",
        ],
      },
      status: "finished",
      class: {
        system: "http://terminology.hl7.org/CodeSystem/v3-ActCode",
        code: promptInput.encounterClass || "IMP",
        display: promptInput.encounterClassDisplay || "inpatient encounter",
      },
      type: [
        {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "183452005",
              display: "Hospital admission",
            },
          ],
          text: promptInput.encounterType || "Hospital admission",
        },
      ],
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      participant: [
        {
          individual: {
            reference: practitionerReference,
            display: promptInput.practitionerName,
          },
        },
      ],
      period: {
        start: promptInput.encounterAdmissionDate,
        end: promptInput.encounterDischargeDate,
      },
      serviceProvider: {
        reference: organizationReference,
        display: "Organization",
      },
    };

    // Create the procedure resource
    const procedureResource = {
      resourceType: "Procedure",
      id: procedureUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure",
        ],
      },
      status: promptInput.procedureStatus || "completed",
      category: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: promptInput.procedureCategory || "387713003",
            display:
              promptInput.procedureCategoryDisplay || "Surgical procedure",
          },
        ],
      },
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: promptInput.procedureCode,
            display: promptInput.procedureDisplay,
          },
        ],
        text: promptInput.procedureText,
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      encounter: {
        reference: encounterReference,
        display: "Encounter",
      },
      performedDateTime: promptInput.procedureDate,
      performer: [
        {
          actor: {
            reference: practitionerReference,
            display: promptInput.practitionerName,
          },
        },
      ],
      outcome: promptInput.procedureOutcome || "Successful procedure",
    };

    // Create the appointment resource
    const appointmentResource = {
      resourceType: "Appointment",
      id: appointmentUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Appointment",
        ],
      },
      status: "booked",
      description: promptInput.appointmentDescription,
      start: promptInput.appointmentStart,
      end: promptInput.appointmentEnd,
      created: promptInput.appointmentCreated,
      participant: [
        {
          actor: {
            reference: patientReference,
            display: "Patient",
          },
          status: "accepted",
        },
        {
          actor: {
            reference: practitionerReference,
            display: promptInput.practitionerName,
          },
          status: "accepted",
        },
      ],
    };

    // Create the document reference resource
    const documentReferenceResource = {
      resourceType: "DocumentReference",
      id: documentReferenceUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference",
        ],
      },
      status: "current",
      docStatus: "final",
      type: {
        coding: [
          {
            system: "http://loinc.org",
            code: "18842-5",
            display: "Discharge summary",
          },
        ],
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      date: promptInput.documentCreated,
      author: [
        {
          reference: practitionerReference,
          display: promptInput.practitionerName,
        },
      ],
      custodian: {
        reference: organizationReference,
        display: "Organization",
      },
      description: promptInput.documentDescription,
      content: [
        {
          attachment: {
            contentType: promptInput.documentContentType,
            data: promptInput.documentUrl,
            title: promptInput.documentTitle,
            creation: promptInput.documentCreated,
          },
        },
      ],
      context: {
        encounter: [
          {
            reference: encounterReference,
            display: "Encounter",
          },
        ],
      },
    };

    // Create the composition resource
    const compositionResource = {
      resourceType: "Composition",
      id: compositionUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DischargeSummaryRecord",
        ],
      },
      language: "en-IN",
      text: {
        status: "generated",
        div: `<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>${promptInput.compositionTitle}</div>`,
      },
      identifier: {
        system: "https://ndhm.in/phr",
        value: promptInput.compositionIdentifier,
      },
      status: "final",
      type: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "373942005",
            display: "Discharge summary",
          },
        ],
        text: "Discharge Summary",
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      encounter: {
        reference: encounterReference,
        display: "Encounter",
      },
      date: promptInput.compositionDate,
      author: [
        {
          reference: practitionerReference,
          display: promptInput.practitionerName,
        },
      ],
      title: promptInput.compositionTitle,
      custodian: {
        reference: organizationReference,
        display: "Organization",
      },
      section: [
        {
          title: "Chief complaints",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "422843007",
                display: "Chief complaint section",
              },
            ],
          },
          entry: [
            {
              reference: conditionReference,
              display: "Condition",
            },
          ],
        },
        {
          title: "Medical History",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "**********",
                display: "Past medical history section",
              },
            ],
          },
          text: {
            status: "generated",
            div: `<div xmlns='http://www.w3.org/1999/xhtml'>${promptInput.medicalHistory || promptInput.admissionDetails || ""}</div>`,
          },
        },
        {
          title: "Investigations",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "721981007",
                display: "Diagnostic studies report",
              },
            ],
          },
          text: {
            status: "generated",
            div: `<div xmlns='http://www.w3.org/1999/xhtml'>${promptInput.investigations || ""}</div>`,
          },
          entry: [
            {
              reference: diagnosticReportReference,
              display: "DiagnosticReport",
            },
          ],
        },
        {
          title: "Procedures",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "**********",
                display: "Procedure section",
              },
            ],
          },
          text: {
            status: "generated",
            div: `<div xmlns='http://www.w3.org/1999/xhtml'>${promptInput.proceduresPerformed || ""}</div>`,
          },
          entry: [
            {
              reference: procedureReference,
              display: "Procedure",
            },
          ],
        },
        {
          title: "Medications",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "1003606003",
                display: "Medication section",
              },
            ],
          },
          text: {
            status: "generated",
            div: `<div xmlns='http://www.w3.org/1999/xhtml'>${promptInput.medicationsList || ""}</div>`,
          },
          entry: [
            {
              reference: medicationReference,
              display: "MedicationRequest",
            },
          ],
        },
        {
          title: "Care Plan",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "734163000",
                display: "Care plan section",
              },
            ],
          },
          text: {
            status: "generated",
            div: `<div xmlns='http://www.w3.org/1999/xhtml'>${promptInput.carePlan || promptInput.dischargeInstructions || ""}</div>`,
          },
        },
        {
          title: "Document Reference",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "373942005",
                display: "Discharge summary",
              },
            ],
          },
          entry: [
            {
              reference: documentReference,
              display: "DocumentReference",
            },
          ],
        },
      ],
    };

    // Construct the bundle
    const bundle = {
      resourceType: "Bundle",
      id: resourceUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle",
        ],
        security: [
          {
            system: "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
            code: "V",
            display: "very restricted",
          },
        ],
      },
      identifier: {
        system: "http://hip.in",
        value: crypto.randomUUID(),
      },
      type: "document",
      timestamp: new Date().toISOString(),
      entry: [
        // Composition entry (always first in a document bundle)
        {
          fullUrl: `urn:uuid:${compositionUUID}`,
          resource: compositionResource,
        },
        // Patient entry
        {
          fullUrl: patientReference,
          resource: JSON.parse(patientResource).resource,
        },
        // Practitioner entry (author)
        {
          fullUrl: practitionerReference,
          resource: JSON.parse(practitionerResource).resource,
        },
        // Organization entry (custodian)
        {
          fullUrl: organizationReference,
          resource: JSON.parse(organizationResource).resource,
        },
        // Encounter entry
        {
          fullUrl: encounterReference,
          resource: encounterResource,
        },
        // Condition entry
        {
          fullUrl: conditionReference,
          resource: JSON.parse(conditionResource).resource,
        },
        // Procedure entry
        {
          fullUrl: procedureReference,
          resource: procedureResource,
        },
        // Medication entry
        {
          fullUrl: medicationReference,
          resource: JSON.parse(medicationResource).resource,
        },
        // Observation entry
        {
          fullUrl: observationReference,
          resource: JSON.parse(observationResource).resource,
        },
        // Diagnostic Report entry
        {
          fullUrl: diagnosticReportReference,
          resource: JSON.parse(diagnosticReportResource).resource,
        },
        // Appointment entry
        {
          fullUrl: appointmentReference,
          resource: appointmentResource,
        },
        // Document Reference entry
        {
          fullUrl: documentReference,
          resource: documentReferenceResource,
        },
      ],
    };

    // Return the formatted JSON string
    return JSON.stringify(bundle, null, 2);
  } catch (error) {
    console.error(
      "Error generating FHIR DischargeSummary document bundle:",
      error,
    );
    throw error;
  }
}

export { ReportDischargeSummaryStrategy };
