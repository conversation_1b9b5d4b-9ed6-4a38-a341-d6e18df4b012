import crypto from "crypto";
import { STRATEGY } from "../constants/entry/patient";
import { InputEntity } from "../entities/entry-patient-input";

// Define transformations for specific fields
const transformations: Record<string, (value: string) => string> = {
  patientID: (value) => value.trim(),
  patientFirstName: (value) => value.trim(),
  patientLastName: (value) => value.trim(),
  patientGender: (value) => value.trim().toLowerCase(),
  patientBirthDate: (value) => value.trim(),
  patientPhone: (value) => value.trim(),
  patientEmail: (value) => value.trim(),
  patientAddress: (value) => value.trim(),
  patientCity: (value) => value.trim(),
  patientState: (value) => value.trim(),
  patientPostalCode: (value) => value.trim(),
  patientCountry: (value) => value.trim(),
  // Add more transformations as needed
};

// Validation function to ensure required fields are present
/**
 * Generates a FHIR Patient resource entry
 *
 * @param promptInput - Input data for the patient
 * @returns JSON string representation of the FHIR resource
 */
async function EntryPatientStrategy(promptInput: InputEntity): Promise<string> {
  try {
    // Validate input

    // Generate a new UUID for the resource
    const uuid = crypto.randomUUID();

    // Add the UUID to the input
    const enhancedInput = {
      ...promptInput,
      resourceUUID: uuid,
    };

    // Convert to string for placeholder replacement
    let jsonString = JSON.stringify(STRATEGY);

    // Apply transformations and replace placeholders
    Object.keys(enhancedInput).forEach((key) => {
      const placeholder = `{${key}}`;

      // Get the value and apply transformation if available
      let value =
        enhancedInput[
          key as keyof (InputEntity & { resourceUUID: string })
        ]?.toString() || "";
      if (transformations[key]) {
        value = transformations[key](value);
      }

      // Replace all occurrences of the placeholder
      jsonString = jsonString.replace(new RegExp(placeholder, "g"), value);
    });

    // Parse and validate the resulting JSON
    const result = JSON.parse(jsonString);

    // Basic validation - ensure required fields are present in the result
    if (
      !result.resource?.identifier?.[0]?.value ||
      !result.resource?.name?.[0]?.family
    ) {
      throw new Error("Generated FHIR resource is missing required fields");
    }

    // Return the formatted JSON string
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error("Error generating FHIR Patient resource:", error);
    throw error;
  }
}

export { EntryPatientStrategy };
