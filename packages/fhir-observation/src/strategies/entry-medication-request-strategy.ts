import crypto from "crypto";
import { STRATEGY } from "../constants/entry/medication-request";
import { InputEntity } from "../entities/entry-medication-request-input";

// Define transformations for specific fields
const transformations: Record<
  string,
  (value: string | number | boolean) => string
> = {
  medicationCode: (value) => String(value).trim(),
  medicationDisplay: (value) => String(value).trim(),
  medicationText: (value) => String(value).trim(),
  patientReference: (value) => String(value).trim(),
  practitionerReference: (value) => String(value).trim(),
  authoredOn: (value) => String(value).trim(),
  status: (value) => String(value).trim(),
  intent: (value) => String(value).trim(),
  dosageText: (value) => String(value).trim(),
  dosageRouteCode: (value) => String(value).trim(),
  dosageRoute: (value) => String(value).trim(),
  dosageMethodCode: (value) => String(value).trim(),
  dosageMethod: (value) => String(value).trim(),
  dosageFrequency: (value) => String(value),
  dosagePeriodValue: (value) => String(value),
  dosagePeriodUnit: (value) => String(value).trim(),
  dosageAsNeeded: (value) => String(value),
  dosageAsNeededCode: (value) => String(value).trim(),
  dosageAsNeededReason: (value) => String(value).trim(),
  // Add more transformations as needed
};

/**
 * Generates a FHIR MedicationRequest resource entry
 *
 * @param promptInput - Input data for the medication request
 * @returns JSON string representation of the FHIR resource
 */
async function EntryMedicationRequestStrategy(
  promptInput: InputEntity,
): Promise<string> {
  try {
    // Validate input

    // Generate a new UUID for the resource
    const uuid = crypto.randomUUID();

    // Add the UUID to the input
    const enhancedInput = {
      ...promptInput,
      resourceUUID: uuid,
    };

    // Convert to string for placeholder replacement
    let jsonString = JSON.stringify(STRATEGY);

    // Apply transformations and replace placeholders
    Object.keys(enhancedInput).forEach((key) => {
      const placeholder = `{${key}}`;

      // Get the value and apply transformation if available
      let value =
        enhancedInput[
          key as keyof (InputEntity & { resourceUUID: string })
        ]?.toString() || "";
      if (transformations[key]) {
        value = transformations[key](
          enhancedInput[key as keyof (InputEntity & { resourceUUID: string })],
        );
      }

      // Replace all occurrences of the placeholder
      jsonString = jsonString.replace(new RegExp(placeholder, "g"), value);
    });

    // Parse and validate the resulting JSON
    const result = JSON.parse(jsonString);

    // Basic validation - ensure required fields are present in the result
    if (
      !result.resource?.medicationCodeableConcept?.coding?.[0]?.code ||
      !result.resource?.subject?.reference
    ) {
      throw new Error("Generated FHIR resource is missing required fields");
    }

    // Return the formatted JSON string
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error("Error generating FHIR MedicationRequest resource:", error);
    throw error;
  }
}

export { EntryMedicationRequestStrategy };
