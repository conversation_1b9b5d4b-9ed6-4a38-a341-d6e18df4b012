import crypto from "crypto";
import { EntryPatientStrategy } from "./entry-patient-strategy";
import { EntryPractitionerStrategy } from "./entry-practitioner-strategy";
import { EntryOrganizationStrategy } from "./entry-organization-strategy";

// Define the input entity interface
export interface HealthDocumentInput {
  // Composition details
  compositionIdentifier: string;
  compositionDate: string;
  compositionTitle: string;

  // Patient details
  patientID: string;
  patientFirstName: string;
  patientLastName: string;
  patientGender: string;
  patientBirthDate: string;
  patientPhone: string;
  patientEmail: string;
  patientAddress: string;
  patientCity: string;
  patientState: string;
  patientPostalCode: string;
  patientCountry: string;

  // Practitioner details (author)
  practitionerID: string;
  practitionerName: string;
  practitionerQualification: string;

  // Organization details (custodian)
  organizationID: string;
  organizationName: string;
  organizationPhone: string;
  organizationEmail: string;
  organizationAddress: string;
  organizationCity: string;
  organizationState: string;
  organizationPostalCode: string;
  organizationCountry: string;

  // Encounter details
  encounterID: string;
  encounterDate: string;
  encounterType: string;

  // Document Reference
  documentTitle: string;
  documentDescription: string;
  documentCreated: string;
  documentContentType: string;
  documentUrl: string;
  documentStatus: string;
  documentType: string;
  documentTypeDisplay: string;
}

// Validation function to ensure required fields are present and provide defaults

/**
 * Generates a FHIR HealthDocument document bundle
 *
 * @param promptInput - Input data for the HealthDocument
 * @returns JSON string representation of the FHIR document bundle
 */
async function ReportHealthDocumentStrategy(
  promptInput: HealthDocumentInput,
): Promise<string> {
  try {
    // Validate input

    // Generate UUIDs for all resources
    const resourceUUID = crypto.randomUUID();
    const compositionUUID = crypto.randomUUID();
    const patientUUID = crypto.randomUUID();
    const practitionerUUID = crypto.randomUUID();
    const organizationUUID = crypto.randomUUID();
    const encounterUUID = crypto.randomUUID();
    const documentReferenceUUID = crypto.randomUUID();

    // Create references
    const patientReference = `urn:uuid:${patientUUID}`;
    const practitionerReference = `urn:uuid:${practitionerUUID}`;
    const organizationReference = `urn:uuid:${organizationUUID}`;
    const encounterReference = `urn:uuid:${encounterUUID}`;
    const documentReference = `urn:uuid:${documentReferenceUUID}`;

    // Generate individual resources
    const patientResource = await EntryPatientStrategy({
      patientID: promptInput.patientID,
      patientFirstName: promptInput.patientFirstName,
      patientLastName: promptInput.patientLastName,
      patientGender: promptInput.patientGender,
      patientBirthDate: promptInput.patientBirthDate,
      patientPhone: promptInput.patientPhone,
      patientEmail: promptInput.patientEmail,
      patientAddress: promptInput.patientAddress,
      patientCity: promptInput.patientCity,
      patientState: promptInput.patientState,
      patientPostalCode: promptInput.patientPostalCode,
      patientCountry: promptInput.patientCountry,
    });

    const practitionerResource = await EntryPractitionerStrategy({
      practitionerID: promptInput.practitionerID,
      practitionerName: promptInput.practitionerName,
      practitionerQualification: promptInput.practitionerQualification,
    });

    const organizationResource = await EntryOrganizationStrategy({
      organizationID: promptInput.organizationID,
      organizationName: promptInput.organizationName,
      organizationPhone: promptInput.organizationPhone,
      organizationEmail: promptInput.organizationEmail,
      organizationAddress: promptInput.organizationAddress,
      organizationCity: promptInput.organizationCity,
      organizationState: promptInput.organizationState,
      organizationPostalCode: promptInput.organizationPostalCode,
      organizationCountry: promptInput.organizationCountry,
    });

    // Create the encounter resource
    const encounterResource = {
      resourceType: "Encounter",
      id: encounterUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter",
        ],
      },
      status: "finished",
      class: {
        system: "http://terminology.hl7.org/CodeSystem/v3-ActCode",
        code: "AMB",
        display: "ambulatory",
      },
      type: [
        {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "11429006",
              display: "Consultation",
            },
          ],
          text: promptInput.encounterType || "Outpatient visit",
        },
      ],
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      participant: [
        {
          individual: {
            reference: practitionerReference,
            display: promptInput.practitionerName,
          },
        },
      ],
      period: {
        start: promptInput.encounterDate,
        end: promptInput.encounterDate,
      },
      serviceProvider: {
        reference: organizationReference,
        display: "Organization",
      },
    };

    // Create the document reference resource
    const documentReferenceResource = {
      resourceType: "DocumentReference",
      id: documentReferenceUUID,
      meta: {
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference",
        ],
      },
      status: promptInput.documentStatus || "current",
      docStatus: "final",
      type: {
        coding: [
          {
            system: "http://loinc.org",
            code: promptInput.documentType || "34108-1",
            display: promptInput.documentTypeDisplay || "Outpatient Note",
          },
        ],
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      date: promptInput.documentCreated,
      author: [
        {
          reference: practitionerReference,
          display: promptInput.practitionerName,
        },
      ],
      custodian: {
        reference: organizationReference,
        display: "Organization",
      },
      description: promptInput.documentDescription,
      content: [
        {
          attachment: {
            contentType: promptInput.documentContentType,
            data: promptInput.documentUrl,
            title: promptInput.documentTitle,
            creation: promptInput.documentCreated,
          },
        },
      ],
      context: {
        encounter: [
          {
            reference: encounterReference,
            display: "Encounter",
          },
        ],
      },
    };

    // Create the composition resource
    const compositionResource = {
      resourceType: "Composition",
      id: compositionUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/HealthDocumentRecord",
        ],
      },
      language: "en-IN",
      text: {
        status: "generated",
        div: `<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>${promptInput.compositionTitle}</div>`,
      },
      identifier: {
        system: "https://ndhm.in/phr",
        value: promptInput.compositionIdentifier,
      },
      status: "final",
      type: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "371530004",
            display: "Clinical report",
          },
        ],
        text: "Health Document",
      },
      subject: {
        reference: patientReference,
        display: "Patient",
      },
      encounter: {
        reference: encounterReference,
        display: "Encounter",
      },
      date: promptInput.compositionDate,
      author: [
        {
          reference: practitionerReference,
          display: promptInput.practitionerName,
        },
      ],
      title: promptInput.compositionTitle,
      custodian: {
        reference: organizationReference,
        display: "Organization",
      },
      section: [
        {
          title: "Document Reference",
          code: {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: "371530004",
                display: "Clinical report",
              },
            ],
          },
          entry: [
            {
              reference: documentReference,
              display: "DocumentReference",
            },
          ],
        },
      ],
    };

    // Construct the bundle
    const bundle = {
      resourceType: "Bundle",
      id: resourceUUID,
      meta: {
        versionId: "1",
        lastUpdated: new Date().toISOString(),
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle",
        ],
        security: [
          {
            system: "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
            code: "V",
            display: "very restricted",
          },
        ],
      },
      identifier: {
        system: "http://hip.in",
        value: crypto.randomUUID(),
      },
      type: "document",
      timestamp: new Date().toISOString(),
      entry: [
        // Composition entry (always first in a document bundle)
        {
          fullUrl: `urn:uuid:${compositionUUID}`,
          resource: compositionResource,
        },
        // Patient entry
        {
          fullUrl: patientReference,
          resource: JSON.parse(patientResource).resource,
        },
        // Practitioner entry (author)
        {
          fullUrl: practitionerReference,
          resource: JSON.parse(practitionerResource).resource,
        },
        // Organization entry (custodian)
        {
          fullUrl: organizationReference,
          resource: JSON.parse(organizationResource).resource,
        },
        // Encounter entry
        {
          fullUrl: encounterReference,
          resource: encounterResource,
        },
        // Document Reference entry
        {
          fullUrl: documentReference,
          resource: documentReferenceResource,
        },
      ],
    };

    // Return the formatted JSON string
    return JSON.stringify(bundle, null, 2);
  } catch (error) {
    console.error(
      "Error generating FHIR HealthDocument document bundle:",
      error,
    );
    throw error;
  }
}

export { ReportHealthDocumentStrategy };
