import crypto from "crypto";
import { STRATEGY } from "../constants/entry/diagnostic-report";
import { InputEntity } from "../entities/entry-diagnostic-report-input";

// Define transformations for specific fields
const transformations: Record<string, (value: string) => string> = {
  reportCode: (value) => value.trim(),
  reportDisplay: (value) => value.trim(),
  reportText: (value) => value.trim(),
  patientReference: (value) => value.trim(),
  practitionerReference: (value) => value.trim(),
  organizationReference: (value) => value.trim(),
  effectiveDateTime: (value) => value.trim(),
  issuedDateTime: (value) => value.trim(),
  status: (value) => value.trim(),
  category: (value) => value.trim(),
  categoryDisplay: (value) => value.trim(),
  conclusion: (value) => value.trim(),
  observationReference: (value) => value.trim(),
  // Add more transformations as needed
};

// Validation function to ensure required fields are present and provide defaults

/**
 * Generates a FHIR DiagnosticReport resource entry
 *
 * @param promptInput - Input data for the diagnostic report
 * @returns JSON string representation of the FHIR resource
 */
async function EntryDiagnosticReportStrategy(
  promptInput: InputEntity,
): Promise<string> {
  try {
    // Validate input and provide defaults for required fields
    const sanitizedInput = {
      ...promptInput,
      // Ensure required fields have valid values
      reportCode: promptInput.reportCode || "33747-0", // Default LOINC code for general lab test
      reportDisplay: promptInput.reportDisplay || "General laboratory test",
      reportText: promptInput.reportText || "Laboratory test result",
      patientReference: promptInput.patientReference || "Patient/unknown",
    };

    // Generate a new UUID for the resource
    const uuid = crypto.randomUUID();

    // Add the UUID to the input
    const enhancedInput = {
      ...sanitizedInput,
      resourceUUID: uuid,
    };

    // Convert to string for placeholder replacement
    let jsonString = JSON.stringify(STRATEGY);

    // Apply transformations and replace placeholders
    Object.keys(enhancedInput).forEach((key) => {
      const placeholder = `{${key}}`;

      // Get the value and apply transformation if available
      let value =
        enhancedInput[
          key as keyof (InputEntity & { resourceUUID: string })
        ]?.toString() || "";
      if (transformations[key]) {
        value = transformations[key](value);
      }

      // Replace all occurrences of the placeholder
      jsonString = jsonString.replace(new RegExp(placeholder, "g"), value);
    });

    // Parse and validate the resulting JSON
    const result = JSON.parse(jsonString);

    // Basic validation - ensure required fields are present in the result
    if (
      !result.resource?.code?.coding?.[0]?.code ||
      !result.resource?.subject?.reference
    ) {
      throw new Error("Generated FHIR resource is missing required fields");
    }

    // Return the formatted JSON string
    return JSON.stringify(result, null, 2);
  } catch (error) {
    console.error("Error generating FHIR DiagnosticReport resource:", error);
    throw error;
  }
}

export { EntryDiagnosticReportStrategy };
