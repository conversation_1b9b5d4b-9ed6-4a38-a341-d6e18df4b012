/**
 * FHIR Observation Blood Pressure Resource Template
 *
 * This template follows the ABDM FHIR R4 specification for Observation resources.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {patientReference}: The reference to the patient resource
 * - {practitionerReference}: The reference to the practitioner resource
 * - {effectiveDateTime}: The date/time when the observation was made
 * - {systolicValue}: The systolic blood pressure value
 * - {diastolicValue}: The diastolic blood pressure value
 * - {bodySiteCode}: The body site code (e.g., SNOMED CT code)
 * - {bodySiteDisplay}: The display name for the body site
 */
export const STRATEGY = {
  fullUrl: "urn:uuid:{resourceUUID}",
  resource: {
    resourceType: "Observation",
    id: "{resourceUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns",
      ],
    },
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>Blood pressure panel with all children optional</div>",
    },
    status: "final",
    category: [
      {
        coding: [
          {
            system:
              "http://terminology.hl7.org/CodeSystem/observation-category",
            code: "vital-signs",
            display: "Vital Signs",
          },
        ],
      },
    ],
    code: {
      coding: [
        {
          system: "http://loinc.org",
          code: "85354-9",
          display: "Blood pressure panel with all children optional",
        },
      ],
      text: "Blood pressure panel with all children optional",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    effectiveDateTime: "{effectiveDateTime}",
    performer: [
      {
        reference: "{practitionerReference}",
        display: "Practitioner",
      },
    ],
    bodySite: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "{bodySiteCode}",
          display: "{bodySiteDisplay}",
        },
      ],
    },
    component: [
      {
        code: {
          coding: [
            {
              system: "http://loinc.org",
              code: "8480-6",
              display: "Systolic blood pressure",
            },
          ],
        },
        valueQuantity: {
          value: "{systolicValue}",
          unit: "mmHg",
          system: "http://unitsofmeasure.org",
          code: "mm[Hg]",
        },
      },
      {
        code: {
          coding: [
            {
              system: "http://loinc.org",
              code: "8462-4",
              display: "Diastolic blood pressure",
            },
          ],
        },
        valueQuantity: {
          value: "{diastolicValue}",
          unit: "mmHg",
          system: "http://unitsofmeasure.org",
          code: "mm[Hg]",
        },
      },
    ],
  },
};
