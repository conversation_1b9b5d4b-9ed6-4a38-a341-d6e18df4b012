/**
 * FHIR Condition Resource Template
 *
 * This template follows the ABDM FHIR R4 specification for Condition resources.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {conditionCode}: The condition code (e.g., SNOMED CT code)
 * - {conditionDisplay}: The display name for the condition
 * - {conditionText}: The text description of the condition
 * - {patientReference}: The reference to the patient resource
 * - {practitionerReference}: The reference to the practitioner resource
 * - {recordedDate}: The date when the condition was recorded
 * - {onsetDate}: The date when the condition started
 * - {clinicalStatus}: The clinical status of the condition (active, recurrence, relapse, inactive, remission, resolved)
 * - {verificationStatus}: The verification status of the condition (unconfirmed, provisional, differential, confirmed, refuted, entered-in-error)
 * - {category}: The category of the condition (problem-list-item, encounter-diagnosis)
 * - {severity}: The severity of the condition (mild, moderate, severe)
 */
export const STRATEGY = {
  fullUrl: "urn:uuid:{resourceUUID}",
  resource: {
    resourceType: "Condition",
    id: "{resourceUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"],
    },
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{conditionText}</div>",
    },
    clinicalStatus: {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/condition-clinical",
          code: "{clinicalStatus}",
          display: "{clinicalStatusDisplay}",
        },
      ],
    },
    verificationStatus: {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/condition-ver-status",
          code: "{verificationStatus}",
          display: "{verificationStatusDisplay}",
        },
      ],
    },
    category: [
      {
        coding: [
          {
            system: "http://terminology.hl7.org/CodeSystem/condition-category",
            code: "{category}",
            display: "{categoryDisplay}",
          },
        ],
      },
    ],
    severity: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "{severityCode}",
          display: "{severity}",
        },
      ],
    },
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "{conditionCode}",
          display: "{conditionDisplay}",
        },
      ],
      text: "{conditionText}",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    onsetDateTime: "{onsetDate}",
    recordedDate: "{recordedDate}",
    recorder: {
      reference: "{practitionerReference}",
      display: "{practitionerName}",
    },
  },
};
