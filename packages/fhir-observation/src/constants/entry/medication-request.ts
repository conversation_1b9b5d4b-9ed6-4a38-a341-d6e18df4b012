/**
 * FHIR MedicationRequest Resource Template
 *
 * This template follows the ABDM FHIR R4 specification for MedicationRequest resources.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {medicationCode}: The medication code (e.g., RxNorm code)
 * - {medicationDisplay}: The display name for the medication
 * - {medicationText}: The text description of the medication
 * - {patientReference}: The reference to the patient resource
 * - {practitionerReference}: The reference to the practitioner resource
 * - {authoredOn}: The date when the prescription was authored
 * - {status}: The status of the medication request (active, on-hold, cancelled, completed, entered-in-error, stopped, draft, unknown)
 * - {intent}: The intent of the medication request (proposal, plan, order, original-order, reflex-order, filler-order, instance-order, option)
 * - {dosageText}: The text description of the dosage
 * - {dosageRoute}: The route of administration
 * - {dosageMethod}: The method of administration
 * - {dosageFrequency}: The frequency of administration
 * - {dosagePeriodUnit}: The unit of the dosage period (s, min, h, d, wk, mo, a)
 * - {dosagePeriodValue}: The value of the dosage period
 * - {dosageAsNeeded}: Whether the medication is to be taken as needed (true/false)
 * - {dosageAsNeededReason}: The reason for taking the medication as needed
 */
export const STRATEGY = {
  fullUrl: "urn:uuid:{resourceUUID}",
  resource: {
    resourceType: "MedicationRequest",
    id: "{resourceUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest",
      ],
    },
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{medicationText}</div>",
    },
    status: "{status}",
    intent: "{intent}",
    medicationCodeableConcept: {
      coding: [
        {
          system: "http://www.nlm.nih.gov/research/umls/rxnorm",
          code: "{medicationCode}",
          display: "{medicationDisplay}",
        },
      ],
      text: "{medicationText}",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    authoredOn: "{authoredOn}",
    requester: {
      reference: "{practitionerReference}",
      display: "{practitionerName}",
    },
    dosageInstruction: [
      {
        text: "{dosageText}",
        route: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "{dosageRouteCode}",
              display: "{dosageRoute}",
            },
          ],
        },
        method: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "{dosageMethodCode}",
              display: "{dosageMethod}",
            },
          ],
        },
        timing: {
          repeat: {
            frequency: "{dosageFrequency}",
            period: "{dosagePeriodValue}",
            periodUnit: "{dosagePeriodUnit}",
          },
        },
        asNeededBoolean: "{dosageAsNeeded}",
        asNeededCodeableConcept: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "{dosageAsNeededCode}",
              display: "{dosageAsNeededReason}",
            },
          ],
        },
      },
    ],
  },
};
