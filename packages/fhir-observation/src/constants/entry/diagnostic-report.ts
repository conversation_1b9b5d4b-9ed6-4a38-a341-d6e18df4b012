/**
 * FHIR DiagnosticReport Resource Template
 *
 * This template follows the ABDM FHIR R4 specification for DiagnosticReport resources.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {reportCode}: The report code (e.g., LOINC code)
 * - {reportDisplay}: The display name for the report
 * - {reportText}: The text description of the report
 * - {patientReference}: The reference to the patient resource
 * - {practitionerReference}: The reference to the practitioner resource
 * - {organizationReference}: The reference to the organization resource
 * - {effectiveDateTime}: The date/time when the report was created
 * - {issuedDateTime}: The date/time when the report was issued
 * - {status}: The status of the report (registered, partial, preliminary, final, amended, corrected, appended, cancelled, entered-in-error, unknown)
 * - {category}: The category of the report (e.g., LAB, RAD, etc.)
 * - {categoryDisplay}: The display name for the category
 * - {conclusion}: The clinical conclusion of the report
 * - {observationReference}: The reference to the observation resource
 */
export const STRATEGY = {
  fullUrl: "urn:uuid:{resourceUUID}",
  resource: {
    resourceType: "DiagnosticReport",
    id: "{resourceUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DiagnosticReportRecord",
      ],
    },
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{reportText}</div>",
    },
    status: "{status}",
    category: [
      {
        coding: [
          {
            system: "http://terminology.hl7.org/CodeSystem/v2-0074",
            code: "{category}",
            display: "{categoryDisplay}",
          },
        ],
      },
    ],
    code: {
      coding: [
        {
          system: "http://loinc.org",
          code: "{reportCode}",
          display: "{reportDisplay}",
        },
      ],
      text: "{reportText}",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    effectiveDateTime: "{effectiveDateTime}",
    issued: "{issuedDateTime}",
    performer: [
      {
        reference: "{practitionerReference}",
        display: "{practitionerName}",
      },
      {
        reference: "{organizationReference}",
        display: "Organization",
      },
    ],
    result: [
      {
        reference: "{observationReference}",
        display: "Observation",
      },
    ],
    conclusion: "{conclusion}",
  },
};
