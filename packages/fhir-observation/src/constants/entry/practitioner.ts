/**
 * FHIR Practitioner Resource Template
 *
 * This template follows the ABDM FHIR R4 specification for Practitioner resources.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {practitionerID}: The practitioner's identifier (e.g., medical license number)
 * - {practitionerName}: The practitioner's full name
 * - {practitionerQualification}: The practitioner's qualification (optional)
 */
export const STRATEGY = {
  fullUrl: "urn:uuid:{resourceUUID}",
  resource: {
    resourceType: "Practitioner",
    id: "{resourceUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner",
      ],
    },
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{practitionerName}, {practitionerQualification}</div>",
    },
    identifier: [
      {
        type: {
          coding: [
            {
              system: "http://terminology.hl7.org/CodeSystem/v2-0203",
              code: "MD",
              display: "Medical License number",
            },
          ],
        },
        system: "https://doctor.ndhm.gov.in",
        value: "{practitionerID}",
      },
    ],
    name: [
      {
        text: "{practitionerName}",
      },
    ],
    qualification: [
      {
        code: {
          text: "{practitionerQualification}",
        },
      },
    ],
  },
};
