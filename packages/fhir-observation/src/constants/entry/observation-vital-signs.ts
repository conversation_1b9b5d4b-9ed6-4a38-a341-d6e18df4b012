/**
 * FHIR Observation Vital Signs Resource Template
 *
 * This template follows the ABDM FHIR R4 specification for Observation resources.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {observationCode}: The observation code (e.g., LOINC code)
 * - {observationDisplay}: The display name for the observation
 * - {observationText}: The text description of the observation
 * - {patientReference}: The reference to the patient resource
 * - {practitionerReference}: The reference to the practitioner resource
 * - {effectiveDateTime}: The date/time when the observation was made
 * - {valueQuantity}: The value of the observation
 * - {valueUnit}: The unit of the observation value
 * - {valueSystem}: The system for the unit
 * - {valueCode}: The code for the unit
 */
export const STRATEGY = {
  fullUrl: "urn:uuid:{resourceUUID}",
  resource: {
    resourceType: "Observation",
    id: "{resourceUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns",
      ],
    },
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{observationText}</div>",
    },
    status: "final",
    category: [
      {
        coding: [
          {
            system:
              "http://terminology.hl7.org/CodeSystem/observation-category",
            code: "vital-signs",
            display: "Vital Signs",
          },
        ],
        text: "Vital Signs",
      },
    ],
    code: {
      coding: [
        {
          system: "http://loinc.org",
          code: "{observationCode}",
          display: "{observationDisplay}",
        },
      ],
      text: "{observationText}",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    effectiveDateTime: "{effectiveDateTime}",
    performer: [
      {
        reference: "{practitionerReference}",
        display: "{practitionerName}",
      },
    ],
    valueQuantity: {
      value: "{valueQuantity}",
      unit: "{valueUnit}",
      system: "{valueSystem}",
      code: "{valueCode}",
    },
  },
};
