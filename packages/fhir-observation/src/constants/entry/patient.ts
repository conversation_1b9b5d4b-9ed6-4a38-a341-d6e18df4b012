/**
 * FHIR Patient Resource Template
 *
 * This template follows the ABDM FHIR R4 specification for Patient resources.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {patientID}: The patient's identifier
 * - {patientFirstName}: The patient's first name
 * - {patientLastName}: The patient's last name
 * - {patientGender}: The patient's gender (male, female, other, unknown)
 * - {patientBirthDate}: The patient's birth date (YYYY-MM-DD)
 * - {patientPhone}: The patient's phone number
 * - {patientEmail}: The patient's email address
 * - {patientAddress}: The patient's address
 * - {patientCity}: The patient's city
 * - {patientState}: The patient's state
 * - {patientPostalCode}: The patient's postal code
 * - {patientCountry}: The patient's country
 */
export const STRATEGY = {
  fullUrl: "urn:uuid:{resourceUUID}",
  resource: {
    resourceType: "Patient",
    id: "{resourceUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"],
    },
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{patientFirstName} {patientLastName}</div>",
    },
    identifier: [
      {
        type: {
          coding: [
            {
              system: "http://terminology.hl7.org/CodeSystem/v2-0203",
              code: "MR",
              display: "Medical record number",
            },
          ],
        },
        system: "https://healthid.ndhm.gov.in",
        value: "{patientID}",
      },
    ],
    name: [
      {
        use: "official",
        family: "{patientLastName}",
        given: ["{patientFirstName}"],
        text: "{patientFirstName} {patientLastName}",
      },
    ],
    telecom: [
      {
        system: "phone",
        value: "{patientPhone}",
        use: "home",
      },
      {
        system: "email",
        value: "{patientEmail}",
        use: "home",
      },
    ],
    gender: "{patientGender}",
    birthDate: "{patientBirthDate}",
    address: [
      {
        use: "home",
        type: "physical",
        line: ["{patientAddress}"],
        city: "{patientCity}",
        state: "{patientState}",
        postalCode: "{patientPostalCode}",
        country: "{patientCountry}",
      },
    ],
    active: true,
  },
};
