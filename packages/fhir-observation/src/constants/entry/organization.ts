/**
 * FHIR Organization Resource Template
 *
 * This template follows the ABDM FHIR R4 specification for Organization resources.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {organizationID}: The organization's identifier
 * - {organizationName}: The organization's name
 * - {organizationPhone}: The organization's phone number
 * - {organizationEmail}: The organization's email address
 * - {organizationAddress}: The organization's address
 * - {organizationCity}: The organization's city
 * - {organizationState}: The organization's state
 * - {organizationPostalCode}: The organization's postal code
 * - {organizationCountry}: The organization's country
 */
export const STRATEGY = {
  fullUrl: "urn:uuid:{resourceUUID}",
  resource: {
    resourceType: "Organization",
    id: "{resourceUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization",
      ],
    },
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{organizationName}</div>",
    },
    identifier: [
      {
        type: {
          coding: [
            {
              system: "http://terminology.hl7.org/CodeSystem/v2-0203",
              code: "PRN",
              display: "Provider number",
            },
          ],
        },
        system: "https://facility.ndhm.gov.in",
        value: "{organizationID}",
      },
    ],
    name: "{organizationName}",
    telecom: [
      {
        system: "phone",
        value: "{organizationPhone}",
        use: "work",
      },
      {
        system: "email",
        value: "{organizationEmail}",
        use: "work",
      },
    ],
    address: [
      {
        use: "work",
        type: "physical",
        line: ["{organizationAddress}"],
        city: "{organizationCity}",
        state: "{organizationState}",
        postalCode: "{organizationPostalCode}",
        country: "{organizationCountry}",
      },
    ],
    active: true,
  },
};
