/**
 * FHIR Composition Entry for Prescription Record
 */
export const PRESCRIPTION_COMPOSITION_ENTRY = {
  fullUrl: "urn:uuid:{compositionUUID}",
  resource: {
    resourceType: "Composition",
    id: "{compositionUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/PrescriptionRecord",
      ],
    },
    language: "en-IN",
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{compositionTitle}</div>",
    },
    identifier: {
      system: "https://ndhm.in/phr",
      value: "{compositionIdentifier}",
    },
    status: "final",
    type: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "440545006",
          display: "Prescription",
        },
      ],
      text: "Prescription",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    encounter: {
      reference: "{encounterReference}",
      display: "Encounter",
    },
    date: "{compositionDate}",
    author: [
      {
        reference: "{authorReference}",
        display: "Practitioner",
      },
    ],
    title: "{compositionTitle}",
    custodian: {
      reference: "{custodianReference}",
      display: "Organization",
    },
    section: [
      {
        title: "Chief Complaint",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "422843007",
              display: "Chief complaint section",
            },
          ],
        },
        entry: [
          {
            reference: "{chiefComplaintReference}",
            display: "Condition",
          },
        ],
      },
      {
        title: "Diagnosis",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "29308-4",
              display: "Diagnosis",
            },
          ],
        },
        entry: [
          {
            reference: "{diagnosisReference}",
            display: "Condition",
          },
        ],
      },
      {
        title: "Medications",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "721912009",
              display: "Medication summary document",
            },
          ],
        },
        entry: [
          {
            reference: "{medicationReference}",
            display: "MedicationRequest",
          },
        ],
      },
      {
        title: "Document Reference",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "371530004",
              display: "Clinical report",
            },
          ],
        },
        entry: [
          {
            reference: "{documentReference}",
            display: "DocumentReference",
          },
        ],
      },
    ],
  },
};
