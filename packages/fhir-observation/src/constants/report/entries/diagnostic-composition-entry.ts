/**
 * FHIR Composition Entry for Diagnostic Report
 */
export const DIAGNOSTIC_COMPOSITION_ENTRY = {
  fullUrl: "urn:uuid:{compositionUUID}",
  resource: {
    resourceType: "Composition",
    id: "{compositionUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DiagnosticReportRecord",
      ],
    },
    language: "en-IN",
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{compositionTitle}</div>",
    },
    identifier: {
      system: "https://ndhm.in/phr",
      value: "{compositionIdentifier}",
    },
    status: "final",
    type: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "721981007",
          display: "Diagnostic studies report",
        },
      ],
      text: "Diagnostic Report",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    encounter: {
      reference: "{encounterReference}",
      display: "Encounter",
    },
    date: "{compositionDate}",
    author: [
      {
        reference: "{authorReference}",
        display: "Practitioner",
      },
    ],
    title: "{compositionTitle}",
    custodian: {
      reference: "{custodianReference}",
      display: "Organization",
    },
    section: [
      {
        title: "Diagnostic Reports",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "721981007",
              display: "Diagnostic studies report",
            },
          ],
        },
        entry: [
          {
            reference: "{diagnosticReportReference}",
            display: "DiagnosticReport",
          },
        ],
      },
      {
        title: "Observations",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "364712009",
              display: "Clinical observation section",
            },
          ],
        },
        entry: [
          {
            reference: "{observationReference}",
            display: "Observation",
          },
        ],
      },
      {
        title: "Specimens",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "123038009",
              display: "Specimen",
            },
          ],
        },
        entry: [
          {
            reference: "{specimenReference}",
            display: "Specimen",
          },
        ],
      },
      {
        title: "Document Reference",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "371530004",
              display: "Clinical report",
            },
          ],
        },
        entry: [
          {
            reference: "{documentReference}",
            display: "DocumentReference",
          },
        ],
      },
    ],
  },
};
