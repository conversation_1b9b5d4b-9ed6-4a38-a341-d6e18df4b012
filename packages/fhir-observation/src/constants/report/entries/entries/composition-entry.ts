/**
 * FHIR Composition Entry for OP-Consult Report
 */
export const COMPOSITION_ENTRY = {
  fullUrl: "urn:uuid:{compositionUUID}",
  resource: {
    resourceType: "Composition",
    id: "{compositionUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/OPConsultRecord",
      ],
    },
    language: "en-IN",
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{compositionTitle}</div>",
    },
    identifier: {
      system: "https://ndhm.in/phr",
      value: "{compositionIdentifier}",
    },
    status: "final",
    type: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "371530004",
          display: "Clinical consultation report",
        },
      ],
      text: "Clinical Consultation report",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    encounter: {
      reference: "{encounterReference}",
      display: "Encounter",
    },
    date: "{compositionDate}",
    author: [
      {
        reference: "{authorReference}",
        display: "Practitioner",
      },
    ],
    title: "{compositionTitle}",
    custodian: {
      reference: "{custodianReference}",
      display: "Organization",
    },
    section: [
      {
        title: "Chief complaints",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "422843007",
              display: "Chief complaint section",
            },
          ],
        },
        entry: [
          {
            reference: "{chiefComplaintReference}",
            display: "Condition",
          },
        ],
      },
      {
        title: "Allergies",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "722446000",
              display: "Allergy record",
            },
          ],
        },
        entry: [
          {
            reference: "{allergyReference}",
            display: "AllergyIntolerance",
          },
        ],
      },
      {
        title: "Medications",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "721912009",
              display: "Medication summary document",
            },
          ],
        },
        entry: [
          {
            reference: "{medicationReference}",
            display: "MedicationRequest",
          },
        ],
      },
      {
        title: "Observations",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "364712009",
              display: "Clinical observation section",
            },
          ],
        },
        entry: [
          {
            reference: "{observationReference}",
            display: "Observation",
          },
        ],
      },
      {
        title: "Diagnostic Reports",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "721981007",
              display: "Diagnostic studies report",
            },
          ],
        },
        entry: [
          {
            reference: "{diagnosticReportReference}",
            display: "DiagnosticReport",
          },
        ],
      },
      {
        title: "Follow Up",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "736271009",
              display: "Outpatient care plan",
            },
          ],
        },
        entry: [
          {
            reference: "{appointmentReference}",
            display: "Appointment",
          },
        ],
      },
      {
        title: "Document Reference",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "371530004",
              display: "Clinical consultation report",
            },
          ],
        },
        entry: [
          {
            reference: "{documentReference}",
            display: "DocumentReference",
          },
        ],
      },
    ],
  },
};
