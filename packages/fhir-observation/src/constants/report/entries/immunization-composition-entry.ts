/**
 * FHIR Composition Entry for Immunization Record
 */
export const IMMUNIZATION_COMPOSITION_ENTRY = {
  fullUrl: "urn:uuid:{compositionUUID}",
  resource: {
    resourceType: "Composition",
    id: "{compositionUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/ImmunizationRecord",
      ],
    },
    language: "en-IN",
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{compositionTitle}</div>",
    },
    identifier: {
      system: "https://ndhm.in/phr",
      value: "{compositionIdentifier}",
    },
    status: "final",
    type: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "41000179103",
          display: "Immunization record",
        },
      ],
      text: "Immunization Record",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    encounter: {
      reference: "{encounterReference}",
      display: "Encounter",
    },
    date: "{compositionDate}",
    author: [
      {
        reference: "{authorReference}",
        display: "Practitioner",
      },
    ],
    title: "{compositionTitle}",
    custodian: {
      reference: "{custodianReference}",
      display: "Organization",
    },
    section: [
      {
        title: "Immunizations",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "41000179103",
              display: "Immunization record",
            },
          ],
        },
        entry: [
          {
            reference: "{immunizationReference}",
            display: "Immunization",
          },
        ],
      },
      {
        title: "Immunization Recommendations",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "422768004",
              display: "Immunization care plan",
            },
          ],
        },
        entry: [
          {
            reference: "{immunizationRecommendationReference}",
            display: "ImmunizationRecommendation",
          },
        ],
      },
      {
        title: "Document Reference",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "371530004",
              display: "Clinical report",
            },
          ],
        },
        entry: [
          {
            reference: "{documentReference}",
            display: "DocumentReference",
          },
        ],
      },
    ],
  },
};
