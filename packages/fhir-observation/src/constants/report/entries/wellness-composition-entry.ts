/**
 * FHIR Composition Entry for Wellness Record
 */
export const WELLNESS_COMPOSITION_ENTRY = {
  fullUrl: "urn:uuid:{compositionUUID}",
  resource: {
    resourceType: "Composition",
    id: "{compositionUUID}",
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/WellnessRecord",
      ],
    },
    language: "en-IN",
    text: {
      status: "generated",
      div: "<div xmlns='http://www.w3.org/1999/xhtml' xml:lang='en-IN' lang='en-IN'>{compositionTitle}</div>",
    },
    identifier: {
      system: "https://ndhm.in/phr",
      value: "{compositionIdentifier}",
    },
    status: "final",
    type: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: "371529009",
          display: "History and physical report",
        },
      ],
      text: "Wellness Record",
    },
    subject: {
      reference: "{patientReference}",
      display: "Patient",
    },
    encounter: {
      reference: "{encounterReference}",
      display: "Encounter",
    },
    date: "{compositionDate}",
    author: [
      {
        reference: "{authorReference}",
        display: "Practitioner",
      },
    ],
    title: "{compositionTitle}",
    custodian: {
      reference: "{custodianReference}",
      display: "Organization",
    },
    section: [
      {
        title: "Vital Signs",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "8716-3",
              display: "Vital signs",
            },
          ],
        },
        entry: [
          {
            reference: "{vitalSignsReference}",
            display: "Observation",
          },
        ],
      },
      {
        title: "Physical Activity",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "68130003",
              display: "Physical activity",
            },
          ],
        },
        entry: [
          {
            reference: "{physicalActivityReference}",
            display: "Observation",
          },
        ],
      },
      {
        title: "Sleep Pattern",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "258158006",
              display: "Sleep pattern",
            },
          ],
        },
        entry: [
          {
            reference: "{sleepPatternReference}",
            display: "Observation",
          },
        ],
      },
      {
        title: "Document Reference",
        code: {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: "371530004",
              display: "Clinical report",
            },
          ],
        },
        entry: [
          {
            reference: "{documentReference}",
            display: "DocumentReference",
          },
        ],
      },
    ],
  },
};
