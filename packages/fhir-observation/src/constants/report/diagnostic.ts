/**
 * FHIR Composition Resource Template for DiagnosticReport
 *
 * This template follows the ABDM FHIR R4 specification for Composition resources in DiagnosticReport.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {compositionIdentifier}: The identifier for the composition
 * - {patientReference}: The reference to the patient resource
 * - {encounterReference}: The reference to the encounter resource
 * - {authorReference}: The reference to the practitioner resource
 * - {custodianReference}: The reference to the organization resource
 * - {compositionDate}: The date when the composition was created
 * - {compositionTitle}: The title of the composition
 * - {diagnosticReportReference}: The reference to the diagnostic report
 * - {observationReference}: The reference to the observation
 * - {specimenReference}: The reference to the specimen
 * - {documentReference}: The reference to any document reference
 */

import {
  PATIENT_ENTRY,
  PRACTITIONER_ENTRY,
  ORGANIZATION_ENTRY,
  ENCOUNTER_ENTRY,
  DIAGNOSTIC_REPORT_ENTRY,
  OBSERVATION_ENTRY,
  SPECIMEN_ENTRY,
  DOCUMENT_REFERENCE_ENTRY,
  DIAGNOSTIC_COMPOSITION_ENTRY,
} from "./entries/index";

// Base bundle structure without entries
const BASE_BUNDLE = {
  resourceType: "Bundle",
  id: "{resourceUUID}",
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle",
    ],
    security: [
      {
        system: "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
        code: "V",
        display: "very restricted",
      },
    ],
  },
  identifier: {
    system: "http://hip.in",
    value: "{bundleIdentifier}",
  },
  type: "document",
  timestamp: new Date().toISOString(),
  entry: [], // Will be populated dynamically
};

// Function to construct the Diagnostic Report bundle with entries
export function constructDiagnosticReportBundle() {
  // Create a deep copy of the base bundle
  const bundle = JSON.parse(JSON.stringify(BASE_BUNDLE));

  // Add entries in the required order
  bundle.entry = [
    DIAGNOSTIC_COMPOSITION_ENTRY,
    PATIENT_ENTRY,
    PRACTITIONER_ENTRY,
    ORGANIZATION_ENTRY,
    ENCOUNTER_ENTRY,
    DIAGNOSTIC_REPORT_ENTRY,
    OBSERVATION_ENTRY,
    SPECIMEN_ENTRY,
    DOCUMENT_REFERENCE_ENTRY,
  ];

  return bundle;
}

// Export the strategy
export const STRATEGY = constructDiagnosticReportBundle();
