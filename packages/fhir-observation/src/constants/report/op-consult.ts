/**
 * FHIR Composition Resource Template for OPConsultNote
 *
 * This template follows the ABDM FHIR R4 specification for Composition resources in OPConsultNote.
 * It contains placeholders that will be replaced with actual values during generation.
 *
 * Placeholders:
 * - {resourceUUID}: A dynamically generated UUID for the resource
 * - {compositionIdentifier}: The identifier for the composition
 * - {patientReference}: The reference to the patient resource
 * - {encounterReference}: The reference to the encounter resource
 * - {authorReference}: The reference to the practitioner resource
 * - {custodianReference}: The reference to the organization resource
 * - {compositionDate}: The date when the composition was created
 * - {compositionTitle}: The title of the composition
 * - {chiefComplaintReference}: The reference to the chief complaint condition
 * - {allergyReference}: The reference to the allergy intolerance
 * - {medicationReference}: The reference to the medication request
 * - {observationReference}: The reference to the observation
 * - {diagnosticReportReference}: The reference to the diagnostic report
 * - {appointmentReference}: The reference to the follow-up appointment
 * - {documentReference}: The reference to any document reference
 */

import {
  COMPOSITION_ENTRY,
  PATIENT_ENTRY,
  PRACTITIONER_ENTRY,
  ORGANIZATION_ENTRY,
  CONDITION_ENTRY,
  ALLERGY_ENTRY,
  MEDICATION_ENTRY,
  OBSERVATION_ENTRY,
  DIAGNOSTIC_REPORT_ENTRY,
  APPOINTMENT_ENTRY,
  DOCUMENT_REFERENCE_ENTRY,
} from "./entries/index";

// Base bundle structure without entries
const BASE_BUNDLE = {
  resourceType: "Bundle",
  id: "{resourceUUID}",
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: [
      "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle",
    ],
    security: [
      {
        system: "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
        code: "V",
        display: "very restricted",
      },
    ],
  },
  identifier: {
    system: "http://hip.in",
    value: "{bundleIdentifier}",
  },
  type: "document",
  timestamp: new Date().toISOString(),
  entry: [], // Will be populated dynamically
};

// Function to construct the OP-Consult bundle with entries
export function constructOPConsultBundle() {
  // Create a deep copy of the base bundle
  const bundle = JSON.parse(JSON.stringify(BASE_BUNDLE));

  // Add entries in the required order
  bundle.entry = [
    COMPOSITION_ENTRY,
    PATIENT_ENTRY,
    PRACTITIONER_ENTRY,
    ORGANIZATION_ENTRY,
    CONDITION_ENTRY,
    ALLERGY_ENTRY,
    MEDICATION_ENTRY,
    OBSERVATION_ENTRY,
    DIAGNOSTIC_REPORT_ENTRY,
    APPOINTMENT_ENTRY,
    DOCUMENT_REFERENCE_ENTRY,
  ];

  return bundle;
}

// Export the strategy
export const STRATEGY = constructOPConsultBundle();
