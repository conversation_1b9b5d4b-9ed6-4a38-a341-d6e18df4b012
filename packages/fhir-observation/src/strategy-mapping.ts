import { EntryPractitionerStrategy } from "./strategies/entry-practitioner-strategy";
import { EntryObservationVitalSignsStrategy } from "./strategies/entry-observation-vital-signs-strategy";
import { EntryObservationBodyMeasurementStrategy } from "./strategies/entry-observation-body-measurement-strategy";
import { EntryObservationBloodPressureStrategy } from "./strategies/entry-observation-blood-pressure-strategy";
import { EntryPatientStrategy } from "./strategies/entry-patient-strategy";
import { EntryOrganizationStrategy } from "./strategies/entry-organization-strategy";
import { EntryConditionStrategy } from "./strategies/entry-condition-strategy";
import { EntryMedicationRequestStrategy } from "./strategies/entry-medication-request-strategy";
import { EntryDiagnosticReportStrategy } from "./strategies/entry-diagnostic-report-strategy";
import { EntryCompositionOPConsultStrategy } from "./strategies/entry-composition-op-consult-strategy";
import { ReportOPConsultStrategy } from "./strategies/report-op-consult-strategy";
import { ReportDiagnosticStrategy } from "./strategies/report-diagnostic-strategy";
import { ReportDischargeSummaryStrategy } from "./strategies/report-discharge-summary-strategy";
import { ReportHealthDocumentStrategy } from "./strategies/report-health-document-strategy";
import { ReportImmunizationStrategy } from "./strategies/report-immunization-strategy";
import { ReportPrescriptionStrategy } from "./strategies/report-prescription-strategy";
import { ReportWellnessStrategy } from "./strategies/report-wellness-strategy";

export const strategyMap = {
  ENTRY_PRACTITIONER_STRATEGY: EntryPractitionerStrategy,
  ENTRY_OBSERVATION_VITAL_SIGNS_STRATEGY: EntryObservationVitalSignsStrategy,
  ENTRY_OBSERVATION_BODY_MEASUREMENT_STRATEGY:
    EntryObservationBodyMeasurementStrategy,
  ENTRY_OBSERVATION_BLOOD_PRESSURE_STRATEGY:
    EntryObservationBloodPressureStrategy,
  ENTRY_PATIENT_STRATEGY: EntryPatientStrategy,
  ENTRY_ORGANIZATION_STRATEGY: EntryOrganizationStrategy,
  ENTRY_CONDITION_STRATEGY: EntryConditionStrategy,
  ENTRY_MEDICATION_REQUEST_STRATEGY: EntryMedicationRequestStrategy,
  ENTRY_DIAGNOSTIC_REPORT_STRATEGY: EntryDiagnosticReportStrategy,
  ENTRY_COMPOSITION_OP_CONSULT_STRATEGY: EntryCompositionOPConsultStrategy,
  REPORT_OP_CONSULT_STRATEGY: ReportOPConsultStrategy,
  REPORT_DIAGNOSTIC_STRATEGY: ReportDiagnosticStrategy,
  REPORT_DISCHARGE_SUMMARY_STRATEGY: ReportDischargeSummaryStrategy,
  REPORT_HEALTH_DOCUMENT_STRATEGY: ReportHealthDocumentStrategy,
  REPORT_IMMUNIZATION_STRATEGY: ReportImmunizationStrategy,
  REPORT_PRESCRIPTION_STRATEGY: ReportPrescriptionStrategy,
  REPORT_WELLNESS_STRATEGY: ReportWellnessStrategy,
};
