/**
 * FHIR Resource Factory
 *
 * This factory provides a centralized way to generate different types of FHIR resources.
 * It uses a strategy pattern to select the appropriate generator based on the resource type.
 */

import { EntryPractitionerStrategy } from "../strategies/entry-practitioner-strategy";
import { EntryObservationVitalSignsStrategy } from "../strategies/entry-observation-vital-signs-strategy";
import { EntryObservationBodyMeasurementStrategy } from "../strategies/entry-observation-body-measurement-strategy";
import { EntryObservationBloodPressureStrategy } from "../strategies/entry-observation-blood-pressure-strategy";
import { EntryPatientStrategy } from "../strategies/entry-patient-strategy";
import { EntryOrganizationStrategy } from "../strategies/entry-organization-strategy";
import { EntryConditionStrategy } from "../strategies/entry-condition-strategy";
import { EntryMedicationRequestStrategy } from "../strategies/entry-medication-request-strategy";
import { EntryDiagnosticReportStrategy } from "../strategies/entry-diagnostic-report-strategy";
import { EntryCompositionOPConsultStrategy } from "../strategies/entry-composition-op-consult-strategy";
import { ReportOPConsultStrategy } from "../strategies/report-op-consult-strategy";
import { ReportDiagnosticStrategy } from "../strategies/report-diagnostic-strategy";
import { ReportDischargeSummaryStrategy } from "../strategies/report-discharge-summary-strategy";
import { ReportHealthDocumentStrategy } from "../strategies/report-health-document-strategy";
import { ReportImmunizationStrategy } from "../strategies/report-immunization-strategy";
import { ReportPrescriptionStrategy } from "../strategies/report-prescription-strategy";
import { ReportWellnessStrategy } from "../strategies/report-wellness-strategy";

// Define the available resource types
export enum ResourceType {
  PRACTITIONER = "ENTRY_PRACTITIONER_STRATEGY",
  OBSERVATION_VITAL_SIGNS = "ENTRY_OBSERVATION_VITAL_SIGNS_STRATEGY",
  OBSERVATION_BODY_MEASUREMENT = "ENTRY_OBSERVATION_BODY_MEASUREMENT_STRATEGY",
  OBSERVATION_BLOOD_PRESSURE = "ENTRY_OBSERVATION_BLOOD_PRESSURE_STRATEGY",
  PATIENT = "ENTRY_PATIENT_STRATEGY",
  ORGANIZATION = "ENTRY_ORGANIZATION_STRATEGY",
  CONDITION = "ENTRY_CONDITION_STRATEGY",
  MEDICATION_REQUEST = "ENTRY_MEDICATION_REQUEST_STRATEGY",
  DIAGNOSTIC_REPORT = "ENTRY_DIAGNOSTIC_REPORT_STRATEGY",
  OP_CONSULT_NOTE = "ENTRY_COMPOSITION_OP_CONSULT_STRATEGY",
  REPORT_OP_CONSULT = "REPORT_OP_CONSULT_STRATEGY",
  REPORT_DIAGNOSTIC = "REPORT_DIAGNOSTIC_STRATEGY",
  REPORT_DISCHARGE_SUMMARY = "REPORT_DISCHARGE_SUMMARY_STRATEGY",
  REPORT_HEALTH_DOCUMENT = "REPORT_HEALTH_DOCUMENT_STRATEGY",
  REPORT_IMMUNIZATION = "REPORT_IMMUNIZATION_STRATEGY",
  REPORT_PRESCRIPTION = "REPORT_PRESCRIPTION_STRATEGY",
  REPORT_WELLNESS = "REPORT_WELLNESS_STRATEGY",
}

// Map resource types to their corresponding strategies
const strategyMap: Record<ResourceType, Function> = {
  [ResourceType.PRACTITIONER]: EntryPractitionerStrategy,
  [ResourceType.OBSERVATION_VITAL_SIGNS]: EntryObservationVitalSignsStrategy,
  [ResourceType.OBSERVATION_BODY_MEASUREMENT]:
    EntryObservationBodyMeasurementStrategy,
  [ResourceType.OBSERVATION_BLOOD_PRESSURE]:
    EntryObservationBloodPressureStrategy,
  [ResourceType.PATIENT]: EntryPatientStrategy,
  [ResourceType.ORGANIZATION]: EntryOrganizationStrategy,
  [ResourceType.CONDITION]: EntryConditionStrategy,
  [ResourceType.MEDICATION_REQUEST]: EntryMedicationRequestStrategy,
  [ResourceType.DIAGNOSTIC_REPORT]: EntryDiagnosticReportStrategy,
  [ResourceType.OP_CONSULT_NOTE]: EntryCompositionOPConsultStrategy,
  [ResourceType.REPORT_OP_CONSULT]: ReportOPConsultStrategy,
  [ResourceType.REPORT_DIAGNOSTIC]: ReportDiagnosticStrategy,
  [ResourceType.REPORT_DISCHARGE_SUMMARY]: ReportDischargeSummaryStrategy,
  [ResourceType.REPORT_HEALTH_DOCUMENT]: ReportHealthDocumentStrategy,
  [ResourceType.REPORT_IMMUNIZATION]: ReportImmunizationStrategy,
  [ResourceType.REPORT_PRESCRIPTION]: ReportPrescriptionStrategy,
  [ResourceType.REPORT_WELLNESS]: ReportWellnessStrategy,
};

/**
 * Generate a FHIR resource bundle based on the specified resource type
 *
 * @param resourceType - The type of FHIR resource to generate
 * @param input - Input data for the resource
 * @returns Promise resolving to a JSON string representation of the FHIR resource
 * @throws Error if the specified resource type is not supported
 */
export async function generateResource(
  resourceType: ResourceType,
  input: any,
): Promise<string> {
  const strategy = strategyMap[resourceType];

  if (!strategy) {
    throw new Error(`Unsupported resource type: ${resourceType}`);
  }

  try {
    return await strategy(input);
  } catch (error) {
    console.error(`Error generating ${resourceType} resource:`, error);
    throw error;
  }
}
