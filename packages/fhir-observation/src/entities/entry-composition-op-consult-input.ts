export interface InputEntity {
  // Composition details
  compositionIdentifier: string;
  compositionDate: string;
  compositionTitle: string;

  // Patient details
  patientID: string;
  patientFirstName: string;
  patientLastName: string;
  patientGender: string;
  patientBirthDate: string;
  patientPhone: string;
  patientEmail: string;
  patientAddress: string;
  patientCity: string;
  patientState: string;
  patientPostalCode: string;
  patientCountry: string;

  // Practitioner details
  practitionerID: string;
  practitionerName: string;
  practitionerQualification: string;

  // Organization details
  organizationID: string;
  organizationName: string;
  organizationPhone: string;
  organizationEmail: string;
  organizationAddress: string;
  organizationCity: string;
  organizationState: string;
  organizationPostalCode: string;
  organizationCountry: string;

  // Encounter details
  encounterID: string;
  encounterDate: string;
  encounterType: string;

  // Chief complaint (Condition)
  conditionCode: string;
  conditionDisplay: string;
  conditionText: string;
  conditionRecordedDate: string;
  conditionOnsetDate: string;
  conditionClinicalStatus: string;
  conditionClinicalStatusDisplay: string;
  conditionVerificationStatus: string;
  conditionVerificationStatusDisplay: string;
  conditionCategory: string;
  conditionCategoryDisplay: string;
  conditionSeverityCode: string;
  conditionSeverity: string;

  // Allergy
  allergyCode: string;
  allergyDisplay: string;
  allergyText: string;
  allergyType: string;
  allergyCriticality: string;
  allergyOnsetDate: string;
  allergyCategory: string;

  // Medication
  medicationCode: string;
  medicationDisplay: string;
  medicationText: string;
  medicationAuthoredOn: string;
  medicationStatus: string;
  medicationIntent: string;
  dosageText: string;
  dosageRouteCode: string;
  dosageRoute: string;
  dosageMethodCode: string;
  dosageMethod: string;
  dosageFrequency: number;
  dosagePeriodValue: number;
  dosagePeriodUnit: string;
  dosageAsNeeded: boolean;
  dosageAsNeededCode: string;
  dosageAsNeededReason: string;

  // Observation (Vital Signs)
  observationCode: string;
  observationDisplay: string;
  observationText: string;
  observationEffectiveDateTime: string;
  observationValueQuantity: number;
  observationValueUnit: string;
  observationValueSystem: string;
  observationValueCode: string;

  // Diagnostic Report
  reportCode: string;
  reportDisplay: string;
  reportText: string;
  reportEffectiveDateTime: string;
  reportIssuedDateTime: string;
  reportStatus: string;
  reportCategory: string;
  reportCategoryDisplay: string;
  reportConclusion: string;

  // Appointment (Follow-up)
  appointmentDescription: string;
  appointmentStart: string;
  appointmentEnd: string;
  appointmentCreated: string;

  // Document Reference
  documentTitle: string;
  documentDescription: string;
  documentCreated: string;
  documentContentType: string;
  documentUrl: string;
}
