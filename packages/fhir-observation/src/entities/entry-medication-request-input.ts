export interface InputEntity {
  medicationCode: string;
  medicationDisplay: string;
  medicationText: string;
  patientReference: string;
  practitionerName: string;
  practitionerReference: string;
  authoredOn: string;
  status: string;
  intent: string;
  dosageText: string;
  dosageRouteCode: string;
  dosageRoute: string;
  dosageMethodCode: string;
  dosageMethod: string;
  dosageFrequency: number;
  dosagePeriodValue: number;
  dosagePeriodUnit: string;
  dosageAsNeeded: boolean;
  dosageAsNeededCode: string;
  dosageAsNeededReason: string;
}
