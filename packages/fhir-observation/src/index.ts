import { generateResource, ResourceType } from "./factory/resource-factory";

/**
 * Generate a FHIR resource bundle
 *
 * @param strategyName - The type of FHIR resource to generate (use ResourceType enum)
 * @param input - Input data for the resource
 * @returns Promise resolving to a JSON string representation of the FHIR resource
 * @throws Error if the specified resource type is not supported
 *
 * @deprecated Use generateResource from the resource-factory instead
 */
export function generateBundle(
  strategyName: string,
  input: any,
): Promise<string> {
  // For backward compatibility, convert string strategy name to ResourceType
  try {
    // Validate that the strategy name is a valid ResourceType
    if (!Object.values(ResourceType).includes(strategyName as ResourceType)) {
      return Promise.reject(
        new Error(
          `Invalid resource type: ${strategyName}. Use ResourceType enum values.`,
        ),
      );
    }

    return generateResource(strategyName as ResourceType, input);
  } catch (error) {
    console.error("Error generating FHIR bundle:", error);
    return Promise.reject(error);
  }
}

// Export the new factory functions and types
export { generateResource, ResourceType };
