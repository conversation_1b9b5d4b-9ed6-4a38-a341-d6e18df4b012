{"resourceType": "StructureDefinition", "id": "PrescriptionRecord", "meta": {"versionId": "2", "lastUpdated": "2023-11-01T06:22:25.725544+00:00"}, "text": {"status": "extensions", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"border: 0px #F0F0F0 solid; font-size: 11px; font-family: verdana; vertical-align: top;\"><tr style=\"border: 1px #F0F0F0 solid; font-size: 11px; font-family: verdana; vertical-align: top\"><th style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"https://build.fhir.org/ig/FHIR/ig-guidance/readingIgs.html#table-views\" title=\"The logical name of the element\">Name</a></th><th style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"https://build.fhir.org/ig/FHIR/ig-guidance/readingIgs.html#table-views\" title=\"Information about the use of the element\">Flags</a></th><th style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"https://build.fhir.org/ig/FHIR/ig-guidance/readingIgs.html#table-views\" title=\"Minimum and Maximum # of times the the element can appear in the instance\">Card.</a></th><th style=\"width: 100px\" class=\"hierarchy\"><a href=\"https://build.fhir.org/ig/FHIR/ig-guidance/readingIgs.html#table-views\" title=\"Reference to the type of the element\">Type</a></th><th style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"https://build.fhir.org/ig/FHIR/ig-guidance/readingIgs.html#table-views\" title=\"Additional information about the element\">Description &amp; Constraints</a><span style=\"float: right\"><a href=\"https://build.fhir.org/ig/FHIR/ig-guidance/readingIgs.html#table-views\" title=\"Legend for this format\"><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3goXBCwdPqAP0wAAAldJREFUOMuNk0tIlFEYhp9z/vE2jHkhxXA0zJCMitrUQlq4lnSltEqCFhFG2MJFhIvIFpkEWaTQqjaWZRkp0g26URZkTpbaaOJkDqk10szoODP//7XIMUe0elcfnPd9zsfLOYplGrpRwZaqTtw3K7PtGem7Q6FoidbGgqHVy/HRb669R+56zx7eRV1L31JGxYbBtjKK93cxeqfyQHbehkZbUkK20goELEuIzEd+dHS+qz/Y8PTSif0FnGkbiwcAjHaU1+QWOptFiyCLp/LnKptpqIuXHx6rbR26kJcBX3yLgBfnd7CxwJmflpP2wUg0HIAoUUpZBmKzELGWcN8nAr6Gpu7tLU/CkwAaoKTWRSQyt89Q8w6J+oVQkKnBoblH7V0PPvUOvDYXfopE/SJmALsxnVm6LbkotrUtNowMeIrVrBcBpaMmdS0j9df7abpSuy7HWehwJdt1lhVwi/J58U5beXGAF6c3UXLycw1wdFklArBn87xdh0ZsZtArghBdAA3+OEDVubG4UEzP6x1FOWneHh2VDAHBAt80IbdXDcesNoCvs3E5AFyNSU5nbrDPZpcUEQQTFZiEVx+51fxMhhyJEAgvlriadIJZZksRuwBYMOPBbO3hePVVqgEJhFeUuFLhIPkRP6BQLIBrmMenujm/3g4zc398awIe90Zb5A1vREALqneMcYgP/xVQWlG+Ncu5vgwwlaUNx+3799rfe96u9K0JSDXcOzOTJg4B6IgmXfsygc7/Bvg9g9E58/cDVmGIBOP/zT8Bz1zqWqpbXIsd0O9hajXfL6u4BaOS6SeWAAAAAElFTkSuQmCC\" alt=\"doco\" style=\"background-color: inherit\"/></a></span></th></tr><tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck1.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_resource.png\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Resource\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition\" title=\"A set of healthcare-related information that is assembled together into a single logical package that provides a single coherent statement of meaning, establishes its own context and that has clinical attestation with regard to who is making the statement. A Composition defines the structure and narrative content necessary for a document. However, a Composition alone does not constitute a document. Rather, the Composition must be the first entry in a Bundle where Bundle.type=document, and any other resources referenced from Composition must be included as subsequent entries in the Bundle (for example Patient, Practitioner, Encounter, etc.).\r\n\r\nA set of healthcare-related information that is assembled together into a Composition to represent a physician's order for the preparation and administration of a drug or device for a patient. A Composition defines the structure, it does not actually contain the content: rather the full content of a document (for example Patient, Practitioner, Organization, MedicationRequest, etc.) is contained in a Bundle, of which the Composition is the first resource contained.\">Composition</a><a name=\"Composition\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">*</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"http://hl7.org/fhir/R4/composition.html\">Composition</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">A set of resources composed into a single coherent clinical statement with clinical attestation / A set of resources composed to define the prescription record.</td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck10.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.status\">status</a><a name=\"Composition.status\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">1</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#code\">code</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">preliminary | final | amended | entered-in-error</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck11.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.type\" title=\"Specifies the particular kind of composition (e.g. History and Physical, Discharge Summary, Progress Note). This usually equates to the purpose of making the composition.\r\n\r\nSpecifies that this composition refer to a Prescription record (SNOMED CT &quot;*********&quot;)\">type</a><a name=\"Composition.type\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">1</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#CodeableConcept\">CodeableConcept</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">Kind of composition (&quot;Prescription record &quot;)</td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck111.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.type.coding\" title=\"A reference to a code defined by a terminology system (SNOMED CT ).\">coding</a><a name=\"Composition.type.coding\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..1</td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#Coding\">Coding</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">Code defined by a terminology system/ Code defined by SNOMED CT for Prescription record</td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck1110.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.type.coding.system\">system</a><a name=\"Composition.type.coding.system\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#uri\">uri</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Identity of the terminology system</span><br/><span style=\"font-weight:bold\">Fixed Value: </span><span style=\"color: darkgreen\">http://snomed.info/sct</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck1110.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.type.coding.code\">code</a><a name=\"Composition.type.coding.code\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#code\">code</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Symbol in syntax defined by the system</span><br/><span style=\"font-weight:bold\">Fixed Value: </span><span style=\"color: darkgreen\">*********</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck1100.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.type.coding.display\">display</a><a name=\"Composition.type.coding.display\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#string\">string</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Representation defined by the system</span><br/><span style=\"font-weight:bold\">Fixed Value: </span><span style=\"color: darkgreen\">Prescription record</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck100.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.type.text\">text</a><a name=\"Composition.type.text\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#string\">string</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Plain text representation of the concept</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck11.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_reference.png\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Reference to another Resource\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.subject\" title=\"Who or what the composition is about. The composition can be about a person, (patient or healthcare practitioner), a device (e.g. a machine) or even a group of subjects (such as a document about a herd of livestock, or a set of patients that share a common exposure). \r\n\r\nWho or what the Prescription record is about. Subject will always refer to the patient in Prescription record.\">subject</a><a name=\"Composition.subject\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"http://hl7.org/fhir/R4/references.html\">Reference</a>(<a href=\"StructureDefinition-Patient.html\">Patient</a>)</td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">Who and/or what the composition/Prescription record is about</td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck100.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.subject.reference\">reference</a><a name=\"Composition.subject.reference\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#string\">string</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Literal reference, Relative, internal or absolute URL</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck10.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_reference.png\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Reference to another Resource\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.encounter\">encounter</a><a name=\"Composition.encounter\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"http://hl7.org/fhir/R4/references.html\">Reference</a>(<a href=\"StructureDefinition-Encounter.html\">Encounter</a>)</td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Context of the Composition</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck10.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.date\">date</a><a name=\"Composition.date\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">1</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#dateTime\">dateTime</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Composition editing time</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck11.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_reference.png\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Reference to another Resource\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.author\" title=\"Identifies who is responsible for the information in the composition, not necessarily who typed it in.\r\n\r\nIdentifies who is responsible for the information in the presciption record, not necessarily who typed it in.\">author</a><a name=\"Composition.author\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">1</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">*</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"http://hl7.org/fhir/R4/references.html\">Reference</a>(<a href=\"StructureDefinition-Practitioner.html\">Practitioner</a> | <a href=\"StructureDefinition-Organization.html\">Organization</a> | <a href=\"StructureDefinition-PractitionerRole.html\">PractitionerRole</a> | <a href=\"StructureDefinition-Patient.html\">Patient</a> | <a href=\"http://hl7.org/fhir/R4/device.html\">Device</a> | <a href=\"http://hl7.org/fhir/R4/relatedperson.html\">RelatedPerson</a>)</td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">Who and/or what authored the composition/Presciption record</td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck100.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.author.reference\">reference</a><a name=\"Composition.author.reference\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#string\">string</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Literal reference, Relative, internal or absolute URL</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck10.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.title\" title=\"Official human-readable label for the composition.\r\n\r\nFor this document title should be &quot;Prescription record&quot; or any equivalent translation\">title</a><a name=\"Composition.title\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">1</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#string\">string</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Human Readable name/title</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck11.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.attester\">attester</a><a name=\"Composition.attester\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">*</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#BackboneElement\">BackboneElement</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Attests to accuracy of composition</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck100.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_reference.png\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Reference to another Resource\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.attester.party\">party</a><a name=\"Composition.attester.party\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"http://hl7.org/fhir/R4/references.html\">Reference</a>(<a href=\"http://hl7.org/fhir/R4/relatedperson.html\">RelatedPerson</a> | <a href=\"StructureDefinition-PractitionerRole.html\">PractitionerRole</a> | <a href=\"StructureDefinition-Practitioner.html\">Practitioner</a> | <a href=\"StructureDefinition-Patient.html\">Patient</a> | <a href=\"StructureDefinition-Organization.html\">Organization</a>)</td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Who attested the composition</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck10.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_reference.png\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Reference to another Resource\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.custodian\">custodian</a><a name=\"Composition.custodian\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"http://hl7.org/fhir/R4/references.html\">Reference</a>(<a href=\"StructureDefinition-Organization.html\">Organization</a>)</td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Organization which maintains the composition</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck01.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section\" title=\"The root of the sections that make up the composition.\r\n\r\nThe root of the section that make up the Prescription record. Section contains a description of the patient's medication requests relevant for the Prescription record.\">section</a><a name=\"Composition.section\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..1</td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#BackboneElement\">BackboneElement</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">Composition is broken into sections / Prescription record contains single section to define the relevant medication requests</td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck011.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.code\">code</a><a name=\"Composition.section.code\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck0111.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.code.coding\">coding</a><a name=\"Composition.section.code.coding\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">*</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#Coding\">Coding</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Code defined by a terminology system</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck01110.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.code.coding.system\">system</a><a name=\"Composition.section.code.coding.system\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#uri\">uri</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Identity of the terminology system</span><br/><span style=\"font-weight:bold\">Fixed Value: </span><span style=\"color: darkgreen\">http://snomed.info/sct</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck01110.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.code.coding.code\">code</a><a name=\"Composition.section.code.coding.code\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#code\">code</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Symbol in syntax defined by the system</span><br/><span style=\"font-weight:bold\">Fixed Value: </span><span style=\"color: darkgreen\">*********</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck01100.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.code.coding.display\">display</a><a name=\"Composition.section.code.coding.display\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#string\">string</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Representation defined by the system</span><br/><span style=\"font-weight:bold\">Fixed Value: </span><span style=\"color: darkgreen\">Prescription record</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck0100.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.code.text\">text</a><a name=\"Composition.section.code.text\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#string\">string</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Plain text representation of the concept</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck003.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_slice.png\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Slice Definition\" class=\"hierarchy\"/> <a style=\"font-style: italic\" href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.entry\" title=\"A reference to the actual resource from which the narrative in the section is derived.\r\n\r\nA list of references to the acutal resource defining the medication requests in prescription record.\">Slices for entry</a><a name=\"Composition.section.entry\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red; font-style: italic\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"font-style: italic\"/><span style=\"font-style: italic\">1</span><span style=\"font-style: italic\">..</span><span style=\"opacity: 0.5; font-style: italic\">*</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"font-style: italic\" href=\"http://hl7.org/fhir/R4/references.html\">Reference</a><span style=\"font-style: italic\">(</span><a style=\"font-style: italic\" href=\"StructureDefinition-MedicationRequest.html\">MedicationRequest</a><span style=\"font-style: italic\"> | </span><a style=\"font-style: italic\" href=\"StructureDefinition-Binary.html\">Binary</a><span style=\"font-style: italic\">)</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5; font-style: italic\">A reference to data that supports this section</span><br style=\"font-style: italic\"/><span style=\"font-weight:bold; font-style: italic\">Slice: </span><span style=\"font-style: italic\">Unordered, Closed by value:type</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck0035.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_slicer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_slice_item.png\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Slice Item\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.entry:MedicationRequest\" title=\"Slice MedicationRequest: A reference to the actual resource from which the narrative in the section is derived.\r\n\r\nA list of references to the acutal resource defining the medication requests in prescription record.\">entry:MedicationRequest</a><a name=\"Composition.section.entry\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"padding-left: 3px; padding-right: 3px; color: white; background-color: red\" title=\"This element must be supported\">S</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span><span style=\"opacity: 0.5\">..</span><span style=\"opacity: 0.5\">*</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"http://hl7.org/fhir/R4/references.html\">Reference</a>(<a href=\"StructureDefinition-MedicationRequest.html\">MedicationRequest</a>)</td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">A reference to data that supports this section</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck00350.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline_slicer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_slice.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.entry:MedicationRequest.reference\">reference</a><a name=\"Composition.section.entry.reference\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#string\">string</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Literal reference, Relative, internal or absolute URL</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck00340.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vline_slicer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end_slice.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.entry:MedicationRequest.type\">type</a><a name=\"Composition.section.entry.type\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#uri\">uri</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Type the reference refers to (e.g. &quot;Patient&quot;)</span><br/><span style=\"font-weight:bold\">Fixed Value: </span><span style=\"color: darkgreen\">MedicationRequest</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck0025.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end_slicer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_slice_item.png\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Slice Item\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.entry:Binary\" title=\"Slice Binary: A reference to the actual resource from which the narrative in the section is derived.\r\n\r\nA reference to the actual resource of binary to provide the document of prescription record\">entry:Binary</a><a name=\"Composition.section.entry\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">0</span>..1</td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a href=\"http://hl7.org/fhir/R4/references.html\">Reference</a>(<a href=\"StructureDefinition-Binary.html\">Binary</a>)</td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">A reference to data that supports this section</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: white\"><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck00250.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_slice.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: white; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.entry:Binary.reference\">reference</a><a name=\"Composition.section.entry.reference\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#string\">string</a></td><td style=\"vertical-align: top; text-align : left; background-color: white; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Literal reference, Relative, internal or absolute URL</span></td></tr>\r\n<tr style=\"border: 0px #F0F0F0 solid; padding:0px; vertical-align: top; background-color: #F7F7F7\"><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px; white-space: nowrap; background-image: url(tbl_bck00240.png)\" class=\"hierarchy\"><img src=\"tbl_spacer.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_blank.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"tbl_vjoin_end_slice.png\" alt=\".\" style=\"background-color: inherit\" class=\"hierarchy\"/><img src=\"icon_element.gif\" alt=\".\" style=\"background-color: #F7F7F7; background-color: inherit\" title=\"Element\" class=\"hierarchy\"/> <a href=\"StructureDefinition-PrescriptionRecord-definitions.html#Composition.section.entry:Binary.type\">type</a><a name=\"Composition.section.entry.type\"> </a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"/><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\">1..<span style=\"opacity: 0.5\">1</span></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><a style=\"opacity: 0.5\" href=\"http://hl7.org/fhir/R4/datatypes.html#uri\">uri</a></td><td style=\"vertical-align: top; text-align : left; background-color: #F7F7F7; border: 0px #F0F0F0 solid; padding:0px 4px 0px 4px\" class=\"hierarchy\"><span style=\"opacity: 0.5\">Type the reference refers to (e.g. &quot;Patient&quot;)</span><br/><span style=\"font-weight:bold\">Fixed Value: </span><span style=\"color: darkgreen\">Binary</span></td></tr>\r\n<tr><td colspan=\"5\" class=\"hierarchy\"><br/><a href=\"https://build.fhir.org/ig/FHIR/ig-guidance/readingIgs.html#table-views\" title=\"Legend for this format\"><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3goXBCwdPqAP0wAAAldJREFUOMuNk0tIlFEYhp9z/vE2jHkhxXA0zJCMitrUQlq4lnSltEqCFhFG2MJFhIvIFpkEWaTQqjaWZRkp0g26URZkTpbaaOJkDqk10szoODP//7XIMUe0elcfnPd9zsfLOYplGrpRwZaqTtw3K7PtGem7Q6FoidbGgqHVy/HRb669R+56zx7eRV1L31JGxYbBtjKK93cxeqfyQHbehkZbUkK20goELEuIzEd+dHS+qz/Y8PTSif0FnGkbiwcAjHaU1+QWOptFiyCLp/LnKptpqIuXHx6rbR26kJcBX3yLgBfnd7CxwJmflpP2wUg0HIAoUUpZBmKzELGWcN8nAr6Gpu7tLU/CkwAaoKTWRSQyt89Q8w6J+oVQkKnBoblH7V0PPvUOvDYXfopE/SJmALsxnVm6LbkotrUtNowMeIrVrBcBpaMmdS0j9df7abpSuy7HWehwJdt1lhVwi/J58U5beXGAF6c3UXLycw1wdFklArBn87xdh0ZsZtArghBdAA3+OEDVubG4UEzP6x1FOWneHh2VDAHBAt80IbdXDcesNoCvs3E5AFyNSU5nbrDPZpcUEQQTFZiEVx+51fxMhhyJEAgvlriadIJZZksRuwBYMOPBbO3hePVVqgEJhFeUuFLhIPkRP6BQLIBrmMenujm/3g4zc398awIe90Zb5A1vREALqneMcYgP/xVQWlG+Ncu5vgwwlaUNx+3799rfe96u9K0JSDXcOzOTJg4B6IgmXfsygc7/Bvg9g9E58/cDVmGIBOP/zT8Bz1zqWqpbXIsd0O9hajXfL6u4BaOS6SeWAAAAAElFTkSuQmCC\" alt=\"doco\" style=\"background-color: inherit\"/> Documentation for this format</a></td></tr></table></div>"}, "url": "https://nrces.in/ndhm/fhir/r4/StructureDefinition/PrescriptionRecord", "version": "6.0.0", "name": "PrescriptionRecord", "status": "draft", "date": "2020-08-17T10:26:05.6051945+00:00", "publisher": "National Resource Center for EHR Standards", "contact": [{"telecom": [{"system": "url", "value": "http://nrces.in/"}]}], "description": "The Clinical Artifact represents the medication advice to the patient in compliance with the Pharmacy Council of India (PCI) guidelines, which can be shared across the health ecosystem.", "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "IN"}]}], "fhirVersion": "4.0.1", "mapping": [{"identity": "workflow", "uri": "http://hl7.org/fhir/workflow", "name": "Workflow Pattern"}, {"identity": "rim", "uri": "http://hl7.org/v3", "name": "RIM Mapping"}, {"identity": "cda", "uri": "http://hl7.org/v3/cda", "name": "CDA (R2)"}, {"identity": "fhirdocumentreference", "uri": "http://hl7.org/fhir/documentreference", "name": "FHIR DocumentReference"}, {"identity": "w5", "uri": "http://hl7.org/fhir/fivews", "name": "FiveWs Pattern Mapping"}], "kind": "resource", "abstract": false, "type": "Composition", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/Composition", "derivation": "constraint", "snapshot": {"element": [{"id": "Composition", "path": "Composition", "short": "A set of resources composed into a single coherent clinical statement with clinical attestation / A set of resources composed to define the prescription record.", "definition": "A set of healthcare-related information that is assembled together into a single logical package that provides a single coherent statement of meaning, establishes its own context and that has clinical attestation with regard to who is making the statement. A Composition defines the structure and narrative content necessary for a document. However, a Composition alone does not constitute a document. Rather, the Composition must be the first entry in a Bundle where Bundle.type=document, and any other resources referenced from Composition must be included as subsequent entries in the Bundle (for example Patient, Practitioner, Encounter, etc.).\r\n\r\nA set of healthcare-related information that is assembled together into a Composition to represent a physician's order for the preparation and administration of a drug or device for a patient. A Composition defines the structure, it does not actually contain the content: rather the full content of a document (for example Patient, Practitioner, Organization, MedicationRequest, etc.) is contained in a Bundle, of which the Composition is the first resource contained.", "comment": "While the focus of this specification is on patient-specific clinical statements, this resource can also apply to other healthcare-related statements such as study protocol designs, healthcare invoices and other activities that are not necessarily patient-specific or clinical.\r\n\r\nThe composition refers to the some ABDM profiles.", "min": 0, "max": "*", "base": {"path": "Composition", "min": 0, "max": "*"}, "constraint": [{"key": "dom-2", "severity": "error", "human": "If the resource is contained in another resource, it SHALL NOT contain nested Resources", "expression": "contained.contained.empty()", "xpath": "not(parent::f:contained and f:contained)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-3", "severity": "error", "human": "If the resource is contained in another resource, it SHALL be referred to from elsewhere in the resource or SHALL refer to the containing resource", "expression": "contained.where((('#'+id in (%resource.descendants().reference | %resource.descendants().as(canonical) | %resource.descendants().as(uri) | %resource.descendants().as(url))) or descendants().where(reference = '#').exists() or descendants().where(as(canonical) = '#').exists() or descendants().where(as(canonical) = '#').exists()).not()).trace('unmatched', id).empty()", "xpath": "not(exists(for $id in f:contained/*/f:id/@value return $contained[not(parent::*/descendant::f:reference/@value=concat('#', $contained/*/id/@value) or descendant::f:reference[@value='#'])]))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-4", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a meta.versionId or a meta.lastUpdated", "expression": "contained.meta.versionId.empty() and contained.meta.lastUpdated.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:versionId)) and not(exists(f:contained/*/f:meta/f:lastUpdated))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-5", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a security label", "expression": "contained.meta.security.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:security))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice", "valueBoolean": true}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice-explanation", "valueMarkdown": "When a resource has no narrative, only systems that fully understand the data can display the resource to a human safely. Including a human readable representation in the resource makes for a much more robust eco-system and cheaper handling of resources by intermediary systems. Some ecosystems restrict distribution of resources to only those systems that do fully understand the resources, and as a consequence implementers may believe that the narrative is superfluous. However experience shows that such eco-systems often open up to new participants over time."}], "key": "dom-6", "severity": "warning", "human": "A resource should have narrative for robust management", "expression": "text.`div`.exists()", "xpath": "exists(f:text/h:div)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "Entity. Role, or Act"}, {"identity": "workflow", "map": "Event"}, {"identity": "rim", "map": "Document[classCode=\"DOC\" and moodCode=\"EVN\" and isNormalAct()]"}, {"identity": "cda", "map": "ClinicalDocument"}, {"identity": "fhirdocumentreference", "map": "when described by DocumentReference"}]}, {"id": "Composition.id", "path": "Composition.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "comment": "The only time that a resource does not have an id is when it is being submitted to the server using a create operation.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "id"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": true}, {"id": "Composition.meta", "path": "Composition.meta", "short": "Metadata about the resource", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Composition.implicitRules", "path": "Composition.implicitRules", "short": "A set of rules under which this content was created", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "comment": "Asserting this rule set restricts the content to be only understood by a limited set of trading partners. This inherently limits the usefulness of the data in the long term. However, the existing health eco-system is highly fractured, and not yet ready to define, collect, and exchange data in a generally computable sense. Wherever possible, implementers and/or specification writers should avoid using this element. Often, when used, the URL is a reference to an implementation guide that defines these special rules as part of it's narrative along with other profiles, value sets, etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labeled as a modifier because the implicit rules may provide additional knowledge about the resource that modifies it's meaning or interpretation", "isSummary": true}, {"id": "Composition.language", "path": "Composition.language", "short": "Language of the resource content", "definition": "The base language in which the resource is written.", "comment": "Language is provided to support indexing and accessibility (typically, services such as text to speech use the language tag). The html language tag in the narrative applies  to the narrative. The language tag on the resource may be used to specify the language of other presentations generated from the data in the resource. Not all the content has to be in the base language. The Resource.language should not be assumed to apply to the narrative automatically. If a language is specified, it should it also be specified on the div element in the html (see rules in HTML5 for information about the relationship between xml:lang and the html lang attribute).", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-maxValueSet", "valueCanonical": "http://hl7.org/fhir/ValueSet/all-languages"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "Language"}], "strength": "preferred", "description": "A human language.", "valueSet": "http://hl7.org/fhir/ValueSet/languages"}}, {"id": "Composition.text", "path": "Composition.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "comment": "Contained resources do not have narrative. Resources that are not contained SHOULD have a narrative. In some cases, a resource may only have text with little or no additional discrete data (as long as all minOccurs=1 elements are satisfied).  This may be necessary for data from legacy systems where information is captured as a \"text blob\" or where text is additionally entered raw or narrated and encoded information is added later.", "alias": ["narrative", "html", "xhtml", "display"], "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "Act.text?"}]}, {"id": "Composition.contained", "path": "Composition.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "comment": "This should never be done when the content can be identified properly, as once identification is lost, it is extremely difficult (and context dependent) to restore it again. Contained resources may have profiles and tags In their meta elements, but SHALL NOT have security labels.", "alias": ["inline resources", "anonymous resources", "contained resources"], "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.extension", "path": "Composition.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.modifierExtension", "path": "Composition.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the resource that contains them", "isSummary": false, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.identifier", "path": "Composition.identifier", "short": "Version-independent identifier for the Composition", "definition": "A version-independent identifier for the Composition. This identifier stays constant as the composition is changed over time.", "comment": "Similar to ClinicalDocument/setId in CDA. See discussion in resource definition for how these relate.", "min": 0, "max": "1", "base": {"path": "Composition.identifier", "min": 0, "max": "1"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "workflow", "map": "Event.identifier"}, {"identity": "w5", "map": "FiveWs.identifier"}, {"identity": "rim", "map": "Document.id / Document.setId"}, {"identity": "cda", "map": ".setId"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.masterIdentifier"}]}, {"id": "Composition.status", "path": "Composition.status", "short": "preliminary | final | amended | entered-in-error", "definition": "The workflow/clinical status of this composition. The status is a marker for the clinical standing of the document.", "comment": "If a composition is marked as withdrawn, the compositions/documents in the series, or data from the composition or document series, should never be displayed to a user without being clearly marked as untrustworthy. The flag \"entered-in-error\" is why this element is labeled as a modifier of other elements.   \n\nSome reporting work flows require that the original narrative of a final document never be altered; instead, only new narrative can be added. The composition resource has no explicit status for explicitly noting whether this business rule is in effect. This would be handled by an extension if required.", "requirements": "Need to be able to mark interim, amended, or withdrawn compositions or documents.", "min": 1, "max": "1", "base": {"path": "Composition.status", "min": 1, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": true, "isModifierReason": "This element is labelled as a modifier because it is a status element that contains status entered-in-error which means that the resource should not be treated as valid", "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "CompositionStatus"}], "strength": "required", "description": "The workflow/clinical status of the composition.", "valueSet": "http://hl7.org/fhir/ValueSet/composition-status|4.0.1"}, "mapping": [{"identity": "workflow", "map": "Event.status"}, {"identity": "w5", "map": "FiveWs.status"}, {"identity": "rim", "map": "interim: .completionCode=\"IN\" & ./statusCode[isNormalDatatype()]=\"active\";  final: .completionCode=\"AU\" &&  ./statusCode[isNormalDatatype()]=\"complete\" and not(./inboundRelationship[typeCode=\"SUBJ\" and isNormalActRelationship()]/source[subsumesCode(\"ActClass#CACT\") and moodCode=\"EVN\" and domainMember(\"ReviseComposition\", code) and isNormalAct()]);  amended: .completionCode=\"AU\" &&  ./statusCode[isNormalDatatype()]=\"complete\" and ./inboundRelationship[typeCode=\"SUBJ\" and isNormalActRelationship()]/source[subsumesCode(\"ActClass#CACT\") and moodCode=\"EVN\" and domainMember(\"ReviseComposition\", code) and isNormalAct() and statusCode=\"completed\"];  withdrawn : .completionCode=NI &&  ./statusCode[isNormalDatatype()]=\"obsolete\""}, {"identity": "cda", "map": "n/a"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.status"}]}, {"id": "Composition.type", "path": "Composition.type", "short": "Kind of composition (\"Prescription record \")", "definition": "Specifies the particular kind of composition (e.g. History and Physical, Discharge Summary, Progress Note). This usually equates to the purpose of making the composition.\r\n\r\nSpecifies that this composition refer to a Prescription record (SNOMED CT \"*********\")", "comment": "For Composition type, LOINC is ubiquitous and strongly endorsed by HL7. Most implementation guides will require a specific LOINC code, or use LOINC as an extensible binding.", "requirements": "Key metadata element describing the composition, used in searching/filtering.", "min": 1, "max": "1", "base": {"path": "Composition.type", "min": 1, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "DocumentType"}], "strength": "preferred", "description": "Type of a composition.", "valueSet": "http://hl7.org/fhir/ValueSet/doc-typecodes"}, "mapping": [{"identity": "workflow", "map": "Event.code"}, {"identity": "w5", "map": "FiveWs.class"}, {"identity": "rim", "map": "./code"}, {"identity": "cda", "map": ".code"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.type"}]}, {"id": "Composition.type.id", "path": "Composition.type.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.type.extension", "path": "Composition.type.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.type.coding", "path": "Composition.type.coding", "short": "Code defined by a terminology system/ Code defined by SNOMED CT for Prescription record", "definition": "A reference to a code defined by a terminology system (SNOMED CT ).", "comment": "Codes may be defined very casually in enumerations, or code lists, up to very formal definitions such as SNOMED CT - see the HL7 v3 Core Principles for more information.  Ordering of codings is undefined and SHALL NOT be used to infer meaning. Generally, at most only one of the coding values will be labeled as UserSelected = true.", "requirements": "Allows for alternative encodings within a code system, and translations to other code systems.", "min": 1, "max": "1", "base": {"path": "CodeableConcept.coding", "min": 0, "max": "*"}, "type": [{"code": "Coding"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.1-8, C*E.10-22"}, {"identity": "rim", "map": "union(., ./translation)"}, {"identity": "orim", "map": "fhir:CodeableConcept.coding rdfs:subPropertyOf dt:CD.coding"}]}, {"id": "Composition.type.coding.id", "path": "Composition.type.coding.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.type.coding.extension", "path": "Composition.type.coding.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.type.coding.system", "path": "Composition.type.coding.system", "short": "Identity of the terminology system", "definition": "The identification of the code system that defines the meaning of the symbol in the code.", "comment": "The URI may be an OID (urn:oid:...) or a UUID (urn:uuid:...).  OIDs and UUIDs SHALL be references to the HL7 OID registry. Otherwise, the URI should come from HL7's list of FHIR defined special URIs or it should reference to some definition that establishes the system clearly and unambiguously.", "requirements": "Need to be unambiguous about the source of the definition of the symbol.", "min": 0, "max": "1", "base": {"path": "Coding.system", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "http://snomed.info/sct", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.3"}, {"identity": "rim", "map": "./codeSystem"}, {"identity": "orim", "map": "fhir:Coding.system rdfs:subPropertyOf dt:CDCoding.codeSystem"}]}, {"id": "Composition.type.coding.version", "path": "Composition.type.coding.version", "short": "Version of the system - if relevant", "definition": "The version of the code system which was used when choosing this code. Note that a well-maintained code system does not need the version reported, because the meaning of codes is consistent across versions. However this cannot consistently be assured, and when the meaning is not guaranteed to be consistent, the version SHOULD be exchanged.", "comment": "Where the terminology does not clearly define what string should be used to identify code system versions, the recommendation is to use the date (expressed in FHIR date format) on which that version was officially published as the version date.", "min": 0, "max": "1", "base": {"path": "Coding.version", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.7"}, {"identity": "rim", "map": "./codeSystemVersion"}, {"identity": "orim", "map": "fhir:Coding.version rdfs:subPropertyOf dt:CDCoding.codeSystemVersion"}]}, {"id": "Composition.type.coding.code", "path": "Composition.type.coding.code", "short": "Symbol in syntax defined by the system", "definition": "A symbol in syntax defined by the system. The symbol may be a predefined code or an expression in a syntax defined by the coding system (e.g. post-coordination).", "requirements": "Need to refer to a particular code in the system.", "min": 1, "max": "1", "base": {"path": "Coding.code", "min": 0, "max": "1"}, "type": [{"code": "code"}], "fixedCode": "*********", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.1"}, {"identity": "rim", "map": "./code"}, {"identity": "orim", "map": "fhir:Coding.code rdfs:subPropertyOf dt:CDCoding.code"}]}, {"id": "Composition.type.coding.display", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Composition.type.coding.display", "short": "Representation defined by the system", "definition": "A representation of the meaning of the code in the system, following the rules of the system.", "requirements": "Need to be able to carry a human-readable meaning of the code for readers that do not know  the system.", "min": 1, "max": "1", "base": {"path": "Coding.display", "min": 0, "max": "1"}, "type": [{"code": "string"}], "fixedString": "Prescription record", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.2 - but note this is not well followed"}, {"identity": "rim", "map": "CV.displayName"}, {"identity": "orim", "map": "fhir:Coding.display rdfs:subPropertyOf dt:CDCoding.displayName"}]}, {"id": "Composition.type.coding.userSelected", "path": "Composition.type.coding.userSelected", "short": "If this coding was chosen directly by the user", "definition": "Indicates that this coding was chosen by a user directly - e.g. off a pick list of available items (codes or displays).", "comment": "Amongst a set of alternatives, a directly chosen code is the most appropriate starting point for new translations. There is some ambiguity about what exactly 'directly chosen' implies, and trading partner agreement may be needed to clarify the use of this element and its consequences more completely.", "requirements": "This has been identified as a clinical safety criterium - that this exact system/code pair was chosen explicitly, rather than inferred by the system based on some rules or language processing.", "min": 0, "max": "1", "base": {"path": "Coding.userSelected", "min": 0, "max": "1"}, "type": [{"code": "boolean"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "Sometimes implied by being first"}, {"identity": "rim", "map": "CD.codingRationale"}, {"identity": "orim", "map": "fhir:Coding.userSelected fhir:mapsTo dt:CDCoding.codingRationale. fhir:Coding.userSelected fhir:hasMap fhir:Coding.userSelected.map. fhir:Coding.userSelected.map a fhir:Map;   fhir:target dt:CDCoding.codingRationale. fhir:Coding.userSelected\\#true a [     fhir:source \"true\";     fhir:target dt:CDCoding.codingRationale\\#O   ]"}]}, {"id": "Composition.type.text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Composition.type.text", "short": "Plain text representation of the concept", "definition": "A human language representation of the concept as seen/selected/uttered by the user who entered the data and/or which represents the intended meaning of the user.", "comment": "Very often the text is the same as a displayName of one of the codings.", "requirements": "The codes from the terminologies do not always capture the correct meaning with all the nuances of the human using them, or sometimes there is no appropriate code at all. In these cases, the text is used to capture the full meaning of the source.", "min": 0, "max": "1", "base": {"path": "CodeableConcept.text", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.9. But note many systems use C*E.2 for this"}, {"identity": "rim", "map": "./originalText[mediaType/code=\"text/plain\"]/data"}, {"identity": "orim", "map": "fhir:CodeableConcept.text rdfs:subPropertyOf dt:CD.originalText"}]}, {"id": "Composition.category", "path": "Composition.category", "short": "Categorization of Composition", "definition": "A categorization for the type of the composition - helps for indexing and searching. This may be implied by or derived from the code specified in the Composition Type.", "comment": "This is a metadata field from [XDS/MHD](http://wiki.ihe.net/index.php?title=Mobile_access_to_Health_Documents_(MHD)).", "requirements": "Helps humans to assess whether the composition is of interest when viewing an index of compositions or documents.", "min": 0, "max": "*", "base": {"path": "Composition.category", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "DocumentCategory"}], "strength": "example", "description": "High-level kind of a clinical document at a macro level.", "valueSet": "http://hl7.org/fhir/ValueSet/document-classcodes"}, "mapping": [{"identity": "workflow", "map": "Event.subject"}, {"identity": "w5", "map": "FiveWs.class"}, {"identity": "rim", "map": ".outboundRelationship[typeCode=\"COMP].target[classCode=\"LIST\", moodCode=\"EVN\"].code"}, {"identity": "cda", "map": "n/a"}, {"identity": "fhirdocumentreference", "map": "DocumenttReference.category"}]}, {"id": "Composition.subject", "path": "Composition.subject", "short": "Who and/or what the composition/Prescription record is about", "definition": "Who or what the composition is about. The composition can be about a person, (patient or healthcare practitioner), a device (e.g. a machine) or even a group of subjects (such as a document about a herd of livestock, or a set of patients that share a common exposure). \r\n\r\nWho or what the Prescription record is about. Subject will always refer to the patient in Prescription record.", "comment": "For clinical documents, this is usually the patient.", "requirements": "Essential metadata for searching for the composition. Identifies who and/or what the composition/document is about.", "min": 1, "max": "1", "base": {"path": "Composition.subject", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "mapping": [{"identity": "w5", "map": "FiveWs.subject[x]"}, {"identity": "rim", "map": ".participation[typeCode=\"SBJ\"].role[typeCode=\"PAT\"]"}, {"identity": "cda", "map": ".record<PERSON>ar<PERSON>"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.subject"}, {"identity": "w5", "map": "FiveWs.subject"}]}, {"id": "Composition.subject.id", "path": "Composition.subject.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.subject.extension", "path": "Composition.subject.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.subject.reference", "path": "Composition.subject.reference", "short": "Literal reference, Relative, internal or absolute URL", "definition": "A reference to a location at which the other resource is found. The reference may be a relative reference, in which case it is relative to the service base URL, or an absolute URL that resolves to the location where the resource is found. The reference may be version specific or not. If the reference is not to a FHIR RESTful server, then it should be assumed to be version specific. Internal fragment references (start with '#') refer to contained resources.", "comment": "Using absolute URLs provides a stable scalable approach suitable for a cloud/web context, while using relative/logical references provides a flexible approach suitable for use when trading across closed eco-system boundaries.   Absolute URLs do not need to point to a FHIR RESTful server, though this is the preferred approach. If the URL conforms to the structure \"/[type]/[id]\" then it should be assumed that the reference is to a FHIR RESTful server.", "min": 1, "max": "1", "base": {"path": "Reference.reference", "min": 0, "max": "1"}, "type": [{"code": "string"}], "condition": ["ref-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.subject.type", "path": "Composition.subject.type", "short": "Type the reference refers to (e.g. \"Patient\")", "definition": "The expected type of the target of the reference. If both Reference.type and Reference.reference are populated and Reference.reference is a FHIR URL, both SHALL be consistent.\n\nThe type is the Canonical URL of Resource Definition that is the type this reference refers to. References are URLs that are relative to http://hl7.org/fhir/StructureDefinition/ e.g. \"Patient\" is a reference to http://hl7.org/fhir/StructureDefinition/Patient. Absolute URLs are only allowed for logical models (and can only be used in references in logical models, not resources).", "comment": "This element is used to indicate the type of  the target of the reference. This may be used which ever of the other elements are populated (or not). In some cases, the type of the target may be determined by inspection of the reference (e.g. a RESTful URL) or by resolving the target of the reference; if both the type and a reference is provided, the reference SHALL resolve to a resource of the same type as that specified.", "min": 0, "max": "1", "base": {"path": "Reference.type", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "FHIRResourceTypeExt"}], "strength": "extensible", "description": "Aa resource (or, for logical models, the URI of the logical model).", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types"}, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.subject.identifier", "path": "Composition.subject.identifier", "short": "Logical reference, when literal reference is not known", "definition": "An identifier for the target resource. This is used when there is no way to reference the other resource directly, either because the entity it represents is not available through a FHIR server, or because there is no way for the author of the resource to convert a known identifier to an actual location. There is no requirement that a Reference.identifier point to something that is actually exposed as a FHIR instance, but it SHALL point to a business concept that would be expected to be exposed as a FHIR instance, and that instance would need to be of a FHIR resource type allowed by the reference.", "comment": "When an identifier is provided in place of a reference, any system processing the reference will only be able to resolve the identifier to a reference if it understands the business context in which the identifier is used. Sometimes this is global (e.g. a national identifier) but often it is not. For this reason, none of the useful mechanisms described for working with references (e.g. chaining, includes) are possible, nor should servers be expected to be able resolve the reference. Servers may accept an identifier based reference untouched, resolve it, and/or reject it - see CapabilityStatement.rest.resource.referencePolicy. \n\nWhen both an identifier and a literal reference are provided, the literal reference is preferred. Applications processing the resource are allowed - but not required - to check that the identifier matches the literal reference\n\nApplications converting a logical reference to a literal reference may choose to leave the logical reference present, or remove it.\n\nReference is intended to point to a structure that can potentially be expressed as a FHIR resource, though there is no need for it to exist as an actual FHIR resource instance - except in as much as an application wishes to actual find the target of the reference. The content referred to be the identifier must meet the logical constraints implied by any limitations on what resource types are permitted for the reference.  For example, it would not be legitimate to send the identifier for a drug prescription if the type were Reference(Observation|DiagnosticReport).  One of the use-cases for Reference.identifier is the situation where no FHIR representation exists (where the type is Reference (Any).", "min": 0, "max": "1", "base": {"path": "Reference.identifier", "min": 0, "max": "1"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": ".identifier"}]}, {"id": "Composition.subject.display", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Composition.subject.display", "short": "Text alternative for the resource", "definition": "Plain text narrative that identifies the resource in addition to the resource reference.", "comment": "This is generally not the same as the Resource.text of the referenced resource.  The purpose is to identify what's being referenced, not to fully describe it.", "min": 0, "max": "1", "base": {"path": "Reference.display", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.encounter", "path": "Composition.encounter", "short": "Context of the Composition", "definition": "Describes the clinical encounter or type of care this documentation is associated with.", "requirements": "Provides context for the composition and supports searching.", "min": 0, "max": "1", "base": {"path": "Composition.encounter", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "workflow", "map": "Event.context"}, {"identity": "w5", "map": "FiveWs.context"}, {"identity": "rim", "map": "unique(highest(./outboundRelationship[typeCode=\"SUBJ\" and isNormalActRelationship()], priorityNumber)/target[moodCode=\"EVN\" and classCode=(\"ENC\", \"PCPR\") and isNormalAct])"}, {"identity": "cda", "map": ".componentOf.encompassingEncounter"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.context.encounter"}]}, {"id": "Composition.date", "path": "Composition.date", "short": "Composition editing time", "definition": "The composition editing time, when the composition was last logically changed by the author.", "comment": "The Last Modified Date on the composition may be after the date of the document was attested without being changed.", "requirements": "dateTime is used for tracking, organizing versions and searching. Note that this is the time of *authoring*. When packaged in a document, [Bundle.timestamp](bundle-definitions.html#Bundle.timestamp) is the date of packaging.", "min": 1, "max": "1", "base": {"path": "Composition.date", "min": 1, "max": "1"}, "type": [{"code": "dateTime"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "mapping": [{"identity": "workflow", "map": "Event.occurrence[x]"}, {"identity": "w5", "map": "FiveWs.done[x]"}, {"identity": "rim", "map": ".effectiveTime[type=\"TS\"]"}, {"identity": "cda", "map": ".effectiveTime"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.content.attachment.date, DocumentReference.created"}]}, {"id": "Composition.author", "path": "Composition.author", "short": "Who and/or what authored the composition/Presciption record", "definition": "Identifies who is responsible for the information in the composition, not necessarily who typed it in.\r\n\r\nIdentifies who is responsible for the information in the presciption record, not necessarily who typed it in.", "requirements": "Identifies who is responsible for the content.", "min": 1, "max": "*", "base": {"path": "Composition.author", "min": 1, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/PractitionerRole", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient", "http://hl7.org/fhir/StructureDefinition/Device", "http://hl7.org/fhir/StructureDefinition/RelatedPerson"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "mapping": [{"identity": "workflow", "map": "Event.performer"}, {"identity": "w5", "map": "FiveWs.author"}, {"identity": "rim", "map": ".participation[typeCode=\"AUT\"].role[classCode=\"ASSIGNED\"]"}, {"identity": "cda", "map": ".author.<PERSON><PERSON><PERSON><PERSON>"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.agent"}]}, {"id": "Composition.author.id", "path": "Composition.author.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.author.extension", "path": "Composition.author.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.author.reference", "path": "Composition.author.reference", "short": "Literal reference, Relative, internal or absolute URL", "definition": "A reference to a location at which the other resource is found. The reference may be a relative reference, in which case it is relative to the service base URL, or an absolute URL that resolves to the location where the resource is found. The reference may be version specific or not. If the reference is not to a FHIR RESTful server, then it should be assumed to be version specific. Internal fragment references (start with '#') refer to contained resources.", "comment": "Using absolute URLs provides a stable scalable approach suitable for a cloud/web context, while using relative/logical references provides a flexible approach suitable for use when trading across closed eco-system boundaries.   Absolute URLs do not need to point to a FHIR RESTful server, though this is the preferred approach. If the URL conforms to the structure \"/[type]/[id]\" then it should be assumed that the reference is to a FHIR RESTful server.", "min": 1, "max": "1", "base": {"path": "Reference.reference", "min": 0, "max": "1"}, "type": [{"code": "string"}], "condition": ["ref-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.author.type", "path": "Composition.author.type", "short": "Type the reference refers to (e.g. \"Patient\")", "definition": "The expected type of the target of the reference. If both Reference.type and Reference.reference are populated and Reference.reference is a FHIR URL, both SHALL be consistent.\n\nThe type is the Canonical URL of Resource Definition that is the type this reference refers to. References are URLs that are relative to http://hl7.org/fhir/StructureDefinition/ e.g. \"Patient\" is a reference to http://hl7.org/fhir/StructureDefinition/Patient. Absolute URLs are only allowed for logical models (and can only be used in references in logical models, not resources).", "comment": "This element is used to indicate the type of  the target of the reference. This may be used which ever of the other elements are populated (or not). In some cases, the type of the target may be determined by inspection of the reference (e.g. a RESTful URL) or by resolving the target of the reference; if both the type and a reference is provided, the reference SHALL resolve to a resource of the same type as that specified.", "min": 0, "max": "1", "base": {"path": "Reference.type", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "FHIRResourceTypeExt"}], "strength": "extensible", "description": "Aa resource (or, for logical models, the URI of the logical model).", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types"}, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.author.identifier", "path": "Composition.author.identifier", "short": "Logical reference, when literal reference is not known", "definition": "An identifier for the target resource. This is used when there is no way to reference the other resource directly, either because the entity it represents is not available through a FHIR server, or because there is no way for the author of the resource to convert a known identifier to an actual location. There is no requirement that a Reference.identifier point to something that is actually exposed as a FHIR instance, but it SHALL point to a business concept that would be expected to be exposed as a FHIR instance, and that instance would need to be of a FHIR resource type allowed by the reference.", "comment": "When an identifier is provided in place of a reference, any system processing the reference will only be able to resolve the identifier to a reference if it understands the business context in which the identifier is used. Sometimes this is global (e.g. a national identifier) but often it is not. For this reason, none of the useful mechanisms described for working with references (e.g. chaining, includes) are possible, nor should servers be expected to be able resolve the reference. Servers may accept an identifier based reference untouched, resolve it, and/or reject it - see CapabilityStatement.rest.resource.referencePolicy. \n\nWhen both an identifier and a literal reference are provided, the literal reference is preferred. Applications processing the resource are allowed - but not required - to check that the identifier matches the literal reference\n\nApplications converting a logical reference to a literal reference may choose to leave the logical reference present, or remove it.\n\nReference is intended to point to a structure that can potentially be expressed as a FHIR resource, though there is no need for it to exist as an actual FHIR resource instance - except in as much as an application wishes to actual find the target of the reference. The content referred to be the identifier must meet the logical constraints implied by any limitations on what resource types are permitted for the reference.  For example, it would not be legitimate to send the identifier for a drug prescription if the type were Reference(Observation|DiagnosticReport).  One of the use-cases for Reference.identifier is the situation where no FHIR representation exists (where the type is Reference (Any).", "min": 0, "max": "1", "base": {"path": "Reference.identifier", "min": 0, "max": "1"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": ".identifier"}]}, {"id": "Composition.author.display", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Composition.author.display", "short": "Text alternative for the resource", "definition": "Plain text narrative that identifies the resource in addition to the resource reference.", "comment": "This is generally not the same as the Resource.text of the referenced resource.  The purpose is to identify what's being referenced, not to fully describe it.", "min": 0, "max": "1", "base": {"path": "Reference.display", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.title", "path": "Composition.title", "short": "Human Readable name/title", "definition": "Official human-readable label for the composition.\r\n\r\nFor this document title should be \"Prescription record\" or any equivalent translation", "comment": "For many compositions, the title is the same as the text or a display name of Composition.type (e.g. a \"consultation\" or \"progress note\"). Note that CDA does not make title mandatory, but there are no known cases where it is useful for title to be omitted, so it is mandatory here. Feedback on this requirement is welcome during the trial use period.", "min": 1, "max": "1", "base": {"path": "Composition.title", "min": 1, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "./title"}, {"identity": "cda", "map": ".title"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.description, DocumentReference.content.attachment.title"}]}, {"id": "Composition.confidentiality", "path": "Composition.confidentiality", "short": "As defined by affinity domain", "definition": "The code specifying the level of confidentiality of the Composition.", "comment": "The exact use of this element, and enforcement and issues related to highly sensitive documents are out of scope for the base specification, and delegated to implementation profiles (see security section).  This element is labeled as a modifier because highly confidential documents must not be treated as if they are not.", "min": 0, "max": "1", "base": {"path": "Composition.confidentiality", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "DocumentConfidentiality"}], "strength": "required", "description": "Codes specifying the level of confidentiality of the composition.", "valueSet": "http://terminology.hl7.org/ValueSet/v3-ConfidentialityClassification|2014-03-26"}, "mapping": [{"identity": "rim", "map": ".confidentialityCode"}, {"identity": "cda", "map": ".confidentialityCode"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.securityLabel"}]}, {"id": "Composition.attester", "path": "Composition.attester", "short": "Attests to accuracy of composition", "definition": "A participant who has attested to the accuracy of the composition/document.", "comment": "Only list each attester once.", "requirements": "Identifies responsibility for the accuracy of the composition content.", "min": 0, "max": "*", "base": {"path": "Composition.attester", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": ".participation[typeCode=\"AUTHEN\"].role[classCode=\"ASSIGNED\"]"}, {"identity": "cda", "map": ".authenticator/.legalAuthenticator"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.authenticator"}]}, {"id": "Composition.attester.id", "path": "Composition.attester.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.attester.extension", "path": "Composition.attester.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.attester.modifierExtension", "path": "Composition.attester.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.attester.mode", "path": "Composition.attester.mode", "short": "personal | professional | legal | official", "definition": "The type of attestation the authenticator offers.", "requirements": "Indicates the level of authority of the attestation.", "min": 1, "max": "1", "base": {"path": "Composition.attester.mode", "min": 1, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "CompositionAttestationMode"}], "strength": "required", "description": "The way in which a person authenticated a composition.", "valueSet": "http://hl7.org/fhir/ValueSet/composition-attestation-mode|4.0.1"}, "mapping": [{"identity": "rim", "map": "unique(./modeCode)"}, {"identity": "cda", "map": "implied by .authenticator/.legalAuthenticator"}]}, {"id": "Composition.attester.time", "path": "Composition.attester.time", "short": "When the composition was attested", "definition": "When the composition was attested by the party.", "requirements": "Identifies when the information in the composition was deemed accurate.  (Things may have changed since then.).", "min": 0, "max": "1", "base": {"path": "Composition.attester.time", "min": 0, "max": "1"}, "type": [{"code": "dateTime"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "./time[type=\"TS\" and isNormalDatatype()]"}, {"identity": "cda", "map": ".authenticator.time"}]}, {"id": "Composition.attester.party", "path": "Composition.attester.party", "short": "Who attested the composition", "definition": "Who attested the composition in the specified way.", "requirements": "Identifies who has taken on the responsibility for accuracy of the composition content.", "min": 0, "max": "1", "base": {"path": "Composition.attester.party", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/RelatedPerson", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/PractitionerRole", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "w5", "map": "FiveWs.witness"}, {"identity": "rim", "map": "./role[classCode=\"ASSIGNED\" and isNormalRole]/player[determinerCode=\"INST\" and classCode=(\"DEV\", \"PSN\") and isNormalEntity()] or ./role[classCode=\"ASSIGNED\" and isNormalRole and not(player)]/scoper[determinerCode=\"INST\" and classCode=\"ORG\" and isNormalEntity()]"}, {"identity": "cda", "map": ".authenticator.assignedEnttty"}]}, {"id": "Composition.custodian", "path": "Composition.custodian", "short": "Organization which maintains the composition", "definition": "Identifies the organization or group who is responsible for ongoing maintenance of and access to the composition/document information.", "comment": "This is useful when documents are derived from a composition - provides guidance for how to get the latest version of the document. This is optional because this is sometimes not known by the authoring system, and can be inferred by context. However, it is important that this information be known when working with a derived document, so providing a custodian is encouraged.", "requirements": "Identifies where to go to find the current version, where to report issues, etc.", "min": 0, "max": "1", "base": {"path": "Composition.custodian", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": ".participation[typeCode=\"RCV\"].role[classCode=\"CUST\"].scoper[classCode=\"ORG\" and determinerCode=\"INST\"]"}, {"identity": "cda", "map": ".custodian.assigned<PERSON><PERSON><PERSON>ian"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.custodian"}]}, {"id": "Composition.relatesTo", "path": "Composition.relatesTo", "short": "Relationships to other compositions/documents", "definition": "Relationships that this composition has with other compositions or documents that already exist.", "comment": "A document is a version specific composition.", "min": 0, "max": "*", "base": {"path": "Composition.relatesTo", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": ".outboundRelationship"}, {"identity": "cda", "map": ".relatedDocument"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.relatesTo"}]}, {"id": "Composition.relatesTo.id", "path": "Composition.relatesTo.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.relatesTo.extension", "path": "Composition.relatesTo.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.relatesTo.modifierExtension", "path": "Composition.relatesTo.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.relatesTo.code", "path": "Composition.relatesTo.code", "short": "replaces | transforms | signs | appends", "definition": "The type of relationship that this composition has with anther composition or document.", "comment": "If this document appends another document, then the document cannot be fully understood without also accessing the referenced document.", "min": 1, "max": "1", "base": {"path": "Composition.relatesTo.code", "min": 1, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "DocumentRelationshipType"}], "strength": "required", "description": "The type of relationship between documents.", "valueSet": "http://hl7.org/fhir/ValueSet/document-relationship-type|4.0.1"}, "mapping": [{"identity": "rim", "map": ".outboundRelationship.typeCode"}, {"identity": "cda", "map": ".relatedDocument.typeCode"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.relatesTo.code"}]}, {"id": "Composition.relatesTo.target[x]", "path": "Composition.relatesTo.target[x]", "short": "Target of the relationship", "definition": "The target composition/document of this relationship.", "min": 1, "max": "1", "base": {"path": "Composition.relatesTo.target[x]", "min": 1, "max": "1"}, "type": [{"code": "Identifier"}, {"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Composition"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": ".target[classCode=\"DOC\", moodCode=\"EVN\"].id"}, {"identity": "cda", "map": ".relatedDocument.id"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.relatesTo.target"}]}, {"id": "Composition.event", "path": "Composition.event", "short": "The clinical service(s) being documented", "definition": "The clinical service, such as a colonoscopy or an appendectomy, being documented.", "comment": "The event needs to be consistent with the type element, though can provide further information if desired.", "requirements": "Provides context for the composition and creates a linkage between a resource describing an event and the composition created describing the event.", "min": 0, "max": "*", "base": {"path": "Composition.event", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "outboundRelationship[typeCode=\"SUBJ\"].target[classCode<'ACT']"}, {"identity": "cda", "map": ".documentationOf.serviceEvent"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.event"}]}, {"id": "Composition.event.id", "path": "Composition.event.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.event.extension", "path": "Composition.event.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.event.modifierExtension", "path": "Composition.event.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.event.code", "path": "Composition.event.code", "short": "Code(s) that apply to the event being documented", "definition": "This list of codes represents the main clinical acts, such as a colonoscopy or an appendectomy, being documented. In some cases, the event is inherent in the typeCode, such as a \"History and Physical Report\" in which the procedure being documented is necessarily a \"History and Physical\" act.", "comment": "An event can further specialize the act inherent in the typeCode, such as where it is simply \"Procedure Report\" and the procedure was a \"colonoscopy\". If one or more eventCodes are included, they SHALL NOT conflict with the values inherent in the classCode, practiceSettingCode or typeCode, as such a conflict would create an ambiguous situation. This short list of codes is provided to be used as key words for certain types of queries.", "min": 0, "max": "*", "base": {"path": "Composition.event.code", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "DocumentEventType"}], "strength": "example", "description": "This list of codes represents the main clinical acts being documented.", "valueSet": "http://terminology.hl7.org/ValueSet/v3-ActCode"}, "mapping": [{"identity": "rim", "map": ".code"}, {"identity": "cda", "map": ".code"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.event.code"}]}, {"id": "Composition.event.period", "path": "Composition.event.period", "short": "The period covered by the documentation", "definition": "The period of time covered by the documentation. There is no assertion that the documentation is a complete representation for this period, only that it documents events during this time.", "min": 0, "max": "1", "base": {"path": "Composition.event.period", "min": 0, "max": "1"}, "type": [{"code": "Period"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": ".effectiveTime"}, {"identity": "cda", "map": ".effectiveTime"}, {"identity": "fhirdocumentreference", "map": "DocumentReference.event.period"}]}, {"id": "Composition.event.detail", "path": "Composition.event.detail", "short": "The event(s) being documented", "definition": "The description and/or reference of the event(s) being documented. For example, this could be used to document such a colonoscopy or an appendectomy.", "min": 0, "max": "*", "base": {"path": "Composition.event.detail", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Resource"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": ".outboundRelationship[typeCode=\"SUBJ\"].target"}, {"identity": "cda", "map": "n/a"}]}, {"id": "Composition.section", "path": "Composition.section", "short": "Composition is broken into sections / Prescription record contains single section to define the relevant medication requests", "definition": "The root of the sections that make up the composition.\r\n\r\nThe root of the section that make up the Prescription record. Section contains a description of the patient's medication requests relevant for the Prescription record.", "min": 1, "max": "1", "base": {"path": "Composition.section", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "cmp-1", "severity": "error", "human": "A section must contain at least one of text, entries, or sub-sections", "expression": "text.exists() or entry.exists() or section.exists()", "xpath": "exists(f:text) or exists(f:entry) or exists(f:section)", "source": "http://hl7.org/fhir/StructureDefinition/Composition"}, {"key": "cmp-2", "severity": "error", "human": "A section can only have an emptyReason if it is empty", "expression": "emptyReason.empty() or entry.empty()", "xpath": "not(exists(f:emptyReason) and exists(f:entry))", "source": "http://hl7.org/fhir/StructureDefinition/Composition"}, {"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "./outboundRelationship[typeCode=\"COMP\" and isNormalActRelationship()]/target[moodCode=\"EVN\" and classCode=\"DOCSECT\" and isNormalAct]"}, {"identity": "cda", "map": ".component.structuredBody.component.section"}]}, {"id": "Composition.section.id", "path": "Composition.section.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.extension", "path": "Composition.section.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.modifierExtension", "path": "Composition.section.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.section.title", "path": "Composition.section.title", "short": "Label for section (e.g. for ToC)", "definition": "The label for this particular section.  This will be part of the rendered content for the document, and is often used to build a table of contents.", "comment": "The title identifies the section for a human reader. The title must be consistent with the narrative of the resource that is the target of the section.content reference. Generally, sections SHOULD have titles, but in some documents, it is unnecessary or inappropriate. Typically, this is where a section has subsections that have their own adequately distinguishing title,  or documents that only have a single section. Most Implementation Guides will make section title to be a required element.", "requirements": "Section headings are often standardized for different types of documents.  They give guidance to humans on how the document is organized.", "alias": ["header", "label", "caption"], "min": 0, "max": "1", "base": {"path": "Composition.section.title", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "./title"}, {"identity": "cda", "map": ".title"}]}, {"id": "Composition.section.code", "path": "Composition.section.code", "short": "Classification of section (recommended)", "definition": "A code identifying the kind of content contained within the section. This must be consistent with the section title.", "comment": "The code identifies the section for an automated processor of the document. This is particularly relevant when using profiles to control the structure of the document.   \n\nIf the section has content (instead of sub-sections), the section.code does not change the meaning or interpretation of the resource that is the content of the section in the comments for the section.code.", "requirements": "Provides computable standardized labels to topics within the document.", "min": 0, "max": "1", "base": {"path": "Composition.section.code", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "CompositionSectionType"}], "strength": "example", "description": "Classification of a section of a composition/document.", "valueSet": "http://hl7.org/fhir/ValueSet/doc-section-codes"}, "mapping": [{"identity": "rim", "map": "./code"}, {"identity": "cda", "map": ".code"}]}, {"id": "Composition.section.code.id", "path": "Composition.section.code.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.code.extension", "path": "Composition.section.code.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.code.coding", "path": "Composition.section.code.coding", "short": "Code defined by a terminology system", "definition": "A reference to a code defined by a terminology system.", "comment": "Codes may be defined very casually in enumerations, or code lists, up to very formal definitions such as SNOMED CT - see the HL7 v3 Core Principles for more information.  Ordering of codings is undefined and SHALL NOT be used to infer meaning. Generally, at most only one of the coding values will be labeled as UserSelected = true.", "requirements": "Allows for alternative encodings within a code system, and translations to other code systems.", "min": 0, "max": "*", "base": {"path": "CodeableConcept.coding", "min": 0, "max": "*"}, "type": [{"code": "Coding"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.1-8, C*E.10-22"}, {"identity": "rim", "map": "union(., ./translation)"}, {"identity": "orim", "map": "fhir:CodeableConcept.coding rdfs:subPropertyOf dt:CD.coding"}]}, {"id": "Composition.section.code.coding.id", "path": "Composition.section.code.coding.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.code.coding.extension", "path": "Composition.section.code.coding.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.code.coding.system", "path": "Composition.section.code.coding.system", "short": "Identity of the terminology system", "definition": "The identification of the code system that defines the meaning of the symbol in the code.", "comment": "The URI may be an OID (urn:oid:...) or a UUID (urn:uuid:...).  OIDs and UUIDs SHALL be references to the HL7 OID registry. Otherwise, the URI should come from HL7's list of FHIR defined special URIs or it should reference to some definition that establishes the system clearly and unambiguously.", "requirements": "Need to be unambiguous about the source of the definition of the symbol.", "min": 1, "max": "1", "base": {"path": "Coding.system", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "http://snomed.info/sct", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.3"}, {"identity": "rim", "map": "./codeSystem"}, {"identity": "orim", "map": "fhir:Coding.system rdfs:subPropertyOf dt:CDCoding.codeSystem"}]}, {"id": "Composition.section.code.coding.version", "path": "Composition.section.code.coding.version", "short": "Version of the system - if relevant", "definition": "The version of the code system which was used when choosing this code. Note that a well-maintained code system does not need the version reported, because the meaning of codes is consistent across versions. However this cannot consistently be assured, and when the meaning is not guaranteed to be consistent, the version SHOULD be exchanged.", "comment": "Where the terminology does not clearly define what string should be used to identify code system versions, the recommendation is to use the date (expressed in FHIR date format) on which that version was officially published as the version date.", "min": 0, "max": "1", "base": {"path": "Coding.version", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.7"}, {"identity": "rim", "map": "./codeSystemVersion"}, {"identity": "orim", "map": "fhir:Coding.version rdfs:subPropertyOf dt:CDCoding.codeSystemVersion"}]}, {"id": "Composition.section.code.coding.code", "path": "Composition.section.code.coding.code", "short": "Symbol in syntax defined by the system", "definition": "A symbol in syntax defined by the system. The symbol may be a predefined code or an expression in a syntax defined by the coding system (e.g. post-coordination).", "requirements": "Need to refer to a particular code in the system.", "min": 1, "max": "1", "base": {"path": "Coding.code", "min": 0, "max": "1"}, "type": [{"code": "code"}], "fixedCode": "*********", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.1"}, {"identity": "rim", "map": "./code"}, {"identity": "orim", "map": "fhir:Coding.code rdfs:subPropertyOf dt:CDCoding.code"}]}, {"id": "Composition.section.code.coding.display", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Composition.section.code.coding.display", "short": "Representation defined by the system", "definition": "A representation of the meaning of the code in the system, following the rules of the system.", "requirements": "Need to be able to carry a human-readable meaning of the code for readers that do not know  the system.", "min": 1, "max": "1", "base": {"path": "Coding.display", "min": 0, "max": "1"}, "type": [{"code": "string"}], "fixedString": "Prescription record", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.2 - but note this is not well followed"}, {"identity": "rim", "map": "CV.displayName"}, {"identity": "orim", "map": "fhir:Coding.display rdfs:subPropertyOf dt:CDCoding.displayName"}]}, {"id": "Composition.section.code.coding.userSelected", "path": "Composition.section.code.coding.userSelected", "short": "If this coding was chosen directly by the user", "definition": "Indicates that this coding was chosen by a user directly - e.g. off a pick list of available items (codes or displays).", "comment": "Amongst a set of alternatives, a directly chosen code is the most appropriate starting point for new translations. There is some ambiguity about what exactly 'directly chosen' implies, and trading partner agreement may be needed to clarify the use of this element and its consequences more completely.", "requirements": "This has been identified as a clinical safety criterium - that this exact system/code pair was chosen explicitly, rather than inferred by the system based on some rules or language processing.", "min": 0, "max": "1", "base": {"path": "Coding.userSelected", "min": 0, "max": "1"}, "type": [{"code": "boolean"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "Sometimes implied by being first"}, {"identity": "rim", "map": "CD.codingRationale"}, {"identity": "orim", "map": "fhir:Coding.userSelected fhir:mapsTo dt:CDCoding.codingRationale. fhir:Coding.userSelected fhir:hasMap fhir:Coding.userSelected.map. fhir:Coding.userSelected.map a fhir:Map;   fhir:target dt:CDCoding.codingRationale. fhir:Coding.userSelected\\#true a [     fhir:source \"true\";     fhir:target dt:CDCoding.codingRationale\\#O   ]"}]}, {"id": "Composition.section.code.text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Composition.section.code.text", "short": "Plain text representation of the concept", "definition": "A human language representation of the concept as seen/selected/uttered by the user who entered the data and/or which represents the intended meaning of the user.", "comment": "Very often the text is the same as a displayName of one of the codings.", "requirements": "The codes from the terminologies do not always capture the correct meaning with all the nuances of the human using them, or sometimes there is no appropriate code at all. In these cases, the text is used to capture the full meaning of the source.", "min": 0, "max": "1", "base": {"path": "CodeableConcept.text", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "mapping": [{"identity": "v2", "map": "C*E.9. But note many systems use C*E.2 for this"}, {"identity": "rim", "map": "./originalText[mediaType/code=\"text/plain\"]/data"}, {"identity": "orim", "map": "fhir:CodeableConcept.text rdfs:subPropertyOf dt:CD.originalText"}]}, {"id": "Composition.section.author", "path": "Composition.section.author", "short": "Who and/or what authored the section", "definition": "Identifies who is responsible for the information in this section, not necessarily who typed it in.", "requirements": "Identifies who is responsible for the content.", "min": 0, "max": "*", "base": {"path": "Composition.section.author", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Practitioner", "http://hl7.org/fhir/StructureDefinition/PractitionerRole", "http://hl7.org/fhir/StructureDefinition/Device", "http://hl7.org/fhir/StructureDefinition/Patient", "http://hl7.org/fhir/StructureDefinition/RelatedPerson", "http://hl7.org/fhir/StructureDefinition/Organization"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "w5", "map": "FiveWs.author"}, {"identity": "rim", "map": ".participation[typeCode=\"AUT\"].role[classCode=\"ASSIGNED\"]"}, {"identity": "cda", "map": ".author.<PERSON><PERSON><PERSON><PERSON>"}]}, {"id": "Composition.section.focus", "path": "Composition.section.focus", "short": "Who/what the section is about, when it is not about the subject of composition", "definition": "The actual focus of the section when it is not the subject of the composition, but instead represents something or someone associated with the subject such as (for a patient subject) a spouse, parent, fetus, or donor. If not focus is specified, the focus is assumed to be focus of the parent section, or, for a section in the Composition itself, the subject of the composition. Sections with a focus SHALL only include resources where the logical subject (patient, subject, focus, etc.) matches the section focus, or the resources have no logical subject (few resources).", "comment": "Typically, sections in a doument are about the subject of the document, whether that is a  patient, or group of patients, location, or device, or whatever. For some kind of documents, some sections actually contain data about related entities. Typical examples are  a section in a newborn discharge summary concerning the mother, or family history documents, with a section about each family member, though there are many other examples.", "min": 0, "max": "1", "base": {"path": "Composition.section.focus", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Resource"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "cda", "map": ".subject? (CDA did not differentiate between subject and focus)"}]}, {"id": "Composition.section.text", "path": "Composition.section.text", "short": "Text summary of the section, for human interpretation", "definition": "A human-readable narrative that contains the attested content of the section, used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative.", "comment": "Document profiles may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "Composition.section.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}], "condition": ["cmp-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": ".text"}, {"identity": "cda", "map": ".text"}]}, {"id": "Composition.section.mode", "path": "Composition.section.mode", "short": "working | snapshot | changes", "definition": "How the entry list was prepared - whether it is a working list that is suitable for being maintained on an ongoing basis, or if it represents a snapshot of a list of items from another source, or whether it is a prepared list where items may be marked as added, modified or deleted.", "comment": "This element is labeled as a modifier because a change list must not be misunderstood as a complete list.", "requirements": "Sections are used in various ways, and it must be known in what way it is safe to use the entries in them.", "min": 0, "max": "1", "base": {"path": "Composition.section.mode", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "SectionMode"}], "strength": "required", "description": "The processing mode that applies to this section.", "valueSet": "http://hl7.org/fhir/ValueSet/list-mode|4.0.1"}, "mapping": [{"identity": "w5", "map": "FiveWs.class"}, {"identity": "rim", "map": ".outBoundRelationship[typeCode=COMP].target[classCode=OBS\"].value"}, {"identity": "cda", "map": "n/a"}]}, {"id": "Composition.section.orderedBy", "path": "Composition.section.orderedBy", "short": "Order of section entries", "definition": "Specifies the order applied to the items in the section entries.", "comment": "Applications SHOULD render ordered lists in the order provided, but MAY allow users to re-order based on their own preferences as well. If there is no order specified, the order is unknown, though there may still be some order.", "requirements": "Important for presentation and rendering.  Lists may be sorted to place more important information first or to group related entries.", "min": 0, "max": "1", "base": {"path": "Composition.section.orderedBy", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "SectionEntryOrder"}], "strength": "preferred", "description": "What order applies to the items in the entry.", "valueSet": "http://hl7.org/fhir/ValueSet/list-order"}, "mapping": [{"identity": "rim", "map": ".outboundRelationship[typeCode=COMP].sequenceNumber > 1"}, {"identity": "cda", "map": "n/a"}]}, {"id": "Composition.section.entry", "path": "Composition.section.entry", "slicing": {"discriminator": [{"type": "value", "path": "type"}], "rules": "closed"}, "short": "A reference to data that supports this section", "definition": "A reference to the actual resource from which the narrative in the section is derived.\r\n\r\nA list of references to the acutal resource defining the medication requests in prescription record.", "comment": "Only two types of entries are allowed, i.e. MedicationRequest and Binary.", "min": 1, "max": "*", "base": {"path": "Composition.section.entry", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Binary"]}], "condition": ["cmp-2"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": ".outboundRelationship[typeCode=COMP] or  .participation[typeCode=SBJ]"}, {"identity": "cda", "map": ".entry"}]}, {"id": "Composition.section.entry:MedicationRequest", "path": "Composition.section.entry", "sliceName": "MedicationRequest", "short": "A reference to data that supports this section", "definition": "A reference to the actual resource from which the narrative in the section is derived.\r\n\r\nA list of references to the acutal resource defining the medication requests in prescription record.", "comment": "If there are no entries in the list, an emptyReason SHOULD be provided.", "min": 0, "max": "*", "base": {"path": "Composition.section.entry", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]}], "condition": ["cmp-2"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": ".outboundRelationship[typeCode=COMP] or  .participation[typeCode=SBJ]"}, {"identity": "cda", "map": ".entry"}]}, {"id": "Composition.section.entry:MedicationRequest.id", "path": "Composition.section.entry.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.entry:MedicationRequest.extension", "path": "Composition.section.entry.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.entry:MedicationRequest.reference", "path": "Composition.section.entry.reference", "short": "Literal reference, Relative, internal or absolute URL", "definition": "A reference to a location at which the other resource is found. The reference may be a relative reference, in which case it is relative to the service base URL, or an absolute URL that resolves to the location where the resource is found. The reference may be version specific or not. If the reference is not to a FHIR RESTful server, then it should be assumed to be version specific. Internal fragment references (start with '#') refer to contained resources.", "comment": "Using absolute URLs provides a stable scalable approach suitable for a cloud/web context, while using relative/logical references provides a flexible approach suitable for use when trading across closed eco-system boundaries.   Absolute URLs do not need to point to a FHIR RESTful server, though this is the preferred approach. If the URL conforms to the structure \"/[type]/[id]\" then it should be assumed that the reference is to a FHIR RESTful server.", "min": 1, "max": "1", "base": {"path": "Reference.reference", "min": 0, "max": "1"}, "type": [{"code": "string"}], "condition": ["ref-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.section.entry:MedicationRequest.type", "path": "Composition.section.entry.type", "short": "Type the reference refers to (e.g. \"Patient\")", "definition": "The expected type of the target of the reference. If both Reference.type and Reference.reference are populated and Reference.reference is a FHIR URL, both SHALL be consistent.\n\nThe type is the Canonical URL of Resource Definition that is the type this reference refers to. References are URLs that are relative to http://hl7.org/fhir/StructureDefinition/ e.g. \"Patient\" is a reference to http://hl7.org/fhir/StructureDefinition/Patient. Absolute URLs are only allowed for logical models (and can only be used in references in logical models, not resources).", "comment": "This element is used to indicate the type of  the target of the reference. This may be used which ever of the other elements are populated (or not). In some cases, the type of the target may be determined by inspection of the reference (e.g. a RESTful URL) or by resolving the target of the reference; if both the type and a reference is provided, the reference SHALL resolve to a resource of the same type as that specified.", "min": 1, "max": "1", "base": {"path": "Reference.type", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "MedicationRequest", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "FHIRResourceTypeExt"}], "strength": "extensible", "description": "Aa resource (or, for logical models, the URI of the logical model).", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types"}, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.section.entry:MedicationRequest.identifier", "path": "Composition.section.entry.identifier", "short": "Logical reference, when literal reference is not known", "definition": "An identifier for the target resource. This is used when there is no way to reference the other resource directly, either because the entity it represents is not available through a FHIR server, or because there is no way for the author of the resource to convert a known identifier to an actual location. There is no requirement that a Reference.identifier point to something that is actually exposed as a FHIR instance, but it SHALL point to a business concept that would be expected to be exposed as a FHIR instance, and that instance would need to be of a FHIR resource type allowed by the reference.", "comment": "When an identifier is provided in place of a reference, any system processing the reference will only be able to resolve the identifier to a reference if it understands the business context in which the identifier is used. Sometimes this is global (e.g. a national identifier) but often it is not. For this reason, none of the useful mechanisms described for working with references (e.g. chaining, includes) are possible, nor should servers be expected to be able resolve the reference. Servers may accept an identifier based reference untouched, resolve it, and/or reject it - see CapabilityStatement.rest.resource.referencePolicy. \n\nWhen both an identifier and a literal reference are provided, the literal reference is preferred. Applications processing the resource are allowed - but not required - to check that the identifier matches the literal reference\n\nApplications converting a logical reference to a literal reference may choose to leave the logical reference present, or remove it.\n\nReference is intended to point to a structure that can potentially be expressed as a FHIR resource, though there is no need for it to exist as an actual FHIR resource instance - except in as much as an application wishes to actual find the target of the reference. The content referred to be the identifier must meet the logical constraints implied by any limitations on what resource types are permitted for the reference.  For example, it would not be legitimate to send the identifier for a drug prescription if the type were Reference(Observation|DiagnosticReport).  One of the use-cases for Reference.identifier is the situation where no FHIR representation exists (where the type is Reference (Any).", "min": 0, "max": "1", "base": {"path": "Reference.identifier", "min": 0, "max": "1"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": ".identifier"}]}, {"id": "Composition.section.entry:MedicationRequest.display", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Composition.section.entry.display", "short": "Text alternative for the resource", "definition": "Plain text narrative that identifies the resource in addition to the resource reference.", "comment": "This is generally not the same as the Resource.text of the referenced resource.  The purpose is to identify what's being referenced, not to fully describe it.", "min": 0, "max": "1", "base": {"path": "Reference.display", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.section.entry:Binary", "path": "Composition.section.entry", "sliceName": "Binary", "short": "A reference to data that supports this section", "definition": "A reference to the actual resource from which the narrative in the section is derived.\r\n\r\nA reference to the actual resource of binary to provide the document of prescription record", "comment": "If there are no entries in the list, an emptyReason SHOULD be provided.", "min": 0, "max": "1", "base": {"path": "Composition.section.entry", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Binary"]}], "condition": ["cmp-2"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": ".outboundRelationship[typeCode=COMP] or  .participation[typeCode=SBJ]"}, {"identity": "cda", "map": ".entry"}]}, {"id": "Composition.section.entry:Binary.id", "path": "Composition.section.entry.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.entry:Binary.extension", "path": "Composition.section.entry.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "Composition.section.entry:Binary.reference", "path": "Composition.section.entry.reference", "short": "Literal reference, Relative, internal or absolute URL", "definition": "A reference to a location at which the other resource is found. The reference may be a relative reference, in which case it is relative to the service base URL, or an absolute URL that resolves to the location where the resource is found. The reference may be version specific or not. If the reference is not to a FHIR RESTful server, then it should be assumed to be version specific. Internal fragment references (start with '#') refer to contained resources.", "comment": "Using absolute URLs provides a stable scalable approach suitable for a cloud/web context, while using relative/logical references provides a flexible approach suitable for use when trading across closed eco-system boundaries.   Absolute URLs do not need to point to a FHIR RESTful server, though this is the preferred approach. If the URL conforms to the structure \"/[type]/[id]\" then it should be assumed that the reference is to a FHIR RESTful server.", "min": 1, "max": "1", "base": {"path": "Reference.reference", "min": 0, "max": "1"}, "type": [{"code": "string"}], "condition": ["ref-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.section.entry:Binary.type", "path": "Composition.section.entry.type", "short": "Type the reference refers to (e.g. \"Patient\")", "definition": "The expected type of the target of the reference. If both Reference.type and Reference.reference are populated and Reference.reference is a FHIR URL, both SHALL be consistent.\n\nThe type is the Canonical URL of Resource Definition that is the type this reference refers to. References are URLs that are relative to http://hl7.org/fhir/StructureDefinition/ e.g. \"Patient\" is a reference to http://hl7.org/fhir/StructureDefinition/Patient. Absolute URLs are only allowed for logical models (and can only be used in references in logical models, not resources).", "comment": "This element is used to indicate the type of  the target of the reference. This may be used which ever of the other elements are populated (or not). In some cases, the type of the target may be determined by inspection of the reference (e.g. a RESTful URL) or by resolving the target of the reference; if both the type and a reference is provided, the reference SHALL resolve to a resource of the same type as that specified.", "min": 1, "max": "1", "base": {"path": "Reference.type", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "Binary", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "FHIRResourceTypeExt"}], "strength": "extensible", "description": "Aa resource (or, for logical models, the URI of the logical model).", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types"}, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.section.entry:Binary.identifier", "path": "Composition.section.entry.identifier", "short": "Logical reference, when literal reference is not known", "definition": "An identifier for the target resource. This is used when there is no way to reference the other resource directly, either because the entity it represents is not available through a FHIR server, or because there is no way for the author of the resource to convert a known identifier to an actual location. There is no requirement that a Reference.identifier point to something that is actually exposed as a FHIR instance, but it SHALL point to a business concept that would be expected to be exposed as a FHIR instance, and that instance would need to be of a FHIR resource type allowed by the reference.", "comment": "When an identifier is provided in place of a reference, any system processing the reference will only be able to resolve the identifier to a reference if it understands the business context in which the identifier is used. Sometimes this is global (e.g. a national identifier) but often it is not. For this reason, none of the useful mechanisms described for working with references (e.g. chaining, includes) are possible, nor should servers be expected to be able resolve the reference. Servers may accept an identifier based reference untouched, resolve it, and/or reject it - see CapabilityStatement.rest.resource.referencePolicy. \n\nWhen both an identifier and a literal reference are provided, the literal reference is preferred. Applications processing the resource are allowed - but not required - to check that the identifier matches the literal reference\n\nApplications converting a logical reference to a literal reference may choose to leave the logical reference present, or remove it.\n\nReference is intended to point to a structure that can potentially be expressed as a FHIR resource, though there is no need for it to exist as an actual FHIR resource instance - except in as much as an application wishes to actual find the target of the reference. The content referred to be the identifier must meet the logical constraints implied by any limitations on what resource types are permitted for the reference.  For example, it would not be legitimate to send the identifier for a drug prescription if the type were Reference(Observation|DiagnosticReport).  One of the use-cases for Reference.identifier is the situation where no FHIR representation exists (where the type is Reference (Any).", "min": 0, "max": "1", "base": {"path": "Reference.identifier", "min": 0, "max": "1"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": ".identifier"}]}, {"id": "Composition.section.entry:Binary.display", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Composition.section.entry.display", "short": "Text alternative for the resource", "definition": "Plain text narrative that identifies the resource in addition to the resource reference.", "comment": "This is generally not the same as the Resource.text of the referenced resource.  The purpose is to identify what's being referenced, not to fully describe it.", "min": 0, "max": "1", "base": {"path": "Reference.display", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "Composition.section.emptyReason", "path": "Composition.section.emptyReason", "short": "Why the section is empty", "definition": "If the section is empty, why the list is empty. An empty section typically has some text explaining the empty reason.", "comment": "The various reasons for an empty section make a significant interpretation to its interpretation. Note that this code is for use when the entire section content has been suppressed, and not for when individual items are omitted - implementers may consider using a text note or a flag on an entry in these cases.", "requirements": "Allows capturing things like \"none exist\" or \"not asked\" which can be important for most lists.", "min": 0, "max": "1", "base": {"path": "Composition.section.emptyReason", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "condition": ["cmp-2"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "SectionEmptyReason"}], "strength": "preferred", "description": "If a section is empty, why it is empty.", "valueSet": "http://hl7.org/fhir/ValueSet/list-empty-reason"}, "mapping": [{"identity": "rim", "map": ".inboundRelationship[typeCode=SUBJ,code<ListEmptyReason].value[type=CD]"}, {"identity": "cda", "map": "n/a"}]}, {"id": "Composition.section.section", "path": "Composition.section.section", "short": "Nested Section", "definition": "A nested sub-section within this section.", "comment": "Nested sections are primarily used to help human readers navigate to particular portions of the document.", "min": 0, "max": "*", "base": {"path": "Composition.section.section", "min": 0, "max": "*"}, "contentReference": "http://hl7.org/fhir/StructureDefinition/Composition#Composition.section", "condition": ["cmp-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "./outboundRelationship[typeCode=\"COMP\" and isNormalActRelationship()]/target[moodCode=\"EVN\" and classCode=\"DOCSECT\" and isNormalAct]"}, {"identity": "cda", "map": ".component.section"}]}]}, "differential": {"element": [{"id": "Composition", "path": "Composition", "short": "A set of resources composed into a single coherent clinical statement with clinical attestation / A set of resources composed to define the prescription record.", "definition": "A set of healthcare-related information that is assembled together into a single logical package that provides a single coherent statement of meaning, establishes its own context and that has clinical attestation with regard to who is making the statement. A Composition defines the structure and narrative content necessary for a document. However, a Composition alone does not constitute a document. Rather, the Composition must be the first entry in a Bundle where Bundle.type=document, and any other resources referenced from Composition must be included as subsequent entries in the Bundle (for example Patient, Practitioner, Encounter, etc.).\r\n\r\nA set of healthcare-related information that is assembled together into a Composition to represent a physician's order for the preparation and administration of a drug or device for a patient. A Composition defines the structure, it does not actually contain the content: rather the full content of a document (for example Patient, Practitioner, Organization, MedicationRequest, etc.) is contained in a Bundle, of which the Composition is the first resource contained.", "comment": "While the focus of this specification is on patient-specific clinical statements, this resource can also apply to other healthcare-related statements such as study protocol designs, healthcare invoices and other activities that are not necessarily patient-specific or clinical.\r\n\r\nThe composition refers to the some ABDM profiles."}, {"id": "Composition.status", "path": "Composition.status", "mustSupport": true}, {"id": "Composition.type", "path": "Composition.type", "short": "Kind of composition (\"Prescription record \")", "definition": "Specifies the particular kind of composition (e.g. History and Physical, Discharge Summary, Progress Note). This usually equates to the purpose of making the composition.\r\n\r\nSpecifies that this composition refer to a Prescription record (SNOMED CT \"*********\")", "mustSupport": true}, {"id": "Composition.type.coding", "path": "Composition.type.coding", "short": "Code defined by a terminology system/ Code defined by SNOMED CT for Prescription record", "definition": "A reference to a code defined by a terminology system (SNOMED CT ).", "min": 1, "max": "1"}, {"id": "Composition.type.coding.system", "path": "Composition.type.coding.system", "fixedUri": "http://snomed.info/sct"}, {"id": "Composition.type.coding.code", "path": "Composition.type.coding.code", "min": 1, "fixedCode": "*********"}, {"id": "Composition.type.coding.display", "path": "Composition.type.coding.display", "min": 1, "fixedString": "Prescription record"}, {"id": "Composition.type.text", "path": "Composition.type.text", "mustSupport": true}, {"id": "Composition.subject", "path": "Composition.subject", "short": "Who and/or what the composition/Prescription record is about", "definition": "Who or what the composition is about. The composition can be about a person, (patient or healthcare practitioner), a device (e.g. a machine) or even a group of subjects (such as a document about a herd of livestock, or a set of patients that share a common exposure). \r\n\r\nWho or what the Prescription record is about. Subject will always refer to the patient in Prescription record.", "min": 1, "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"]}], "mustSupport": true}, {"id": "Composition.subject.reference", "path": "Composition.subject.reference", "min": 1}, {"id": "Composition.encounter", "path": "Composition.encounter", "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"]}]}, {"id": "Composition.date", "path": "Composition.date", "mustSupport": true}, {"id": "Composition.author", "path": "Composition.author", "short": "Who and/or what authored the composition/Presciption record", "definition": "Identifies who is responsible for the information in the composition, not necessarily who typed it in.\r\n\r\nIdentifies who is responsible for the information in the presciption record, not necessarily who typed it in.", "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/PractitionerRole", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient", "http://hl7.org/fhir/StructureDefinition/Device", "http://hl7.org/fhir/StructureDefinition/RelatedPerson"]}], "mustSupport": true}, {"id": "Composition.author.reference", "path": "Composition.author.reference", "min": 1}, {"id": "Composition.title", "path": "Composition.title", "definition": "Official human-readable label for the composition.\r\n\r\nFor this document title should be \"Prescription record\" or any equivalent translation", "mustSupport": true}, {"id": "Composition.attester", "path": "Composition.attester", "mustSupport": true}, {"id": "Composition.attester.party", "path": "Composition.attester.party", "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/RelatedPerson", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/PractitionerRole", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]}]}, {"id": "Composition.custodian", "path": "Composition.custodian", "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]}]}, {"id": "Composition.section", "path": "Composition.section", "short": "Composition is broken into sections / Prescription record contains single section to define the relevant medication requests", "definition": "The root of the sections that make up the composition.\r\n\r\nThe root of the section that make up the Prescription record. Section contains a description of the patient's medication requests relevant for the Prescription record.", "min": 1, "max": "1", "mustSupport": true}, {"id": "Composition.section.code.coding", "path": "Composition.section.code.coding", "mustSupport": true}, {"id": "Composition.section.code.coding.system", "path": "Composition.section.code.coding.system", "min": 1, "fixedUri": "http://snomed.info/sct"}, {"id": "Composition.section.code.coding.code", "path": "Composition.section.code.coding.code", "min": 1, "fixedCode": "*********"}, {"id": "Composition.section.code.coding.display", "path": "Composition.section.code.coding.display", "min": 1, "fixedString": "Prescription record"}, {"id": "Composition.section.code.text", "path": "Composition.section.code.text", "mustSupport": true}, {"id": "Composition.section.entry", "path": "Composition.section.entry", "slicing": {"discriminator": [{"type": "value", "path": "type"}], "rules": "closed"}, "definition": "A reference to the actual resource from which the narrative in the section is derived.\r\n\r\nA list of references to the acutal resource defining the medication requests in prescription record.", "comment": "Only two types of entries are allowed, i.e. MedicationRequest and Binary.", "min": 1, "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest", "https://nrces.in/ndhm/fhir/r4/StructureDefinition/Binary"]}], "mustSupport": true}, {"id": "Composition.section.entry:MedicationRequest", "path": "Composition.section.entry", "sliceName": "MedicationRequest", "definition": "A reference to the actual resource from which the narrative in the section is derived.\r\n\r\nA list of references to the acutal resource defining the medication requests in prescription record.", "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]}], "mustSupport": true}, {"id": "Composition.section.entry:MedicationRequest.reference", "path": "Composition.section.entry.reference", "min": 1}, {"id": "Composition.section.entry:MedicationRequest.type", "path": "Composition.section.entry.type", "min": 1, "fixedUri": "MedicationRequest"}, {"id": "Composition.section.entry:Binary", "path": "Composition.section.entry", "sliceName": "Binary", "definition": "A reference to the actual resource from which the narrative in the section is derived.\r\n\r\nA reference to the actual resource of binary to provide the document of prescription record", "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Binary"]}]}, {"id": "Composition.section.entry:Binary.reference", "path": "Composition.section.entry.reference", "min": 1}, {"id": "Composition.section.entry:Binary.type", "path": "Composition.section.entry.type", "min": 1, "fixedUri": "Binary"}]}}