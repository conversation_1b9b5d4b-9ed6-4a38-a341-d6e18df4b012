import { NextRequest, NextResponse } from "next/server";

/**
 * Middleware to capture all webhook requests and store them in the hiuConsentNotify table
 * This allows us to display all webhook callbacks in a unified interface
 */
export async function webhookCaptureMiddleware(
  req: NextRequest,
  res: NextResponse,
) {
  try {
    // Check if this is a webhook request
    const url = req.nextUrl.pathname;
    if (!url.includes("/api/webhook/")) {
      return res;
    }

    // Clone the request to read the body
    const clonedReq = req.clone();

    // Get the request ID from headers
    const requestId = req.headers.get("request-id") || `req-${Date.now()}`;

    // Extract webhook type from URL
    const webhookType = getWebhookTypeFromUrl(url);

    // Parse the request body
    let payload;
    try {
      payload = await clonedReq.json();
    } catch (error) {
      console.error("Error parsing webhook payload:", error);
      payload = { error: "Failed to parse payload" };
    }

    // Extract headers
    const headers: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      headers[key] = value;
    });

    // Instead of storing in the database directly from middleware (which can cause issues),
    // we'll make a request to an API endpoint that will handle the storage
    try {
      await fetch(`${req.nextUrl.origin}/api/webhook-notify/capture`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          requestId,
          webhookType,
          payload,
          headers,
        }),
      });
    } catch (fetchError) {
      console.error("Error sending webhook data to capture API:", fetchError);
    }

    console.log(`Captured webhook: ${webhookType} (${requestId})`);

    return res;
  } catch (error) {
    console.error("Error in webhook capture middleware:", error);
    return res;
  }
}

/**
 * Extract webhook type from URL
 */
function getWebhookTypeFromUrl(url: string): string {
  // Extract the webhook type from the URL
  if (url.includes("/consent/request/hip/notify")) {
    return "CONSENT_HIP_NOTIFY";
  } else if (url.includes("/consent/request/on-init")) {
    return "CONSENT_INIT";
  } else if (url.includes("/consent/request/notify")) {
    return "CONSENT_NOTIFY";
  } else if (url.includes("/health-information/request")) {
    return "HEALTH_INFO_REQUEST";
  } else if (url.includes("/health-information/notify")) {
    return "HEALTH_INFO_NOTIFY";
  } else if (url.includes("/token/on-generate-token")) {
    return "TOKEN_GENERATE";
  } else if (url.includes("/token/generate-token-notify")) {
    return "TOKEN_NOTIFY";
  } else if (url.includes("/care-context/on-notify")) {
    return "CARE_CONTEXT_NOTIFY";
  } else if (url.includes("/uil-otp/notify")) {
    return "UIL_OTP_NOTIFY";
  } else {
    return "UNKNOWN";
  }
}
