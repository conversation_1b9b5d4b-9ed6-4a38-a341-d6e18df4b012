import { NextRequest, NextResponse } from "next/server";

/**
 * Middleware to check organization status and redirect to deactivation page if needed
 */
export async function organizationStatusMiddleware(request: NextRequest) {
  try {
    // Get user role and organization context from cookies
    const userInfoCookie = request.cookies.get("user-info");
    const orgContextCookie = request.cookies.get("org-context");
    const currentRoleCookie = request.cookies.get("current-role");

    // Check if user is super admin - they should bypass organization status checks
    let userRole = null;
    if (currentRoleCookie) {
      userRole = currentRoleCookie.value;
    } else if (userInfoCookie) {
      try {
        const userInfo = JSON.parse(decodeURIComponent(userInfoCookie.value));
        userRole = userInfo.role;
      } catch (error) {
        console.error("Error parsing user info cookie:", error);
      }
    }

    // Super admins bypass organization status checks
    if (userRole === "superAdmin") {
      return null;
    }

    if (!userInfoCookie && !orgContextCookie) {
      // No organization context, let the request proceed
      return null;
    }

    let organizationId: string | null = null;

    // Try to get organization ID from cookies
    if (orgContextCookie) {
      try {
        const orgContext = JSON.parse(orgContextCookie.value);
        organizationId = orgContext.organizationId;
      } catch (error) {
        console.error("Error parsing org-context cookie:", error);
      }
    }

    if (!organizationId && userInfoCookie) {
      try {
        const userInfo = JSON.parse(decodeURIComponent(userInfoCookie.value));
        organizationId = userInfo.organizationId;
      } catch (error) {
        console.error("Error parsing user-info cookie:", error);
      }
    }

    if (!organizationId) {
      // No organization ID found, let the request proceed
      return null;
    }

    // Check organization status via API route
    try {
      const response = await fetch(
        `${request.nextUrl.origin}/api/organization/status?orgId=${organizationId}`,
        {
          headers: {
            cookie: request.headers.get("cookie") || "",
          },
        },
      );

      if (response.ok) {
        const organization = await response.json();

        // If organization is inactive, redirect to deactivation page
        if (organization.status === "inactive") {
          const url = request.nextUrl.clone();
          url.pathname = "/organization-deactivated";
          url.searchParams.set("orgId", organization.id);
          url.searchParams.set("orgName", organization.name);
          if (organization.logo) {
            url.searchParams.set("orgLogo", organization.logo);
          }
          return NextResponse.redirect(url);
        }
      }
    } catch (apiError) {
      console.error("Error checking organization status via API:", apiError);
      // On API error, let the request proceed to avoid breaking the app
    }

    // Organization is active, let the request proceed
    return null;
  } catch (error) {
    console.error("Error in organization status middleware:", error);
    // On error, let the request proceed to avoid breaking the app
    return null;
  }
}

/**
 * Helper function to check if an organization is active
 * Note: This function is kept for backward compatibility but should be used sparingly
 * as it makes HTTP requests. Consider using the API route directly in your components.
 */
export async function isOrganizationActive(
  organizationId: string,
): Promise<boolean> {
  try {
    // Use the API route to check organization status
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3005"}/api/organization/status?orgId=${organizationId}`,
    );

    if (response.ok) {
      const organization = await response.json();
      return organization?.status === "active";
    }

    // Default to true to avoid breaking functionality
    return true;
  } catch (error) {
    console.error("Error checking organization status:", error);
    // Default to true to avoid breaking functionality
    return true;
  }
}

/**
 * Helper function to get organization status with details
 * Note: This function is kept for backward compatibility but should be used sparingly
 * as it makes HTTP requests. Consider using the API route directly in your components.
 */
export async function getOrganizationStatus(organizationId: string) {
  try {
    // Use the API route to get organization status
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3005"}/api/organization/status?orgId=${organizationId}`,
    );

    if (response.ok) {
      const organization = await response.json();
      return organization;
    }

    return null;
  } catch (error) {
    console.error("Error getting organization status:", error);
    return null;
  }
}
