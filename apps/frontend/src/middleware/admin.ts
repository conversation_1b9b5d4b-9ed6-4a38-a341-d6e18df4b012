import { NextRequest, NextResponse } from "next/server";

/**
 * Middleware to protect admin routes
 * Only allows users with superAdmin role to access /admin routes
 */
export function adminMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Only apply to admin routes
  if (!pathname.startsWith("/admin")) {
    return NextResponse.next();
  }

  // Check for user role in cookies
  const userInfoCookie = request.cookies.get("user-info");
  const currentRoleCookie = request.cookies.get("current-role");

  let userRole = null;

  // Try to get role from current-role cookie first
  if (currentRoleCookie) {
    userRole = currentRoleCookie.value;
  } else if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie.value));
      userRole = userInfo.role;
    } catch (error) {
      console.error("Error parsing user info cookie:", error);
    }
  }

  // Check if user has superAdmin role
  if (userRole !== "superAdmin") {
    // Redirect to main dashboard if not super admin
    const url = request.nextUrl.clone();
    url.pathname = "/dashboard";
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}
