/**
 * Utility functions for consent management
 */

/**
 * Check if a consent is expired based on dataEraseAt only
 * The consent should only expire when the data erase date is reached
 */
export function isConsentExpired(consent: any): boolean {
  if (!consent) return false;

  const now = new Date();

  // Parse permission data
  const permission =
    typeof consent.permission === "string"
      ? JSON.parse(consent.permission)
      : consent.permission;

  if (!permission) return false;

  // Check if dataEraseAt is in the past
  // This is the ONLY condition that should cause consent expiration
  if (permission.dataEraseAt) {
    const dataEraseDate = new Date(permission.dataEraseAt);
    if (dataEraseDate < now) {
      return true;
    }
  }

  // Note: dateRange.to represents the end of the data access period
  // but does NOT affect consent validity - only dataEraseAt does

  return false;
}

/**
 * Get the effective status of a consent, considering expiration
 */
export function getEffectiveConsentStatus(consent: any): string {
  if (!consent) return "UNKNOWN";

  // If already marked as expired, return that
  if (consent.status === "EXPIRED") {
    return "EXPIRED";
  }

  // If consent is granted but expired based on dates, it should be treated as expired
  if (consent.status === "GRANTED" && isConsentExpired(consent)) {
    return "EXPIRED";
  }

  return consent.status;
}

/**
 * Check if consent data should be accessible
 */
export function isConsentDataAccessible(consent: any): boolean {
  const effectiveStatus = getEffectiveConsentStatus(consent);
  return effectiveStatus === "GRANTED";
}
