/**
 * ActivityLog type definition
 */

export interface ActivityLog {
  id: string;
  title: string;
  type: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: {
    id: string;
    name: string;
  };
  updatedBy?: {
    id: string;
    name: string;
  };
  user?: {
    id: string;
    name: string;
    email?: string;
    image?: string;
  };
  entityType?: string;
  lead?: {
    id: string;
    name?: string;
    email?: string;
    company?: string;
  };
  contact?: {
    id: string;
    name?: string;
    email?: string;
    company?: string;
  };
  account?: {
    id: string;
    name?: string;
    industry?: string;
    website?: string;
  };
  opportunity?: {
    id: string;
    name?: string;
    value?: number;
    stage?: string;
  };
}
