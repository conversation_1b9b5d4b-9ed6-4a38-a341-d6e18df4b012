/**
 * Types for ABDM consent notification webhook
 */

export interface ConsentNotification {
  status: string;
  consentId: string;
  consentDetail: ConsentDetail;
  signature: string;
  grantAcknowledgement: boolean;
}

export interface ConsentDetail {
  schemaVersion: string;
  consentId: string;
  createdAt: string;
  patient: {
    id: string;
  };
  careContexts: CareContext[];
  purpose: {
    text: string;
    code: string;
    refUri: string;
  };
  hip: {
    id: string;
  };
  consentManager: {
    id: string;
  };
  hiTypes: string[];
  permission: {
    accessMode: string;
    dateRange: {
      from: string;
      to: string;
    };
    dataEraseAt: string;
    frequency: {
      unit: string;
      value: number;
      repeats: number;
    };
  };
}

export interface CareContext {
  patientReference: string;
  careContextReference: string;
}
