/**
 * Types for ABDM health information request webhook
 */

export interface HiRequestPayload {
  transactionId: string;
  hiRequest: HiRequest;
}

export interface HiRequest {
  consent: {
    id: string;
  };
  dateRange: {
    from: string;
    to: string;
  };
  dataPushUrl: string;
  keyMaterial: {
    cryptoAlg: string;
    curve: string;
    dhPublicKey: {
      expiry: string;
      parameters: string;
      keyValue: string;
    };
    nonce: string;
  };
}
