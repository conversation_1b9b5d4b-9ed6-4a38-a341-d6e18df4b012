import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { isDatabaseConnected } from "./lib/db";

// This middleware runs before API routes
export async function middleware(request: NextRequest) {
  // Only run this middleware for API routes
  if (!request.nextUrl.pathname?.startsWith("/api")) {
    return NextResponse.next();
  }

  // Check if the database is connected
  const isConnected = await isDatabaseConnected();
  if (!isConnected) {
    console.error("Database connection error in middleware");
    return NextResponse.json(
      { error: "Database connection error" },
      { status: 500 },
    );
  }

  return NextResponse.next();
}

// Configure the middleware to run only for API routes
export const config = {
  matcher: "/api/:path*",
};
