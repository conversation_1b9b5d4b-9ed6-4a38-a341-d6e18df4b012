import * as React from "react";
import { MainContainer } from "@/components/email/main-container";
import { But<PERSON> } from "@/components/email/button";
import { Heading, Paragraph } from "@/components/email/text";

interface VerificationEmailProps {
  hospitalName: string;
  adminName: string;
  verificationUrl: string;
}

export default function VerificationEmail({
  hospitalName,
  adminName,
  verificationUrl,
}: VerificationEmailProps) {
  return (
    <MainContainer>
      <Heading>Verify Your Email</Heading>

      <Paragraph>Hello {adminName},</Paragraph>

      <Paragraph>
        Thank you for registering {hospitalName} with Aran Care HIMS. To
        complete your registration and access your hospital dashboard, please
        verify your email address by clicking the button below:
      </Paragraph>

      <Button href={verificationUrl} label="Verify Email" />

      <Paragraph>
        If the button doesn't work, you can also copy and paste this link into
        your browser:{" "}
        <a
          href={verificationUrl}
          target="_blank"
          rel="noopener noreferrer"
          style={{ color: "#4f46e5", textDecoration: "underline" }}
        >
          {verificationUrl}
        </a>
      </Paragraph>

      <Paragraph>
        This link will expire in 24 hours for security reasons. If you did not
        create an account with Aran Care HIMS, please ignore this email.
      </Paragraph>

      <Paragraph>
        If you have any questions or need assistance, please contact our support
        team.
      </Paragraph>
    </MainContainer>
  );
}
