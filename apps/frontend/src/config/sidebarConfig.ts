"use client";

import {
  HomeIcon,
  UsersIcon,
  SettingsIcon,
  UserIcon,
  CalendarIcon,
  BuildingIcon,
  FolderIcon,
  TagIcon,
  StethoscopeIcon,
  UserCogIcon,
  ClockIcon,
  UserPlusIcon,
  ListIcon,
  FileText,
  Activity,
  Pill,
  ShieldIcon,
} from "lucide-react";
// Define all possible user roles
export type UserRole =
  | "admin"
  | "hospitalAdmin"
  | "branchAdmin"
  | "doctor"
  | "nurse"
  | "receptionist"
  | "labTechnician"
  | "pharmacist";

// Define menu item structure
export interface MenuItem {
  title: string;
  href: string;
  icon: any; // Using any for icon type to avoid TypeScript issues
  roles: UserRole[];
  indented?: boolean;
}

export interface MenuGroup {
  title: string;
  icon: any; // Using any for icon type to avoid TypeScript issues
  items: MenuItem[];
  roles: UserRole[];
}

// Dashboard item (shown separately)
export const dashboardItem: MenuItem = {
  title: "Dashboard",
  href: "/dashboard",
  icon: HomeIcon,
  roles: [
    "admin",
    "hospitalAdmin",
    "branchAd<PERSON>",
    "doctor",
    "nurse",
    "receptionist",
    "labTechnician",
    "pharmacist",
  ],
};

// Main sidebar configuration
export const menuGroups: MenuGroup[] = [
  {
    title: "Daily Operations",
    icon: ClockIcon,
    roles: ["admin", "hospitalAdmin", "branchAdmin", "doctor", "receptionist"],
    items: [
      {
        title: "Appointment List",
        href: "/appointments",
        icon: CalendarIcon,
        roles: [
          "admin",
          "hospitalAdmin",
          "branchAdmin",
          "doctor",
          "receptionist",
        ],
      },
      {
        title: "Doctor Appointment",
        href: "/doctor-appointments",
        icon: CalendarIcon,
        roles: [
          "admin",
          "hospitalAdmin",
          "branchAdmin",
          "doctor",
          "receptionist",
        ],
      },
      {
        title: "Queue",
        href: "/queue",
        icon: ListIcon,
        roles: [
          "admin",
          "hospitalAdmin",
          "branchAdmin",
          "doctor",
          "receptionist",
        ],
      },
      {
        title: "Walk-ins",
        href: "/walk-ins",
        icon: UserPlusIcon,
        roles: ["hospitalAdmin", "branchAdmin", "receptionist"],
      },
    ],
  },
  {
    title: "Clinical",
    icon: StethoscopeIcon,
    roles: ["hospitalAdmin", "branchAdmin", "doctor", "nurse"],
    items: [
      {
        title: "Consultations",
        href: "/consultations",
        icon: FileText,
        roles: ["hospitalAdmin", "branchAdmin", "doctor", "nurse"],
      },
      {
        title: "Vitals",
        href: "/vitals",
        icon: Activity,
        roles: ["hospitalAdmin", "branchAdmin", "doctor", "nurse"],
      },
      {
        title: "Prescriptions",
        href: "/prescriptions",
        icon: Pill,
        roles: ["hospitalAdmin", "branchAdmin", "doctor", "pharmacist"],
      },
    ],
  },
  {
    title: "Management",
    icon: UsersIcon,
    roles: ["hospitalAdmin", "branchAdmin", "doctor"],
    items: [
      {
        title: "Patients",
        href: "/patients",
        icon: UserIcon,
        roles: ["hospitalAdmin", "branchAdmin", "doctor", "receptionist"],
      },
      {
        title: "Doctors",
        href: "/doctors",
        icon: StethoscopeIcon,
        roles: ["hospitalAdmin", "branchAdmin"],
      },
      {
        title: "Doctor Schedules",
        href: "/doctor-schedules",
        icon: CalendarIcon,
        roles: ["hospitalAdmin", "branchAdmin", "doctor"],
      },
      {
        title: "Consent Management",
        href: "/consents",
        icon: ShieldIcon,
        roles: ["hospitalAdmin", "branchAdmin"],
      },
    ],
  },
  {
    title: "Branch Administration",
    icon: BuildingIcon,
    roles: ["hospitalAdmin", "branchAdmin"],
    items: [
      {
        title: "Department",
        href: "/branch-departments",
        icon: FolderIcon,
        roles: ["hospitalAdmin", "branchAdmin"],
      },
      {
        title: "Staff",
        href: "/staff",
        icon: UserCogIcon,
        roles: ["hospitalAdmin", "branchAdmin"],
      },
      {
        title: "Branch Admin Invitations",
        href: "/branch-admin-invitations",
        icon: UserPlusIcon,
        roles: ["hospitalAdmin"],
      },
      {
        title: "Settings",
        href: "/branch-settings",
        icon: SettingsIcon,
        roles: ["hospitalAdmin", "branchAdmin"],
      },
    ],
  },
  {
    title: "Clinic Administration",
    icon: BuildingIcon,
    roles: ["hospitalAdmin"],
    items: [
      {
        title: "Department",
        href: "/departments",
        icon: FolderIcon,
        roles: ["hospitalAdmin"],
      },
      {
        title: "Care Type",
        href: "/care-types",
        icon: TagIcon,
        roles: ["hospitalAdmin"],
      },
      {
        title: "Branches",
        href: "/branches",
        icon: BuildingIcon,
        roles: ["hospitalAdmin"],
      },
      {
        title: "Settings",
        href: "/settings",
        icon: SettingsIcon,
        roles: ["hospitalAdmin"],
      },
    ],
  },
];

// Helper function to filter menu groups by user role
export const getMenuGroupsByRole = (role: UserRole): MenuGroup[] => {
  return menuGroups
    .filter((group) => group.roles.includes(role))
    .map((group) => ({
      ...group,
      items: group.items.filter((item) => item.roles.includes(role)),
    }))
    .filter((group) => group.items.length > 0);
};

// Helper function to check if a route is active
export const isRouteActive = (pathname: string, href: string): boolean => {
  return pathname === href || pathname?.startsWith(`${href}/`);
};
