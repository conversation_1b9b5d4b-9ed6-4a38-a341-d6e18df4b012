"use client";

import {
  HomeIcon,
  UsersIcon,
  SettingsIcon,
  UserIcon,
  CalendarIcon,
  BuildingIcon,
  FolderIcon,
  TagIcon,
  StethoscopeIcon,
  UserCogIcon,
  ClockIcon,
  ListIcon,
  FileText,
  Activity,
  Pill,
  ShieldIcon,
  QrCode,
  UserPlus,
  UserCheck,
  Link2,
  Bell,
} from "lucide-react";

// Define all possible user roles
export type UserRole =
  | "superAdmin"
  | "admin"
  | "hospitalAdmin"
  | "branchAdmin"
  | "clinicAdmin" // Alias for branchAdmin in the current system
  | "doctor"
  | "nurse"
  | "receptionist"
  | "labTechnician"
  | "pharmacist";

// Define menu item structure
export interface MenuItem {
  title: string;
  href: string;
  icon: any; // Using any for icon type to avoid TypeScript issues
  indented?: boolean; // Allow nested menu items
  items?: MenuItem[]; // Allow nested menu items
}

export interface MenuGroup {
  title: string;
  icon: any; // Using any for icon type to avoid TypeScript issues
  items: MenuItem[];
}

// Dashboard item (shown separately)
export const dashboardItem: MenuItem = {
  title: "Dashboard",
  href: "/dashboard",
  icon: HomeIcon,
};

// Hospital Admin sidebar configuration
export const hospitalAdminMenu: MenuGroup[] = [
  {
    title: "Patient Care",
    icon: ClockIcon,
    items: [
      {
        title: "Appointment List",
        href: "/appointments",
        icon: CalendarIcon,
      },
      {
        title: "Doctor Appointment",
        href: "/doctor-appointments",
        icon: CalendarIcon,
      },
      {
        title: "Queue Management",
        href: "/queue",
        icon: ListIcon,
      },
    ],
  },
  {
    title: "Clinical Services",
    icon: StethoscopeIcon,
    items: [
      {
        title: "Consultations",
        href: "/consultations",
        icon: FileText,
      },
      {
        title: "Reconciliation",
        href: "/reconciliation",
        icon: FileText,
      },
    ],
  },
  {
    title: "People Management",
    icon: UsersIcon,
    items: [
      {
        title: "Patients",
        href: "/patients",
        icon: UserIcon,
      },
      {
        title: "Doctors",
        href: "/doctors",
        icon: StethoscopeIcon,
      },
      {
        title: "Doctor Schedules",
        href: "/doctor-schedules",
        icon: CalendarIcon,
      },

      {
        title: "Staff",
        href: "/staff",
        icon: UserCogIcon,
      },
    ],
  },
  {
    title: "ABDM Integration",
    icon: ShieldIcon,
    items: [
      {
        title: "ABHA Creation",
        href: "/abdm/abha-creation",
        icon: UserPlus,
      },
      {
        title: "ABHA Verification",
        href: "/abdm/abha-verification",
        icon: UserCheck,
      },
      {
        title: "ABHA Scan Desk",
        href: "/abdm/scan-desk",
        icon: QrCode,
      },
      {
        title: "Care Context Management",
        href: "/abdm/care-contexts",
        icon: Link2,
      },
      {
        title: "Consent Management",
        href: "/abdm/consents",
        icon: ShieldIcon,
      },
    ],
  },
  {
    title: "Organization Setup",
    icon: BuildingIcon,
    items: [
      {
        title: "Branches",
        href: "/branches",
        icon: BuildingIcon,
      },
      {
        title: "Departments",
        href: "/departments",
        icon: FolderIcon,
      },
      {
        title: "Branch Departments",
        href: "/branch-departments",
        icon: FolderIcon,
      },
      {
        title: "Care Types",
        href: "/care-types",
        icon: TagIcon,
      },
    ],
  },
  {
    title: "System",
    icon: SettingsIcon,
    items: [
      {
        title: "Settings",
        href: "/settings",
        icon: SettingsIcon,
      },
    ],
  },
];

// Clinic Admin sidebar configuration - optimized for hospital admin usability
export const clinicAdminMenu: MenuGroup[] = [
  {
    title: "Patient Care",
    icon: ClockIcon,
    items: [
      {
        title: "Appointment List",
        href: "/appointments",
        icon: CalendarIcon,
      },
      {
        title: "Doctor Appointment",
        href: "/doctor-appointments",
        icon: CalendarIcon,
      },
      {
        title: "Queue Management",
        href: "/queue",
        icon: ListIcon,
      },
    ],
  },
  {
    title: "Clinical Services",
    icon: StethoscopeIcon,
    items: [
      {
        title: "Consultations",
        href: "/consultations",
        icon: FileText,
      },
      {
        title: "Reconciliation",
        href: "/reconciliation",
        icon: FileText,
      },
    ],
  },
  {
    title: "People Management",
    icon: UsersIcon,
    items: [
      {
        title: "Patients",
        href: "/patients",
        icon: UserIcon,
      },
      {
        title: "Doctors",
        href: "/doctors",
        icon: StethoscopeIcon,
      },
      {
        title: "Doctor Schedules",
        href: "/doctor-schedules",
        icon: CalendarIcon,
      },

      {
        title: "Staff",
        href: "/staff",
        icon: UserCogIcon,
      },
    ],
  },
  {
    title: "ABDM Integration",
    icon: ShieldIcon,
    items: [
      {
        title: "ABHA Creation",
        href: "/abdm/abha-creation",
        icon: UserPlus,
      },
      {
        title: "ABHA Patient Creation",
        href: "/abdm/abha-patient-creation",
        icon: UserPlus,
      },
      {
        title: "ABHA Verification",
        href: "/abdm/abha-verification",
        icon: UserCheck,
      },
      {
        title: "ABHA Scan Desk",
        href: "/abdm/scan-desk",
        icon: QrCode,
      },
      {
        title: "Care Context Management",
        href: "/abdm/care-contexts",
        icon: Link2,
      },
      {
        title: "Consent Management",
        href: "/abdm/consents",
        icon: ShieldIcon,
      },
    ],
  },
  {
    title: "Branch Setup",
    icon: BuildingIcon,
    items: [
      {
        title: "Branch Departments",
        href: "/branch-departments",
        icon: FolderIcon,
      },
    ],
  },
  {
    title: "System",
    icon: SettingsIcon,
    items: [
      {
        title: "Settings",
        href: "/settings",
        icon: SettingsIcon,
      },
    ],
  },
];

// Branch Admin sidebar configuration
export const branchAdminMenu: MenuGroup[] = [
  {
    title: "Patient Care",
    icon: ClockIcon,
    items: [
      {
        title: "Appointment List",
        href: "/appointments",
        icon: CalendarIcon,
      },
      {
        title: "Doctor Appointment",
        href: "/doctor-appointments",
        icon: CalendarIcon,
      },
      {
        title: "Queue Management",
        href: "/queue",
        icon: ListIcon,
      },
    ],
  },
  {
    title: "Clinical Services",
    icon: StethoscopeIcon,
    items: [
      {
        title: "Consultations",
        href: "/consultations",
        icon: FileText,
      },
      {
        title: "Reconciliation",
        href: "/reconciliation",
        icon: FileText,
      },
    ],
  },
  {
    title: "People Management",
    icon: UsersIcon,
    items: [
      {
        title: "Patients",
        href: "/patients",
        icon: UserIcon,
      },
      {
        title: "Doctors",
        href: "/doctors",
        icon: StethoscopeIcon,
      },
      {
        title: "Doctor Schedules",
        href: "/doctor-schedules",
        icon: CalendarIcon,
      },

      {
        title: "Staff",
        href: "/staff",
        icon: UserCogIcon,
      },
    ],
  },
  {
    title: "ABDM Integration",
    icon: ShieldIcon,
    items: [
      {
        title: "ABHA Creation",
        href: "/abdm/abha-creation",
        icon: UserPlus,
      },
      {
        title: "ABHA Patient Creation",
        href: "/abdm/abha-patient-creation",
        icon: UserPlus,
      },
      {
        title: "ABHA Verification",
        href: "/abdm/abha-verification",
        icon: UserCheck,
      },
      {
        title: "ABHA Scan Desk",
        href: "/abdm/scan-desk",
        icon: QrCode,
      },
      {
        title: "Care Context Management",
        href: "/abdm/care-contexts",
        icon: Link2,
      },
      {
        title: "Consent Management",
        href: "/abdm/consents",
        icon: ShieldIcon,
      },
    ],
  },
  {
    title: "Branch Setup",
    icon: BuildingIcon,
    items: [
      {
        title: "Branch Departments",
        href: "/branch-departments",
        icon: FolderIcon,
      },
    ],
  },
  {
    title: "System",
    icon: SettingsIcon,
    items: [
      {
        title: "Settings",
        href: "/settings",
        icon: SettingsIcon,
      },
    ],
  },
];

// Doctor sidebar configuration
export const doctorMenu: MenuGroup[] = [
  {
    title: "My Schedule",
    icon: ClockIcon,
    items: [
      {
        title: "Appointment List",
        href: "/appointments",
        icon: CalendarIcon,
      },
      {
        title: "Doctor Appointment",
        href: "/doctor-appointments",
        icon: CalendarIcon,
      },
      {
        title: "Queue Management",
        href: "/queue",
        icon: ListIcon,
      },
      {
        title: "Doctor Schedules",
        href: "/doctor-schedules",
        icon: CalendarIcon,
      },
    ],
  },
  {
    title: "Patient Care",
    icon: StethoscopeIcon,
    items: [
      {
        title: "Consultations",
        href: "/consultations",
        icon: FileText,
      },
    ],
  },
  {
    title: "Patients",
    icon: UsersIcon,
    items: [
      {
        title: "My Patients",
        href: "/patients",
        icon: UserIcon,
      },
    ],
  },
  {
    title: "ABDM Integration",
    icon: ShieldIcon,
    items: [
      {
        title: "ABHA Scan Desk",
        href: "/abdm/scan-desk",
        icon: QrCode,
      },
      {
        title: "Consent Management",
        href: "/abdm/consents",
        icon: ShieldIcon,
      },

      {
        title: "Link Token Notifications",
        href: "/link-token-notify",
        icon: Bell,
      },
      {
        title: "UIL OTP Notifications",
        href: "/uil-otp-notify",
        icon: Bell,
      },
    ],
  },
];

// Nurse sidebar configuration
export const nurseMenu: MenuGroup[] = [
  {
    title: "Patient Care",
    icon: StethoscopeIcon,
    items: [
      {
        title: "Consultations",
        href: "/consultations",
        icon: FileText,
      },
      {
        title: "Patient Vitals",
        href: "/vitals",
        icon: Activity,
      },
    ],
  },
  {
    title: "Patients",
    icon: UsersIcon,
    items: [
      {
        title: "Patient Records",
        href: "/patients",
        icon: UserIcon,
      },
    ],
  },
  {
    title: "ABDM Integration",
    icon: ShieldIcon,
    items: [],
  },
];

// Receptionist sidebar configuration
export const receptionistMenu: MenuGroup[] = [
  {
    title: "Front Desk",
    icon: ClockIcon,
    items: [
      {
        title: "Appointment List",
        href: "/appointments",
        icon: CalendarIcon,
      },
      {
        title: "Doctor Appointment",
        href: "/doctor-appointments",
        icon: CalendarIcon,
      },
      {
        title: "Queue Management",
        href: "/queue",
        icon: ListIcon,
      },
    ],
  },
  {
    title: "Patients",
    icon: UsersIcon,
    items: [
      {
        title: "Patient Registration",
        href: "/patients",
        icon: UserIcon,
      },
    ],
  },
];

// Lab Technician sidebar configuration
export const labTechnicianMenu: MenuGroup[] = [
  {
    title: "Laboratory Services",
    icon: Activity,
    items: [
      {
        title: "Patient Records",
        href: "/patients",
        icon: UserIcon,
      },
    ],
  },
];

// Pharmacist sidebar configuration
export const pharmacistMenu: MenuGroup[] = [
  {
    title: "Pharmacy Services",
    icon: Pill,
    items: [
      {
        title: "Prescriptions",
        href: "/prescriptions",
        icon: Pill,
      },
      {
        title: "Patient Records",
        href: "/patients",
        icon: UserIcon,
      },
    ],
  },
];

// Admin sidebar configuration (same as hospital admin for now)
export const adminMenu: MenuGroup[] = [...hospitalAdminMenu];

// Get menu configuration based on user role
export const getMenuByRole = (role: UserRole): MenuGroup[] => {
  switch (role) {
    case "superAdmin":
      // Super admins should be redirected to admin portal, but fallback to hospital admin menu
      return hospitalAdminMenu;
    case "admin":
      return hospitalAdminMenu;
    case "hospitalAdmin":
      return hospitalAdminMenu;
    case "branchAdmin":
      return branchAdminMenu;
    case "clinicAdmin":
      return clinicAdminMenu;
    case "doctor":
      return doctorMenu;
    case "nurse":
      return nurseMenu;
    case "receptionist":
      return receptionistMenu;
    case "labTechnician":
      return labTechnicianMenu;
    case "pharmacist":
      return pharmacistMenu;
    default:
      return [];
  }
};

// Helper function to check if a route is active
export const isRouteActive = (pathname: string, href: string): boolean => {
  return pathname === href || pathname?.startsWith(`${href}/`);
};
