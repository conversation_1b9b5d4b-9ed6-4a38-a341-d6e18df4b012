"use client";

import React, { useState, useEffect } from "react";
import {
  AzureFileUpload,
  AzureBlobConfig,
  UploadResult,
  UploadError,
} from "@workspace/ui/components/azure-file-upload";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle, FileText, Upload } from "lucide-react";
import { toast } from "sonner";

interface AzureFhirUploadProps {
  consultationId: string;
  patientId: string;
  bundleType:
    | "HealthDocumentRecord"
    | "WellnessRecord"
    | "WellnessDocument"
    | "OPConsultNote"
    | "DischargeSummary"
    | "Prescription"
    | "OPConsultationRecord"
    | "DiagnosticReport"
    | "DischargeSummary"
    | "ImmunizationRecord";
  title: string;
  description: string;
  onUploadSuccess?: (result: any) => void;
  onUploadError?: (error: any) => void;
  className?: string;
  hideWhenFilesExist?: boolean;
}

export function AzureFhirUpload({
  consultationId,
  patientId,
  bundleType,
  title,
  description,
  onUploadSuccess,
  onUploadError,
  className,
  hideWhenFilesExist = true,
}: AzureFhirUploadProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedDocument, setUploadedDocument] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [existingFiles, setExistingFiles] = useState<any[]>([]);
  const [checkingFiles, setCheckingFiles] = useState(true);

  // Check for existing files on component mount
  useEffect(() => {
    const checkExistingFiles = async () => {
      if (!hideWhenFilesExist) {
        setCheckingFiles(false);
        return;
      }

      try {
        const params = new URLSearchParams({
          consultationId,
          bundleType,
        });

        const response = await fetch(`/api/fhir/uploaded-bundles?${params}`);
        if (response.ok) {
          const data = await response.json();
          setExistingFiles(data.bundles || []);
        }
      } catch (error) {
        console.error("Error checking existing files:", error);
      } finally {
        setCheckingFiles(false);
      }
    };

    checkExistingFiles();
  }, [consultationId, bundleType, hideWhenFilesExist]);

  // Azure configuration from environment variables
  const azureConfig: AzureBlobConfig = {
    connectionString:
      process.env.NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING || "",
    containerName:
      process.env.NEXT_PUBLIC_AZURE_STORAGE_CONTAINER_NAME || "documents",
    accountName: process.env.NEXT_PUBLIC_AZURE_STORAGE_ACCOUNT_NAME,
  };

  const handleAzureUploadSuccess = async (result: UploadResult) => {
    try {
      setIsProcessing(true);
      setError(null);

      console.log("Azure upload successful:", result);
      toast.success("File uploaded to Azure successfully");

      // FIXED: For all bundle types, only store the document info without generating bundle
      // Bundle will be generated when the respective form is saved (vitals, clinical notes, etc.)
      if (["WellnessRecord", "WellnessDocument", "DiagnosticReport", "DischargeSummary", "ImmunizationRecord", "OPConsultationRecord", "Prescription"].includes(bundleType)) {
        console.log(
          `${bundleType} PDF uploaded - storing document info for later bundle generation`,
        );

        // Map bundle types to document titles
        const documentTitles: Record<string, string> = {
          "WellnessRecord": "Wellness Record Document",
          "WellnessDocument": "Wellness Record Document",
          "DiagnosticReport": "Diagnostic Report Document",
          "DischargeSummary": "Discharge Summary Document",
          "ImmunizationRecord": "Immunization Record Document",
          "OPConsultationRecord": "Clinical Notes Document",
          "Prescription": "Prescription Document",
        };

        // Store document information in a simple format for later use
        const documentInfo = {
          documentUrl: result.url,
          documentTitle: documentTitles[bundleType] || "Healthcare Document",
          documentDescription: description,
          contentType: result.contentType,
          uploadedAt: new Date().toISOString(),
          consultationId,
          patientId,
          bundleType,
        };

        // Store in database as a simple record (not a full FHIR bundle)
        const response = await fetch("/api/fhir/store-wellness-document-info", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...documentInfo,
            bundleType, // Pass the bundle type to the API
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.error || "Failed to store document information",
          );
        }

        const storeResult = await response.json();
        console.log(`${bundleType} document info stored:`, storeResult);

        setUploadedDocument({
          azureResult: result,
          documentInfo: storeResult,
          bundleType,
        });

        toast.success(
          "Wellness document uploaded successfully. Bundle will be generated when vitals are saved.",
        );
        onUploadSuccess?.(storeResult);
        return;
      }

      // For other bundle types, generate FHIR bundle immediately
      let apiEndpoint = "";
      let documentTitle = title;

      switch (bundleType as string) {
        case "HealthDocumentRecord":
          apiEndpoint = "/api/fhir/save-health-document-bundle";
          documentTitle = "Health Document";
          break;
        case "WellnessRecord":
          apiEndpoint = "/api/fhir/save-wellness-document-bundle";
          documentTitle = "Wellness Record Document";
          break;
        case "WellnessDocument":
          apiEndpoint = "/api/fhir/store-wellness-document-info";
          documentTitle = "Wellness Document";
          break;
        // case 'OPConsultationRecord':
        //   apiEndpoint = '/api/fhir/save-opconsult-document-bundle';
        //   documentTitle = 'OP Consultation Document';
        //   break;
        case "OPConsultationRecord":
          apiEndpoint = "/api/fhir/save-opconsultation-document-bundle";
          documentTitle = "Clinical Notes Document";
          break;
        case "Prescription":
          apiEndpoint = "/api/fhir/save-prescription-document-bundle";
          documentTitle = "Prescription Document";
          break;
        case "DiagnosticReport":
          apiEndpoint = "/api/fhir/save-diagnostic-document-bundle";
          documentTitle = "Lab Report Document";
          break;
        case "DischargeSummary":
          apiEndpoint = "/api/fhir/save-discharge-document-bundle";
          documentTitle = "Discharge Summary Document";
          break;
        case "DischargeSummary":
          apiEndpoint = "/api/fhir/save-discharge-summary-document-bundle";
          documentTitle = "Discharge Summary Document";
          break;
        case "ImmunizationRecord":
          apiEndpoint = "/api/fhir/save-immunization-document-bundle";
          documentTitle = "Immunization Document";
          break;
        default:
          throw new Error(`Unsupported bundle type: ${bundleType}`);
      }

      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          consultationId,
          patientId,
          documentUrl: result.url,
          documentTitle,
          documentDescription: description,
          contentType: result.contentType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate FHIR bundle");
      }

      const fhirResult = await response.json();
      console.log("FHIR bundle generated and saved:", fhirResult);

      setUploadedDocument({
        azureResult: result,
        fhirResult,
        bundleType,
      });

      toast.success(
        `${bundleType} FHIR bundle generated and saved successfully`,
      );
      onUploadSuccess?.(fhirResult);
    } catch (error: any) {
      console.error("Error processing upload:", error);
      const errorMessage = error.message || "Failed to process upload";
      setError(errorMessage);
      toast.error(errorMessage);
      onUploadError?.(error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleAzureUploadError = (error: UploadError) => {
    console.error("Azure upload failed:", error);
    setError(error.error);
    toast.error(`Upload failed: ${error.error}`);
    onUploadError?.(error);
  };

  // Don't render upload component if files exist and hideWhenFilesExist is true
  if (hideWhenFilesExist && !checkingFiles && existingFiles.length > 0) {
    return null;
  }

  // Show loading state while checking for existing files
  if (checkingFiles) {
    return (
      <div className={className}>
        <Card className="border-2 border-purple-100 shadow-sm">
          <CardHeader className="bg-purple-50 pb-3">
            <CardTitle className="flex items-center text-purple-700">
              <Upload className="h-5 w-5 mr-2" />
              {title}
            </CardTitle>
            <CardDescription>Checking for existing uploads...</CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
              <span className="ml-2 text-muted-foreground">Loading...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      <Card className="border-2 border-purple-100 shadow-sm">
        <CardHeader className="bg-purple-50 pb-3">
          <CardTitle className="flex items-center text-purple-700">
            <Upload className="h-5 w-5 mr-2" />
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="pt-4">
          {error && (
            <Alert className="mb-4 border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {uploadedDocument ? (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <div className="space-y-2">
                  <p className="font-medium">
                    Document uploaded and processed successfully!
                  </p>
                  <div className="text-sm space-y-1">
                    <p>
                      <strong>File:</strong>{" "}
                      {uploadedDocument.azureResult.blobName}
                    </p>
                    <p>
                      <strong>Azure URL:</strong>{" "}
                      <a
                        href={uploadedDocument.azureResult.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        View Document
                      </a>
                    </p>
                    {uploadedDocument.fhirResult?.bundleId ? (
                      <p>
                        <strong>FHIR Bundle:</strong>{" "}
                        {uploadedDocument.fhirResult.bundleId}
                      </p>
                    ) : uploadedDocument.documentInfo?.bundleId ? (
                      <p>
                        <strong>Document ID:</strong>{" "}
                        {uploadedDocument.documentInfo.bundleId}
                      </p>
                    ) : null}
                    <p>
                      <strong>Bundle Type:</strong>{" "}
                      {uploadedDocument.bundleType}
                    </p>
                    {["WellnessRecord", "WellnessDocument", "DiagnosticReport", "DischargeSummary", "ImmunizationRecord", "OPConsultationRecord", "Prescription"].includes(bundleType) && (
                      <p className="text-blue-600 font-medium">
                        Document will be included in FHIR bundle when the respective form is saved.
                      </p>
                    )}
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          ) : (
            <AzureFileUpload
              config={azureConfig}
              options={{
                maxFileSize: 25 * 1024 * 1024, // 25MB
                allowedMimeTypes: ["application/pdf"],
                generateUniqueFilename: true,
                filenamePrefix: bundleType.toLowerCase(),
                metadata: {
                  consultationId,
                  patientId,
                  bundleType,
                  uploadedBy: "healthcare-provider",
                  timestamp: new Date().toISOString(),
                },
                accessTier: "Hot",
              }}
              onUploadSuccess={handleAzureUploadSuccess}
              onUploadError={handleAzureUploadError}
              disabled={isProcessing}
              placeholder={`Drop your PDF document here or click to browse (${bundleType})`}
              showProgress={true}
              enableDragDrop={true}
              className="min-h-[150px]"
            />
          )}

          {isProcessing && (
            <Alert className="mt-4 border-blue-200 bg-blue-50">
              <FileText className="h-4 w-4 text-blue-600 animate-pulse" />
              <AlertDescription className="text-blue-800">
                Processing upload and generating FHIR bundle...
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
