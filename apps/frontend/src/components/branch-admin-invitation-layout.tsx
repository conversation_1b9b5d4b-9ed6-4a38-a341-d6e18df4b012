"use client";

import React from "react";
import { BranchAdminInvitationProvider } from "@/contexts/branch-admin-invitation-context";

interface BranchAdminInvitationLayoutProps {
  children: React.ReactNode;
}

export function BranchAdminInvitationLayout({
  children,
}: BranchAdminInvitationLayoutProps) {
  return (
    <BranchAdminInvitationProvider>{children}</BranchAdminInvitationProvider>
  );
}
