"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { RefreshCw, Loader2, AlertCircle, XCircle, Clock } from "lucide-react";
import { toast } from "sonner";

import { format } from "date-fns";

interface TokenNotification {
  id: string;
  requestId: string;
  error?: {
    code: string;
    message: string;
  } | null;
  response: {
    requestId: string;
    abhaAddress?: string;
    linkToken?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export function TokenNotificationsTable() {
  const [notifications, setNotifications] = useState<TokenNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch token notifications
  const fetchNotifications = async (showToast = false) => {
    try {
      setRefreshing(true);
      const response = await fetch(
        `/api/webhook/api/v3/hip/token/generate-token-notify/check`,
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to fetch token notifications",
        );
      }

      const data = await response.json();
      setNotifications(data.records || []);

      if (showToast) {
        toast.success("Token notifications refreshed");
      }
    } catch (error) {
      console.error("Error fetching token notifications:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to fetch token notifications",
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchNotifications();

    // Set up polling to refresh notifications every 30 seconds
    const intervalId = setInterval(() => fetchNotifications(), 30000);

    return () => clearInterval(intervalId);
  }, []);

  // Get status badge for a notification
  const getStatusBadge = (notification: TokenNotification) => {
    const isError = !!notification.error;
    const isSuccess = !isError && !!notification.response?.linkToken;

    if (isError) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Error
        </Badge>
      );
    } else if (isSuccess) {
      return (
        <Badge
          variant="success"
          className="flex items-center gap-1 bg-green-500"
        >
          <AlertCircle className="h-3 w-3" />
          Success
        </Badge>
      );
    } else {
      return (
        <Badge
          variant="outline"
          className="flex items-center gap-1 bg-yellow-100 text-yellow-800 border-yellow-300"
        >
          <Clock className="h-3 w-3" />
          Pending
        </Badge>
      );
    }
  };

  // Get message for a notification
  const getMessage = (notification: TokenNotification) => {
    const isError = !!notification.error;
    const isSuccess = !isError && !!notification.response?.linkToken;

    if (isError) {
      return notification.error?.message || "Unknown error";
    } else if (isSuccess) {
      return `Token generated for ${notification.response?.abhaAddress || "unknown"}`;
    } else {
      return "Waiting for response from ABDM";
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm:ss");
    } catch (error) {
      return dateString;
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>Token Generation Notifications</CardTitle>
          <CardDescription>
            Recent token generation notifications from ABDM
          </CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => fetchNotifications(true)}
          disabled={refreshing}
        >
          {refreshing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </>
          )}
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : notifications.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No token notifications found
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Status</TableHead>
                  <TableHead>Message</TableHead>
                  <TableHead>Request ID</TableHead>
                  <TableHead>Time</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {notifications.map((notification) => (
                  <TableRow key={notification.id}>
                    <TableCell>{getStatusBadge(notification)}</TableCell>
                    <TableCell className="max-w-md truncate">
                      {getMessage(notification)}
                    </TableCell>
                    <TableCell className="font-mono text-xs">
                      {notification.requestId}
                    </TableCell>
                    <TableCell>{formatDate(notification.createdAt)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
