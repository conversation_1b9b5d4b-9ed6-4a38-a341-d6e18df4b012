"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ConsentStatusBadge } from "@/components/consent-status-badge";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import { Loader2, RefreshCw, Search } from "lucide-react";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface ConsentListProps {
  patientId?: string;
  onSelectConsent?: (consentId: string) => void;
}

export function ConsentList({ patientId, onSelectConsent }: ConsentListProps) {
  const router = useRouter();
  const [consents, setConsents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState({
    status: "",
    search: "",
  });

  // For UI display purposes
  const displayStatus = filters.status === "" ? "ALL" : filters.status;

  // Fetch consents
  const fetchConsents = async (page = 1) => {
    try {
      setLoading(true);

      let url = `/api/abdm/consent/list?page=${page}&limit=${pagination.limit}`;

      if (patientId) {
        url += `&patientId=${patientId}`;
      }

      if (filters.status) {
        url += `&status=${filters.status}`;
      }

      const response = await Fetch.get(url);

      if (response.success && response.data) {
        setConsents(response.data);
        setPagination(response.pagination);
      } else {
        toast.error(response.error || "Failed to fetch consents");
      }
    } catch (error) {
      console.error("Error fetching consents:", error);
      toast("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Refresh consents
  const refreshConsents = async () => {
    try {
      setRefreshing(true);
      await fetchConsents(pagination.page);
    } catch (error) {
      console.error("Error refreshing consents:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchConsents(page);
  };

  // Handle status filter change
  const handleStatusChange = (status: string) => {
    // If ALL is selected, set status to empty string for the API
    const apiStatus = status === "ALL" ? "" : status;
    setFilters({ ...filters, status: apiStatus });
    fetchConsents(1);
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchConsents(1);
  };

  // Fetch consents on mount
  useEffect(() => {
    fetchConsents();
  }, [patientId]);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Consent Requests</CardTitle>
            <CardDescription>
              {patientId
                ? "Consent requests for this patient"
                : "All consent requests"}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshConsents}
            disabled={refreshing}
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            <span className="sr-only">Refresh</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <form
              onSubmit={handleSearch}
              className="flex w-full max-w-sm items-center space-x-2"
            >
              <Input
                type="search"
                placeholder="Search..."
                value={filters.search}
                onChange={(e) =>
                  setFilters({ ...filters, search: e.target.value })
                }
              />
              <Button type="submit" size="sm">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </form>
            <Select value={displayStatus} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Statuses</SelectItem>
                <SelectItem value="REQUESTED">Pending</SelectItem>
                <SelectItem value="GRANTED">Approved</SelectItem>
                <SelectItem value="DENIED">Rejected</SelectItem>
                <SelectItem value="EXPIRED">Expired</SelectItem>
                <SelectItem value="REVOKED">Revoked</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : consents.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No consent requests found
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Patient</TableHead>
                    <TableHead>Purpose</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {consents.map((consent) => (
                    <TableRow key={consent.id}>
                      <TableCell>
                        {consent.patient?.firstName} {consent.patient?.lastName}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {consent.purpose}
                      </TableCell>
                      <TableCell>
                        <ConsentStatusBadge status={consent.status} />
                      </TableCell>
                      <TableCell>
                        {format(new Date(consent.createdAt), "PPP")}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // If we have a patient ID from the consent data, navigate to patient consent details
                            if (consent.patient?.id) {
                              router.push(
                                `/patients/${consent.patient.id}/consents/details/${consent.id}`,
                              );
                            } else if (onSelectConsent) {
                              // Fallback to the callback if patient ID is not available
                              onSelectConsent(consent.id);
                            }
                          }}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {pagination.totalPages > 1 && (
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (pagination.page > 1) {
                        handlePageChange(pagination.page - 1);
                      }
                    }}
                    aria-disabled={pagination.page === 1}
                    className={
                      pagination.page === 1
                        ? "pointer-events-none opacity-50"
                        : ""
                    }
                  />
                </PaginationItem>
                {Array.from({ length: pagination.totalPages }).map((_, i) => {
                  const page = i + 1;
                  // Show first page, last page, current page, and pages around current page
                  if (
                    page === 1 ||
                    page === pagination.totalPages ||
                    (page >= pagination.page - 1 && page <= pagination.page + 1)
                  ) {
                    return (
                      <PaginationItem key={page}>
                        <PaginationLink
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(page);
                          }}
                          isActive={page === pagination.page}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  } else if (
                    (page === 2 && pagination.page > 3) ||
                    (page === pagination.totalPages - 1 &&
                      pagination.page < pagination.totalPages - 2)
                  ) {
                    return (
                      <PaginationItem key={page}>
                        <PaginationEllipsis />
                      </PaginationItem>
                    );
                  }
                  return null;
                })}
                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (pagination.page < pagination.totalPages) {
                        handlePageChange(pagination.page + 1);
                      }
                    }}
                    aria-disabled={pagination.page === pagination.totalPages}
                    className={
                      pagination.page === pagination.totalPages
                        ? "pointer-events-none opacity-50"
                        : ""
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
