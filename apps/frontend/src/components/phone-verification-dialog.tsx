"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Loader2, Phone, Shield } from "lucide-react";
import { usePhoneVerification } from "@/hooks/use-phone-verification";

interface PhoneVerificationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  phoneNumber: string;
  onVerificationSuccess: () => void;
  purpose?: string;
  patientId?: string;
}

export function PhoneVerificationDialog({
  open,
  onOpenChange,
  phoneNumber,
  onVerificationSuccess,
  purpose = "phone-verification",
  patientId,
}: PhoneVerificationDialogProps) {
  const [otp, setOtp] = useState("");

  const {
    isLoading,
    isVerifying,
    isVerified,
    otpSent,
    error,
    cooldownTime,
    sendOTP,
    verifyOTP,
    resendOTP,
    resetVerification,
  } = usePhoneVerification({
    onVerificationSuccess: () => {
      onVerificationSuccess();
      onOpenChange(false);
      setOtp("");
    },
  });

  // Auto-send OTP when dialog opens
  useEffect(() => {
    if (open && phoneNumber && !otpSent && !isVerified) {
      sendOTP(phoneNumber, purpose, patientId);
    }
  }, [open, phoneNumber, otpSent, isVerified, sendOTP, purpose, patientId]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setOtp("");
      resetVerification();
    }
  }, [open, resetVerification]);

  const handleVerifyOTP = async () => {
    if (otp.length === 6) {
      await verifyOTP(phoneNumber, otp, purpose);
    }
  };

  const handleResendOTP = async () => {
    await resendOTP(phoneNumber, purpose, patientId);
    setOtp("");
  };

  const formatPhoneNumber = (phone: string) => {
    if (phone.length === 10) {
      return `+91 ${phone.slice(0, 5)} ${phone.slice(5)}`;
    }
    return phone;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-600" />
            Verify Phone Number
          </DialogTitle>
          <DialogDescription>
            We've sent a 6-digit verification code to{" "}
            <span className="font-medium">
              {formatPhoneNumber(phoneNumber)}
            </span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Phone number display */}
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Phone className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">
              {formatPhoneNumber(phoneNumber)}
            </span>
          </div>

          {/* OTP Input */}
          <div className="space-y-2">
            <Label htmlFor="otp">Enter verification code</Label>
            <Input
              id="otp"
              type="text"
              placeholder="000000"
              value={otp}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, "").slice(0, 6);
                setOtp(value);
              }}
              maxLength={6}
              className="text-center text-lg tracking-widest"
              disabled={isVerifying || isVerified}
            />
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>

          {/* Resend OTP */}
          <div className="text-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResendOTP}
              disabled={cooldownTime > 0 || isLoading}
              className="text-blue-600 hover:text-blue-700"
            >
              {cooldownTime > 0 ? (
                `Resend in ${cooldownTime}s`
              ) : isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                "Resend Code"
              )}
            </Button>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isVerifying}
          >
            Cancel
          </Button>
          <Button
            onClick={handleVerifyOTP}
            disabled={otp.length !== 6 || isVerifying || isVerified}
          >
            {isVerifying ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : isVerified ? (
              "Verified"
            ) : (
              "Verify"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
