"use client";

import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChevronDown, User, MapPin } from "lucide-react";
import { toast } from "sonner";

interface Organization {
  id: string;
  name: string;
  slug: string;
  roles: string[];
  isDefault: boolean;
  organizationName: string;
}

interface Branch {
  id: string;
  name: string;
  isHeadOffice: boolean;
}

interface UserContext {
  organization: {
    organizationName: string;
    id: string;
    organizationId?: string;
    name: string;
    slug: string;
  } | null;
  role: string | null;
  branch: {
    id: string;
    name: string;
    isHeadOffice: boolean;
  } | null;
}

interface ContextSwitcherProps {
  className?: string;
}

export default function ContextSwitcher({
  className = "",
}: ContextSwitcherProps) {
  const [currentContext, setCurrentContext] = useState<UserContext>({
    organization: null,
    role: null,
    branch: null,
  });

  const [availableOptions, setAvailableOptions] = useState<{
    organizations: Organization[];
    roles: string[];
    branches: Branch[];
  }>({
    organizations: [],
    roles: [],
    branches: [],
  });

  const [loading, setLoading] = useState(true);
  const [switching, setSwitching] = useState(false);

  // Fetch current context and available options
  const fetchContext = async () => {
    try {
      const response = await fetch("/api/user/switch-context");

      if (response.ok) {
        const data = await response.json();
        console.log("Context data received:", {
          currentContext: data.currentContext,
          availableOptions: data.availableOptions,
          organizationsCount: data.availableOptions?.organizations?.length,
          rolesCount: data.availableOptions?.roles?.length,
          branchesCount: data.availableOptions?.branches?.length,
        });
        setCurrentContext(data.currentContext);
        setAvailableOptions(data.availableOptions);
      } else {
        console.error("Failed to fetch context:", response.status);
      }
    } catch (error) {
      console.error("Error fetching context:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContext();
  }, []);

  // Switch organization
  // const switchOrganization = async (organizationId: string) => {
  //   console.log("Attempting to switch organization:", {
  //     newOrgId: organizationId,
  //     currentOrgId: currentContext.organization?.id,
  //     switching,
  //   });

  //   if (switching) {
  //     console.log("Already switching, ignoring request");
  //     return;
  //   }

  //   const currentOrgId =
  //     currentContext.organization?.organizationId ||
  //     currentContext.organization?.id;
  //   if (organizationId === currentOrgId) {
  //     console.log("Same organization selected, ignoring");
  //     return;
  //   }

  //   setSwitching(true);
  //   try {
  //     const response = await fetch("/api/user/switch-context", {
  //       method: "POST",
  //       headers: { "Content-Type": "application/json" },
  //       body: JSON.stringify({ organizationId }),
  //     });

  //     if (response.ok) {
  //       const data = await response.json();
  //       console.log("Organization switched:", data);

  //       // Update the context with the response data
  //       setCurrentContext(data.context);

  //       // Refresh available options for the new organization
  //       await fetchContext();

  //       toast.success("Organization switched successfully");

  //       // Refresh the page to update all components with new context
  //       setTimeout(() => {
  //         window.location.reload();
  //       }, 500);
  //     } else {
  //       const error = await response.json();
  //       toast.error(error.message || "Failed to switch organization");
  //     }
  //   } catch (error) {
  //     console.error("Error switching organization:", error);
  //     toast.error("Failed to switch organization");
  //   } finally {
  //     setSwitching(false);
  //   }
  // };

  // Switch role
  const switchRole = async (role: string) => {
    if (
      switching ||
      role === currentContext.role ||
      !currentContext.organization
    )
      return;

    setSwitching(true);
    console.log("switchRole", currentContext.organization, role);
    try {
      const response = await fetch("/api/user/switch-context", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          organizationId:
            currentContext.organization.organizationId ||
            currentContext.organization.id,
          role,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Role switched:", data);

        // Update the context with the response data
        setCurrentContext(data.context);

        toast.success("Role switched successfully");

        // Refresh the page to update all components with new role context
        setTimeout(() => {
          window.location.reload();
        }, 500);
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to switch role");
      }
    } catch (error) {
      console.error("Error switching role:", error);
      toast.error("Failed to switch role");
    } finally {
      setSwitching(false);
    }
  };

  // Switch branch
  const switchBranch = async (branchId: string) => {
    console.log("switchBranch", currentContext.organization, branchId);
    if (
      switching ||
      branchId === currentContext.branch?.id ||
      !currentContext.organization
    ) {
      return;
    }
    setSwitching(true);
    try {
      const response = await fetch("/api/user/switch-context", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          organizationId:
            currentContext.organization.organizationId ||
            currentContext.organization.id,
          role: currentContext.role,
          branchId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.organizationDeactivated) {
          // Redirect to organization deactivated page
          window.location.href = `/organization-deactivated?orgId=${errorData.organizationId}&orgName=${encodeURIComponent(errorData.organizationName)}`;
          return;
        }
      }
      console.log(response);

      if (response.ok) {
        const data = await response.json();
        console.log("Branch switched:", data);

        // Update the context with the response data
        setCurrentContext(data.context);

        toast.success("Branch switched successfully");

        // Refresh the page to update all components with new branch context
        setTimeout(() => {
          window.location.reload();
        }, 500);
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to switch branch");
      }
    } catch (error) {
      console.error("Error switching branch:", error);
      toast.error("Failed to switch branch");
    } finally {
      setSwitching(false);
    }
  };

  const getRoleDisplayName = (role: string): string => {
    const roleNames: Record<string, string> = {
      hospitalAdmin: "Hospital Admin",
      branchAdmin: "Branch Admin",
      doctor: "Doctor",
      nurse: "Nurse",
      staff: "Staff",
      receptionist: "Receptionist",
      pharmacist: "Pharmacist",
      technician: "Technician",
    };
    return roleNames[role] || role;
  };

  if (loading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="animate-pulse bg-gray-200 h-8 w-32 rounded"></div>
        <div className="animate-pulse bg-gray-200 h-8 w-24 rounded"></div>
        <div className="animate-pulse bg-gray-200 h-8 w-28 rounded"></div>
      </div>
    );
  }

  // If no organizations available, show a simple message
  if (availableOptions.organizations.length === 0) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-sm text-gray-500">
          No organizations available
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Organization Switcher */}
      {/* {availableOptions.organizations.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-9 gap-1 hover-card btn-hover-effect"
              disabled={switching}
            >
              <Building className="h-4 w-4 text-primary" />
              <span className="max-w-[150px] truncate">
                {currentContext.organization?.organizationName ||
                  availableOptions.organizations[0]?.organizationName ||
                  "Select Org"}
              </span>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[200px]">
            <DropdownMenuLabel>Organizations</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {availableOptions.organizations.map((org) => (
              <DropdownMenuItem
                key={org.id}
                onClick={() => {
                  console.log("Organization clicked:", org.name, org.id);
                  const currentOrgId =
                    currentContext.organization?.organizationId ||
                    currentContext.organization?.id;
                  if (org.id !== currentOrgId) {
                    switchOrganization(org.id);
                  }
                }}
                className={
                  org.id ===
                  (currentContext.organization?.organizationId ||
                    currentContext.organization?.id)
                    ? "bg-accent text-accent-foreground"
                    : ""
                }
              >
                <Building className="mr-2 h-4 w-4" />
                <span className="truncate">{org.name}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )} */}

      {/* Role Switcher */}
      {availableOptions.roles.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-9 gap-1 hover-card btn-hover-effect"
              disabled={switching || availableOptions.roles.length === 1}
            >
              <User className="h-4 w-4 text-primary" />
              <span className="max-w-[120px] truncate">
                {getRoleDisplayName(
                  currentContext.role || availableOptions.roles[0] || "",
                )}
              </span>
              {availableOptions.roles.length > 1 && (
                <ChevronDown className="h-4 w-4 opacity-50" />
              )}
            </Button>
          </DropdownMenuTrigger>
          {availableOptions.roles.length > 1 && (
            <DropdownMenuContent align="start" className="w-[180px]">
              <DropdownMenuLabel>Roles</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {availableOptions.roles.map((role) => (
                <DropdownMenuItem
                  key={role}
                  onClick={() => {
                    console.log("Role clicked:", role);
                    if (role !== currentContext.role) {
                      switchRole(role);
                    }
                  }}
                  className={
                    role === currentContext.role
                      ? "bg-accent text-accent-foreground"
                      : ""
                  }
                >
                  <User className="mr-2 h-4 w-4" />
                  <span className="truncate">{getRoleDisplayName(role)}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          )}
        </DropdownMenu>
      )}

      {/* Branch Switcher */}
      {availableOptions.branches.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-9 gap-1 hover-card btn-hover-effect"
              disabled={switching || availableOptions.branches.length === 1}
            >
              <MapPin className="h-4 w-4 text-primary" />
              <span className="max-w-[150px] truncate">
                {currentContext.branch?.name ||
                  availableOptions.branches[0]?.name ||
                  "Select Branch"}
                {(currentContext.branch?.isHeadOffice ||
                  availableOptions.branches[0]?.isHeadOffice) &&
                  " (HQ)"}
              </span>
              {availableOptions.branches.length > 1 && (
                <ChevronDown className="h-4 w-4 opacity-50" />
              )}
            </Button>
          </DropdownMenuTrigger>
          {availableOptions.branches.length > 1 && (
            <DropdownMenuContent align="start" className="w-[200px]">
              <DropdownMenuLabel>Branches</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {availableOptions.branches.map((branch) => (
                <DropdownMenuItem
                  key={branch.id}
                  onClick={() => {
                    console.log("Branch clicked:", branch.name, branch.id);
                    console.log("Current branch:", currentContext.branch?.id);
                    if (branch.id !== currentContext.branch?.id) {
                      switchBranch(branch.id);
                    }
                  }}
                  className={
                    branch.id === currentContext.branch?.id
                      ? "bg-accent text-accent-foreground"
                      : ""
                  }
                >
                  <MapPin className="mr-2 h-4 w-4" />
                  <span className="truncate">{branch.name}</span>
                  {branch.isHeadOffice && (
                    <span className="ml-2 text-xs text-muted-foreground">
                      (HQ)
                    </span>
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          )}
        </DropdownMenu>
      )}
    </div>
  );
}
