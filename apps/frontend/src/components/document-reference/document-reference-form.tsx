"use client";

import { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { toast } from "sonner";
import { Fetch } from "@/services/fetch";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, Trash2, Upload } from "lucide-react";

// Define the attachment schema
const attachmentSchema = z.object({
  contentType: z.string(),
  url: z.string().optional(),
  title: z.string().optional(),
  creation: z.date().optional(),
  data: z.string().optional(),
  size: z.number().optional(),
  hash: z.string().optional(),
});

// Define the content schema
const contentSchema = z.object({
  attachment: attachmentSchema,
  format: z.string().optional(),
});

// Define the context schema
const contextSchema = z
  .object({
    encounter: z.array(z.string()).optional(),
    period: z
      .object({
        start: z.date().optional(),
        end: z.date().optional(),
      })
      .optional(),
    facilityType: z.string().optional(),
    practiceSetting: z.string().optional(),
    sourcePatientInfo: z.string().optional(),
    related: z
      .array(
        z.object({
          reference: z.string(),
          type: z.string().optional(),
        }),
      )
      .optional(),
  })
  .optional();

// Define the form schema
const formSchema = z.object({
  patientId: z.string(),
  doctorId: z.string(),
  consultationId: z.string().optional(),
  status: z.string(),
  docStatus: z.string(),
  type: z.string(),
  typeDisplay: z.string(),
  category: z.string(),
  categoryDisplay: z.string(),
  subject: z.string(),
  date: z.date(),
  author: z.string().optional(),
  authenticator: z.string().optional(),
  custodian: z.string().optional(),
  description: z.string().optional(),
  securityLabel: z.string().optional(),
  content: z.array(contentSchema),
  context: contextSchema,
});

type FormValues = z.infer<typeof formSchema>;

interface DocumentReferenceFormProps {
  patientId: string;
  doctorId: string;
  consultationId?: string;
  onCancel?: () => void;
  onSuccess?: () => void;
}

export function DocumentReferenceForm({
  patientId,
  doctorId,
  consultationId,
  onCancel,
  onSuccess,
}: DocumentReferenceFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [files, setFiles] = useState<File[]>([]);

  // Define form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      patientId,
      doctorId,
      consultationId,
      status: "current",
      docStatus: "final",
      type: "clinical-note",
      typeDisplay: "Clinical Note",
      category: "clinical-document",
      categoryDisplay: "Clinical Document",
      subject: patientId,
      date: new Date(),
      content: [
        {
          attachment: {
            contentType: "application/pdf",
          },
        },
      ],
      context: {
        encounter: consultationId ? [consultationId] : [],
      },
    },
  });

  // Setup field array for content
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "content",
  });

  // Handle file upload
  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number,
  ) => {
    const fileList = event.target.files;
    if (!fileList || fileList.length === 0) return;

    const file = fileList[0];
    const newFiles = [...files];
    newFiles[index] = file;
    setFiles(newFiles);

    // Update form with file metadata
    form.setValue(`content.${index}.attachment.contentType`, file.type);
    form.setValue(`content.${index}.attachment.title`, file.name);
    form.setValue(`content.${index}.attachment.size`, file.size);
    form.setValue(`content.${index}.attachment.creation`, new Date());

    // Read file as base64 for data field
    const reader = new FileReader();
    reader.onload = (e) => {
      const base64Data = e.target?.result?.toString().split(",")[1];
      if (base64Data) {
        form.setValue(`content.${index}.attachment.data`, base64Data);
      }
    };
    reader.readAsDataURL(file);
  };

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      const response = await Fetch.post("/api/document-reference", data);

      if (response.error) {
        throw new Error(response.error);
      }

      toast.success("Document reference created successfully");
      form.reset();
      setFiles([]);
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error creating document reference:", error);
      toast.error("Failed to create document reference");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="typeDisplay"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Document Type</FormLabel>
                <FormControl>
                  <Input placeholder="Type of document" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Type Code</FormLabel>
                <FormControl>
                  <Input placeholder="LOINC or SNOMED code" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="categoryDisplay"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    // Update category code based on display value
                    const categoryMap: Record<string, string> = {
                      "Clinical Document": "clinical-document",
                      "Diagnostic Report": "diagnostic-report",
                      "Discharge Summary": "discharge-summary",
                      "Laboratory Report": "laboratory",
                      "Procedure Report": "procedure",
                      "Radiology Report": "radiology",
                      "Referral Note": "referral",
                    };
                    form.setValue(
                      "category",
                      categoryMap[value] ||
                        value.toLowerCase().replace(/\s+/g, "-"),
                    );
                  }}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Clinical Document">
                      Clinical Document
                    </SelectItem>
                    <SelectItem value="Diagnostic Report">
                      Diagnostic Report
                    </SelectItem>
                    <SelectItem value="Discharge Summary">
                      Discharge Summary
                    </SelectItem>
                    <SelectItem value="Laboratory Report">
                      Laboratory Report
                    </SelectItem>
                    <SelectItem value="Procedure Report">
                      Procedure Report
                    </SelectItem>
                    <SelectItem value="Radiology Report">
                      Radiology Report
                    </SelectItem>
                    <SelectItem value="Referral Note">Referral Note</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Document Date</FormLabel>
                <DatePicker date={field.value} setDate={field.onChange} />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="current">Current</SelectItem>
                    <SelectItem value="superseded">Superseded</SelectItem>
                    <SelectItem value="entered-in-error">
                      Entered in Error
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="docStatus"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Document Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select document status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="preliminary">Preliminary</SelectItem>
                    <SelectItem value="final">Final</SelectItem>
                    <SelectItem value="amended">Amended</SelectItem>
                    <SelectItem value="entered-in-error">
                      Entered in Error
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="author"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Author</FormLabel>
                <FormControl>
                  <Input placeholder="Author of the document" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="authenticator"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Authenticator</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Person who authenticated the document"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Document description"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Document Content</h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() =>
                append({ attachment: { contentType: "application/pdf" } })
              }
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Document
            </Button>
          </div>

          {fields.map((field, index) => (
            <Card key={field.id} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-4">
                  <h4 className="text-sm font-medium">Document {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => remove(index)}
                    className="h-8 w-8 p-0"
                    disabled={index === 0}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name={`content.${index}.attachment.title`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Document title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`content.${index}.attachment.contentType`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Content Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select content type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="application/pdf">PDF</SelectItem>
                            <SelectItem value="image/jpeg">
                              JPEG Image
                            </SelectItem>
                            <SelectItem value="image/png">PNG Image</SelectItem>
                            <SelectItem value="text/plain">
                              Plain Text
                            </SelectItem>
                            <SelectItem value="application/dicom">
                              DICOM
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="md:col-span-2">
                    <FormLabel>Upload Document</FormLabel>
                    <div className="mt-1 flex items-center">
                      <label
                        htmlFor={`file-upload-${index}`}
                        className="cursor-pointer rounded-md bg-white px-4 py-2 border border-input hover:bg-accent hover:text-accent-foreground flex items-center"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Choose File
                      </label>
                      <input
                        id={`file-upload-${index}`}
                        type="file"
                        className="sr-only"
                        onChange={(e) => handleFileChange(e, index)}
                      />
                      <span className="ml-4 text-sm text-muted-foreground">
                        {files[index]?.name || "No file chosen"}
                      </span>
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name={`content.${index}.format`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Format</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Document format (e.g., HL7 CDA)"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Document Reference"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
