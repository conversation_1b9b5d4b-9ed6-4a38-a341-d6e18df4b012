"use client";

import { useState, useEffect } from "react";
import { Fetch } from "@/services/fetch";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  FileText,
  Download,
  Share,
  File,
  ExternalLink,
} from "lucide-react";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";

interface Attachment {
  contentType: string;
  url?: string;
  title?: string;
  creation?: string;
  data?: string;
  size?: number;
  hash?: string;
}

interface Content {
  attachment: Attachment;
  format?: string;
}

interface DocumentReference {
  id: string;
  status: string;
  docStatus: string;
  type: string;
  typeDisplay: string;
  category: string;
  categoryDisplay: string;
  subject: string;
  date: string;
  author?: string;
  authenticator?: string;
  custodian?: string;
  description?: string;
  securityLabel?: string;
  content: Content[];
  context?: any;
  createdAt: string;
  updatedAt: string;
}

interface DocumentReferenceListProps {
  patientId: string;
  consultationId?: string;
  category?: string;
  refreshTrigger?: number;
}

export function DocumentReferenceList({
  patientId,
  consultationId,
  category,
  refreshTrigger = 0,
}: DocumentReferenceListProps) {
  const [documents, setDocuments] = useState<DocumentReference[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] =
    useState<DocumentReference | null>(null);

  useEffect(() => {
    const fetchDocuments = async () => {
      setLoading(true);
      setError(null);
      try {
        let url = "";
        if (consultationId) {
          url = `/api/document-reference?consultationId=${consultationId}`;
        } else if (category) {
          url = `/api/document-reference?patientId=${patientId}&category=${category}`;
        } else {
          url = `/api/document-reference?patientId=${patientId}`;
        }

        const response = await Fetch.get(url);

        if (response.error) {
          throw new Error(response.error);
        }

        setDocuments(response.documents || []);
      } catch (err) {
        console.error("Error fetching document references:", err);
        setError("Failed to load document references");
        toast.error("Failed to load document references");
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [patientId, consultationId, category, refreshTrigger]);

  const handleExportFhir = async (documentId: string) => {
    try {
      const response = await Fetch.get(
        `/api/document-reference/${documentId}/fhir`,
      );

      if (response.error) {
        throw new Error(response.error);
      }

      // Create a downloadable JSON file
      const blob = new Blob([JSON.stringify(response, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `document-${documentId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success("FHIR resource exported successfully");
    } catch (err) {
      console.error("Error exporting FHIR resource:", err);
      toast.error("Failed to export FHIR resource");
    }
  };

  const handleShareWithAbdm = async (documentId: string) => {
    try {
      const response = await Fetch.post("/api/abdm/health-record/package", {
        patientId,
        recordType: "documentReference",
        recordId: documentId,
      });

      if (response.error) {
        throw new Error(response.error);
      }

      toast.success("Document packaged for ABDM sharing");
    } catch (err) {
      console.error("Error packaging document for ABDM:", err);
      toast.error("Failed to package document for ABDM");
    }
  };

  const handleViewDocument = (document: DocumentReference) => {
    setSelectedDocument(document);

    // If the document has a URL, open it in a new tab
    if (document.content[0]?.attachment?.url) {
      window.open(document.content[0].attachment.url, "_blank");
    }

    // If the document has base64 data, create a blob and open it
    if (document.content[0]?.attachment?.data) {
      const contentType =
        document.content[0].attachment.contentType ||
        "application/octet-stream";
      const byteCharacters = atob(document.content[0].attachment.data);
      const byteNumbers = new Array(byteCharacters.length);

      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: contentType });
      const url = URL.createObjectURL(blob);

      window.open(url, "_blank");

      // Clean up the URL after opening
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 100);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "current":
        return "default";
      case "superseded":
        return "secondary";
      case "entered-in-error":
        return "destructive";
      default:
        return "outline";
    }
  };

  const getDocStatusBadgeVariant = (docStatus: string) => {
    switch (docStatus) {
      case "preliminary":
        return "secondary";
      case "final":
        return "default";
      case "amended":
        return "outline";
      case "entered-in-error":
        return "destructive";
      default:
        return "outline";
    }
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith("image/")) {
      return <File className="h-4 w-4" />;
    } else if (contentType === "application/pdf") {
      return <FileText className="h-4 w-4" />;
    } else {
      return <File className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="p-4 text-center">
        <p className="text-muted-foreground">No documents found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map((document) => (
            <TableRow key={document.id}>
              <TableCell className="font-medium">
                <div className="flex items-center">
                  {document.content[0]?.attachment &&
                    getFileIcon(document.content[0].attachment.contentType)}
                  <span className="ml-2">
                    {document.content[0]?.attachment?.title ||
                      document.typeDisplay}
                  </span>
                </div>
              </TableCell>
              <TableCell>{document.categoryDisplay}</TableCell>
              <TableCell>{formatDate(new Date(document.date))}</TableCell>
              <TableCell>
                <div className="flex flex-col space-y-1">
                  <Badge variant={getStatusBadgeVariant(document.status)}>
                    {document.status}
                  </Badge>
                  <Badge variant={getDocStatusBadgeVariant(document.docStatus)}>
                    {document.docStatus}
                  </Badge>
                </div>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedDocument(document)}
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        Details
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle className="flex items-center">
                          <FileText className="h-5 w-5 mr-2 text-primary" />
                          Document Details
                        </DialogTitle>
                        <DialogDescription>
                          {document.content[0]?.attachment?.title ||
                            document.typeDisplay}{" "}
                          - {formatDate(new Date(document.date))}
                        </DialogDescription>
                      </DialogHeader>
                      {selectedDocument && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h4 className="text-sm font-medium">Type</h4>
                              <p>{selectedDocument.typeDisplay}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Category</h4>
                              <p>{selectedDocument.categoryDisplay}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Status</h4>
                              <p className="capitalize">
                                {selectedDocument.status}
                              </p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">
                                Document Status
                              </h4>
                              <p className="capitalize">
                                {selectedDocument.docStatus}
                              </p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Date</h4>
                              <p>
                                {formatDate(new Date(selectedDocument.date))}
                              </p>
                            </div>
                            {selectedDocument.author && (
                              <div>
                                <h4 className="text-sm font-medium">Author</h4>
                                <p>{selectedDocument.author}</p>
                              </div>
                            )}
                            {selectedDocument.authenticator && (
                              <div>
                                <h4 className="text-sm font-medium">
                                  Authenticator
                                </h4>
                                <p>{selectedDocument.authenticator}</p>
                              </div>
                            )}
                            {selectedDocument.custodian && (
                              <div>
                                <h4 className="text-sm font-medium">
                                  Custodian
                                </h4>
                                <p>{selectedDocument.custodian}</p>
                              </div>
                            )}
                          </div>

                          {selectedDocument.description && (
                            <div>
                              <h4 className="text-sm font-medium">
                                Description
                              </h4>
                              <p className="whitespace-pre-wrap">
                                {selectedDocument.description}
                              </p>
                            </div>
                          )}

                          {selectedDocument.content &&
                            selectedDocument.content.length > 0 && (
                              <div>
                                <h4 className="text-sm font-medium mb-2">
                                  Content
                                </h4>
                                <div className="space-y-4">
                                  {selectedDocument.content.map(
                                    (content, index) => (
                                      <div
                                        key={index}
                                        className="border rounded-md p-3 bg-muted/30"
                                      >
                                        <div className="grid grid-cols-2 gap-2">
                                          {content.attachment.title && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Title
                                              </h5>
                                              <p>{content.attachment.title}</p>
                                            </div>
                                          )}
                                          <div>
                                            <h5 className="text-xs font-medium">
                                              Content Type
                                            </h5>
                                            <p>
                                              {content.attachment.contentType}
                                            </p>
                                          </div>
                                          {content.attachment.creation && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Creation Date
                                              </h5>
                                              <p>
                                                {formatDate(
                                                  new Date(
                                                    content.attachment.creation,
                                                  ),
                                                )}
                                              </p>
                                            </div>
                                          )}
                                          {content.attachment.size && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Size
                                              </h5>
                                              <p>
                                                {Math.round(
                                                  content.attachment.size /
                                                    1024,
                                                )}{" "}
                                                KB
                                              </p>
                                            </div>
                                          )}
                                          {content.format && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Format
                                              </h5>
                                              <p>{content.format}</p>
                                            </div>
                                          )}
                                        </div>
                                        <div className="mt-2">
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() =>
                                              handleViewDocument(
                                                selectedDocument,
                                              )
                                            }
                                          >
                                            <ExternalLink className="h-4 w-4 mr-1" />
                                            View Document
                                          </Button>
                                        </div>
                                      </div>
                                    ),
                                  )}
                                </div>
                              </div>
                            )}

                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              onClick={() =>
                                handleExportFhir(selectedDocument.id)
                              }
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Export FHIR
                            </Button>
                            <Button
                              onClick={() =>
                                handleShareWithAbdm(selectedDocument.id)
                              }
                            >
                              <Share className="h-4 w-4 mr-1" />
                              Share with ABDM
                            </Button>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDocument(document)}
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    View
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExportFhir(document.id)}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    FHIR
                  </Button>

                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleShareWithAbdm(document.id)}
                  >
                    <Share className="h-4 w-4 mr-1" />
                    ABDM
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
