"use client";

import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, MapPin } from "lucide-react";
import { useBranch } from "@/contexts/branch-context";
import { useRouter } from "next/navigation";
import { RoleDisplay } from "@/components/role-display";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export function BranchSwitcher() {
  const {
    currentBranch,
    branches,
    setCurrentBranch,
    isLoading,
    refreshBranches,
  } = useBranch();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newBranchData, setNewBranchData] = useState({
    name: "",
    facilityType: "clinic",
    phone: "",
    email: "",
    address: "",
    city: "",
    state: "",
    pincode: "",
  });
  const [isCreating, setIsCreating] = useState(false);
  const router = useRouter();

  // Refresh branches when component mounts
  useEffect(() => {
    if (isLoading && branches.length === 0) {
      refreshBranches();
    }
  }, [isLoading, branches.length, refreshBranches]);

  const handleSwitchBranch = async (branchId: string) => {
    const branch = branches.find((b) => b.id === branchId);
    if (branch) {
      await setCurrentBranch(branch);
      router.refresh();
    }
  };

  const handleCreateBranch = async () => {
    if (!newBranchData.name.trim()) return;

    setIsCreating(true);
    try {
      const response = await fetch("/api/branches", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newBranchData),
      });

      if (response.ok) {
        await response.json();
        setIsDialogOpen(false);
        setNewBranchData({
          name: "",
          facilityType: "clinic",
          phone: "",
          email: "",
          address: "",
          city: "",
          state: "",
          pincode: "",
        });

        // Refresh the branches list
        await refreshBranches();
        router.refresh();
      }
    } catch (error) {
      console.error("Error creating branch:", error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewBranchData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (value: string) => {
    setNewBranchData((prev) => ({ ...prev, facilityType: value }));
  };

  if (isLoading || !currentBranch) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="h-9 gap-1 opacity-70 btn-hover-effect"
      >
        <MapPin className="h-4 w-4" />
        <span className="max-w-[150px] truncate">Loading...</span>
      </Button>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {/* Role Display */}
      <RoleDisplay />

      {/* Branch Switcher */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-9 gap-1 hover-card btn-hover-effect"
          >
            <MapPin className="h-4 w-4 text-primary" />
            <span className="max-w-[150px] truncate">{currentBranch.name}</span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[200px]">
          <DropdownMenuLabel>Branches</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {branches.map((branch) => (
            <DropdownMenuItem
              key={branch.id}
              onClick={() => handleSwitchBranch(branch.id)}
              className={
                branch.id === currentBranch.id
                  ? "bg-accent text-accent-foreground"
                  : ""
              }
            >
              <MapPin className="mr-2 h-4 w-4" />
              <span className="truncate">{branch.name}</span>
              {branch.isHeadOffice && (
                <span className="ml-2 text-xs text-muted-foreground">(HQ)</span>
              )}
            </DropdownMenuItem>
          ))}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Add New Branch</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Branch Name</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Enter branch name"
                    value={newBranchData.name}
                    onChange={handleInputChange}
                    className="h-10"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="facilityType">Facility Type</Label>
                  <Select
                    value={newBranchData.facilityType}
                    onValueChange={handleSelectChange}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="Select facility type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="clinic">Clinic</SelectItem>
                      <SelectItem value="hospital">Hospital</SelectItem>
                      <SelectItem value="laboratory">Laboratory</SelectItem>
                      <SelectItem value="pharmacy">Pharmacy</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      placeholder="Phone number"
                      value={newBranchData.phone}
                      onChange={handleInputChange}
                      className="h-10"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Email address"
                      value={newBranchData.email}
                      onChange={handleInputChange}
                      className="h-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    name="address"
                    placeholder="Street address"
                    value={newBranchData.address}
                    onChange={handleInputChange}
                    className="h-10"
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      name="city"
                      placeholder="City"
                      value={newBranchData.city}
                      onChange={handleInputChange}
                      className="h-10"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      name="state"
                      placeholder="State"
                      value={newBranchData.state}
                      onChange={handleInputChange}
                      className="h-10"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="pincode">Pincode</Label>
                    <Input
                      id="pincode"
                      name="pincode"
                      placeholder="Pincode"
                      value={newBranchData.pincode}
                      onChange={handleInputChange}
                      className="h-10"
                    />
                  </div>
                </div>

                <Button
                  onClick={handleCreateBranch}
                  disabled={!newBranchData.name.trim() || isCreating}
                  className="w-full btn-hover-effect"
                >
                  {isCreating ? (
                    <>
                      <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></span>
                      Creating...
                    </>
                  ) : (
                    "Create Branch"
                  )}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
