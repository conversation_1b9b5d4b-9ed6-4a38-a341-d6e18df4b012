"use client";

import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import {
  Building,
  ChevronDown,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import { useOrganization } from "@/contexts/organization-context";

export function OrganizationHeaderSwitcher() {
  const {
    currentOrganization,
    organizations,
    setCurrentOrganization,
    isLoading,
    refreshOrganizations,
  } = useOrganization();
  const [isSwitching, setIsSwitching] = useState(false);

  // Refresh organizations when component mounts
  useEffect(() => {
    // If we're loading or have no organization, try to refresh
    if ((isLoading || !currentOrganization) && organizations.length === 0) {
      refreshOrganizations();
    }
  }, [
    isLoading,
    currentOrganization,
    organizations.length,
    refreshOrganizations,
  ]);

  const handleSwitchOrganization = async (orgId: string) => {
    const org = organizations.find((o) => o.id === orgId);
    if (org) {
      setIsSwitching(true);
      try {
        // Use the switch-context API which handles all cookie updates properly
        const response = await fetch("/api/user/switch-context", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            organizationId: org.id,
            // Let the API determine the default role and branch
          }),
        });

        if (response.ok) {
          setCurrentOrganization(org);
          // Dispatch custom event to notify other components
          window.dispatchEvent(new CustomEvent("organizationChanged", { detail: org }));
          // Force a full page reload to ensure all contexts are updated
          window.location.reload();
        } else {
          const errorData = await response.json();
          if (errorData.organizationDeactivated) {
            // Redirect to organization deactivated page
            window.location.href = `/organization-deactivated?orgId=${errorData.organizationId}&orgName=${encodeURIComponent(errorData.organizationName)}`;
          } else {
            console.error("Failed to switch organization:", errorData);
            setIsSwitching(false);
          }
        }
      } catch (error) {
        console.error("Error switching organization:", error);
        setIsSwitching(false);
      }
    }
  };

  // Show loading state
  if (isLoading || !currentOrganization) {
    return (
      <div className="text-xl font-semibold gradient-text">Loading...</div>
    );
  }

  // If user has multiple organizations, show dropdown
  if (organizations.length > 1) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="h-auto p-0 hover:bg-transparent border-none focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            disabled={isSwitching}
          >
            <span className="text-xl font-semibold gradient-text">
              {isSwitching ? "..." : (currentOrganization.name)}
            </span>
            <ChevronDown className="ml-2 h-4 w-4 text-muted-foreground" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[250px]">
          <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {organizations.map((org) => (
            <DropdownMenuItem
              key={org.id}
              onClick={() => handleSwitchOrganization(org.id)}
              disabled={isSwitching}
              className={`${
                org.id === currentOrganization.id
                  ? "bg-accent text-accent-foreground"
                  : ""
              } ${org.status === "inactive" ? "opacity-60" : ""}`}
            >
              <Building className="mr-2 h-4 w-4" />
              <div className="flex-1 flex items-center justify-between">
                <span className="truncate">{org.name}</span>
                <div className="flex items-center gap-1 ml-2">
                  {org.status === "active" ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-3 w-3 text-orange-500" />
                  )}
                  <span
                    className={`text-xs ${
                      org.status === "active"
                        ? "text-green-600"
                        : "text-orange-600"
                    }`}
                  >
                    {org.status === "active" ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // If user has only one organization, just show the name
  return (
    <div className="text-xl font-semibold gradient-text">
      {currentOrganization.name}
    </div>
  );
}
