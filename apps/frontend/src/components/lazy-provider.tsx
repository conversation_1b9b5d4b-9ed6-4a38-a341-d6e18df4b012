"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";

interface LazyProviderProps {
  children: ReactNode;
  Provider: React.ComponentType<{ children: ReactNode }>;
  contextName: string;
}

// Create a context to track which contexts are being used
export const ContextUsageContext = createContext<{
  markContextAsUsed: (contextName: string) => void;
  usedContexts: Set<string>;
}>({
  markContextAsUsed: () => {},
  usedContexts: new Set(),
});

export function ContextUsageProvider({ children }: { children: ReactNode }) {
  const [usedContexts, setUsedContexts] = useState<Set<string>>(new Set());

  const markContextAsUsed = (contextName: string) => {
    setUsedContexts((prev) => {
      const newSet = new Set(prev);
      newSet.add(contextName);
      return newSet;
    });
  };

  return (
    <ContextUsageContext.Provider value={{ markContextAsUsed, usedContexts }}>
      {children}
    </ContextUsageContext.Provider>
  );
}

export function LazyProvider({
  children,
  Provider,
  contextName,
}: LazyProviderProps) {
  const { usedContexts } = useContext(ContextUsageContext);
  const [shouldRender, setShouldRender] = useState(false);

  // Check if this context is being used
  useEffect(() => {
    // If the context is being used, render the provider
    if (usedContexts.has(contextName)) {
      setShouldRender(true);
    }
  }, [usedContexts, contextName]);

  // If the context is not being used, just render the children without the provider
  if (!shouldRender) {
    return <>{children}</>;
  }

  // If the context is being used, render the provider
  return <Provider>{children}</Provider>;
}

// Higher-order function to create a hook that marks a context as used
export function createLazyContextHook<T>(
  useOriginalHook: () => T,
  contextName: string,
): () => T {
  return () => {
    const { markContextAsUsed } = useContext(ContextUsageContext);

    // Mark this context as used
    useEffect(() => {
      markContextAsUsed(contextName);
    }, []);

    // Use the original hook
    return useOriginalHook();
  };
}
