"use client";

import React, { useCallback } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  HomeIcon,
  UsersIcon,
  CalendarIcon,
  ClockIcon,
  MenuIcon,
  FileText as FileTextIcon,
  Activity as ActivityIcon,
  Pill as PillIcon,
  StethoscopeIcon,
  ListIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useUserRole } from "@/hooks/use-user-role";

interface MenuGroup {
  title: string;
  icon: React.ReactNode;
  items: MenuItem[];
}

interface MenuItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  indented?: boolean;
}

interface DoctorSidebarNavProps {
  id?: string;
  className?: string;
}

// Export the MobileMenuTrigger component for use in the header
export const DoctorMobileMenuTrigger = () => {
  const { isDoctor } = useUserRole();

  if (!isDoctor) return null;

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <MenuIcon className="h-6 w-6" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[300px] sm:w-[350px] p-0">
        <div className="h-full overflow-y-auto">
          <DoctorSidebarContent />
        </div>
      </SheetContent>
    </Sheet>
  );
};

// Extract the sidebar content to a separate component for reuse
export const DoctorSidebarContent = () => {
  const pathname = usePathname();

  // Function to check if a route is active
  const isRouteActive = useCallback(
    (href: string) => {
      return pathname === href || pathname?.startsWith(`${href}/`);
    },
    [pathname],
  );

  // Define the menu structure
  const menuGroups: MenuGroup[] = [
    {
      title: "Daily Operations",
      icon: <ClockIcon className="h-5 w-5" />,
      items: [
        {
          title: "Appointment List",
          href: "/appointments",
          icon: <CalendarIcon className="h-5 w-5" />,
        },
        {
          title: "Doctor Appointment",
          href: "/doctor-appointments",
          icon: <CalendarIcon className="h-5 w-5" />,
        },
        {
          title: "Queue",
          href: "/queue",
          icon: <ListIcon className="h-5 w-5" />,
        },
      ],
    },
    {
      title: "Clinical",
      icon: <StethoscopeIcon className="h-5 w-5" />,
      items: [
        {
          title: "Consultations",
          href: "/consultations",
          icon: <FileTextIcon className="h-5 w-5" />,
        },
        {
          title: "Vitals",
          href: "/vitals",
          icon: <ActivityIcon className="h-5 w-5" />,
        },
        {
          title: "Prescriptions",
          href: "/prescriptions",
          icon: <PillIcon className="h-5 w-5" />,
        },
      ],
    },
    {
      title: "Patients",
      icon: <UsersIcon className="h-5 w-5" />,
      items: [
        {
          title: "My Patients",
          href: "/patients",
          icon: <UsersIcon className="h-5 w-5" />,
        },
      ],
    },
  ];

  return (
    <>
      <div className="p-6">
        <h2 className="text-2xl font-bold text-primary">Aran Care</h2>
      </div>

      <nav className="px-4 space-y-4">
        {/* Dashboard */}
        <Button
          variant={isRouteActive("/doctor-dashboard") ? "secondary" : "default"}
          className={cn(
            "w-full justify-start mb-2",
            isRouteActive("/doctor-dashboard") && "font-medium",
          )}
          asChild
        >
          <Link href="/doctor-dashboard">
            <HomeIcon className="mr-2 h-5 w-5" />
            Clinical Dashboard
          </Link>
        </Button>

        {/* Menu Groups */}
        {menuGroups.map((group) => (
          <div key={group.title} className="space-y-2 mt-8 first:mt-4">
            {/* Group Header - Now just a label */}
            <div className="px-2 py-2">
              <h3 className="text-xs font-bold text-muted-foreground uppercase tracking-wider">
                {group.title}
              </h3>
            </div>

            {/* Group Items - Always visible */}
            <div className="space-y-1.5 pl-2">
              {group.items.map((item) => (
                <Button
                  key={item.title}
                  variant={isRouteActive(item.href) ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    item.indented && "pl-8",
                    isRouteActive(item.href) && "font-medium",
                  )}
                  asChild
                >
                  <Link href={item.href}>
                    {item.icon}
                    <span className="ml-2">{item.title}</span>
                  </Link>
                </Button>
              ))}
            </div>
          </div>
        ))}
      </nav>
    </>
  );
};

export function DoctorSidebarNav({ id, className }: DoctorSidebarNavProps) {
  // Return the desktop sidebar
  return (
    <div
      className={cn(
        "hidden md:block w-64 h-full bg-background dark:bg-gray-900 border-r overflow-y-auto crm-scrollbar",
        className,
      )}
      id={id}
    >
      <DoctorSidebarContent />
    </div>
  );
}
