"use client";

import React, { useCallback } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  HomeIcon,
  UsersIcon,
  SettingsIcon,
  UserIcon,
  CalendarIcon,
  BuildingIcon,
  FolderIcon,
  TagIcon,
  StethoscopeIcon,
  UserCogIcon,
  ClockIcon,
  UserPlusIcon,
  ListIcon,
  MenuIcon,
  FileText as FileTextIcon,
  Activity as ActivityIcon,
  Pill as PillIcon,
  ShieldIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useUserRole } from "@/hooks/use-user-role";

interface MenuGroup {
  title: string;
  icon: React.ReactNode;
  items: MenuItem[];
}

interface MenuItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  indented?: boolean;
}

interface ClinicAdminSidebarNavProps {
  id?: string;
  className?: string;
}

// Export the MobileMenuTrigger component for use in the header
export const ClinicAdminMobileMenuTrigger = () => {
  const { isBranchAdmin } = useUserRole();

  if (!isBranchAdmin) return null;

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <MenuIcon className="h-6 w-6" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[300px] sm:w-[350px] p-0">
        <div className="h-full overflow-y-auto">
          <ClinicAdminSidebarContent />
        </div>
      </SheetContent>
    </Sheet>
  );
};

// Extract the sidebar content to a separate component for reuse
export const ClinicAdminSidebarContent = () => {
  const pathname = usePathname();

  // No longer needed for non-collapsible menu
  // Keeping this commented out for reference
  /*
  const toggleGroup = (groupTitle: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupTitle]: !prev[groupTitle],
    }));
  };
  */

  // Function to check if a route is active
  const isRouteActive = useCallback(
    (href: string) => {
      return pathname === href || pathname?.startsWith(`${href}/`);
    },
    [pathname],
  );

  // Define the menu structure
  const menuGroups: MenuGroup[] = [
    {
      title: "Daily Operations",
      icon: <ClockIcon className="h-5 w-5" />,
      items: [
        {
          title: "Appointment List",
          href: "/appointments",
          icon: <CalendarIcon className="h-5 w-5" />,
        },
        {
          title: "Doctor Appointment",
          href: "/doctor-appointments",
          icon: <CalendarIcon className="h-5 w-5" />,
        },
        {
          title: "Queue",
          href: "/queue",
          icon: <ListIcon className="h-5 w-5" />,
        },
        {
          title: "Walk-ins",
          href: "/walk-ins",
          icon: <UserPlusIcon className="h-5 w-5" />,
        },
      ],
    },
    {
      title: "Clinical",
      icon: <StethoscopeIcon className="h-5 w-5" />,
      items: [
        {
          title: "Consultations",
          href: "/consultations",
          icon: <FileTextIcon className="h-5 w-5" />,
        },
        {
          title: "Vitals",
          href: "/vitals",
          icon: <ActivityIcon className="h-5 w-5" />,
        },
        {
          title: "Prescriptions",
          href: "/prescriptions",
          icon: <PillIcon className="h-5 w-5" />,
        },
      ],
    },
    {
      title: "Management",
      icon: <UsersIcon className="h-5 w-5" />,
      items: [
        {
          title: "Patients",
          href: "/patients",
          icon: <UserIcon className="h-5 w-5" />,
        },
        {
          title: "Doctors",
          href: "/doctors",
          icon: <StethoscopeIcon className="h-5 w-5" />,
        },
        {
          title: "Doctor Schedules",
          href: "/doctor-schedules",
          icon: <CalendarIcon className="h-5 w-5" />,
        },
        {
          title: "Consent Management",
          href: "/consents",
          icon: <ShieldIcon className="h-5 w-5" />,
        },
      ],
    },
    {
      title: "Branch Administration",
      icon: <BuildingIcon className="h-5 w-5" />,
      items: [
        {
          title: "Department",
          href: "/branch-departments",
          icon: <FolderIcon className="h-5 w-5" />,
        },
        {
          title: "Staff",
          href: "/staff",
          icon: <UserCogIcon className="h-5 w-5" />,
        },
        {
          title: "Branch Admin Invitations",
          href: "/branch-admin-invitations",
          icon: <UserPlusIcon className="h-5 w-5" />,
        },
        {
          title: "Settings",
          href: "/branch-settings",
          icon: <SettingsIcon className="h-5 w-5" />,
        },
      ],
    },
    {
      title: "Clinic Administration",
      icon: <BuildingIcon className="h-5 w-5" />,
      items: [
        {
          title: "Department",
          href: "/departments",
          icon: <FolderIcon className="h-5 w-5" />,
        },
        {
          title: "Care Type",
          href: "/care-types",
          icon: <TagIcon className="h-5 w-5" />,
        },
        {
          title: "Branches",
          href: "/branches",
          icon: <BuildingIcon className="h-5 w-5" />,
        },
        {
          title: "Settings",
          href: "/settings",
          icon: <SettingsIcon className="h-5 w-5" />,
        },
      ],
    },
  ];

  // No longer needed for non-collapsible menu
  // Removing the useEffect hook that was causing infinite updates
  /*
  useEffect(() => {
    menuGroups.forEach((group) => {
      const hasActiveRoute = group.items.some((item) =>
        isRouteActive(item.href)
      );
      if (hasActiveRoute) {
        setExpandedGroups((prev) => ({
          ...prev,
          [group.title]: true,
        }));
      }
    });
  }, [isRouteActive]); // Removed menuGroups from dependencies
  */

  return (
    <>
      <div className="p-6">
        <h2 className="text-2xl font-bold text-primary">Aran Care</h2>
      </div>

      <nav className="px-4 space-y-4">
        {/* Dashboard */}
        <Button
          variant={isRouteActive("/dashboard") ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start",
            isRouteActive("/dashboard") && "font-medium",
          )}
          asChild
        >
          <Link href="/dashboard">
            <HomeIcon className="mr-2 h-5 w-5" />
            Dashboard
          </Link>
        </Button>

        {/* Menu Groups */}
        {menuGroups.map((group) => (
          <div key={group.title} className="space-y-2 mt-8 first:mt-4">
            {/* Group Header - Now just a label */}
            <div className="px-2 py-2">
              <h3 className="text-xs font-bold text-muted-foreground uppercase tracking-wider">
                {group.title}
              </h3>
            </div>

            {/* Group Items - Always visible */}
            <div className="space-y-1.5 pl-2">
              {group.items.map((item) => (
                <Button
                  key={item.title}
                  variant={isRouteActive(item.href) ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    item.indented && "pl-8",
                    isRouteActive(item.href) && "font-medium",
                  )}
                  asChild
                >
                  <Link href={item.href}>
                    {item.icon}
                    <span className="ml-2">{item.title}</span>
                  </Link>
                </Button>
              ))}
            </div>
          </div>
        ))}
      </nav>
    </>
  );
};

export function ClinicAdminSidebarNav({
  id,
  className,
}: ClinicAdminSidebarNavProps) {
  // Return the desktop sidebar
  return (
    <div
      className={cn(
        "hidden md:block w-64 h-full bg-background dark:bg-gray-900 border-r overflow-y-auto crm-scrollbar",
        className,
      )}
      id={id}
    >
      <ClinicAdminSidebarContent />
    </div>
  );
}
