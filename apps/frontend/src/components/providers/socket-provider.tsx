"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { Socket } from "socket.io-client";
import {
  connectSocket,
  disconnectSocket,
  getSocket,
  joinOrganization,
  joinBranch,
  joinDoctor,
  getAllLastKnownQueueStates,
} from "@/lib/socket-client";

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  joinRooms: (
    organizationId: string,
    branchId?: string,
    doctorId?: string,
  ) => void;
  lastConnectionTime: Date | null;
  reconnectionAttempts: number;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  joinRooms: () => {},
  lastConnectionTime: null,
  reconnectionAttempts: 0,
});

export const useSocket = () => useContext(SocketContext);

export function SocketProvider({ children }: { children: React.ReactNode }) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [lastConnectionTime, setLastConnectionTime] = useState<Date | null>(
    null,
  );
  const [reconnectionAttempts, setReconnectionAttempts] = useState(0);

  // Function to join rooms (organization, branch, doctor)
  const joinRooms = useCallback(
    (organizationId: string, branchId?: string, doctorId?: string) => {
      if (organizationId) {
        joinOrganization(organizationId);
        console.log(`Joined organization room: ${organizationId}`);
      }

      if (branchId) {
        joinBranch(branchId);
        console.log(`Joined branch room: ${branchId}`);
      }

      if (doctorId) {
        joinDoctor(doctorId);
        console.log(`Joined doctor room: ${doctorId}`);
      }
    },
    [],
  );

  useEffect(() => {
    // Initialize socket
    const socketInstance = getSocket();
    setSocket(socketInstance);

    // Connect to socket server
    connectSocket();

    // Set up event listeners
    const onConnect = () => {
      console.log("Socket connected in provider");
      setIsConnected(true);
      setLastConnectionTime(new Date());

      // Reset reconnection attempts on successful connection
      setReconnectionAttempts(0);

      // Check if we have any pending queue updates to send
      const pendingUpdates = getAllLastKnownQueueStates();
      if (Object.keys(pendingUpdates).length > 0) {
        console.log(
          `Found ${
            Object.keys(pendingUpdates).length
          } pending queue updates to sync`,
        );
      }
    };

    const onDisconnect = (reason: string) => {
      console.log(`Socket disconnected in provider: ${reason}`);
      setIsConnected(false);
    };

    const onReconnectAttempt = (attemptNumber: number) => {
      console.log(`Socket reconnection attempt ${attemptNumber}`);
      setReconnectionAttempts(attemptNumber);
    };

    const onReconnect = (attemptNumber: number) => {
      console.log(`Socket reconnected after ${attemptNumber} attempts`);
      setIsConnected(true);
      setLastConnectionTime(new Date());
      setReconnectionAttempts(0);
    };

    socketInstance.on("connect", onConnect);
    socketInstance.on("disconnect", onDisconnect);
    socketInstance.on("reconnect_attempt", onReconnectAttempt);
    socketInstance.on("reconnect", onReconnect);

    // Update initial connection state
    setIsConnected(socketInstance.connected);
    if (socketInstance.connected) {
      setLastConnectionTime(new Date());
    }

    // Clean up on unmount
    return () => {
      socketInstance.off("connect", onConnect);
      socketInstance.off("disconnect", onDisconnect);
      socketInstance.off("reconnect_attempt", onReconnectAttempt);
      socketInstance.off("reconnect", onReconnect);
      disconnectSocket();
    };
  }, []);

  return (
    <SocketContext.Provider
      value={{
        socket,
        isConnected,
        joinRooms,
        lastConnectionTime,
        reconnectionAttempts,
      }}
    >
      {children}
    </SocketContext.Provider>
  );
}
