"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { AlertCircle, CheckCircle2, Loader2, RefreshCw } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";

interface AbhaLoginFormProps {
  abhaNumber: string;
  patientId?: string;
  onLoginSuccess?: () => void;
  onCancel?: () => void;
}

export function AbhaLoginForm({
  abhaNumber,
  patientId,
  onLoginSuccess,
  onCancel,
}: AbhaLoginFormProps) {
  // State for ABHA login flow
  const [otp, setOtp] = useState("");
  const [txnId, setTxnId] = useState("");
  const [step, setStep] = useState<"request-otp" | "verify-otp" | "success">(
    "request-otp",
  );
  const [isRequestingOtp, setIsRequestingOtp] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Resend OTP state
  const [resendAttempts, setResendAttempts] = useState(0);
  const [resendTimeRemaining, setResendTimeRemaining] = useState(0);
  const [isResendTimerActive, setIsResendTimerActive] = useState(false);

  // Timer refs
  const resendTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Format time for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Resend timer effect
  useEffect(() => {
    if (isResendTimerActive && resendTimeRemaining > 0) {
      resendTimerRef.current = setTimeout(() => {
        setResendTimeRemaining((prev) => prev - 1);
      }, 1000);
    } else if (resendTimeRemaining === 0) {
      setIsResendTimerActive(false);
    }

    return () => {
      if (resendTimerRef.current) {
        clearTimeout(resendTimerRef.current);
      }
    };
  }, [isResendTimerActive, resendTimeRemaining]);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (resendTimerRef.current) {
        clearTimeout(resendTimerRef.current);
      }
    };
  }, []);
  // Function to request OTP
  const handleRequestOtp = async () => {
    setError(null);
    setIsRequestingOtp(true);

    try {
      const response = await fetch("/api/abdm/abha-login/request-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ abhaNumber }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to request OTP");
      }

      setTxnId(data.txnId);
      setStep("verify-otp");

      // Reset resend attempts when generating OTP for the first time
      setResendAttempts(0);

      // Start the resend cooldown timer (60 seconds)
      setResendTimeRemaining(60);
      setIsResendTimerActive(true);

      toast.success("OTP sent successfully to your registered mobile number");
    } catch (error) {
      console.error("Error requesting OTP:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to request OTP. Please try again.",
      );
    } finally {
      setIsRequestingOtp(false);
    }
  };

  // Function to resend OTP
  const handleResendOtp = async () => {
    // Don't allow resend if timer is still active or maximum attempts reached
    if (isResendTimerActive) {
      toast.error(
        `Please wait ${resendTimeRemaining} seconds before resending OTP`,
        {
          duration: 3000,
          position: "top-center",
        },
      );
      return;
    }

    if (resendAttempts >= 2) {
      toast.error("Maximum resend attempts reached", {
        duration: 3000,
        position: "top-center",
      });
      return;
    }

    setError(null);
    setIsRequestingOtp(true);

    try {
      const response = await fetch("/api/abdm/abha-login/request-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ abhaNumber }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to resend OTP");
      }

      setTxnId(data.txnId);

      // Increment resend attempts
      const newResendAttempts = resendAttempts + 1;
      setResendAttempts(newResendAttempts);

      // Start the resend cooldown timer (60 seconds)
      setResendTimeRemaining(60);
      setIsResendTimerActive(true);

      // Show appropriate toast message based on remaining attempts
      const remainingAttempts = 2 - newResendAttempts;
      const successMessage =
        remainingAttempts > 0
          ? `OTP resent successfully. ${remainingAttempts} resend attempt${remainingAttempts === 1 ? "" : "s"} remaining.`
          : "OTP resent successfully. No more resend attempts available.";

      toast.success(successMessage, {
        duration: 4000,
        position: "top-center",
      });
    } catch (error) {
      console.error("Error resending OTP:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to resend OTP. Please try again.",
      );
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to resend OTP. Please try again.",
        {
          duration: 4000,
          position: "top-center",
        },
      );
    } finally {
      setIsRequestingOtp(false);
    }
  };

  // Function to verify OTP
  const handleVerifyOtp = async () => {
    // Validate OTP
    if (!otp || otp.length !== 6 || !/^\d+$/.test(otp)) {
      setError("Please enter a valid 6-digit OTP");
      return;
    }

    setError(null);
    setIsVerifyingOtp(true);

    try {
      console.log("Starting ABHA login OTP verification with payload:", {
        otp: "REDACTED",
        txnId,
        patientId: patientId || "",
      });

      const response = await fetch("/api/abdm/abha-login/verify-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          otp,
          txnId,
          patientId: patientId || "", // Pass patientId if available
        }),
      });

      const data = await response.json();

      console.log("ABHA login OTP verification response:", {
        ok: response.ok,
        status: response.status,
        hasData: !!data,
      });

      // Enhanced error checking
      if (!response.ok) {
        const errorMessage =
          data.error || data.message || "Failed to verify OTP";
        console.error("ABHA login OTP verification failed:", errorMessage);
        throw new Error(errorMessage);
      }

      // Additional validation - ensure we have the required data
      if (!data || (data.authResult && data.authResult === "Failed")) {
        const errorMessage =
          data.message ||
          "OTP verification failed. Please check your OTP and try again.";
        console.error(
          "ABHA login OTP verification failed with authResult:",
          data.authResult,
        );
        throw new Error(errorMessage);
      }

      console.log("ABHA login OTP verification successful");

      setStep("success");
      toast.success("ABHA login successful");

      // If we're in the context of a patient view (with patientId), refresh the page
      // Otherwise, proceed with the normal flow
      if (patientId) {
        // Use a small timeout to ensure the toast is visible before refresh
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else if (onLoginSuccess) {
        // If no patientId but we have a success callback, call it
        onLoginSuccess();
      }
    } catch (error) {
      console.error("Error verifying OTP:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to verify OTP. Please try again.",
      );
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto pb-6">
      <CardHeader>
        <CardTitle>ABHA Login</CardTitle>
        <CardDescription>
          Login to your ABHA (Ayushman Bharat Health Account) to view your ABHA
          card
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {step === "request-otp" && (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="abhaNumber">ABHA Number</Label>
              <Input
                id="abhaNumber"
                value={abhaNumber}
                disabled
                className="bg-muted"
              />
              <p className="text-sm text-muted-foreground">
                You will receive an OTP on your registered mobile number
              </p>
            </div>
            <Button
              onClick={handleRequestOtp}
              disabled={isRequestingOtp}
              className="w-full"
            >
              {isRequestingOtp && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Request OTP
            </Button>
          </div>
        )}

        {step === "verify-otp" && (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="otp">Enter OTP</Label>
              <Input
                id="otp"
                placeholder="Enter 6-digit OTP"
                value={otp}
                onChange={(e) =>
                  setOtp(e.target.value.replace(/\D/g, "").slice(0, 6))
                }
                maxLength={6}
                disabled={isVerifyingOtp}
              />
              <p className="text-sm text-muted-foreground">
                OTP has been sent to your registered mobile number
              </p>
            </div>

            {/* Resend OTP Section */}
            <div className="flex justify-center">
              {isResendTimerActive ? (
                <span className="text-sm text-muted-foreground italic">
                  Wait {formatTime(resendTimeRemaining)} to resend (
                  {2 - resendAttempts} attempt
                  {2 - resendAttempts === 1 ? "" : "s"} left)
                </span>
              ) : resendAttempts >= 2 ? (
                <span className="text-sm text-red-500 italic">
                  Maximum resend attempts reached
                </span>
              ) : (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleResendOtp}
                  disabled={
                    isRequestingOtp ||
                    isVerifyingOtp ||
                    isResendTimerActive ||
                    resendAttempts >= 2
                  }
                  className="h-8 px-3 text-xs font-medium"
                >
                  {isRequestingOtp ? (
                    <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 h-3 w-3" />
                  )}
                  Resend OTP
                </Button>
              )}
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setStep("request-otp");
                  setOtp("");
                  setTxnId("");
                  setError(null);
                  // Clear the timer
                  if (resendTimerRef.current) {
                    clearTimeout(resendTimerRef.current);
                  }
                  setIsResendTimerActive(false);
                  setResendAttempts(0);
                }}
                disabled={isVerifyingOtp}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                onClick={handleVerifyOtp}
                disabled={isVerifyingOtp || otp.length !== 6}
                className="flex-1"
              >
                {isVerifyingOtp && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Verify OTP
              </Button>
            </div>
          </div>
        )}

        {step === "success" && (
          <div className="space-y-4">
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Success</AlertTitle>
              <AlertDescription className="text-green-700">
                You have successfully logged in to your ABHA account
                {patientId && (
                  <div className="mt-2">
                    <Loader2 className="inline-block mr-2 h-4 w-4 animate-spin" />
                    Refreshing page to show updated ABHA information...
                  </div>
                )}
              </AlertDescription>
            </Alert>
          </div>
        )}
      </CardContent>
      {step === "success" && (
        <CardFooter>
          <Button
            onClick={() => onLoginSuccess && onLoginSuccess()}
            className="w-full"
          >
            Continue to View ABHA Card
          </Button>
        </CardFooter>
      )}
      {step !== "success" && onCancel && (
        <CardFooter>
          <Button
            variant="outline"
            onClick={onCancel}
            className="w-full"
            disabled={isRequestingOtp || isVerifyingOtp}
          >
            Cancel
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
