"use client";

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/contexts/doctor-context";
import { DepartmentProvider } from "@/contexts/department-context";

interface DoctorLayoutProps {
  children: React.ReactNode;
}

export function DoctorLayout({ children }: DoctorLayoutProps) {
  return (
    <DepartmentProvider>
      <DoctorProvider>{children}</DoctorProvider>
    </DepartmentProvider>
  );
}
