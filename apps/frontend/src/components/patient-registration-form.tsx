"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Resolver, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useBranch } from "@/contexts/branch-context";
import { toast } from "sonner";
import { VALIDATION_PATTERNS } from "@/lib/validation";
import {
  AlertCircle,
  CheckCircle,
  Loader2,
  ArrowRight,
  Info,
  Phone,
} from "lucide-react";
import {
  searchPatientByPhone,
  searchAbhaByMobile,
} from "@/services/patient-service";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Define the form schema with proper validation patterns
const patientFormSchema = z.object({
  // Personal Information
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  dateOfBirth: z
    .string()
    .min(1, "Date of birth is required")
    .regex(
      VALIDATION_PATTERNS.DATE_OF_BIRTH,
      "Please enter a valid date in YYYY-MM-DD format",
    ),
  gender: z.string().min(1, "Gender is required"),
  bloodGroup: z.string().optional(),
  maritalStatus: z.string().optional(),
  occupation: z.string().optional(),

  // ABDM Information is now handled in a separate model

  // Contact Information
  email: z
    .string()
    .optional()
    .or(z.literal(""))
    .refine((val) => !val || VALIDATION_PATTERNS.EMAIL.test(val), {
      message: "Please enter a valid email address",
    }),
  phone: z
    .string()
    .min(1, "Aadhaar-linked mobile number is required")
    .regex(
      VALIDATION_PATTERNS.MOBILE,
      "Please enter a valid mobile number (e.g., **********, +************)",
    ),
  communicationMobile: z
    .string()
    .optional()
    .refine((val) => !val || VALIDATION_PATTERNS.MOBILE.test(val), {
      message: "Please enter a valid communication mobile number",
    }),
  alternatePhone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pincode: z.string().optional(),
  country: z.string().default("India"),

  // Medical Information
  allergies: z.string().optional(),
  chronicDiseases: z.string().optional(),
  currentMedications: z.string().optional(),
  familyMedicalHistory: z.string().optional(),

  // Emergency Contact
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
  emergencyContactRelation: z.string().optional(),

  // Insurance Information
  insuranceProvider: z.string().optional(),
  insurancePolicyNumber: z.string().optional(),
  insuranceExpiryDate: z.string().optional(),

  // System Fields
  status: z.string().default("active"),
  primaryBranchId: z.string().min(1, "Branch is required"),
});

type PatientFormValues = z.infer<typeof patientFormSchema>;

interface PatientRegistrationFormProps {
  onSuccess?: (patientId: string) => void;
  patient?: any; // Add patient prop for edit mode
  mode?: "create" | "edit"; // Add mode prop to distinguish between create and edit
  abhaVerified?: boolean; // Flag to indicate if ABHA has been verified
  abhaDetails?: any; // ABHA details from verification
}

export function PatientRegistrationForm({
  onSuccess,
  patient,
  mode = "create",
  abhaVerified = false,
  abhaDetails = null,
}: PatientRegistrationFormProps) {
  // DEBUG: Log what the form component receives
  console.log("=== PATIENT REGISTRATION FORM DEBUG ===");
  console.log("abhaDetails received by form:", abhaDetails);
  console.log(
    "abhaDetails.fromAbhaVerification:",
    abhaDetails?.fromAbhaVerification,
  );
  console.log("abhaVerified flag:", abhaVerified);

  const router = useRouter();
  const { branches, currentBranch } = useBranch();
  const [activeTab, setActiveTab] = useState("personal");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [duplicateWarning, setDuplicateWarning] = useState<any>(null);
  const [isSearchingAbha, setIsSearchingAbha] = useState(false);
  const [abhaSearchResult, setAbhaSearchResult] = useState<any>(null);
  const [abhaSearchError, setAbhaSearchError] = useState<string | null>(null);
  const [showAbhaVerifyDialog, setShowAbhaVerifyDialog] = useState(false);
  const [abhaNumber, setAbhaNumber] = useState<string | null>(null);
  const [selectedAbhaAddress, setSelectedAbhaAddress] = useState<string | null>(
    null,
  );

  // Initialize form with patient data if in edit mode
  const form = useForm<PatientFormValues>({
    resolver: zodResolver(patientFormSchema) as Resolver<PatientFormValues>,
    defaultValues:
      mode === "edit" && patient
        ? {
            firstName: patient.firstName,
            lastName: patient.lastName,
            dateOfBirth: new Date(patient.dateOfBirth)
              .toISOString()
              .split("T")[0],
            gender: patient.gender,
            bloodGroup: patient.bloodGroup || "",
            maritalStatus: patient.maritalStatus || "",
            occupation: patient.occupation || "",
            // ABDM Information is now handled in a separate model
            email: patient.email,
            phone: patient.phone,
            communicationMobile: (patient as any).communicationMobile || "",
            alternatePhone: patient.alternatePhone || "",
            address: patient.address || "",
            city: patient.city || "",
            state: patient.state || "",
            pincode: patient.pincode || "",
            country: patient.country || "India",
            allergies: patient.allergies || "",
            chronicDiseases: patient.chronicDiseases || "",
            currentMedications: patient.currentMedications || "",
            familyMedicalHistory: patient.familyMedicalHistory || "",
            emergencyContactName: patient.emergencyContactName || "",
            emergencyContactPhone: patient.emergencyContactPhone || "",
            emergencyContactRelation: patient.emergencyContactRelation || "",
            insuranceProvider: patient.insuranceProvider || "",
            insurancePolicyNumber: patient.insurancePolicyNumber || "",
            insuranceExpiryDate: patient.insuranceExpiryDate || "",
            status: patient.status || "active",
            primaryBranchId: patient.primaryBranchId || currentBranch?.id || "",
          }
        : {
            // Your existing default values for create mode
            firstName: "",
            lastName: "",
            dateOfBirth: "",
            gender: "",
            bloodGroup: "",
            maritalStatus: "",
            occupation: "",
            // ABDM Information is now handled in a separate model
            email: "",
            phone: "",
            communicationMobile: "",
            alternatePhone: "",
            address: "",
            city: "",
            state: "",
            pincode: "",
            country: "India",
            allergies: "",
            chronicDiseases: "",
            currentMedications: "",
            familyMedicalHistory: "",
            emergencyContactName: "",
            emergencyContactPhone: "",
            emergencyContactRelation: "",
            insuranceProvider: "",
            insurancePolicyNumber: "",
            insuranceExpiryDate: "",
            status: "active",
            primaryBranchId: currentBranch?.id || "",
          },
  });

  // Update primary branch ID when current branch changes
  useEffect(() => {
    if (currentBranch?.id) {
      form.setValue("primaryBranchId", currentBranch.id);
    }
  }, [currentBranch, form]);

  // Focus on email field and set active tab to personal when ABHA is verified
  useEffect(() => {
    if (abhaVerified) {
      // Set active tab to personal
      setActiveTab("personal");

      // Focus on email field after a short delay to ensure the field is rendered
      // Email is optional but recommended for account creation
      setTimeout(() => {
        const emailField = document.querySelector(
          'input[name="email"]',
        ) as HTMLInputElement;
        if (emailField) {
          emailField.focus();
          // Add a placeholder message to indicate email is optional but recommended
          emailField.placeholder =
            "Optional but recommended for account creation";
        }
      }, 500);
    }
  }, [abhaVerified]);

  // Populate form with ABHA details when available
  useEffect(() => {
    if (abhaDetails && mode === "create") {
      console.log("Populating form with ABHA details:", abhaDetails);

      // Set ABHA number and address
      if (abhaDetails.abhaNumber) {
        setAbhaNumber(abhaDetails.abhaNumber);
      }

      // Set phone number
      if (abhaDetails.phone) {
        form.setValue("phone", abhaDetails.phone);
      }

      // Set email
      if (abhaDetails.email) {
        form.setValue("email", abhaDetails.email);
      }

      // Set name (use firstName and lastName if available, otherwise split the name)
      if (abhaDetails.firstName && abhaDetails.lastName) {
        // Use the split names directly from the API response
        form.setValue("firstName", abhaDetails.firstName);
        form.setValue("lastName", abhaDetails.lastName);
        console.log("Using split names from ABHA details:", {
          firstName: abhaDetails.firstName,
          lastName: abhaDetails.lastName,
        });
      } else if (abhaDetails.name) {
        // Fallback to splitting the full name
        const nameParts = abhaDetails.name.split(" ");
        if (nameParts.length > 0) {
          form.setValue("firstName", nameParts[0]);
          if (nameParts.length > 1) {
            form.setValue("lastName", nameParts.slice(1).join(" "));
          }
        }
        console.log("Split name from full name:", {
          fullName: abhaDetails.name,
          firstName: nameParts[0],
          lastName: nameParts.length > 1 ? nameParts.slice(1).join(" ") : "",
        });
      }

      // Set gender
      if (abhaDetails.gender) {
        console.log("Gender from ABHA details:", abhaDetails.gender);
        let formattedGender = "other";

        // Normalize the gender value to handle different formats
        const genderValue = abhaDetails.gender.trim().toUpperCase();

        if (genderValue === "M" || genderValue === "MALE") {
          formattedGender = "male";
        } else if (genderValue === "F" || genderValue === "FEMALE") {
          formattedGender = "female";
        } else if (genderValue === "O" || genderValue === "OTHER") {
          formattedGender = "other";
        }

        console.log("Setting gender to:", formattedGender);

        // Set the gender value and force a re-render of the select component
        setTimeout(() => {
          form.setValue("gender", formattedGender, {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true,
          });
        }, 0);
      }

      // Set date of birth from full DOB or year of birth
      console.log("Setting DOB from ABHA details:", abhaDetails);

      try {
        // Handle date of birth dynamically from the response
        if (abhaDetails.yearOfBirth) {
          console.log("Year of birth available:", abhaDetails.yearOfBirth);
          console.log("Day of birth available:", abhaDetails.dayOfBirth);
          console.log("Month of birth available:", abhaDetails.monthOfBirth);

          // Get day, month, and year from abhaDetails
          let day = abhaDetails.dayOfBirth
            ? parseInt(abhaDetails.dayOfBirth)
            : null;
          let month = abhaDetails.monthOfBirth
            ? parseInt(abhaDetails.monthOfBirth)
            : null;
          const year = parseInt(abhaDetails.yearOfBirth);

          // Create a new Date object if we have all components
          if (
            day &&
            month &&
            year &&
            !isNaN(day) &&
            !isNaN(month) &&
            !isNaN(year)
          ) {
            const dateObj = new Date(year, month - 1, day); // month is 0-indexed in JS Date

            // Format as YYYY-MM-DD for the input field
            const formattedDate = dateObj.toISOString().split("T")[0];
            form.setValue("dateOfBirth", formattedDate);
            console.log(
              "Setting DOB with complete date components:",
              formattedDate,
            );
          } else if (abhaDetails.dob) {
            // If we have a full DOB in DD-MM-YYYY format
            console.log("Full DOB available:", abhaDetails.dob);

            // Check if the DOB matches the DD-MM-YYYY pattern
            const dobMatch = abhaDetails.dob.match(
              /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
            );

            if (dobMatch) {
              // Extract day, month, and year from the regex match
              const [, dayStr, monthStr, yearStr] = dobMatch;

              // Convert to integers
              const day = parseInt(dayStr);
              const month = parseInt(monthStr);
              const year = parseInt(yearStr);

              // Create a new Date object
              const dateObj = new Date(year, month - 1, day); // month is 0-indexed in JS Date

              // Format as YYYY-MM-DD for the input field
              const formattedDate = dateObj.toISOString().split("T")[0];
              form.setValue("dateOfBirth", formattedDate);
              console.log(
                "Setting DOB from regex match with Date object:",
                formattedDate,
              );
            }
          } else {
            // If we only have the year, use a default date (January 1st)
            console.log(
              "Only year available, using default date (January 1st)",
            );
            const dateObj = new Date(year, 0, 1); // January 1st
            const formattedDate = dateObj.toISOString().split("T")[0];
            form.setValue("dateOfBirth", formattedDate);
            console.log("Setting DOB with year only:", formattedDate);
          }
        } else if (abhaDetails.dob) {
          // If we have a full DOB in DD-MM-YYYY format but no yearOfBirth
          console.log("Full DOB available (no yearOfBirth):", abhaDetails.dob);

          // Check if the DOB matches the DD-MM-YYYY pattern
          const dobMatch = abhaDetails.dob.match(
            /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
          );

          if (dobMatch) {
            // Extract day, month, and year from the regex match
            const [, dayStr, monthStr, yearStr] = dobMatch;

            // Convert to integers
            const day = parseInt(dayStr);
            const month = parseInt(monthStr);
            const year = parseInt(yearStr);

            // Create a new Date object
            const dateObj = new Date(year, month - 1, day); // month is 0-indexed in JS Date

            // Format as YYYY-MM-DD for the input field
            const formattedDate = dateObj.toISOString().split("T")[0];
            form.setValue("dateOfBirth", formattedDate);
            console.log(
              "Setting DOB from regex match with Date object:",
              formattedDate,
            );
          }
        }
      } catch (error) {
        console.error("Error setting date of birth:", error);

        // Fallback to using the year with a default date if available
        if (abhaDetails.yearOfBirth) {
          try {
            const year = parseInt(abhaDetails.yearOfBirth);
            // Use January 1st as a safe default
            const dateObj = new Date(year, 0, 1);
            const formattedDate = dateObj.toISOString().split("T")[0];
            form.setValue("dateOfBirth", formattedDate);
            console.log(
              "Setting DOB fallback with Date object:",
              formattedDate,
            );
          } catch (fallbackError) {
            console.error("Error in fallback date setting:", fallbackError);
            // Last resort fallback
            form.setValue("dateOfBirth", `${abhaDetails.yearOfBirth}-01-01`);
          }
        }
      }

      // Set address
      if (abhaDetails.address) {
        form.setValue("address", abhaDetails.address);
      }

      // Set city
      if (abhaDetails.city) {
        form.setValue("city", abhaDetails.city);
      }

      // Set state
      if (abhaDetails.state) {
        form.setValue("state", abhaDetails.state);
      }

      // Set pincode
      if (abhaDetails.pincode) {
        form.setValue("pincode", abhaDetails.pincode);
      }

      // Set ABHA search result to show the verification success message
      if (abhaDetails.abhaNumber) {
        setAbhaSearchResult({
          ABHA: [
            {
              ABHANumber: abhaDetails.abhaNumber,
              name: abhaDetails.name,
              gender: abhaDetails.gender,
              yearOfBirth: abhaDetails.yearOfBirth,
            },
          ],
        });
      }
    }
  }, [abhaDetails, form, mode]);

  // Check for duplicate patients when name, DOB, or phone changes
  const checkForDuplicates = async () => {
    const firstName = form.getValues("firstName");
    const lastName = form.getValues("lastName");
    const dateOfBirth = form.getValues("dateOfBirth");
    const phone = form.getValues("phone");

    // if (!firstName || !lastName || !dateOfBirth || !phone) {
    //   return;
    // }

    try {
      const response = await fetch(
        `/api/patients?search=${firstName} ${lastName}`,
      );
      if (!response.ok) {
        return;
      }

      const data = await response.json();
      const potentialDuplicates = data.patients.filter((patient: any) => {
        // Check if DOB matches
        const patientDOB = new Date(patient.dateOfBirth)
          .toISOString()
          .split("T")[0];
        const formDOB = new Date(dateOfBirth).toISOString().split("T")[0];

        // Check if phone matches
        const phoneMatch = patient.phone === phone;

        return patientDOB === formDOB || phoneMatch;
      });

      if (potentialDuplicates.length > 0) {
        setDuplicateWarning({
          message: "Potential duplicate patient(s) found",
          patients: potentialDuplicates,
        });
      } else {
        setDuplicateWarning(null);
      }
    } catch (error) {
      console.error("Error checking for duplicates:", error);
    }
  };

  // Handle form submission
  const onSubmit = async (values: PatientFormValues) => {
    setIsSubmitting(true);
    try {
      const url =
        mode === "edit" ? `/api/patients/${patient.id}` : "/api/patients";

      const method = mode === "edit" ? "PATCH" : "POST";

      // Include ABHA details if available
      const abhaData = abhaDetails
        ? {
            abhaNumber: abhaDetails.abhaNumber,
            abhaAddress: selectedAbhaAddress || abhaDetails.abhaAddress, // Use selected address
            healthIdNumber: abhaDetails.healthIdNumber,
            abhaStatus: abhaDetails.abhaStatus || "VERIFIED",
            abhaVerified: true,
            fromAbhaVerification: abhaDetails.fromAbhaVerification, // Include this crucial field!
          }
        : {};

      // DEBUG: Log what gets sent to API
      console.log("=== FORM SUBMISSION DEBUG ===");
      console.log("abhaDetails received:", abhaDetails);
      console.log("abhaData being sent:", abhaData);
      console.log(
        "fromAbhaVerification in abhaData:",
        abhaData.fromAbhaVerification,
      );

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          primaryBranchId: values.primaryBranchId,
          branchId: values.primaryBranchId, // Include branchId for backward compatibility
          ...abhaData, // Include ABHA details
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Check if this is a duplicate ABHA profile error
        if (
          response.status === 409 &&
          data.error &&
          data.error.includes("Patient already exists")
        ) {
          // Show duplicate warning with existing patient details
          setDuplicateWarning({
            message: data.error,
            patients: data.existingPatient ? [data.existingPatient] : [],
          });
          throw new Error(data.error || `Failed to ${mode} patient`);
        } else {
          throw new Error(data.error || `Failed to ${mode} patient`);
        }
      }

      toast.success(
        `Patient ${mode === "edit" ? "updated" : "registered"} successfully`,
      );

      if (onSuccess) {
        onSuccess(data.patient.id);
      } else {
        router.push(`/patients/${data.patient.id}`);
      }
    } catch (error) {
      console.error(`Error ${mode}ing patient:`, error);
      toast.error(
        error instanceof Error ? error.message : `Failed to ${mode} patient`,
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle tab navigation
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Navigate to next tab with strict validation
  const goToNextTab = async () => {
    if (activeTab === "personal") {
      // Import validation functions
      const { validateMobile, validateEmail, validateDateOfBirth } =
        await import("@/lib/validation");

      // Validate required personal information fields
      const requiredFields = [
        "firstName",
        "lastName",
        "dateOfBirth",
        "gender",
        "phone",
      ];

      const isBasicValid = requiredFields.every((field) => {
        const value = form.getValues(field as keyof PatientFormValues);
        return value && value.trim() !== "";
      });

      // Validate phone number with proper regex
      const phoneValue = form.getValues("phone");
      const phoneValidation = validateMobile(phoneValue || "");

      // Validate email if provided
      const emailValue = form.getValues("email");
      const emailValidation = !emailValue
        ? { isValid: true }
        : validateEmail(emailValue);

      // Validate date of birth
      const dobValue = form.getValues("dateOfBirth");
      const dobValidation = validateDateOfBirth(dobValue || "");

      const canProceed =
        isBasicValid &&
        phoneValidation.isValid &&
        emailValidation.isValid &&
        dobValidation.isValid;

      if (canProceed) {
        checkForDuplicates();
        setActiveTab("medical");
      } else {
        // Trigger form validation to show errors
        form.trigger([
          "firstName",
          "lastName",
          "dateOfBirth",
          "gender",
          "phone",
          "email",
        ]);

        // Show specific error messages
        if (!isBasicValid) {
          toast.error("Please fill in all required fields");
        } else if (!phoneValidation.isValid) {
          toast.error(
            phoneValidation.message || "Please enter a valid mobile number",
          );
        } else if (!emailValidation.isValid) {
          toast.error(
            emailValidation.message || "Please enter a valid email address",
          );
        } else if (!dobValidation.isValid) {
          toast.error(
            dobValidation.message || "Please enter a valid date of birth",
          );
        }
      }
    } else if (activeTab === "medical") {
      setActiveTab("insurance");
    } else if (activeTab === "insurance") {
      setActiveTab("branch");
    }
  };

  // Navigate to previous tab
  const goToPreviousTab = () => {
    if (activeTab === "medical") {
      setActiveTab("personal");
    } else if (activeTab === "insurance") {
      setActiveTab("medical");
    } else if (activeTab === "branch") {
      setActiveTab("insurance");
    }
  };
  console.log("ABHA Details:", abhaDetails);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">
            {mode === "edit" ? "Edit Patient" : "Patient Registration"}
          </CardTitle>
          <CardDescription>
            {mode === "edit"
              ? "Update patient information in the system"
              : "Register a new patient in the system"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Tabs value={activeTab} onValueChange={handleTabChange}>
                <TabsList className="grid grid-cols-4 mb-6">
                  <TabsTrigger value="personal">
                    Personal Information
                  </TabsTrigger>
                  <TabsTrigger value="medical">Medical Information</TabsTrigger>
                  <TabsTrigger value="insurance">Insurance Details</TabsTrigger>
                  <TabsTrigger value="branch">Branch & Status</TabsTrigger>
                </TabsList>

                {/* Personal Information Tab */}
                <TabsContent value="personal" className="space-y-6">
                  {duplicateWarning && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                      <div className="flex items-start">
                        <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                        <div>
                          <h3 className="text-sm font-medium text-yellow-800">
                            {duplicateWarning.message}
                          </h3>
                          <div className="mt-2 text-sm text-yellow-700">
                            <ul className="list-disc pl-5 space-y-1">
                              {duplicateWarning.patients.map((patient: any) => (
                                <li key={patient.id}>
                                  {patient.firstName} {patient.lastName} (DOB:{" "}
                                  {new Date(
                                    patient.dateOfBirth,
                                  ).toLocaleDateString()}
                                  )
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Phone Number Field */}
                  <div className="mb-6">
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-blue-600" />
                            Patient's Communication Mobile Number *
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="tel"
                              placeholder="Enter 10-digit Aadhaar-linked mobile"
                              {...field}
                              disabled={mode === "edit"}
                              onChange={(e) => {
                                // Only allow digits and limit to 10 characters
                                const cleanValue = e.target.value
                                  .replace(/\D/g, "")
                                  .slice(0, 10);
                                field.onChange(cleanValue);

                                // Handle ABHA search logic
                                if (
                                  mode === "create" &&
                                  cleanValue.length === 10
                                ) {
                                  // Debounce the search
                                  setTimeout(async () => {
                                    if (
                                      cleanValue === form.getValues("phone")
                                    ) {
                                      try {
                                        // First check if this patient already exists in our system
                                        const existingPatient =
                                          await searchPatientByPhone(
                                            cleanValue,
                                          );
                                        if (existingPatient?.patient) {
                                          setDuplicateWarning({
                                            message:
                                              "Patient with this phone number already exists",
                                            patients: [existingPatient.patient],
                                          });
                                          return;
                                        }

                                        // If no existing patient, search for ABHA
                                        setIsSearchingAbha(true);
                                        setAbhaSearchError(null);
                                        setAbhaSearchResult(null);

                                        const result =
                                          await searchAbhaByMobile(cleanValue);

                                        console.log(
                                          "ABHA search result:",
                                          result,
                                        );

                                        // Handle array response format from ABDM API
                                        let abhaData = null;
                                        if (
                                          Array.isArray(result) &&
                                          result.length > 0
                                        ) {
                                          // New format: array of results
                                          abhaData = result[0];
                                        } else if (result && result.ABHA) {
                                          // Old format: direct object
                                          abhaData = result;
                                        }

                                        if (abhaData?.ABHA?.length > 0) {
                                          const abhaDetails = abhaData.ABHA[0];
                                          if (abhaDetails?.ABHANumber) {
                                            setAbhaSearchResult(abhaData);
                                            setAbhaNumber(
                                              abhaDetails.ABHANumber,
                                            );
                                            setShowAbhaVerifyDialog(true);

                                            // Auto-populate form fields if ABHA data is found
                                            if (abhaDetails.name) {
                                              const nameParts =
                                                abhaDetails.name.split(" ");
                                              if (nameParts.length > 0) {
                                                form.setValue(
                                                  "firstName",
                                                  nameParts[0],
                                                );
                                                if (nameParts.length > 1) {
                                                  form.setValue(
                                                    "lastName",
                                                    nameParts
                                                      .slice(1)
                                                      .join(" "),
                                                  );
                                                }
                                              }
                                            }

                                            if (abhaDetails.gender) {
                                              const gender = abhaDetails.gender;
                                              if (gender === "M")
                                                form.setValue("gender", "male");
                                              else if (gender === "F")
                                                form.setValue(
                                                  "gender",
                                                  "female",
                                                );
                                              else if (gender === "O")
                                                form.setValue(
                                                  "gender",
                                                  "other",
                                                );
                                            }

                                            toast.success(
                                              "ABHA information found and pre-filled",
                                            );
                                          } else {
                                            setAbhaSearchError(
                                              "No ABHA information found for this mobile number.",
                                            );
                                          }
                                        } else {
                                          setAbhaSearchError(
                                            "No ABHA information found for this mobile number or invalid response format.",
                                          );
                                        }
                                      } catch (error) {
                                        console.error(
                                          "Error searching ABHA:",
                                          error,
                                        );
                                        setAbhaSearchError(
                                          "Failed to search ABHA. Please continue with manual entry.",
                                        );
                                      } finally {
                                        setIsSearchingAbha(false);
                                      }
                                    }
                                  }, 500);
                                }
                              }}
                              maxLength={10}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {isSearchingAbha && (
                      <div className="mt-2 flex items-center text-sm text-muted-foreground">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Searching for ABHA information...
                      </div>
                    )}

                    {abhaSearchResult && !abhaDetails?.abhaNumber && (
                      <Alert className="mt-2 bg-green-50 border-green-200">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <AlertTitle className="text-green-800">
                          ABHA information found
                        </AlertTitle>
                        <AlertDescription className="text-green-700">
                          ABHA information has been pre-filled. Please review
                          and complete the remaining fields.
                          {abhaNumber && (
                            <div className="mt-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-green-700 border-green-300 hover:bg-green-100"
                                onClick={() => setShowAbhaVerifyDialog(true)}
                              >
                                Verify ABHA
                                <ArrowRight className="ml-2 h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </AlertDescription>
                      </Alert>
                    )}

                    {abhaSearchError && (
                      <Alert className="mt-2 bg-yellow-50 border-yellow-200">
                        <AlertCircle className="h-4 w-4 text-yellow-600" />
                        <AlertTitle className="text-yellow-800">
                          ABHA search failed
                        </AlertTitle>
                        <AlertDescription className="text-yellow-700">
                          {abhaSearchError}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  {/* PHR Address Selection */}
                  {abhaDetails?.phrAddresses &&
                    abhaDetails.phrAddresses.length > 0 && (
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-lg font-semibold mb-2">
                            Select ABHA Address
                          </h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            Multiple ABHA addresses were found for this account.
                            Please select the one you want to use:
                          </p>
                        </div>

                        <div className="space-y-3">
                          {abhaDetails.phrAddresses.map(
                            (address: string, index: number) => (
                              <div
                                key={index}
                                className={`p-4 border rounded-md cursor-pointer transition-colors ${
                                  (selectedAbhaAddress ||
                                    abhaDetails.abhaAddress) === address
                                    ? "border-primary bg-primary/5"
                                    : "hover:border-primary/50"
                                }`}
                                onClick={() => {
                                  setSelectedAbhaAddress(address);
                                  console.log(
                                    "Selected ABHA address:",
                                    address,
                                  );
                                }}
                              >
                                <div className="flex items-center">
                                  <span
                                    className={`w-5 h-5 rounded-full border mr-3 flex items-center justify-center ${
                                      (selectedAbhaAddress ||
                                        abhaDetails.abhaAddress) === address
                                        ? "border-primary"
                                        : "border-muted"
                                    }`}
                                  >
                                    {(selectedAbhaAddress ||
                                      abhaDetails.abhaAddress) === address && (
                                      <div className="w-3 h-3 rounded-full bg-primary" />
                                    )}
                                  </span>
                                  <span className="font-medium">{address}</span>
                                </div>
                              </div>
                            ),
                          )}
                        </div>

                        <Alert className="bg-blue-50 border-blue-200">
                          <Info className="h-4 w-4 text-blue-600" />
                          <AlertTitle className="text-blue-800">
                            Selected ABHA Address
                          </AlertTitle>
                          <AlertDescription className="text-blue-700">
                            Currently selected:{" "}
                            <strong>
                              {selectedAbhaAddress || abhaDetails.abhaAddress}
                            </strong>
                          </AlertDescription>
                        </Alert>
                      </div>
                    )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>First Name *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="John"
                              {...field}
                              disabled={
                                abhaVerified ||
                                (abhaDetails && abhaDetails.abhaNumber)
                              }
                              className={
                                abhaVerified ||
                                (abhaDetails && abhaDetails.abhaNumber)
                                  ? "bg-muted cursor-not-allowed"
                                  : ""
                              }
                            />
                          </FormControl>
                          {(abhaVerified ||
                            (abhaDetails && abhaDetails.abhaNumber)) && (
                            <p className="text-xs text-muted-foreground mt-1">
                              This field is locked as it comes from verified
                              ABHA data
                            </p>
                          )}
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Last Name *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Doe"
                              {...field}
                              disabled={
                                abhaVerified ||
                                (abhaDetails && abhaDetails.abhaNumber)
                              }
                              className={
                                abhaVerified ||
                                (abhaDetails && abhaDetails.abhaNumber)
                                  ? "bg-muted cursor-not-allowed"
                                  : ""
                              }
                            />
                          </FormControl>
                          {(abhaVerified ||
                            (abhaDetails && abhaDetails.abhaNumber)) && (
                            <p className="text-xs text-muted-foreground mt-1">
                              This field is locked as it comes from verified
                              ABHA data
                            </p>
                          )}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="dateOfBirth"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Date of Birth *</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              disabled={
                                abhaVerified ||
                                (abhaDetails && abhaDetails.abhaNumber)
                              }
                              className={
                                abhaVerified ||
                                (abhaDetails && abhaDetails.abhaNumber)
                                  ? "bg-muted cursor-not-allowed"
                                  : ""
                              }
                            />
                          </FormControl>
                          {(abhaVerified ||
                            (abhaDetails && abhaDetails.abhaNumber)) && (
                            <p className="text-xs text-muted-foreground mt-1">
                              This field is locked as it comes from verified
                              ABHA data
                            </p>
                          )}
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Gender *</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                            disabled={
                              abhaVerified ||
                              (abhaDetails && abhaDetails.abhaNumber)
                            }
                          >
                            <FormControl>
                              <SelectTrigger
                                className={
                                  abhaVerified ||
                                  (abhaDetails && abhaDetails.abhaNumber)
                                    ? "bg-muted cursor-not-allowed"
                                    : ""
                                }
                              >
                                <SelectValue placeholder="Select gender" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="male">Male</SelectItem>
                              <SelectItem value="female">Female</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          {(abhaVerified ||
                            (abhaDetails && abhaDetails.abhaNumber)) && (
                            <p className="text-xs text-muted-foreground mt-1">
                              This field is locked as it comes from verified
                              ABHA data
                            </p>
                          )}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="bloodGroup"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Blood Group</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select blood group" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="A+">A+</SelectItem>
                              <SelectItem value="A-">A-</SelectItem>
                              <SelectItem value="B+">B+</SelectItem>
                              <SelectItem value="B-">B-</SelectItem>
                              <SelectItem value="AB+">AB+</SelectItem>
                              <SelectItem value="AB-">AB-</SelectItem>
                              <SelectItem value="O+">O+</SelectItem>
                              <SelectItem value="O-">O-</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maritalStatus"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Marital Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select marital status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="single">Single</SelectItem>
                              <SelectItem value="married">Married</SelectItem>
                              <SelectItem value="divorced">Divorced</SelectItem>
                              <SelectItem value="widowed">Widowed</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="occupation"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Occupation</FormLabel>
                          <FormControl>
                            <Input placeholder="Engineer" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Communication Mobile Field - Hidden as per requirements */}
                  {/* <div className="mb-6">
                    <FormField
                      control={form.control}
                      name="communicationMobile"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-blue-600" />
                            Communication Mobile Number
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="tel"
                              placeholder="Enter 10-digit communication mobile (optional)"
                              {...field}
                              onChange={(e) => {
                                // Only allow digits and limit to 10 characters
                                const cleanValue = e.target.value
                                  .replace(/\D/g, "")
                                  .slice(0, 10);
                                field.onChange(cleanValue);
                              }}
                            />
                          </FormControl>
                          <p className="text-xs text-muted-foreground mt-1">
                            This number will be used for communication and must be different from Aadhaar-linked mobile
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div> */}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="alternatePhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Alternate Phone</FormLabel>
                          <FormControl>
                            <Input placeholder="+91 **********" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="123 Main St, Apartment 4B"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input placeholder="Mumbai" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="state"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>State</FormLabel>
                          <FormControl>
                            <Input placeholder="Maharashtra" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pincode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Pincode</FormLabel>
                          <FormControl>
                            <Input placeholder="400001" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>

                {/* Medical Information Tab */}
                <TabsContent value="medical" className="space-y-6">
                  <FormField
                    control={form.control}
                    name="allergies"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Allergies</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Penicillin, Peanuts, etc."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="chronicDiseases"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Chronic Diseases</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Diabetes, Hypertension, etc."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currentMedications"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Current Medications</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="List all current medications"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="familyMedicalHistory"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Family Medical History</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Any relevant family medical history"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="emergencyContactName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Emergency Contact Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Jane Doe" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="emergencyContactPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Emergency Contact Phone</FormLabel>
                          <FormControl>
                            <Input placeholder="+91 **********" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="emergencyContactRelation"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Relationship</FormLabel>
                          <FormControl>
                            <Input placeholder="Spouse" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>

                {/* Insurance Details Tab */}
                <TabsContent value="insurance" className="space-y-6">
                  <FormField
                    control={form.control}
                    name="insuranceProvider"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Insurance Provider</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Insurance Company Name"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insurancePolicyNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Policy Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Policy Number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insuranceExpiryDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Expiry Date</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                {/* Branch & Status Tab */}
                <TabsContent value="branch" className="space-y-6">
                  <FormField
                    control={form.control}
                    name="primaryBranchId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Primary Branch *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select primary branch" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {branches.map((branch) => (
                              <SelectItem key={branch.id} value={branch.id}>
                                {branch.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div>
                    <FormLabel>Additional Branches</FormLabel>
                    <p className="text-sm text-muted-foreground mb-2">
                      The patient will be automatically registered at the
                      primary branch. You can add additional branches later.
                    </p>
                  </div>

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
              </Tabs>

              {/* ABHA Verification Dialog */}
              <Dialog
                open={showAbhaVerifyDialog}
                onOpenChange={setShowAbhaVerifyDialog}
              >
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>ABHA Verification</DialogTitle>
                    <DialogDescription>
                      This mobile number is linked to an ABHA account. Would you
                      like to verify and link this ABHA account to the patient
                      record?
                    </DialogDescription>
                  </DialogHeader>

                  {abhaSearchResult && abhaNumber && (
                    <div className="space-y-4 py-4">
                      <div className="space-y-2 border rounded-md p-4 bg-muted/50">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">
                            ABHA Number:
                          </span>
                          <span className="text-sm">{abhaNumber}</span>
                        </div>
                        {abhaSearchResult.ABHA &&
                          abhaSearchResult.ABHA[0].name && (
                            <div className="flex justify-between">
                              <span className="text-sm font-medium">Name:</span>
                              <span className="text-sm">
                                {abhaSearchResult.ABHA[0].name}
                              </span>
                            </div>
                          )}
                        {abhaSearchResult.ABHA &&
                          abhaSearchResult.ABHA[0].gender && (
                            <div className="flex justify-between">
                              <span className="text-sm font-medium">
                                Gender:
                              </span>
                              <span className="text-sm">
                                {abhaSearchResult.ABHA[0].gender === "M"
                                  ? "Male"
                                  : abhaSearchResult.ABHA[0].gender === "F"
                                    ? "Female"
                                    : "Other"}
                              </span>
                            </div>
                          )}
                      </div>

                      <p className="text-sm text-muted-foreground">
                        Proceeding will take you to the ABHA verification step
                        after saving the patient record.
                      </p>
                    </div>
                  )}

                  <DialogFooter className="sm:justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowAbhaVerifyDialog(false)}
                    >
                      Continue without verification
                    </Button>
                    <Button
                      type="button"
                      onClick={() => {
                        setShowAbhaVerifyDialog(false);
                        if (abhaNumber) {
                          // Redirect to ABHA verification page with the ABHA number
                          router.push(
                            `/abdm/abha-verification?abhaNumber=${abhaNumber}&phone=${form.getValues("phone")}`,
                          );
                        } else {
                          toast.error("ABHA number not found");
                        }
                      }}
                    >
                      Proceed to verification
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <div className="flex justify-between pt-4">
                {activeTab !== "personal" ? (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={goToPreviousTab}
                  >
                    Previous
                  </Button>
                ) : (
                  <div></div>
                )}

                {activeTab !== "branch" ? (
                  <Button type="button" onClick={goToNextTab}>
                    Next
                  </Button>
                ) : (
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting
                      ? mode === "edit"
                        ? "Updating..."
                        : "Registering..."
                      : mode === "edit"
                        ? "Update Patient"
                        : "Register Patient"}
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
