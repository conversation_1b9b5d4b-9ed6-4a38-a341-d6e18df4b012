"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export interface ComboboxOption {
  value: string;
  label: string;
}

interface AsyncComboboxProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (query: string) => Promise<ComboboxOption[]>;
  placeholder?: string;
  emptyMessage?: string;
  loadingMessage?: string;
  className?: string;
  disabled?: boolean;
  triggerSearchOnFocus?: boolean;
  selectedLabel?: string;
}

export function AsyncCombobox({
  value,
  onChange,
  onSearch,
  placeholder = "Select an option",
  emptyMessage = "No results found.",
  loadingMessage = "Loading...",
  className,
  disabled = false,
  triggerSearchOnFocus = true,
  selectedLabel,
}: AsyncComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [options, setOptions] = React.useState<ComboboxOption[]>([]);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [loading, setLoading] = React.useState(false);

  // Find the selected option from the current options list
  const selectedOption = options.find((option) => option.value === value);

  // Track if this is an initial load
  const [isInitialLoad, setIsInitialLoad] = React.useState(false);

  // Debounce search
  React.useEffect(() => {
    const timer = setTimeout(async () => {
      // If the component is open and we should trigger search on focus, or if there's a search query
      if (
        ((open && triggerSearchOnFocus && isInitialLoad) ||
          searchQuery.length > 0) &&
        !disabled
      ) {
        setLoading(true);
        try {
          // Pass the search query, but trim any leading/trailing spaces
          const trimmedQuery = searchQuery.trim();

          const results = await onSearch(trimmedQuery);

          setOptions(results);

          // Reset the initial load flag after the first search
          if (isInitialLoad) {
            setIsInitialLoad(false);
          }
        } catch (error) {
          console.error("Error searching:", error);
          setOptions([]);
        } finally {
          setLoading(false);
        }
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [
    searchQuery,
    open,
    onSearch,
    disabled,
    triggerSearchOnFocus,
    isInitialLoad,
  ]);

  return (
    <Popover
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        // If opening and we want to trigger search on focus, set the initial load flag
        if (isOpen && triggerSearchOnFocus) {
          setIsInitialLoad(true);
          // Force an immediate search with an empty query
          onSearch("")
            .then((results) => {
              setOptions(results);
            })
            .catch((error) => {
              console.error("AsyncCombobox: Error in initial search:", error);
            });
        }
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between h-10 px-3 py-2 text-sm",
            className,
          )}
          disabled={disabled}
          onClick={() => {
            // Always trigger a search when the combobox is opened
            if (!open) {
              setOpen(true);
              if (triggerSearchOnFocus) {
                setIsInitialLoad(true); // Set the initial load flag to trigger a search
              }
            }
          }}
        >
          <span className="truncate">
            {selectedLabel || selectedOption?.label || placeholder}
          </span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full min-w-[300px] p-0" align="start">
        <Command>
          <CommandInput
            placeholder={`Search ${placeholder.toLowerCase()}...`}
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="h-10 px-3 py-2 text-sm border-b"
            autoFocus
          />
          <CommandList>
            {loading && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-5 w-5 animate-spin text-primary" />
                <span className="ml-2 text-sm text-muted-foreground">
                  {loadingMessage}
                </span>
              </div>
            )}
            {!loading && options.length === 0 && (
              <CommandEmpty className="py-3 text-sm text-center text-muted-foreground">
                {emptyMessage}
              </CommandEmpty>
            )}
            {!loading && options.length > 0 && (
              <CommandGroup className="max-h-60 overflow-y-auto">
                {options.map((option) => {
                  return (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={() => {
                        onChange(option.value);
                        setOpen(false);
                      }}
                      className="py-2 px-2 text-sm cursor-pointer"
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4 flex-shrink-0",
                          value === option.value ? "opacity-100" : "opacity-0",
                        )}
                      />
                      <span className="truncate">{option.label}</span>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
