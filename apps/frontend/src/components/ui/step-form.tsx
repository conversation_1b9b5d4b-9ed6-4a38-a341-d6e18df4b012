import React, { useState, ReactNode } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, ArrowRight, AlertCircle, Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

export interface StepConfig {
  id: string;
  title: string;
  description?: string;
  component: ReactNode;
  validate?: () => string | null;
  onNext?: () => Promise<boolean>;
  onBack?: () => void;
  nextButtonText?: string;
  backButtonText?: string;
  showBackButton?: boolean;
  showNextButton?: boolean;
}

interface StepFormProps {
  steps: StepConfig[];
  title?: string;
  subtitle?: string;
  onComplete?: () => void;
  initialStep?: number;
  className?: string;
  showStepIndicator?: boolean;
  showStepNumbers?: boolean;
}

export function StepForm({
  steps,
  title,
  subtitle,
  onComplete,
  initialStep = 0,
  className = "",
  showStepIndicator = true,
  showStepNumbers = true,
}: StepFormProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(initialStep);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const currentStep = steps[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;

  const getStepLabel = (index: number) => {
    return showStepNumbers
      ? `Step ${index + 1}: ${steps[index].title}`
      : steps[index].title;
  };

  const getProgressPercentage = () => {
    return ((currentStepIndex + 1) / steps.length) * 100;
  };

  const handleNext = async () => {
    // Clear any previous errors
    setError(null);

    // Run validation if available
    if (currentStep.validate) {
      const validationError = currentStep.validate();
      if (validationError) {
        setError(validationError);
        return;
      }
    }

    // If there's a custom onNext handler, run it
    if (currentStep.onNext) {
      setIsProcessing(true);
      try {
        const canProceed = await currentStep.onNext();
        if (!canProceed) {
          return; // Don't proceed if the handler returns false
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
        return;
      } finally {
        setIsProcessing(false);
      }
    }

    // If we're on the last step, call onComplete
    if (isLastStep && onComplete) {
      onComplete();
      return;
    }

    // Otherwise, move to the next step
    setCurrentStepIndex((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const handleBack = () => {
    // Clear any errors when going back
    setError(null);

    // If there's a custom onBack handler, run it
    if (currentStep.onBack) {
      currentStep.onBack();
    }

    // Move to the previous step
    setCurrentStepIndex((prev) => Math.max(prev - 1, 0));
  };

  return (
    <Card className={`overflow-hidden ${className}`}>
      {(title || subtitle) && (
        <CardHeader className="space-y-1 bg-gradient-to-r from-primary/10 via-primary/5 to-secondary/10 p-6">
          {title && <h2 className="text-2xl font-bold">{title}</h2>}
          {subtitle && (
            <p className="text-base text-muted-foreground">{subtitle}</p>
          )}

          {showStepIndicator && (
            <div className="space-y-2 mt-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">
                  {getStepLabel(currentStepIndex)}
                </span>
                <Badge
                  variant={isLastStep ? "success" : "outline"}
                  className="text-xs"
                >
                  {currentStepIndex + 1} of {steps.length}
                </Badge>
              </div>
              <Progress value={getProgressPercentage()} className="h-2" />
            </div>
          )}
        </CardHeader>
      )}

      <CardContent className="p-6">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
          {/* Current step content - left aligned */}
          <div className="flex justify-start">
            <div className="w-full max-w-[420px]">{currentStep.component}</div>
          </div>

          {/* Navigation buttons */}
          <div className="flex justify-between items-center w-full mt-8">
            {/* Back button - always left aligned */}
            <div className="flex-1">
              {currentStep.showBackButton !== false && !isFirstStep && (
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={isProcessing}
                  className="h-10 px-4"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  {currentStep.backButtonText || "Back"}
                </Button>
              )}
            </div>

            {/* Next button - always right aligned */}
            <div className="flex justify-end flex-1">
              {currentStep.showNextButton !== false && (
                <Button
                  onClick={handleNext}
                  disabled={isProcessing}
                  className="h-10 px-4 shadow-sm transition-all hover:shadow-md"
                >
                  {isProcessing ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <ArrowRight className="mr-2 h-4 w-4" />
                  )}
                  {isProcessing
                    ? "Processing..."
                    : currentStep.nextButtonText ||
                      (isLastStep ? "Complete" : "Continue")}
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
