"use client";

import React, { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { validateAbhaNumber } from "@/lib/validation";

interface AbhaNumberInputProps {
  value?: string;
  onChange?: (value: string) => void;
  onValidationChange?: (isValid: boolean, message?: string) => void;
  disabled?: boolean;
  className?: string;
  label?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  validateOnChange?: boolean;
}

export function AbhaNumberInput({
  value = "",
  onChange,
  onValidationChange,
  disabled = false,
  className,
  label = "ABHA Number",
  placeholder,
  error,
  required = false,
  validateOnChange = true,
}: AbhaNumberInputProps) {
  // Parse the value into 4 parts
  const parseValue = (val: string) => {
    // Remove all non-digits and hyphens, then remove hyphens to get clean digits
    const cleanValue = val.replace(/[^\d-]/g, "").replace(/-/g, "");

    return {
      part1: cleanValue.slice(0, 2),
      part2: cleanValue.slice(2, 6),
      part3: cleanValue.slice(6, 10),
      part4: cleanValue.slice(10, 14),
    };
  };

  const { part1, part2, part3, part4 } = parseValue(value);

  const [parts, setParts] = useState({
    part1,
    part2,
    part3,
    part4,
  });

  // Refs for each input
  const part1Ref = useRef<HTMLInputElement>(null);
  const part2Ref = useRef<HTMLInputElement>(null);
  const part3Ref = useRef<HTMLInputElement>(null);
  const part4Ref = useRef<HTMLInputElement>(null);

  // Update parts when value prop changes
  useEffect(() => {
    const parsed = parseValue(value);
    setParts(parsed);
  }, [value]);

  // Format and call onChange with validation
  const updateValue = (newParts: typeof parts) => {
    const formattedValue = `${newParts.part1}-${newParts.part2}-${newParts.part3}-${newParts.part4}`;
    const cleanValue = formattedValue.replace(/-+$/, ""); // Remove trailing hyphens

    // Validate if enabled
    if (validateOnChange && onValidationChange) {
      const validation = validateAbhaNumber(cleanValue);
      onValidationChange(validation.isValid, validation.message);
    }

    onChange?.(cleanValue);
  };

  // Handle input change for each part
  const handlePartChange = (
    partName: keyof typeof parts,
    newValue: string,
    maxLength: number,
    nextRef?: React.RefObject<HTMLInputElement>,
  ) => {
    // Only allow digits
    const cleanValue = newValue.replace(/\D/g, "");

    // Handle case where user types/pastes more digits than the current field can hold
    if (cleanValue.length > maxLength) {
      // Distribute the extra digits to subsequent fields
      const currentFieldValue = cleanValue.slice(0, maxLength);
      const remainingDigits = cleanValue.slice(maxLength);

      // Update current field
      const newParts = { ...parts, [partName]: currentFieldValue };

      // Distribute remaining digits to next fields
      if (partName === "part1" && remainingDigits.length > 0) {
        newParts.part2 = remainingDigits.slice(0, 4);
        if (remainingDigits.length > 4) {
          newParts.part3 = remainingDigits.slice(4, 8);
          if (remainingDigits.length > 8) {
            newParts.part4 = remainingDigits.slice(8, 12);
          }
        }
      } else if (partName === "part2" && remainingDigits.length > 0) {
        newParts.part3 = remainingDigits.slice(0, 4);
        if (remainingDigits.length > 4) {
          newParts.part4 = remainingDigits.slice(4, 8);
        }
      } else if (partName === "part3" && remainingDigits.length > 0) {
        newParts.part4 = remainingDigits.slice(0, 4);
      }

      setParts(newParts);
      updateValue(newParts);

      // Focus the appropriate next field only for overflow/paste scenarios
      setTimeout(() => {
        let targetRef: React.RefObject<HTMLInputElement> | null = null;

        if (partName === "part1" && remainingDigits.length > 8) {
          targetRef = part4Ref;
        } else if (partName === "part1" && remainingDigits.length > 4) {
          targetRef = part3Ref;
        } else if (partName === "part1" && remainingDigits.length > 0) {
          targetRef = part2Ref;
        } else if (partName === "part2" && remainingDigits.length > 4) {
          targetRef = part4Ref;
        } else if (partName === "part2" && remainingDigits.length > 0) {
          targetRef = part3Ref;
        } else if (partName === "part3" && remainingDigits.length > 0) {
          targetRef = part4Ref;
        }

        if (targetRef?.current) {
          targetRef.current.focus();
          targetRef.current.setSelectionRange(
            targetRef.current.value.length,
            targetRef.current.value.length,
          );
        }
      }, 100);
    } else {
      // Normal case - just update the current field
      const newParts = { ...parts, [partName]: cleanValue };
      setParts(newParts);
      updateValue(newParts);

      // Auto-focus next input ONLY when current field is completely filled
      if (cleanValue.length === maxLength && nextRef?.current) {
        // Immediate focus for better UX
        setTimeout(() => {
          if (nextRef.current) {
            nextRef.current.focus();
            nextRef.current.setSelectionRange(0, nextRef.current.value.length); // Select all content in next field
          }
        }, 0);
      }
    }
  };

  // Handle backspace to move to previous input
  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    partName: keyof typeof parts,
    prevRef?: React.RefObject<HTMLInputElement>,
  ) => {
    if (e.key === "Backspace" && parts[partName] === "" && prevRef?.current) {
      prevRef.current.focus();
    }
  };

  // Handle paste
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");
    const cleanValue = pastedText.replace(/[^\d-]/g, "").replace(/-/g, "");

    if (cleanValue.length <= 14) {
      const parsed = parseValue(cleanValue);
      setParts(parsed);
      updateValue(parsed);

      // Focus the appropriate input based on the length with setTimeout for proper focus
      setTimeout(() => {
        let targetRef: React.RefObject<HTMLInputElement> | null = null;

        if (cleanValue.length <= 2) {
          targetRef = part1Ref;
        } else if (cleanValue.length <= 6) {
          targetRef = part2Ref;
        } else if (cleanValue.length <= 10) {
          targetRef = part3Ref;
        } else {
          targetRef = part4Ref;
        }

        if (targetRef?.current) {
          targetRef.current.focus();
          targetRef.current.setSelectionRange(
            targetRef.current.value.length,
            targetRef.current.value.length,
          );
        }
      }, 50);
    }
  };

  const isComplete =
    parts.part1.length === 2 &&
    parts.part2.length === 4 &&
    parts.part3.length === 4 &&
    parts.part4.length === 4;

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}

      <div className="flex items-center space-x-2">
        {/* Part 1: 2 digits */}
        <Input
          ref={part1Ref}
          value={parts.part1}
          onChange={(e) =>
            handlePartChange("part1", e.target.value, 2, part2Ref)
          }
          onKeyDown={(e) => handleKeyDown(e, "part1")}
          onPaste={handlePaste}
          placeholder={placeholder ? "12" : "12"}
          maxLength={2}
          disabled={disabled}
          className={cn(
            "w-16 text-center font-mono",
            error && "border-red-500 focus:border-red-500",
          )}
        />

        <span className="text-muted-foreground font-mono">-</span>

        {/* Part 2: 4 digits */}
        <Input
          ref={part2Ref}
          value={parts.part2}
          onChange={(e) =>
            handlePartChange("part2", e.target.value, 4, part3Ref)
          }
          onKeyDown={(e) => handleKeyDown(e, "part2", part1Ref)}
          onPaste={handlePaste}
          placeholder={placeholder ? "3456" : "3456"}
          maxLength={4}
          disabled={disabled}
          className={cn(
            "w-20 text-center font-mono",
            error && "border-red-500 focus:border-red-500",
          )}
        />

        <span className="text-muted-foreground font-mono">-</span>

        {/* Part 3: 4 digits */}
        <Input
          ref={part3Ref}
          value={parts.part3}
          onChange={(e) =>
            handlePartChange("part3", e.target.value, 4, part4Ref)
          }
          onKeyDown={(e) => handleKeyDown(e, "part3", part2Ref)}
          onPaste={handlePaste}
          placeholder={placeholder ? "7890" : "7890"}
          maxLength={4}
          disabled={disabled}
          className={cn(
            "w-20 text-center font-mono",
            error && "border-red-500 focus:border-red-500",
          )}
        />

        <span className="text-muted-foreground font-mono">-</span>

        {/* Part 4: 4 digits */}
        <Input
          ref={part4Ref}
          value={parts.part4}
          onChange={(e) => handlePartChange("part4", e.target.value, 4)}
          onKeyDown={(e) => handleKeyDown(e, "part4", part3Ref)}
          onPaste={handlePaste}
          placeholder={placeholder ? "1234" : "1234"}
          maxLength={4}
          disabled={disabled}
          className={cn(
            "w-20 text-center font-mono",
            error && "border-red-500 focus:border-red-500",
          )}
        />
      </div>

      {/* Helper text */}
      <div className="flex justify-between items-center">
        <p className="text-sm text-muted-foreground">
          Enter your 14-digit ABHA number
        </p>
        {isComplete && (
          <p className="text-sm text-green-600 font-medium">✓ Complete</p>
        )}
      </div>

      {/* Error message */}
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
}
