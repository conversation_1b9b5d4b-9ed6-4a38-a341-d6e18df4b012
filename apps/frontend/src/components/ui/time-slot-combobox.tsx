import * as React from "react";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export interface TimeSlotOption {
  value: string;
  label: string;
  duration: number;
}

interface TimeSlotComboboxProps {
  value: string;
  onChange: (value: string, duration: number) => void;
  onSearch: (query: string) => Promise<TimeSlotOption[]>;
  placeholder: string;
  emptyMessage: string;
  loadingMessage: string;
  disabled?: boolean;
  className?: string;
  selectedLabel?: string;
  triggerSearchOnFocus?: boolean;
  doctorId?: string;
  date?: string;
}

export function TimeSlotCombobox({
  value,
  onChange,
  onSearch,
  placeholder,
  emptyMessage,
  loadingMessage,
  disabled = false,
  className,
  selectedLabel,
  triggerSearchOnFocus = true,
  doctorId,
  date,
}: TimeSlotComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [options, setOptions] = React.useState<TimeSlotOption[]>([]);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [loading, setLoading] = React.useState(false);

  // Find the selected option from the current options list
  const selectedOption = options.find((option) => option.value === value);

  // Track if this is an initial load
  const [isInitialLoad, setIsInitialLoad] = React.useState(false);

  // Debounce search
  React.useEffect(() => {
    const timer = setTimeout(async () => {
      // If the component is open and we should trigger search on focus, or if there's a search query
      if (
        ((open && triggerSearchOnFocus && isInitialLoad) ||
          searchQuery.length > 0) &&
        !disabled &&
        doctorId &&
        date
      ) {
        setLoading(true);
        try {
          // Pass the search query, but trim any leading/trailing spaces
          const trimmedQuery = searchQuery.trim();

          const results = await onSearch(trimmedQuery);

          setOptions(results);

          // Reset the initial load flag after the first search
          if (isInitialLoad) {
            setIsInitialLoad(false);
          }
        } catch (error) {
          console.error("Error searching time slots:", error);
          setOptions([]);
        } finally {
          setLoading(false);
        }
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [
    searchQuery,
    open,
    onSearch,
    disabled,
    triggerSearchOnFocus,
    isInitialLoad,
    doctorId,
    date,
  ]);

  // Load time slots when doctorId or date changes
  React.useEffect(() => {
    if (doctorId && date && open) {
      setIsInitialLoad(true);
    }
  }, [doctorId, date, open]);

  // Auto-load time slots when component has a pre-selected value but no options
  React.useEffect(() => {
    if (
      value && // Has a pre-selected value
      doctorId &&
      date &&
      options.length === 0 && // No options loaded yet
      !loading && // Not currently loading
      !open // Dropdown is not open
    ) {
      // Automatically load time slots to validate the pre-selected value
      const loadInitialOptions = async () => {
        setLoading(true);
        try {
          const results = await onSearch("");
          setOptions(results);
        } catch (error) {
          console.error("Error loading initial time slots:", error);
          setOptions([]);
        } finally {
          setLoading(false);
        }
      };

      loadInitialOptions();
    }
  }, [value, doctorId, date, options.length, loading, open, onSearch]);

  // Track if we've already triggered onChange for the pre-selected value
  const [hasTriggeredPreselectedChange, setHasTriggeredPreselectedChange] =
    React.useState(false);

  // Trigger onChange when pre-selected value is found in loaded options (only once)
  React.useEffect(() => {
    if (value && options.length > 0 && !hasTriggeredPreselectedChange) {
      const selectedOption = options.find((option) => option.value === value);
      if (selectedOption) {
        // Call onChange to ensure the form is updated with the duration
        onChange(selectedOption.value, selectedOption.duration);
        setHasTriggeredPreselectedChange(true);
      }
    }
  }, [value, options, onChange, hasTriggeredPreselectedChange]);

  return (
    <Popover
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        // If opening and we should trigger search on focus, set the initial load flag
        if (isOpen && triggerSearchOnFocus && doctorId && date) {
          setIsInitialLoad(true);
          // Force an immediate search with an empty query
          onSearch("")
            .then((results) => {
              setOptions(results);
            })
            .catch((error) => {
              console.error(
                "TimeSlotCombobox: Error in initial search:",
                error,
              );
            });
        }
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between h-10 px-3 py-2 text-sm",
            className,
          )}
          disabled={disabled || !doctorId || !date}
          onClick={() => {
            // Always trigger a search when the combobox is opened
            if (!open && doctorId && date) {
              setOpen(true);
              if (triggerSearchOnFocus) {
                setIsInitialLoad(true); // Set the initial load flag to trigger a search
              }
            }
          }}
        >
          <span className="truncate">
            {selectedLabel || selectedOption?.label || placeholder}
          </span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full min-w-[300px] p-0" align="start">
        <Command>
          <CommandInput
            placeholder={`Search ${placeholder.toLowerCase()}...`}
            value={searchQuery}
            onValueChange={setSearchQuery}
            className="h-10 px-3 py-2 text-sm border-b"
            autoFocus
          />
          <CommandList>
            {loading && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-5 w-5 animate-spin text-primary" />
                <span className="ml-2 text-sm text-muted-foreground">
                  {loadingMessage}
                </span>
              </div>
            )}
            {!loading && options.length === 0 && (
              <CommandEmpty className="py-3 text-sm text-center text-muted-foreground">
                {emptyMessage}
              </CommandEmpty>
            )}
            {!loading && options.length > 0 && (
              <CommandGroup className="max-h-60 overflow-y-auto">
                {options.map((option) => {
                  return (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={() => {
                        onChange(option.value, option.duration);
                        setOpen(false);
                      }}
                      className="py-2 px-2 text-sm cursor-pointer"
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4 flex-shrink-0",
                          value === option.value ? "opacity-100" : "opacity-0",
                        )}
                      />
                      <span className="truncate">{option.label}</span>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
