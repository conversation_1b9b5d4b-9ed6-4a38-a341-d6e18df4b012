"use client";

import React, { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { validateOTP } from "@/lib/validation";

interface OtpInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidationChange?: (isValid: boolean, message?: string) => void;
  onComplete?: (otp: string) => void;
  length?: number;
  disabled?: boolean;
  className?: string;
  label?: string;
  required?: boolean;
  autoFocus?: boolean;
  blockInvalidInput?: boolean;
  showValidationIcon?: boolean;
  showValidationMessage?: boolean;
  validateOnChange?: boolean;
  placeholder?: string;
}

export function OtpInput({
  value,
  onChange,
  onValidationChange,
  onComplete,
  length = 6,
  disabled = false,
  className,
  label = "Enter OTP",
  required = false,
  autoFocus = false,
  blockInvalidInput = true,
  showValidationIcon = true,
  showValidationMessage = true,
  validateOnChange = true,
  placeholder,
}: OtpInputProps) {
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    message?: string;
  }>({ isValid: true });
  const [hasBeenTouched, setHasBeenTouched] = useState(false);

  // Create refs for each input
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // Parse value into individual digits
  const digits = value.padEnd(length, "").slice(0, length).split("");

  // Perform validation
  const performValidation = (inputValue: string) => {
    if (!inputValue && !required) {
      const result = { isValid: true };
      setValidationResult(result);
      if (onValidationChange) onValidationChange(true);
      return result;
    }

    const result = validateOTP(inputValue);
    setValidationResult(result);

    if (onValidationChange) {
      onValidationChange(result.isValid, result.message);
    }

    return result;
  };

  // Handle input change
  const handleChange = (index: number, newValue: string) => {
    // Block non-numeric input if enabled
    if (blockInvalidInput && newValue && !/^\d$/.test(newValue)) {
      return;
    }

    const newDigits = [...digits];

    // Handle paste or multiple characters
    if (newValue.length > 1) {
      const pastedDigits = newValue.replace(/\D/g, "").slice(0, length);
      for (let i = 0; i < length; i++) {
        newDigits[i] = pastedDigits[i] || "";
      }

      // Focus the next empty field or the last field
      const nextEmptyIndex = newDigits.findIndex((digit) => !digit);
      const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : length - 1;

      setTimeout(() => {
        inputRefs.current[focusIndex]?.focus();
      }, 0);
    } else {
      // Single character input
      newDigits[index] = newValue;

      // Auto-focus next input if current is filled
      if (newValue && index < length - 1) {
        setTimeout(() => {
          inputRefs.current[index + 1]?.focus();
        }, 0);
      }
    }

    const newOtp = newDigits.join("").replace(/\s/g, "");
    onChange(newOtp);
    setHasBeenTouched(true);

    // Validate if enabled
    if (validateOnChange) {
      performValidation(newOtp);
    }

    // Call onComplete if OTP is complete and valid
    if (newOtp.length === length && onComplete) {
      const validation = validateOTP(newOtp);
      if (validation.isValid) {
        onComplete(newOtp);
      }
    }
  };

  // Handle key down for navigation
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (e.key === "Backspace" && !digits[index] && index > 0) {
      // Move to previous input on backspace if current is empty
      setTimeout(() => {
        inputRefs.current[index - 1]?.focus();
      }, 0);
    } else if (e.key === "ArrowLeft" && index > 0) {
      setTimeout(() => {
        inputRefs.current[index - 1]?.focus();
      }, 0);
    } else if (e.key === "ArrowRight" && index < length - 1) {
      setTimeout(() => {
        inputRefs.current[index + 1]?.focus();
      }, 0);
    }
  };

  // Handle paste
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");
    const pastedDigits = pastedText.replace(/\D/g, "").slice(0, length);

    if (pastedDigits) {
      onChange(pastedDigits);
      setHasBeenTouched(true);

      if (validateOnChange) {
        performValidation(pastedDigits);
      }

      // Focus the next empty field or the last field
      const nextEmptyIndex = pastedDigits.length;
      const focusIndex = Math.min(nextEmptyIndex, length - 1);

      setTimeout(() => {
        inputRefs.current[focusIndex]?.focus();
      }, 0);
    }
  };

  // Auto-focus first input
  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  // Validate when value changes externally
  useEffect(() => {
    if (hasBeenTouched && validateOnChange) {
      performValidation(value);
    }
  }, [value, hasBeenTouched, validateOnChange]);

  const isValid = validationResult.isValid;
  const hasError = !isValid && hasBeenTouched && value;
  const hasSuccess = isValid && hasBeenTouched && value.length === length;

  return (
    <div className={cn("space-y-3", className)}>
      {label && (
        <Label className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}

      <div className="flex items-center justify-center space-x-2">
        {Array.from({ length }, (_, index) => (
          <div key={index} className="relative">
            <Input
              ref={(el) => {
                inputRefs.current[index] = el;
              }}
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={1}
              value={digits[index] || ""}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={handlePaste}
              disabled={disabled}
              placeholder={placeholder || "0"}
              className={cn(
                "w-12 h-12 text-center text-lg font-mono",
                hasError &&
                  "border-red-500 focus:border-red-500 focus:ring-red-500",
                hasSuccess &&
                  "border-green-500 focus:border-green-500 focus:ring-green-500",
              )}
            />

            {showValidationIcon &&
              index === length - 1 &&
              (hasError || hasSuccess) && (
                <div className="absolute -right-6 top-1/2 -translate-y-1/2">
                  {hasError ? (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  ) : hasSuccess ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : null}
                </div>
              )}
          </div>
        ))}
      </div>

      {showValidationMessage && hasError && validationResult.message && (
        <Alert variant="destructive" className="py-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {validationResult.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Helper text */}
      {!hasError && (
        <p className="text-sm text-muted-foreground text-center">
          Enter the {length}-digit OTP sent to your device
        </p>
      )}
    </div>
  );
}

// Hook for OTP validation state
export function useOtpValidation(
  initialValue: string = "",
  length: number = 6,
) {
  const [otp, setOtp] = useState(initialValue);
  const [isValid, setIsValid] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string>("");
  const [isComplete, setIsComplete] = useState(false);

  const handleValidationChange = (valid: boolean, message?: string) => {
    setIsValid(valid);
    setValidationMessage(message || "");
    setIsComplete(valid && otp.length === length);
  };

  return {
    otp,
    setOtp,
    isValid,
    validationMessage,
    isComplete,
    handleValidationChange,
  };
}
