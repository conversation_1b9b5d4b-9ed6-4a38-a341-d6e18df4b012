"use client";

import React, { useRef, useCallback } from "react";
import ReCA<PERSON><PERSON><PERSON> from "react-google-recaptcha";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface SimpleCaptchaProps {
  onValidationChange?: (isValid: boolean) => void;
  onCaptchaChange?: (token: string | null) => void;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  theme?: "light" | "dark";
  size?: "compact" | "normal";
}

export function SimpleCaptcha({
  onValidationChange,
  onCaptchaChange,
  className,
  disabled = false,
  required = false,
  theme = "light",
  size = "normal",
}: SimpleCaptchaProps) {
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  // Handle captcha change
  const handleCaptchaChange = useCallback(
    (token: string | null) => {
      const isValid = !!token;
      onValidationChange?.(isValid);
      onCaptchaChange?.(token);
    },
    [onValidationChange, onCaptchaChange],
  );

  // Handle captcha expiry
  const handleCaptchaExpired = useCallback(() => {
    onValidationChange?.(false);
    onCaptchaChange?.(null);
  }, [onValidationChange, onCaptchaChange]);

  // Handle captcha error
  const handleCaptchaError = useCallback(() => {
    onValidationChange?.(false);
    onCaptchaChange?.(null);
  }, [onValidationChange, onCaptchaChange]);

  // Get site key from environment variables
  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;

  if (!siteKey) {
    return (
      <div className={cn("space-y-3", className)}>
        <Label className="text-sm font-medium">
          Security Verification{" "}
          {required && <span className="text-red-500">*</span>}
        </Label>
        <div className="p-4 border border-red-200 bg-red-50 rounded-md">
          <p className="text-sm text-red-800">
            reCAPTCHA is not configured. Please add
            NEXT_PUBLIC_RECAPTCHA_SITE_KEY to your environment variables.
          </p>
          <p className="text-xs text-red-600 mt-1">
            This feature requires proper reCAPTCHA configuration.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      <Label className="text-sm font-medium">
        Security Verification{" "}
        {required && <span className="text-red-500">*</span>}
      </Label>

      <div className="flex flex-col space-y-2">
        {/* @ts-ignore */}
        <ReCAPTCHA
          ref={recaptchaRef}
          sitekey={siteKey}
          onChange={handleCaptchaChange}
          onExpired={handleCaptchaExpired}
          onError={handleCaptchaError}
          theme={theme}
          size={size}
          className={disabled ? "opacity-50 pointer-events-none" : ""}
        />

        <div className="text-xs text-muted-foreground">
          Please complete the security verification above
        </div>
      </div>
    </div>
  );
}

// Export with multiple names for compatibility
export { SimpleCaptcha as GoogleCaptcha };
export { SimpleCaptcha as MathCaptcha };
