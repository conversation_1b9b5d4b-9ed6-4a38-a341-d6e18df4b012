"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { validateAbhaAddress } from "@/lib/validation";

interface AbhaAddressInputProps {
  label?: string;
  value: string;
  onChange: (value: string) => void;
  onValidationChange?: (isValid: boolean, message?: string) => void;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  showValidationIcon?: boolean;
  showValidationMessage?: boolean;
  validateOnBlur?: boolean;
  validateOnChange?: boolean;
  className?: string;
  inputClassName?: string;
  id?: string;
}

export function AbhaAddressInput({
  label = "ABHA Address",
  value,
  onChange,
  onValidationChange,
  required = false,
  disabled = false,
  placeholder = "Enter your ABHA Address (e.g., john.doe@sbx)",
  showValidationIcon = true,
  showValidationMessage = true,
  validateOnBlur = true,
  validateOnChange = false,
  className,
  inputClassName,
  id,
}: AbhaAddressInputProps) {
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    message?: string;
  }>({ isValid: true });
  const [hasBeenTouched, setHasBeenTouched] = useState(false);
  const [showValidation, setShowValidation] = useState(false);

  // Perform validation
  const performValidation = (
    inputValue: string,
    shouldShow: boolean = true,
  ) => {
    if (!inputValue && !required) {
      const result = { isValid: true };
      setValidationResult(result);
      if (shouldShow) setShowValidation(false);
      if (onValidationChange) onValidationChange(true);
      return result;
    }

    const result = validateAbhaAddress(inputValue);
    setValidationResult(result);

    if (shouldShow) {
      setShowValidation(true);
    }

    if (onValidationChange) {
      onValidationChange(result.isValid, result.message);
    }

    return result;
  };

  // Handle input change with validation
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value;

    // Convert to lowercase for ABHA addresses (standard practice)
    newValue = newValue.toLowerCase();

    // Remove spaces and invalid characters, but allow alphanumeric, dots, underscores, and @
    newValue = newValue.replace(/[^a-z0-9._@-]/g, "");

    // Ensure only one @ symbol
    const atCount = (newValue.match(/@/g) || []).length;
    if (atCount > 1) {
      // Keep only the first @ and remove others
      const firstAtIndex = newValue.indexOf("@");
      newValue =
        newValue.substring(0, firstAtIndex + 1) +
        newValue.substring(firstAtIndex + 1).replace(/@/g, "");
    }

    onChange(newValue);

    if (validateOnChange) {
      performValidation(newValue, hasBeenTouched);
    }
  };

  // Handle blur
  const handleBlur = () => {
    setHasBeenTouched(true);
    if (validateOnBlur) {
      performValidation(value, true);
    }
  };

  // Handle focus
  const handleFocus = () => {
    if (!hasBeenTouched) {
      setHasBeenTouched(true);
    }
  };

  // Handle paste
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");

    // Clean and format pasted text
    let cleanedText = pastedText.toLowerCase().replace(/[^a-z0-9._@-]/g, "");

    // Ensure only one @ symbol
    const atCount = (cleanedText.match(/@/g) || []).length;
    if (atCount > 1) {
      const firstAtIndex = cleanedText.indexOf("@");
      cleanedText =
        cleanedText.substring(0, firstAtIndex + 1) +
        cleanedText.substring(firstAtIndex + 1).replace(/@/g, "");
    }

    onChange(cleanedText);
  };

  // Effect to validate when value changes externally
  useEffect(() => {
    if (hasBeenTouched || validateOnChange) {
      performValidation(value, showValidation);
    }
  }, [value, hasBeenTouched, validateOnChange, showValidation]);

  // Determine validation state for styling
  const isValid = validationResult.isValid;
  const hasError = !isValid && showValidation && hasBeenTouched && value;
  const hasSuccess = isValid && showValidation && hasBeenTouched && value;

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor={id} className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}

      <div className="relative">
        <Input
          id={id}
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          onPaste={handlePaste}
          disabled={disabled}
          className={cn(
            "pr-10",
            hasError &&
              "border-red-500 focus:border-red-500 focus:ring-red-500",
            hasSuccess &&
              "border-green-500 focus:border-green-500 focus:ring-green-500",
            inputClassName,
          )}
        />

        {showValidationIcon && (hasError || hasSuccess) && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {hasError ? (
              <AlertCircle className="h-4 w-4 text-red-500" />
            ) : hasSuccess ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : null}
          </div>
        )}
      </div>

      {showValidationMessage && hasError && validationResult.message && (
        <Alert variant="destructive" className="py-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {validationResult.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Helper text */}
      {!hasError && !hasSuccess && (
        <p className="text-sm text-muted-foreground">
          Enter your ABHA Address in the format username@domain (e.g.,
          john.doe@sbx, user123@abdm)
        </p>
      )}

      {/* Format examples */}
      {!hasError && !hasSuccess && (
        <div className="text-xs text-muted-foreground space-y-1">
          <p className="font-medium">Valid formats:</p>
          <ul className="list-disc list-inside space-y-0.5 ml-2">
            <li>john.doe@sbx</li>
            <li>user123@abdm</li>
            <li>patient_name@ndhm</li>
            <li>test.user@hid</li>
          </ul>
        </div>
      )}
    </div>
  );
}

// Hook for ABHA address validation state
export function useAbhaAddressValidation(initialValue: string = "") {
  const [abhaAddress, setAbhaAddress] = useState(initialValue);
  const [isValid, setIsValid] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string>("");

  const handleValidationChange = (valid: boolean, message?: string) => {
    setIsValid(valid);
    setValidationMessage(message || "");
  };

  return {
    abhaAddress,
    setAbhaAddress,
    isValid,
    validationMessage,
    handleValidationChange,
  };
}
