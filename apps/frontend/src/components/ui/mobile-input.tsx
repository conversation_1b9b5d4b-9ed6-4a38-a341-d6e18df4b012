"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { validateMobile } from "@/lib/validation";

interface MobileInputProps {
  label?: string;
  value: string;
  onChange: (value: string) => void;
  onValidationChange?: (isValid: boolean, message?: string) => void;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  showCountryCode?: boolean;
  countryCode?: string;
  blockInvalidInput?: boolean;
  showValidationIcon?: boolean;
  showValidationMessage?: boolean;
  validateOnBlur?: boolean;
  validateOnChange?: boolean;
  className?: string;
  inputClassName?: string;
  id?: string;
}

export function MobileInput({
  label = "Mobile Number",
  value,
  onChange,
  onValidationChange,
  required = false,
  disabled = false,
  placeholder = "Enter 10-digit mobile number",
  showCountryCode = true,
  countryCode = "+91",
  blockInvalidInput = true,
  showValidationIcon = true,
  showValidationMessage = true,
  validateOnBlur = true,
  validateOnChange = false,
  className,
  inputClassName,
  id,
}: MobileInputProps) {
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    message?: string;
  }>({ isValid: true });
  const [hasBeenTouched, setHasBeenTouched] = useState(false);
  const [showValidation, setShowValidation] = useState(false);

  // Perform validation
  const performValidation = (
    inputValue: string,
    shouldShow: boolean = true,
  ) => {
    if (!inputValue && !required) {
      const result = { isValid: true };
      setValidationResult(result);
      if (shouldShow) setShowValidation(false);
      if (onValidationChange) onValidationChange(true);
      return result;
    }

    const result = validateMobile(inputValue);
    setValidationResult(result);

    if (shouldShow) {
      setShowValidation(true);
    }

    if (onValidationChange) {
      onValidationChange(result.isValid, result.message);
    }

    return result;
  };

  // Handle input change with validation
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value;

    // Remove non-digit characters except + at the beginning
    if (newValue.startsWith("+")) {
      newValue = "+" + newValue.slice(1).replace(/\D/g, "");
    } else {
      newValue = newValue.replace(/\D/g, "");
    }

    // Limit length based on format
    if (newValue.startsWith("+91")) {
      newValue = newValue.slice(0, 13); // +91 + 10 digits
    } else if (newValue.startsWith("0")) {
      newValue = newValue.slice(0, 11); // 0 + 10 digits
    } else {
      newValue = newValue.slice(0, 10); // 10 digits
    }

    // If blocking invalid input, validate before allowing change
    if (blockInvalidInput && newValue && newValue.length > value.length) {
      // Allow the change if it's getting closer to valid (progressive input)
      const isProgressive =
        newValue.length <= 13 && /^(\+91|0)?[0-9]*$/.test(newValue);
      if (!isProgressive) {
        return; // Block the input
      }
    }

    onChange(newValue);

    if (validateOnChange) {
      performValidation(newValue, hasBeenTouched);
    }
  };

  // Handle blur
  const handleBlur = () => {
    setHasBeenTouched(true);
    if (validateOnBlur) {
      performValidation(value, true);
    }
  };

  // Handle focus
  const handleFocus = () => {
    if (!hasBeenTouched) {
      setHasBeenTouched(true);
    }
  };

  // Effect to validate when value changes externally
  useEffect(() => {
    if (hasBeenTouched || validateOnChange) {
      performValidation(value, showValidation);
    }
  }, [value, hasBeenTouched, validateOnChange, showValidation]);

  // Determine validation state for styling
  const isValid = validationResult.isValid;
  const hasError = !isValid && showValidation && hasBeenTouched && value;
  const hasSuccess = isValid && showValidation && hasBeenTouched && value;

  // Format display value for better UX
  const getDisplayValue = () => {
    if (!value) return "";

    // If value already has country code, return as is
    if (value.startsWith("+91") || value.startsWith("0")) {
      return value;
    }

    // For 10-digit numbers, show as is in the input
    return value;
  };

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor={id} className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}

      <div className="relative">
        {showCountryCode ? (
          <div className="flex">
            <div className="flex items-center justify-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground font-medium">
              {countryCode}
            </div>
            <Input
              id={id}
              type="tel"
              placeholder={placeholder}
              value={getDisplayValue()}
              onChange={handleChange}
              onBlur={handleBlur}
              onFocus={handleFocus}
              disabled={disabled}
              className={cn(
                "rounded-l-none pr-10",
                hasError &&
                  "border-red-500 focus:border-red-500 focus:ring-red-500",
                hasSuccess &&
                  "border-green-500 focus:border-green-500 focus:ring-green-500",
                inputClassName,
              )}
              maxLength={10}
            />
          </div>
        ) : (
          <Input
            id={id}
            type="tel"
            placeholder={placeholder}
            value={getDisplayValue()}
            onChange={handleChange}
            onBlur={handleBlur}
            onFocus={handleFocus}
            disabled={disabled}
            className={cn(
              "pr-10",
              hasError &&
                "border-red-500 focus:border-red-500 focus:ring-red-500",
              hasSuccess &&
                "border-green-500 focus:border-green-500 focus:ring-green-500",
              inputClassName,
            )}
            maxLength={13}
          />
        )}

        {showValidationIcon && (hasError || hasSuccess) && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {hasError ? (
              <AlertCircle className="h-4 w-4 text-red-500" />
            ) : hasSuccess ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : null}
          </div>
        )}
      </div>

      {showValidationMessage && hasError && validationResult.message && (
        <Alert variant="destructive" className="py-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {validationResult.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Helper text */}
      {!hasError && !hasSuccess && (
        <p className="text-sm text-muted-foreground">
          {showCountryCode
            ? "Enter your 10-digit mobile number"
            : "Enter mobile number (e.g., 9876543210, +919876543210)"}
        </p>
      )}
    </div>
  );
}

// Hook for mobile validation state
export function useMobileValidation(initialValue: string = "") {
  const [mobile, setMobile] = useState(initialValue);
  const [isValid, setIsValid] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string>("");

  const handleValidationChange = (valid: boolean, message?: string) => {
    setIsValid(valid);
    setValidationMessage(message || "");
  };

  return {
    mobile,
    setMobile,
    isValid,
    validationMessage,
    handleValidationChange,
  };
}
