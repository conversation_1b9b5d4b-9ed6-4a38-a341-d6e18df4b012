"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  validateMobile,
  validateEmail,
  validatePassword,
  validateDateOfBirth,
  validateAbhaNumber,
  validateOTP,
  validateAadhaar,
  ValidationResult,
} from "@/lib/validation";

export type ValidationType =
  | "mobile"
  | "email"
  | "password"
  | "dateOfBirth"
  | "abhaNumber"
  | "otp"
  | "aadhaar"
  | "none";

interface ValidatedInputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    "onChange" | "value"
  > {
  label?: string;
  validationType: ValidationType;
  value: string;
  onChange: (value: string) => void;
  onValidationChange?: (isValid: boolean, message?: string) => void;
  required?: boolean;
  showValidationIcon?: boolean;
  showValidationMessage?: boolean;
  validateOnBlur?: boolean;
  validateOnChange?: boolean;
  blockInvalidInput?: boolean;
  customValidator?: (value: string) => ValidationResult;
  className?: string;
  inputClassName?: string;
}

export function ValidatedInput({
  label,
  validationType,
  value,
  onChange,
  onValidationChange,
  required = false,
  showValidationIcon = true,
  showValidationMessage = true,
  validateOnBlur = true,
  validateOnChange = false,
  blockInvalidInput = false,
  customValidator,
  className,
  inputClassName,
  ...props
}: ValidatedInputProps) {
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
  });
  const [hasBeenTouched, setHasBeenTouched] = useState(false);
  const [showValidation, setShowValidation] = useState(false);

  // Validation function based on type
  const validateValue = (inputValue: string): ValidationResult => {
    if (customValidator) {
      return customValidator(inputValue);
    }

    if (!inputValue && required) {
      return { isValid: false, message: `${label || "Field"} is required` };
    }

    if (!inputValue && !required) {
      return { isValid: true };
    }

    switch (validationType) {
      case "mobile":
        return validateMobile(inputValue);
      case "email":
        return validateEmail(inputValue);
      case "password":
        return validatePassword(inputValue);
      case "dateOfBirth":
        return validateDateOfBirth(inputValue);
      case "abhaNumber":
        return validateAbhaNumber(inputValue);
      case "otp":
        return validateOTP(inputValue);
      case "aadhaar":
        return validateAadhaar(inputValue);
      case "none":
      default:
        return { isValid: true };
    }
  };

  // Handle validation
  const performValidation = (
    inputValue: string,
    shouldShow: boolean = true,
  ) => {
    const result = validateValue(inputValue);
    setValidationResult(result);

    if (shouldShow) {
      setShowValidation(true);
    }

    if (onValidationChange) {
      onValidationChange(result.isValid, result.message);
    }

    return result;
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // If blocking invalid input, validate first
    if (blockInvalidInput && newValue) {
      const result = validateValue(newValue);
      if (!result.isValid && newValue.length > value.length) {
        // Don't allow the change if it makes the input invalid and it's getting longer
        return;
      }
    }

    onChange(newValue);

    if (validateOnChange) {
      performValidation(newValue, hasBeenTouched);
    }
  };

  // Handle blur
  const handleBlur = () => {
    setHasBeenTouched(true);
    if (validateOnBlur) {
      performValidation(value, true);
    }
  };

  // Effect to validate when value changes externally
  useEffect(() => {
    if (hasBeenTouched || validateOnChange) {
      performValidation(value, showValidation);
    }
  }, [value, hasBeenTouched, validateOnChange, showValidation]);

  // Determine validation state for styling
  const isValid = validationResult.isValid;
  const hasError = !isValid && showValidation && hasBeenTouched;
  const hasSuccess = isValid && showValidation && hasBeenTouched && value;

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor={props.id} className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}

      <div className="relative">
        <Input
          {...props}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          className={cn(
            "pr-10",
            hasError &&
              "border-red-500 focus:border-red-500 focus:ring-red-500",
            hasSuccess &&
              "border-green-500 focus:border-green-500 focus:ring-green-500",
            inputClassName,
          )}
        />

        {showValidationIcon && (hasError || hasSuccess) && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {hasError ? (
              <AlertCircle className="h-4 w-4 text-red-500" />
            ) : hasSuccess ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : null}
          </div>
        )}
      </div>

      {showValidationMessage && hasError && validationResult.message && (
        <Alert variant="destructive" className="py-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {validationResult.message}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

// Hook for form validation
export function useFormValidation() {
  const [validationStates, setValidationStates] = useState<
    Record<string, boolean>
  >({});
  const [validationMessages, setValidationMessages] = useState<
    Record<string, string>
  >({});

  const updateValidation = (
    fieldName: string,
    isValid: boolean,
    message?: string,
  ) => {
    setValidationStates((prev) => ({ ...prev, [fieldName]: isValid }));
    setValidationMessages((prev) => ({
      ...prev,
      [fieldName]: message || "",
    }));
  };

  const isFormValid = () => {
    return Object.values(validationStates).every((isValid) => isValid);
  };

  const getFieldValidation = (fieldName: string) => {
    return {
      isValid: validationStates[fieldName] ?? true,
      message: validationMessages[fieldName] ?? "",
    };
  };

  const resetValidation = () => {
    setValidationStates({});
    setValidationMessages({});
  };

  return {
    validationStates,
    validationMessages,
    updateValidation,
    isFormValid,
    getFieldValidation,
    resetValidation,
  };
}
