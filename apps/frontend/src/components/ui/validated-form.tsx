"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface ValidationRule {
  field: string;
  validator: (value: any) => { isValid: boolean; message?: string };
  required?: boolean;
}

interface ValidatedFormProps {
  children: React.ReactNode;
  onSubmit: (data: any) => Promise<void> | void;
  validationRules?: ValidationRule[];
  formData: Record<string, any>;
  submitButtonText?: string;
  submitButtonClassName?: string;
  showValidationSummary?: boolean;
  preventSubmitOnInvalid?: boolean;
  validateOnChange?: boolean;
  className?: string;
  disabled?: boolean;
}

export function ValidatedForm({
  children,
  onSubmit,
  validationRules = [],
  formData,
  submitButtonText = "Submit",
  submitButtonClassName,
  showValidationSummary = true,
  preventSubmitOnInvalid = true,
  validateOnChange = true,
  className,
  disabled = false,
}: ValidatedFormProps) {
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

  // Validate all fields
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    let isValid = true;

    validationRules.forEach((rule) => {
      const value = formData[rule.field];

      // Check if required field is empty
      if (
        rule.required &&
        (!value || (typeof value === "string" && value.trim() === ""))
      ) {
        errors[rule.field] = `${rule.field} is required`;
        isValid = false;
        return;
      }

      // Skip validation if field is empty and not required
      if (!value && !rule.required) {
        return;
      }

      // Run custom validator
      const result = rule.validator(value);
      if (!result.isValid) {
        errors[rule.field] = result.message || `${rule.field} is invalid`;
        isValid = false;
      }
    });

    setValidationErrors(errors);
    return isValid;
  };

  // Validate on form data change
  useEffect(() => {
    if (
      validateOnChange &&
      (hasAttemptedSubmit || Object.keys(validationErrors).length > 0)
    ) {
      validateForm();
    }
  }, [formData, validateOnChange, hasAttemptedSubmit]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setHasAttemptedSubmit(true);

    const isValid = validateForm();

    if (preventSubmitOnInvalid && !isValid) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const hasErrors = Object.keys(validationErrors).length > 0;
  const canSubmit = !hasErrors && !isSubmitting && !disabled;

  return (
    <form onSubmit={handleSubmit} className={cn("space-y-6", className)}>
      {children}

      {/* Validation Summary */}
      {showValidationSummary && hasErrors && hasAttemptedSubmit && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="font-medium mb-2">
              Please fix the following errors:
            </div>
            <ul className="list-disc list-inside space-y-1">
              {Object.entries(validationErrors).map(([field, message]) => (
                <li key={field} className="text-sm">
                  {message}
                </li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={
            preventSubmitOnInvalid ? !canSubmit : isSubmitting || disabled
          }
          className={cn(
            "min-w-[120px]",
            !canSubmit &&
              preventSubmitOnInvalid &&
              "opacity-50 cursor-not-allowed",
            submitButtonClassName,
          )}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : (
            submitButtonText
          )}
        </Button>
      </div>
    </form>
  );
}

// Utility function to create validation rules
export const createValidationRule = (
  field: string,
  validator: (value: any) => { isValid: boolean; message?: string },
  required: boolean = false,
): ValidationRule => ({
  field,
  validator,
  required,
});

// Common validation rule creators
export const validationRules = {
  required: (field: string, customMessage?: string) =>
    createValidationRule(
      field,
      (value) => ({
        isValid: value && value.toString().trim() !== "",
        message: customMessage || `${field} is required`,
      }),
      true,
    ),

  mobile: (field: string) => {
    const { validateMobile } = require("@/lib/validation");
    return createValidationRule(
      field,
      (value) => validateMobile(value || ""),
      false,
    );
  },

  email: (field: string) => {
    const { validateEmail } = require("@/lib/validation");
    return createValidationRule(
      field,
      (value) => validateEmail(value || ""),
      false,
    );
  },

  password: (field: string) => {
    const { validatePassword } = require("@/lib/validation");
    return createValidationRule(
      field,
      (value) => validatePassword(value || ""),
      false,
    );
  },

  dateOfBirth: (field: string) => {
    const { validateDateOfBirth } = require("@/lib/validation");
    return createValidationRule(
      field,
      (value) => validateDateOfBirth(value || ""),
      false,
    );
  },

  abhaNumber: (field: string) => {
    const { validateAbhaNumber } = require("@/lib/validation");
    return createValidationRule(
      field,
      (value) => validateAbhaNumber(value || ""),
      false,
    );
  },

  otp: (field: string) => {
    const { validateOTP } = require("@/lib/validation");
    return createValidationRule(
      field,
      (value) => validateOTP(value || ""),
      false,
    );
  },

  minLength: (field: string, minLength: number) =>
    createValidationRule(
      field,
      (value) => ({
        isValid: !value || value.toString().length >= minLength,
        message: `${field} must be at least ${minLength} characters long`,
      }),
      false,
    ),

  maxLength: (field: string, maxLength: number) =>
    createValidationRule(
      field,
      (value) => ({
        isValid: !value || value.toString().length <= maxLength,
        message: `${field} must be no more than ${maxLength} characters long`,
      }),
      false,
    ),

  custom: (
    field: string,
    validator: (value: any) => boolean,
    message: string,
  ) =>
    createValidationRule(
      field,
      (value) => ({
        isValid: validator(value),
        message,
      }),
      false,
    ),
};
