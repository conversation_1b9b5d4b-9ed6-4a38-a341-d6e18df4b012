"use client";

import React from "react";
import { CareTypeProvider } from "@/contexts/care-type-context";
import { DepartmentProvider } from "@/contexts/department-context";

interface CareTypeLayoutProps {
  children: React.ReactNode;
}

export function CareTypeLayout({ children }: CareTypeLayoutProps) {
  return (
    <DepartmentProvider>
      <CareTypeProvider>{children}</CareTypeProvider>
    </DepartmentProvider>
  );
}
