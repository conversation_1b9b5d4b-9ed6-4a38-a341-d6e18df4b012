"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle, FileText, Download } from "lucide-react";
import { toast } from "sonner";
import {
  packageHealthR<PERSON>ord,
  uploadHealthR<PERSON>ord,
  getPatientFhirBundles,
  canShareHealthRecord,
} from "@/services/health-record-service";

interface HealthRecordManagerProps {
  patientId: string;
  vitalsRecords: any[];
  Prescriptions: any[];
  clinicalNoteRecords: any[];
}

export function HealthRecordManager({
  patientId,
  vitalsRecords,
  Prescriptions,
  clinicalNoteRecords,
}: HealthRecordManagerProps) {
  const [selectedTab, setSelectedTab] = useState("vitals");
  const [selectedRecord, setSelectedRecord] = useState<string | null>(null);
  const [isPackaging, setIsPackaging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [bundleId, setBundleId] = useState<string | null>(null);
  const [bundles, setBundles] = useState<any[]>([]);
  const [activeConsents, setActiveConsents] = useState<any[]>([]);
  const [selectedConsent, setSelectedConsent] = useState<string | null>(null);
  const [careContextReference, setCareContextReference] = useState("");
  const [recipientPublicKey, setRecipientPublicKey] = useState("");
  const [showUploadDialog, setShowUploadDialog] = useState(false);

  // Get record type based on selected tab
  const getRecordType = () => {
    switch (selectedTab) {
      case "vitals":
        return "vitals";
      case "prescriptions":
        return "prescription";
      case "clinicalNotes":
        return "clinicalNote";
      default:
        return "";
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Check if record can be shared
  const checkCanShare = async () => {
    try {
      const result = await canShareHealthRecord(patientId, getRecordType());
      setActiveConsents(result.activeConsents);

      if (!result.canShare) {
        toast.error(
          "No active consents found for sharing this type of health record",
        );
      }

      return result.canShare;
    } catch (error) {
      console.error("Error checking if health record can be shared:", error);
      toast.error("Failed to check if health record can be shared");
      return false;
    }
  };

  // Load FHIR bundles
  const loadBundles = async () => {
    try {
      const bundles = await getPatientFhirBundles(patientId);
      setBundles(bundles);
    } catch (error) {
      console.error("Error loading FHIR bundles:", error);
      toast.error("Failed to load FHIR bundles");
    }
  };

  // Handle record selection
  const handleRecordSelect = (recordId: string) => {
    setSelectedRecord(recordId);
  };

  // Handle package button click
  const handlePackage = async () => {
    if (!selectedRecord) {
      toast.error("Please select a record to package");
      return;
    }

    try {
      setIsPackaging(true);

      // Check if record can be shared
      const canShareResult = await checkCanShare();
      if (!canShareResult) {
        setIsPackaging(false);
        return;
      }

      // Package health record
      const result = await packageHealthRecord(
        patientId,
        getRecordType(),
        selectedRecord,
      );

      setBundleId(result.bundleId);
      toast.success("Health record packaged successfully");

      // Load bundles
      await loadBundles();

      // Show upload dialog
      setShowUploadDialog(true);
    } catch (error: any) {
      console.error("Error packaging health record:", error);
      toast.error(
        `Failed to package health record: ${error.message || "Unknown error"}`,
      );
    } finally {
      setIsPackaging(false);
    }
  };

  // Handle upload button click
  const handleUpload = async () => {
    if (!bundleId) {
      toast.error("No bundle ID found");
      return;
    }

    if (!selectedConsent) {
      toast.error("Please select a consent");
      return;
    }

    if (!careContextReference) {
      toast.error("Please enter a care context reference");
      return;
    }

    if (!recipientPublicKey) {
      toast.error("Please enter a recipient public key");
      return;
    }

    try {
      setIsUploading(true);

      // Upload health record
      await uploadHealthRecord(
        bundleId,
        selectedConsent,
        careContextReference,
        recipientPublicKey,
      );

      toast.success("Health record uploaded successfully");

      // Close upload dialog
      setShowUploadDialog(false);

      // Reset form
      setSelectedRecord(null);
      setBundleId(null);
      setSelectedConsent(null);
      setCareContextReference("");
      setRecipientPublicKey("");

      // Load bundles
      await loadBundles();
    } catch (error: any) {
      console.error("Error uploading health record:", error);
      toast.error(
        `Failed to upload health record: ${error.message || "Unknown error"}`,
      );
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5 text-primary" />
              Health Record Management
            </CardTitle>
            <CardDescription>
              Package and upload health records to ABDM
            </CardDescription>
          </div>
          <Button asChild variant="outline" size="sm">
            <Link href={`/patients/${patientId}/health-records/fetch`}>
              <Download className="mr-2 h-4 w-4" />
              Fetch Records
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue="vitals"
          value={selectedTab}
          onValueChange={(value) => {
            setSelectedTab(value);
            setSelectedRecord(null);
          }}
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="vitals">Vitals</TabsTrigger>
            <TabsTrigger value="prescriptions">Prescriptions</TabsTrigger>
            <TabsTrigger value="clinicalNotes">Clinical Notes</TabsTrigger>
          </TabsList>
          <TabsContent value="vitals">
            <div className="space-y-4 mt-4">
              <h3 className="text-sm font-medium">Select Vitals Record</h3>
              {vitalsRecords.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>No vitals records found</AlertTitle>
                  <AlertDescription>
                    There are no vitals records available for this patient.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="grid gap-2">
                  {vitalsRecords.map((record) => (
                    <div
                      key={record.id}
                      className={`p-3 border rounded-md cursor-pointer ${
                        selectedRecord === record.id
                          ? "border-primary bg-primary/5"
                          : "border-border"
                      }`}
                      onClick={() => handleRecordSelect(record.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">
                            Vitals - {formatDate(record.recordedAt)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {record.bloodPressureSystolic &&
                            record.bloodPressureDiastolic
                              ? `BP: ${record.bloodPressureSystolic}/${record.bloodPressureDiastolic} mmHg`
                              : ""}
                            {record.pulse
                              ? ` | Pulse: ${record.pulse} bpm`
                              : ""}
                            {record.temperature
                              ? ` | Temp: ${record.temperature}°C`
                              : ""}
                          </p>
                        </div>
                        {selectedRecord === record.id && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="prescriptions">
            <div className="space-y-4 mt-4">
              <h3 className="text-sm font-medium">Select Prescription</h3>
              {Prescriptions.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>No prescriptions found</AlertTitle>
                  <AlertDescription>
                    There are no prescriptions available for this patient.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="grid gap-2">
                  {Prescriptions.map((record) => (
                    <div
                      key={record.id}
                      className={`p-3 border rounded-md cursor-pointer ${
                        selectedRecord === record.id
                          ? "border-primary bg-primary/5"
                          : "border-border"
                      }`}
                      onClick={() => handleRecordSelect(record.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">
                            Prescription - {formatDate(record.prescriptionDate)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {record.items?.length || 0} medications
                          </p>
                        </div>
                        {selectedRecord === record.id && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="clinicalNotes">
            <div className="space-y-4 mt-4">
              <h3 className="text-sm font-medium">Select Clinical Note</h3>
              {clinicalNoteRecords.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>No clinical notes found</AlertTitle>
                  <AlertDescription>
                    There are no clinical notes available for this patient.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="grid gap-2">
                  {clinicalNoteRecords.map((record) => (
                    <div
                      key={record.id}
                      className={`p-3 border rounded-md cursor-pointer ${
                        selectedRecord === record.id
                          ? "border-primary bg-primary/5"
                          : "border-border"
                      }`}
                      onClick={() => handleRecordSelect(record.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">
                            Clinical Note - {formatDate(record.createdAt)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {record.content.substring(0, 50)}
                            {record.content.length > 50 ? "..." : ""}
                          </p>
                        </div>
                        {selectedRecord === record.id && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-6">
          <h3 className="text-sm font-medium mb-2">FHIR Bundles</h3>
          {bundles.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>No FHIR bundles found</AlertTitle>
              <AlertDescription>
                There are no FHIR bundles available for this patient.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid gap-2">
              {bundles.map((bundle) => (
                <div key={bundle.id} className="p-3 border rounded-md">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">
                        Bundle - {bundle.bundleType}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Created: {formatDate(bundle.createdAt)}
                      </p>
                    </div>
                    <Badge
                      variant={
                        bundle.status === "created"
                          ? "outline"
                          : bundle.status === "packaged"
                            ? "secondary"
                            : bundle.status === "uploaded"
                              ? "default"
                              : "destructive"
                      }
                    >
                      {bundle.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={loadBundles}>
          Refresh Bundles
        </Button>
        <Button
          onClick={handlePackage}
          disabled={!selectedRecord || isPackaging}
        >
          {isPackaging ? "Packaging..." : "Package Record"}
        </Button>
      </CardFooter>

      {/* Upload Dialog */}
      <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Upload Health Record</DialogTitle>
            <DialogDescription>
              Upload the packaged health record to ABDM.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="consent" className="text-right">
                Consent
              </Label>
              <Select
                value={selectedConsent || ""}
                onValueChange={setSelectedConsent}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select consent" />
                </SelectTrigger>
                <SelectContent>
                  {activeConsents.map((consent) => (
                    <SelectItem
                      key={consent.consentId}
                      value={consent.consentId}
                    >
                      {consent.purpose} - {formatDate(consent.expiryDate)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="careContext" className="text-right">
                Care Context
              </Label>
              <Input
                id="careContext"
                value={careContextReference}
                onChange={(e) => setCareContextReference(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="publicKey" className="text-right">
                Public Key
              </Label>
              <Input
                id="publicKey"
                value={recipientPublicKey}
                onChange={(e) => setRecipientPublicKey(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowUploadDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              disabled={
                !bundleId ||
                !selectedConsent ||
                !careContextReference ||
                !recipientPublicKey ||
                isUploading
              }
            >
              {isUploading ? "Uploading..." : "Upload"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
