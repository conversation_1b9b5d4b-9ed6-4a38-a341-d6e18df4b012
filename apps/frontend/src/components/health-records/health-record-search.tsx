"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { DatePicker } from "@/components/ui/date-picker";
import { Badge } from "@/components/ui/badge";
import { Loader2, Search, FileText } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";

// Define the search form schema
const searchFormSchema = z.object({
  patientId: z.string().optional(),
  patientName: z.string().optional(),
  recordType: z.enum(["all", "vitals", "prescription", "clinicalNote"]),
  bundleStatus: z.enum(["all", "created", "packaged", "uploaded", "error"]),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

type SearchFormValues = z.infer<typeof searchFormSchema>;

interface HealthRecordSearchResult {
  id: string;
  bundleId: string;
  bundleType: string;
  status: string;
  patientId: string;
  patient: {
    firstName: string;
    lastName: string;
  };
  createdAt: string;
  updatedAt: string;
  sourceType?: string;
}

export function HealthRecordSearch() {
  const router = useRouter();
  const [results, setResults] = useState<HealthRecordSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize form
  const form = useForm<SearchFormValues>({
    resolver: zodResolver(searchFormSchema),
    defaultValues: {
      patientId: "",
      patientName: "",
      recordType: "all",
      bundleStatus: "all",
      startDate: undefined,
      endDate: undefined,
    },
  });

  // Handle form submission
  const onSubmit = async (values: SearchFormValues) => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      if (values.patientId) {
        params.append("patientId", values.patientId);
      }
      if (values.patientName) {
        params.append("patientName", values.patientName);
      }
      if (values.recordType !== "all") {
        params.append("recordType", values.recordType);
      }
      if (values.bundleStatus !== "all") {
        params.append("status", values.bundleStatus);
      }
      if (values.startDate) {
        params.append("startDate", values.startDate.toISOString());
      }
      if (values.endDate) {
        params.append("endDate", values.endDate.toISOString());
      }

      // Make API request
      const response = await fetch(`/api/abdm/health-record/search?${params}`);
      if (!response.ok) {
        throw new Error("Failed to search health records");
      }
      const data = await response.json();
      setResults(data.results);
    } catch (error) {
      console.error("Error searching health records:", error);
      setError(
        error instanceof Error ? error.message : "An unknown error occurred",
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle view bundle
  const handleViewBundle = (bundleId: string) => {
    router.push(`/abdm/health-records/bundles/${bundleId}`);
  };

  // Handle view patient
  const handleViewPatient = (patientId: string) => {
    router.push(`/patients/${patientId}`);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "PPP p");
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Search className="mr-2 h-5 w-5 text-primary" />
          Health Record Search
        </CardTitle>
        <CardDescription>
          Search for health records by patient, type, status, and date
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="patientId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Patient ID</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter patient ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="patientName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Patient Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter patient name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="recordType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Record Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select record type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="vitals">Vitals</SelectItem>
                        <SelectItem value="prescription">
                          Prescription
                        </SelectItem>
                        <SelectItem value="clinicalNote">
                          Clinical Note
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="bundleStatus"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bundle Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select bundle status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="created">Created</SelectItem>
                        <SelectItem value="packaged">Packaged</SelectItem>
                        <SelectItem value="uploaded">Uploaded</SelectItem>
                        <SelectItem value="error">Error</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date</FormLabel>
                    <DatePicker date={field.value} setDate={field.onChange} />
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date</FormLabel>
                    <DatePicker date={field.value} setDate={field.onChange} />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Searching...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Search
                </>
              )}
            </Button>
          </form>
        </Form>

        {error && <div className="mt-6 text-destructive">{error}</div>}

        {results.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-4">Search Results</h3>
            <div className="space-y-4">
              {results.map((result) => (
                <div key={result.id} className="p-4 border rounded-md">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <FileText className="h-4 w-4 text-primary" />
                        <span className="font-medium">
                          Bundle: {result.bundleId.substring(0, 8)}...
                        </span>
                        <Badge
                          variant={
                            result.status === "created"
                              ? "outline"
                              : result.status === "packaged"
                                ? "secondary"
                                : result.status === "uploaded"
                                  ? "default"
                                  : "destructive"
                          }
                        >
                          {result.status}
                        </Badge>
                        {result.sourceType && (
                          <Badge variant="outline">{result.sourceType}</Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Patient:{" "}
                        <Button
                          variant="link"
                          className="p-0 h-auto text-sm"
                          onClick={() => handleViewPatient(result.patientId)}
                        >
                          {result.patient.firstName} {result.patient.lastName}
                        </Button>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Created: {formatDate(result.createdAt)}
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewBundle(result.bundleId)}
                    >
                      View Bundle
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        {results.length > 0 && (
          <div className="text-sm text-muted-foreground">
            Found {results.length} results
          </div>
        )}
      </CardFooter>
    </Card>
  );
}
