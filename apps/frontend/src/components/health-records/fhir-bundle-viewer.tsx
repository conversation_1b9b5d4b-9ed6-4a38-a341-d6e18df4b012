"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Loader2, Download, Copy, Check } from "lucide-react";
import { format } from "date-fns";

interface FhirBundleViewerProps {
  bundleId: string;
  initialBundle?: any;
}

export function FhirBundleViewer({
  bundleId,
  initialBundle,
}: FhirBundleViewerProps) {
  const [bundle, setBundle] = useState<any>(initialBundle);
  const [loading, setLoading] = useState(!initialBundle);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("formatted");
  const [copied, setCopied] = useState(false);

  // Fetch bundle if not provided
  const fetchBundle = async () => {
    if (initialBundle) {
      console.log(
        "🔍 FHIR Bundle Viewer - Using initial bundle:",
        initialBundle,
      );
      setBundle(initialBundle);
      return;
    }

    try {
      setLoading(true);
      console.log("🔍 FHIR Bundle Viewer - Fetching bundle:", bundleId);

      const response = await fetch(
        `/api/abdm/health-record/bundles/${bundleId}`,
      );

      console.log("🔍 FHIR Bundle Viewer - Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("🔍 FHIR Bundle Viewer - Error response:", errorText);
        throw new Error(
          `Failed to fetch FHIR bundle: ${response.status} ${errorText}`,
        );
      }

      const data = await response.json();
      console.log("🔍 FHIR Bundle Viewer - Received data:", data);

      // The API returns { bundle, resources }, so we need to use data.bundle
      if (data.bundle) {
        setBundle(data.bundle);
        console.log("🔍 FHIR Bundle Viewer - Bundle set:", data.bundle);
      } else {
        console.error("🔍 FHIR Bundle Viewer - No bundle in response:", data);
        setError("No bundle data found in response");
      }
    } catch (error) {
      console.error("Error fetching FHIR bundle:", error);
      setError(
        error instanceof Error ? error.message : "An unknown error occurred",
      );
    } finally {
      setLoading(false);
    }
  };

  // Call fetchBundle on mount if bundle not provided
  if (!initialBundle && !bundle && !loading && !error) {
    fetchBundle();
  }

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "PPP p");
    } catch (e) {
      return dateString;
    }
  };

  // Handle copy to clipboard
  const handleCopy = () => {
    navigator.clipboard.writeText(JSON.stringify(bundle.bundleJson, null, 2));
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Handle download
  const handleDownload = () => {
    const dataStr = JSON.stringify(bundle.bundleJson, null, 2);
    const dataUri =
      "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);
    const downloadAnchorNode = document.createElement("a");
    downloadAnchorNode.setAttribute("href", dataUri);
    downloadAnchorNode.setAttribute(
      "download",
      `fhir-bundle-${bundle.bundleId}.json`,
    );
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };

  // Render resource type badge
  const renderResourceTypeBadge = (resourceType: string) => {
    const colors: Record<string, string> = {
      Patient: "bg-blue-100 text-blue-800",
      Practitioner: "bg-purple-100 text-purple-800",
      Observation: "bg-green-100 text-green-800",
      MedicationRequest: "bg-red-100 text-red-800",
      Condition: "bg-yellow-100 text-yellow-800",
      Composition: "bg-indigo-100 text-indigo-800",
      Bundle: "bg-gray-100 text-gray-800",
    };

    return (
      <Badge
        className={`${
          colors[resourceType] || "bg-gray-100 text-gray-800"
        } font-medium`}
      >
        {resourceType}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>FHIR Bundle Viewer</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>FHIR Bundle Viewer</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-destructive">Error: {error}</div>
        </CardContent>
      </Card>
    );
  }

  if (!bundle) {
    console.log("🔍 FHIR Bundle Viewer - No bundle state:", {
      initialBundle: !!initialBundle,
      bundle: !!bundle,
      loading,
      error,
      bundleId,
    });
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>FHIR Bundle Viewer</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground">Bundle not found</div>
          <div className="text-xs text-muted-foreground mt-2">
            Debug: bundleId={bundleId}, loading={loading.toString()}, error=
            {error || "none"}
          </div>
        </CardContent>
      </Card>
    );
  }

  console.log("🔍 FHIR Bundle Viewer - Rendering bundle:", {
    bundleId: bundle.bundleId,
    hasJson: !!bundle.bundleJson,
    jsonEntries: bundle.bundleJson?.entry?.length || 0,
  });

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>FHIR Bundle</CardTitle>
          <div className="text-sm text-muted-foreground mt-1">
            ID: {bundle.bundleId}
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="flex items-center gap-1"
          >
            {copied ? (
              <>
                <Check className="h-4 w-4" /> Copied
              </>
            ) : (
              <>
                <Copy className="h-4 w-4" /> Copy
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="flex items-center gap-1"
          >
            <Download className="h-4 w-4" /> Download
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-background p-4 rounded-lg border">
            <div className="text-sm font-medium text-muted-foreground">
              Bundle Type
            </div>
            <div className="font-medium">{bundle.bundleType}</div>
          </div>
          <div className="bg-background p-4 rounded-lg border">
            <div className="text-sm font-medium text-muted-foreground">
              Status
            </div>
            <div className="font-medium">
              <Badge
                variant={
                  bundle.status === "created"
                    ? "outline"
                    : bundle.status === "packaged"
                      ? "secondary"
                      : bundle.status === "uploaded"
                        ? "default"
                        : "destructive"
                }
              >
                {bundle.status}
              </Badge>
            </div>
          </div>
          <div className="bg-background p-4 rounded-lg border">
            <div className="text-sm font-medium text-muted-foreground">
              Created
            </div>
            <div className="font-medium">{formatDate(bundle.createdAt)}</div>
          </div>
        </div>

        <Tabs
          defaultValue="formatted"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="formatted">Formatted</TabsTrigger>
            <TabsTrigger value="resources">Resources</TabsTrigger>
            <TabsTrigger value="raw">Raw JSON</TabsTrigger>
          </TabsList>

          <TabsContent value="formatted" className="space-y-4">
            <div className="rounded-md border p-4">
              <h3 className="text-lg font-medium mb-2">Bundle Information</h3>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm font-medium">Bundle ID</div>
                <div className="text-sm">{bundle.bundleId}</div>
                <div className="text-sm font-medium">Type</div>
                <div className="text-sm">{bundle.bundleType}</div>
                <div className="text-sm font-medium">Status</div>
                <div className="text-sm">{bundle.status}</div>
                {bundle.transactionId && (
                  <>
                    <div className="text-sm font-medium">Transaction ID</div>
                    <div className="text-sm">{bundle.transactionId}</div>
                  </>
                )}
                {bundle.consentId && (
                  <>
                    <div className="text-sm font-medium">Consent ID</div>
                    <div className="text-sm">{bundle.consentId}</div>
                  </>
                )}
                {bundle.packageChecksum && (
                  <>
                    <div className="text-sm font-medium">Checksum</div>
                    <div
                      className="text-sm truncate"
                      title={bundle.packageChecksum}
                    >
                      {bundle.packageChecksum}
                    </div>
                  </>
                )}
                <div className="text-sm font-medium">Created</div>
                <div className="text-sm">{formatDate(bundle.createdAt)}</div>
                <div className="text-sm font-medium">Updated</div>
                <div className="text-sm">{formatDate(bundle.updatedAt)}</div>
              </div>
            </div>

            {bundle.bundleJson && bundle.bundleJson.entry && (
              <div className="rounded-md border p-4">
                <h3 className="text-lg font-medium mb-2">Resources</h3>
                <div className="space-y-2">
                  {bundle.bundleJson.entry.map((entry: any, index: number) => (
                    <div key={index} className="p-3 border rounded-md">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {renderResourceTypeBadge(entry.resource.resourceType)}
                          <span className="font-medium">
                            {entry.resource.id}
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            navigator.clipboard.writeText(
                              JSON.stringify(entry.resource, null, 2),
                            );
                          }}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="resources">
            {bundle.bundleJson && bundle.bundleJson.entry && (
              <Accordion type="single" collapsible className="w-full">
                {bundle.bundleJson.entry.map((entry: any, index: number) => (
                  <AccordionItem
                    key={index}
                    value={`item-${index}`}
                    className="border rounded-md mb-2"
                  >
                    <AccordionTrigger className="px-4">
                      <div className="flex items-center gap-2">
                        {renderResourceTypeBadge(entry.resource.resourceType)}
                        <span className="font-medium">{entry.resource.id}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pb-4">
                      <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                        {JSON.stringify(entry.resource, null, 2)}
                      </pre>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </TabsContent>

          <TabsContent value="raw">
            <pre className="bg-muted p-4 rounded-md overflow-auto text-xs max-h-[500px]">
              {JSON.stringify(bundle.bundleJson, null, 2)}
            </pre>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
