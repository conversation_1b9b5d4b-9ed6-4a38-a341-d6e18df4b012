"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import { Loader2 } from "lucide-react";

interface HealthRecordStatistics {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  packageOperations: number;
  uploadOperations: number;
  validateOperations: number;
  successRate: number;
  recordTypes: Record<string, number>;
  operationsByDay: Record<string, number>;
}

interface HealthRecordDashboardProps {
  organizationId: string;
  days?: number;
}

export function HealthRecordDashboard({
  organizationId,
  days = 30,
}: HealthRecordDashboardProps) {
  const [statistics, setStatistics] = useState<HealthRecordStatistics | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `/api/abdm/health-record/statistics?days=${days}`,
        );
        if (!response.ok) {
          throw new Error("Failed to fetch health record statistics");
        }
        const data = await response.json();
        setStatistics(data);
      } catch (error) {
        console.error("Error fetching health record statistics:", error);
        setError(
          error instanceof Error ? error.message : "An unknown error occurred",
        );
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, [organizationId, days]);

  // Prepare data for charts
  const prepareOperationTypeData = () => {
    if (!statistics) return [];
    return [
      { name: "Package", value: statistics.packageOperations },
      { name: "Upload", value: statistics.uploadOperations },
      { name: "Validate", value: statistics.validateOperations },
    ];
  };

  const prepareStatusData = () => {
    if (!statistics) return [];
    return [
      { name: "Success", value: statistics.successfulOperations },
      { name: "Failed", value: statistics.failedOperations },
    ];
  };

  const prepareRecordTypeData = () => {
    if (!statistics) return [];
    return Object.entries(statistics.recordTypes).map(([name, value]) => ({
      name,
      value,
    }));
  };

  const prepareDailyOperationsData = () => {
    if (!statistics) return [];
    return Object.entries(statistics.operationsByDay)
      .map(([date, count]) => ({
        date,
        count,
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  };

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Statistics</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-destructive">Error: {error}</div>
        </CardContent>
      </Card>
    );
  }

  if (!statistics) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground">No data available</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Health Record Statistics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-background p-4 rounded-lg border">
            <div className="text-2xl font-bold">
              {statistics.totalOperations}
            </div>
            <div className="text-muted-foreground">Total Operations</div>
          </div>
          <div className="bg-background p-4 rounded-lg border">
            <div className="text-2xl font-bold">
              {statistics.successRate.toFixed(1)}%
            </div>
            <div className="text-muted-foreground">Success Rate</div>
          </div>
          <div className="bg-background p-4 rounded-lg border">
            <div className="text-2xl font-bold">
              {statistics.uploadOperations}
            </div>
            <div className="text-muted-foreground">Uploads</div>
          </div>
        </div>

        <Tabs defaultValue="daily" className="w-full">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="daily">Daily Operations</TabsTrigger>
            <TabsTrigger value="types">Operation Types</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
            <TabsTrigger value="records">Record Types</TabsTrigger>
          </TabsList>

          <TabsContent value="daily" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={prepareDailyOperationsData()}
                margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Operations" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="types" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={prepareOperationTypeData()}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  label={({ name, percent }) =>
                    `${name}: ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {prepareOperationTypeData().map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="status" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={prepareStatusData()}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  label={({ name, percent }) =>
                    `${name}: ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  <Cell fill="#4CAF50" />
                  <Cell fill="#F44336" />
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="records" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={prepareRecordTypeData()}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  label={({ name, percent }) =>
                    `${name}: ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {prepareRecordTypeData().map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
