"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2, RefreshCw } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface HealthRecordOperation {
  id: string;
  operation: string;
  status: string;
  patientId: string;
  patient: {
    firstName: string;
    lastName: string;
  };
  recordType: string | null;
  recordId: string | null;
  bundleId: string | null;
  consentId: string | null;
  transactionId: string | null;
  errorMessage: string | null;
  timestamp: string;
}

interface HealthRecordOperationsListProps {
  patientId?: string;
  limit?: number;
}

export function HealthRecordOperationsList({
  patientId,
  limit = 10,
}: HealthRecordOperationsListProps) {
  const router = useRouter();
  const [operations, setOperations] = useState<HealthRecordOperation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [operationFilter, setOperationFilter] = useState<string>("all");

  const fetchOperations = async () => {
    try {
      setLoading(true);
      const url = patientId
        ? `/api/patients/${patientId}/health-record-operations?limit=${limit}`
        : `/api/abdm/health-record/operations?limit=${limit}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error("Failed to fetch health record operations");
      }
      const data = await response.json();
      setOperations(data.operations);
    } catch (error) {
      console.error("Error fetching health record operations:", error);
      setError(
        error instanceof Error ? error.message : "An unknown error occurred",
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOperations();
  }, [patientId, limit]);

  const filteredOperations = operations.filter((op) => {
    const statusMatch =
      statusFilter === "all" || op.status.toLowerCase() === statusFilter;
    const operationMatch =
      operationFilter === "all" ||
      op.operation.toLowerCase() === operationFilter;
    return statusMatch && operationMatch;
  });

  const handleRefresh = () => {
    fetchOperations();
  };

  const handleViewPatient = (patientId: string) => {
    router.push(`/patients/${patientId}`);
  };

  const handleViewBundle = (bundleId: string) => {
    router.push(`/abdm/health-records/bundles/${bundleId}`);
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Operations</CardTitle>
          <CardDescription>Recent health record operations</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Operations</CardTitle>
          <CardDescription>Recent health record operations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-destructive">Error: {error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Health Record Operations</CardTitle>
            <CardDescription>Recent health record operations</CardDescription>
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={handleRefresh}
            title="Refresh"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Status:</span>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="success">Success</SelectItem>
                <SelectItem value="failure">Failure</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Operation:</span>
            <Select value={operationFilter} onValueChange={setOperationFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Operation" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="package">Package</SelectItem>
                <SelectItem value="upload">Upload</SelectItem>
                <SelectItem value="validate">Validate</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {filteredOperations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No operations found
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Operation</TableHead>
                  <TableHead>Status</TableHead>
                  {!patientId && <TableHead>Patient</TableHead>}
                  <TableHead>Record Type</TableHead>
                  <TableHead>Time</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOperations.map((op) => (
                  <TableRow key={op.id}>
                    <TableCell className="font-medium">
                      {op.operation}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          op.status === "SUCCESS" ? "default" : "destructive"
                        }
                      >
                        {op.status}
                      </Badge>
                    </TableCell>
                    {!patientId && (
                      <TableCell>
                        <Button
                          variant="link"
                          className="p-0 h-auto"
                          onClick={() => handleViewPatient(op.patientId)}
                        >
                          {op.patient.firstName} {op.patient.lastName}
                        </Button>
                      </TableCell>
                    )}
                    <TableCell>{op.recordType || "N/A"}</TableCell>
                    <TableCell>
                      {formatDistanceToNow(new Date(op.timestamp), {
                        addSuffix: true,
                      })}
                    </TableCell>
                    <TableCell>
                      {op.bundleId && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewBundle(op.bundleId!)}
                        >
                          View Bundle
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {filteredOperations.length} of {operations.length} operations
        </div>
        {operations.length > limit && (
          <Button variant="outline" size="sm">
            View All
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
