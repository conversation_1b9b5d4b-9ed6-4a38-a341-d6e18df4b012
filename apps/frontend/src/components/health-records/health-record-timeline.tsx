"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Loader2,
  AlertCircle,
  FileText,
  Pill,
  Clipboard,
  Calendar,
  ChevronDown,
  Filter,
} from "lucide-react";
import { format, parseISO } from "date-fns";
import { Fetch } from "@/services/fetch";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HealthRecordTimelineProps {
  patientId: string;
}

export function HealthRecordTimeline({ patientId }: HealthRecordTimelineProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resources, setResources] = useState<any[]>([]);
  const [filteredResources, setFilteredResources] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("all");
  const [selectedFilters, setSelectedFilters] = useState<string[]>([
    "DiagnosticReport",
    "MedicationRequest",
    "Condition",
    "Observation",
  ]);

  // Fetch health records
  useEffect(() => {
    const fetchHealthRecords = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await Fetch.get(
          `/api/abdm/health-record/fetched?patientId=${patientId}`,
        );

        if (response && response.success) {
          // Combine all resources and add source information
          const allResources = [];

          // Add diagnostic reports
          if (response.resourcesByType?.diagnosticReports) {
            allResources.push(
              ...response.resourcesByType.diagnosticReports.map(
                (resource: any) => ({
                  ...resource,
                  displayType: "Diagnostic Report",
                  date: getResourceDate(resource, "DiagnosticReport"),
                }),
              ),
            );
          }

          // Add prescriptions
          if (response.resourcesByType?.prescriptions) {
            allResources.push(
              ...response.resourcesByType.prescriptions.map(
                (resource: any) => ({
                  ...resource,
                  displayType: "Prescription",
                  date: getResourceDate(resource, "MedicationRequest"),
                }),
              ),
            );
          }

          // Add clinical notes
          if (response.resourcesByType?.clinicalNotes) {
            allResources.push(
              ...response.resourcesByType.clinicalNotes.map(
                (resource: any) => ({
                  ...resource,
                  displayType: "Clinical Note",
                  date: getResourceDate(resource, "Condition"),
                }),
              ),
            );
          }

          // Add observations
          if (response.resourcesByType?.observations) {
            allResources.push(
              ...response.resourcesByType.observations.map((resource: any) => ({
                ...resource,
                displayType: "Observation",
                date: getResourceDate(resource, "Observation"),
              })),
            );
          }

          // Sort by date (newest first)
          const sortedResources = allResources.sort((a, b) => {
            if (!a.date) return 1;
            if (!b.date) return -1;
            return new Date(b.date).getTime() - new Date(a.date).getTime();
          });

          setResources(sortedResources);
          setFilteredResources(sortedResources);
        }
      } catch (error) {
        console.error("Error fetching health records:", error);
        setError(
          error instanceof Error
            ? error.message
            : "Failed to fetch health records",
        );
      } finally {
        setLoading(false);
      }
    };

    fetchHealthRecords();
  }, [patientId]);

  // Get date from resource based on type
  const getResourceDate = (resource: any, resourceType: string) => {
    const fhirJson = resource.fhirJson;

    switch (resourceType) {
      case "DiagnosticReport":
        return (
          fhirJson.effectiveDateTime || fhirJson.issued || resource.createdAt
        );
      case "MedicationRequest":
        return fhirJson.authoredOn || resource.createdAt;
      case "Condition":
        return (
          fhirJson.recordedDate || fhirJson.onsetDateTime || resource.createdAt
        );
      case "Observation":
        return (
          fhirJson.effectiveDateTime || fhirJson.issued || resource.createdAt
        );
      default:
        return resource.createdAt;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), "PPP");
    } catch (e) {
      return "Unknown date";
    }
  };

  // Get icon based on resource type
  const getResourceIcon = (resourceType: string) => {
    switch (resourceType) {
      case "DiagnosticReport":
        return <Clipboard className="h-4 w-4 text-blue-500" />;
      case "MedicationRequest":
        return <Pill className="h-4 w-4 text-green-500" />;
      case "Condition":
        return <FileText className="h-4 w-4 text-amber-500" />;
      case "Observation":
        return <FileText className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get resource title
  const getResourceTitle = (resource: any) => {
    const fhirJson = resource.fhirJson;
    const resourceType = resource.resourceType;

    switch (resourceType) {
      case "DiagnosticReport":
        return (
          fhirJson.code?.text ||
          fhirJson.code?.coding?.[0]?.display ||
          "Diagnostic Report"
        );
      case "MedicationRequest":
        return (
          fhirJson.medicationCodeableConcept?.text ||
          fhirJson.medicationCodeableConcept?.coding?.[0]?.display ||
          "Medication"
        );
      case "Condition":
        return (
          fhirJson.code?.text ||
          fhirJson.code?.coding?.[0]?.display ||
          "Condition"
        );
      case "Observation":
        return (
          fhirJson.code?.text ||
          fhirJson.code?.coding?.[0]?.display ||
          "Observation"
        );
      default:
        return "Health Record";
    }
  };

  // Get resource status
  const getResourceStatus = (resource: any) => {
    const fhirJson = resource.fhirJson;
    return fhirJson.status || "unknown";
  };

  // Get resource details
  const getResourceDetails = (resource: any) => {
    const fhirJson = resource.fhirJson;
    const resourceType = resource.resourceType;

    switch (resourceType) {
      case "DiagnosticReport":
        return fhirJson.conclusion || "No details available";
      case "MedicationRequest":
        return (
          fhirJson.dosageInstruction?.[0]?.text ||
          "No dosage instructions available"
        );
      case "Condition":
        return fhirJson.note?.[0]?.text || "No notes available";
      case "Observation":
        if (fhirJson.valueQuantity) {
          return `${fhirJson.valueQuantity.value} ${
            fhirJson.valueQuantity.unit || ""
          }`;
        }
        if (fhirJson.valueString) {
          return fhirJson.valueString;
        }
        if (fhirJson.valueCodeableConcept) {
          return (
            fhirJson.valueCodeableConcept.text ||
            fhirJson.valueCodeableConcept.coding?.[0]?.display ||
            "No value available"
          );
        }
        return "No value available";
      default:
        return "No details available";
    }
  };

  // Get resource provider
  const getResourceProvider = (resource: any) => {
    const fhirJson = resource.fhirJson;

    if (fhirJson.performer && fhirJson.performer.length > 0) {
      return fhirJson.performer[0].display || "Unknown provider";
    }

    if (fhirJson.recorder) {
      return fhirJson.recorder.display || "Unknown provider";
    }

    if (fhirJson.requester) {
      return fhirJson.requester.display || "Unknown provider";
    }

    return "Unknown provider";
  };

  // Filter resources by type
  useEffect(() => {
    if (activeTab === "all") {
      setFilteredResources(
        resources.filter((resource) =>
          selectedFilters.includes(resource.resourceType),
        ),
      );
    } else {
      setFilteredResources(
        resources.filter(
          (resource) =>
            resource.resourceType === activeTab &&
            selectedFilters.includes(resource.resourceType),
        ),
      );
    }
  }, [activeTab, resources, selectedFilters]);

  // Handle filter change
  const handleFilterChange = (resourceType: string) => {
    setSelectedFilters((prev) => {
      if (prev.includes(resourceType)) {
        return prev.filter((type) => type !== resourceType);
      } else {
        return [...prev, resourceType];
      }
    });
  };

  // Group resources by date
  const groupedResources = filteredResources.reduce<Record<string, any[]>>(
    (groups, resource) => {
      const date = resource.date ? formatDate(resource.date) : "Unknown date";
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(resource);
      return groups;
    },
    {},
  );

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Timeline</CardTitle>
          <CardDescription>
            View health records in chronological order
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Timeline</CardTitle>
          <CardDescription>
            View health records in chronological order
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Health Record Timeline</CardTitle>
            <CardDescription>
              View health records in chronological order
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuCheckboxItem
                checked={selectedFilters.includes("DiagnosticReport")}
                onCheckedChange={() => handleFilterChange("DiagnosticReport")}
              >
                Diagnostic Reports
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedFilters.includes("MedicationRequest")}
                onCheckedChange={() => handleFilterChange("MedicationRequest")}
              >
                Medications
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedFilters.includes("Condition")}
                onCheckedChange={() => handleFilterChange("Condition")}
              >
                Conditions
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedFilters.includes("Observation")}
                onCheckedChange={() => handleFilterChange("Observation")}
              >
                Observations
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue="all"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="DiagnosticReport">Diagnostics</TabsTrigger>
            <TabsTrigger value="MedicationRequest">Medications</TabsTrigger>
            <TabsTrigger value="Condition">Conditions</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {filteredResources.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No records found</AlertTitle>
                <AlertDescription>
                  No health records match the selected filters.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-6">
                {Object.entries(groupedResources).map(
                  ([date, dateResources]) => (
                    <div key={date} className="space-y-2">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        <h3 className="text-sm font-medium">{date}</h3>
                      </div>
                      <div className="ml-6 space-y-2 border-l pl-4">
                        {dateResources.map((resource, index) => (
                          <Collapsible
                            key={`${resource.id}-${index}`}
                            className="border rounded-md"
                          >
                            <CollapsibleTrigger className="flex justify-between items-center w-full p-3 text-left">
                              <div className="flex items-center">
                                {getResourceIcon(resource.resourceType)}
                                <span className="font-medium ml-2">
                                  {getResourceTitle(resource)}
                                </span>
                                <Badge variant="outline" className="ml-2">
                                  {resource.displayType}
                                </Badge>
                              </div>
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            </CollapsibleTrigger>
                            <CollapsibleContent className="p-3 pt-0 border-t">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                                <div>
                                  <h4 className="text-sm font-medium text-muted-foreground">
                                    Status
                                  </h4>
                                  <p>{getResourceStatus(resource)}</p>
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-muted-foreground">
                                    Provider
                                  </h4>
                                  <p>{getResourceProvider(resource)}</p>
                                </div>
                              </div>
                              <div className="mt-3">
                                <h4 className="text-sm font-medium text-muted-foreground">
                                  Details
                                </h4>
                                <p className="whitespace-pre-wrap">
                                  {getResourceDetails(resource)}
                                </p>
                              </div>
                              <div className="mt-3 pt-3 border-t">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="w-full"
                                >
                                  View Full Record
                                </Button>
                              </div>
                            </CollapsibleContent>
                          </Collapsible>
                        ))}
                      </div>
                    </div>
                  ),
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="DiagnosticReport" className="space-y-4">
            {filteredResources.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No diagnostic reports found</AlertTitle>
                <AlertDescription>
                  No diagnostic reports are available for this patient.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-6">
                {Object.entries(groupedResources).map(
                  ([date, dateResources]) => (
                    <div key={date} className="space-y-2">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        <h3 className="text-sm font-medium">{date}</h3>
                      </div>
                      <div className="ml-6 space-y-2 border-l pl-4">
                        {dateResources.map((resource, index) => (
                          <Collapsible
                            key={`${resource.id}-${index}`}
                            className="border rounded-md"
                          >
                            <CollapsibleTrigger className="flex justify-between items-center w-full p-3 text-left">
                              <div className="flex items-center">
                                {getResourceIcon(resource.resourceType)}
                                <span className="font-medium ml-2">
                                  {getResourceTitle(resource)}
                                </span>
                              </div>
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            </CollapsibleTrigger>
                            <CollapsibleContent className="p-3 pt-0 border-t">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                                <div>
                                  <h4 className="text-sm font-medium text-muted-foreground">
                                    Status
                                  </h4>
                                  <p>{getResourceStatus(resource)}</p>
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-muted-foreground">
                                    Provider
                                  </h4>
                                  <p>{getResourceProvider(resource)}</p>
                                </div>
                              </div>
                              <div className="mt-3">
                                <h4 className="text-sm font-medium text-muted-foreground">
                                  Details
                                </h4>
                                <p className="whitespace-pre-wrap">
                                  {getResourceDetails(resource)}
                                </p>
                              </div>
                              <div className="mt-3 pt-3 border-t">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="w-full"
                                >
                                  View Full Record
                                </Button>
                              </div>
                            </CollapsibleContent>
                          </Collapsible>
                        ))}
                      </div>
                    </div>
                  ),
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="MedicationRequest" className="space-y-4">
            {filteredResources.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No medications found</AlertTitle>
                <AlertDescription>
                  No medication records are available for this patient.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-6">
                {Object.entries(groupedResources).map(
                  ([date, dateResources]) => (
                    <div key={date} className="space-y-2">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        <h3 className="text-sm font-medium">{date}</h3>
                      </div>
                      <div className="ml-6 space-y-2 border-l pl-4">
                        {dateResources.map((resource, index) => (
                          <Collapsible
                            key={`${resource.id}-${index}`}
                            className="border rounded-md"
                          >
                            <CollapsibleTrigger className="flex justify-between items-center w-full p-3 text-left">
                              <div className="flex items-center">
                                {getResourceIcon(resource.resourceType)}
                                <span className="font-medium ml-2">
                                  {getResourceTitle(resource)}
                                </span>
                              </div>
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            </CollapsibleTrigger>
                            <CollapsibleContent className="p-3 pt-0 border-t">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                                <div>
                                  <h4 className="text-sm font-medium text-muted-foreground">
                                    Status
                                  </h4>
                                  <p>{getResourceStatus(resource)}</p>
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-muted-foreground">
                                    Provider
                                  </h4>
                                  <p>{getResourceProvider(resource)}</p>
                                </div>
                              </div>
                              <div className="mt-3">
                                <h4 className="text-sm font-medium text-muted-foreground">
                                  Dosage Instructions
                                </h4>
                                <p className="whitespace-pre-wrap">
                                  {getResourceDetails(resource)}
                                </p>
                              </div>
                              <div className="mt-3 pt-3 border-t">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="w-full"
                                >
                                  View Full Record
                                </Button>
                              </div>
                            </CollapsibleContent>
                          </Collapsible>
                        ))}
                      </div>
                    </div>
                  ),
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="Condition" className="space-y-4">
            {filteredResources.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No conditions found</AlertTitle>
                <AlertDescription>
                  No condition records are available for this patient.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-6">
                {Object.entries(groupedResources).map(
                  ([date, dateResources]) => (
                    <div key={date} className="space-y-2">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        <h3 className="text-sm font-medium">{date}</h3>
                      </div>
                      <div className="ml-6 space-y-2 border-l pl-4">
                        {dateResources.map((resource, index) => (
                          <Collapsible
                            key={`${resource.id}-${index}`}
                            className="border rounded-md"
                          >
                            <CollapsibleTrigger className="flex justify-between items-center w-full p-3 text-left">
                              <div className="flex items-center">
                                {getResourceIcon(resource.resourceType)}
                                <span className="font-medium ml-2">
                                  {getResourceTitle(resource)}
                                </span>
                              </div>
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            </CollapsibleTrigger>
                            <CollapsibleContent className="p-3 pt-0 border-t">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                                <div>
                                  <h4 className="text-sm font-medium text-muted-foreground">
                                    Status
                                  </h4>
                                  <p>{getResourceStatus(resource)}</p>
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-muted-foreground">
                                    Provider
                                  </h4>
                                  <p>{getResourceProvider(resource)}</p>
                                </div>
                              </div>
                              <div className="mt-3">
                                <h4 className="text-sm font-medium text-muted-foreground">
                                  Notes
                                </h4>
                                <p className="whitespace-pre-wrap">
                                  {getResourceDetails(resource)}
                                </p>
                              </div>
                              <div className="mt-3 pt-3 border-t">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="w-full"
                                >
                                  View Full Record
                                </Button>
                              </div>
                            </CollapsibleContent>
                          </Collapsible>
                        ))}
                      </div>
                    </div>
                  ),
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
