"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { FileText, Pill, Stethoscope, Clipboard } from "lucide-react";
import { format } from "date-fns";

interface FhirResourceDisplayProps {
  resources: any[];
  resourceType:
    | "DiagnosticReport"
    | "MedicationRequest"
    | "Condition"
    | "Observation";
  title: string;
}

export function FhirResourceDisplay({
  resources,
  resourceType,
  title,
}: FhirResourceDisplayProps) {
  const [activeTab, setActiveTab] = useState("formatted");

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "PPP p");
    } catch (e) {
      return dateString;
    }
  };

  // Get icon based on resource type
  const getIcon = () => {
    switch (resourceType) {
      case "DiagnosticReport":
        return <Clipboard className="h-5 w-5 text-primary" />;
      case "MedicationRequest":
        return <Pill className="h-5 w-5 text-primary" />;
      case "Condition":
        return <Stethoscope className="h-5 w-5 text-primary" />;
      case "Observation":
        return <FileText className="h-5 w-5 text-primary" />;
      default:
        return <FileText className="h-5 w-5 text-primary" />;
    }
  };

  // Format diagnostic report
  const formatDiagnosticReport = (resource: any) => {
    const fhirJson = resource.fhirJson;
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Test</h3>
            <p className="font-medium">
              {fhirJson.code?.text ||
                fhirJson.code?.coding?.[0]?.display ||
                "Unknown Test"}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="font-medium">
              {fhirJson.effectiveDateTime
                ? formatDate(fhirJson.effectiveDateTime)
                : "Unknown Date"}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
          <Badge
            variant={
              fhirJson.status === "final"
                ? "default"
                : fhirJson.status === "preliminary"
                  ? "secondary"
                  : "outline"
            }
          >
            {fhirJson.status || "Unknown"}
          </Badge>
        </div>

        {fhirJson.conclusion && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Conclusion
            </h3>
            <p>{fhirJson.conclusion}</p>
          </div>
        )}

        {fhirJson.result && fhirJson.result.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Results
            </h3>
            <div className="space-y-2 mt-2">
              {fhirJson.result.map((result: any, index: number) => (
                <div key={index} className="p-2 border rounded-md">
                  <p className="font-medium">
                    {result.display || "Result " + (index + 1)}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Format medication request
  const formatMedicationRequest = (resource: any) => {
    const fhirJson = resource.fhirJson;
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Medication
            </h3>
            <p className="font-medium">
              {fhirJson.medicationCodeableConcept?.text ||
                fhirJson.medicationCodeableConcept?.coding?.[0]?.display ||
                "Unknown Medication"}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="font-medium">
              {fhirJson.authoredOn
                ? formatDate(fhirJson.authoredOn)
                : "Unknown Date"}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
          <Badge
            variant={
              fhirJson.status === "active"
                ? "default"
                : fhirJson.status === "completed"
                  ? "secondary"
                  : "outline"
            }
          >
            {fhirJson.status || "Unknown"}
          </Badge>
        </div>

        {fhirJson.dosageInstruction &&
          fhirJson.dosageInstruction.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Dosage Instructions
              </h3>
              <p>
                {fhirJson.dosageInstruction[0].text ||
                  "No instructions provided"}
              </p>
            </div>
          )}

        {fhirJson.note && fhirJson.note.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p>{fhirJson.note[0].text || "No notes provided"}</p>
          </div>
        )}
      </div>
    );
  };

  // Format condition
  const formatCondition = (resource: any) => {
    const fhirJson = resource.fhirJson;
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Condition
            </h3>
            <p className="font-medium">
              {fhirJson.code?.text ||
                fhirJson.code?.coding?.[0]?.display ||
                "Unknown Condition"}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="font-medium">
              {fhirJson.onsetDateTime
                ? formatDate(fhirJson.onsetDateTime)
                : fhirJson.recordedDate
                  ? formatDate(fhirJson.recordedDate)
                  : "Unknown Date"}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">
            Clinical Status
          </h3>
          <Badge
            variant={
              fhirJson.clinicalStatus?.coding?.[0]?.code === "active"
                ? "default"
                : fhirJson.clinicalStatus?.coding?.[0]?.code === "resolved"
                  ? "secondary"
                  : "outline"
            }
          >
            {fhirJson.clinicalStatus?.coding?.[0]?.code || "Unknown"}
          </Badge>
        </div>

        {fhirJson.note && fhirJson.note.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p>{fhirJson.note[0].text || "No notes provided"}</p>
          </div>
        )}
      </div>
    );
  };

  // Format observation
  const formatObservation = (resource: any) => {
    const fhirJson = resource.fhirJson;
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Observation
            </h3>
            <p className="font-medium">
              {fhirJson.code?.text ||
                fhirJson.code?.coding?.[0]?.display ||
                "Unknown Observation"}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="font-medium">
              {fhirJson.effectiveDateTime
                ? formatDate(fhirJson.effectiveDateTime)
                : fhirJson.issued
                  ? formatDate(fhirJson.issued)
                  : "Unknown Date"}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
          <Badge variant={fhirJson.status === "final" ? "default" : "outline"}>
            {fhirJson.status || "Unknown"}
          </Badge>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Value</h3>
          {fhirJson.valueQuantity ? (
            <p>
              {fhirJson.valueQuantity.value} {fhirJson.valueQuantity.unit || ""}
            </p>
          ) : fhirJson.valueString ? (
            <p>{fhirJson.valueString}</p>
          ) : fhirJson.valueCodeableConcept ? (
            <p>
              {fhirJson.valueCodeableConcept.text ||
                fhirJson.valueCodeableConcept.coding?.[0]?.display ||
                "No value available"}
            </p>
          ) : (
            <p>No value available</p>
          )}
        </div>

        {fhirJson.note && fhirJson.note.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p>{fhirJson.note[0].text || "No notes provided"}</p>
          </div>
        )}
      </div>
    );
  };

  // Format resource based on type
  const formatResource = (resource: any) => {
    switch (resourceType) {
      case "DiagnosticReport":
        return formatDiagnosticReport(resource);
      case "MedicationRequest":
        return formatMedicationRequest(resource);
      case "Condition":
        return formatCondition(resource);
      case "Observation":
        return formatObservation(resource);
      default:
        return (
          <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
            {JSON.stringify(resource.fhirJson, null, 2)}
          </pre>
        );
    }
  };

  if (!resources || resources.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader className="flex flex-row items-center">
          <div className="flex items-center">
            {getIcon()}
            <CardTitle className="ml-2">{title}</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            No {title.toLowerCase()} found
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center">
        <div className="flex items-center">
          {getIcon()}
          <CardTitle className="ml-2">{title}</CardTitle>
        </div>
        <Badge className="ml-2">{resources.length}</Badge>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue="formatted"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="formatted">Formatted</TabsTrigger>
            <TabsTrigger value="raw">Raw JSON</TabsTrigger>
          </TabsList>

          <TabsContent value="formatted">
            <Accordion type="single" collapsible className="w-full">
              {resources.map((resource, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="border rounded-md mb-2"
                >
                  <AccordionTrigger className="px-4">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {resourceType === "DiagnosticReport"
                          ? resource.fhirJson.code?.text ||
                            resource.fhirJson.code?.coding?.[0]?.display ||
                            "Diagnostic Report"
                          : resourceType === "MedicationRequest"
                            ? resource.fhirJson.medicationCodeableConcept
                                ?.text ||
                              resource.fhirJson.medicationCodeableConcept
                                ?.coding?.[0]?.display ||
                              "Medication"
                            : resource.fhirJson.code?.text ||
                              resource.fhirJson.code?.coding?.[0]?.display ||
                              "Condition"}
                      </span>
                      <Badge variant="outline" className="ml-2">
                        {resource.fhirJson.effectiveDateTime
                          ? formatDate(resource.fhirJson.effectiveDateTime)
                          : resource.fhirJson.authoredOn
                            ? formatDate(resource.fhirJson.authoredOn)
                            : resource.fhirJson.recordedDate
                              ? formatDate(resource.fhirJson.recordedDate)
                              : formatDate(resource.createdAt)}
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4">
                    {formatResource(resource)}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </TabsContent>

          <TabsContent value="raw">
            <Accordion type="single" collapsible className="w-full">
              {resources.map((resource, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="border rounded-md mb-2"
                >
                  <AccordionTrigger className="px-4">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {resourceType === "DiagnosticReport"
                          ? resource.fhirJson.code?.text ||
                            resource.fhirJson.code?.coding?.[0]?.display ||
                            "Diagnostic Report"
                          : resourceType === "MedicationRequest"
                            ? resource.fhirJson.medicationCodeableConcept
                                ?.text ||
                              resource.fhirJson.medicationCodeableConcept
                                ?.coding?.[0]?.display ||
                              "Medication"
                            : resource.fhirJson.code?.text ||
                              resource.fhirJson.code?.coding?.[0]?.display ||
                              "Condition"}
                      </span>
                      <Badge variant="outline" className="ml-2">
                        {resource.fhirJson.effectiveDateTime
                          ? formatDate(resource.fhirJson.effectiveDateTime)
                          : resource.fhirJson.authoredOn
                            ? formatDate(resource.fhirJson.authoredOn)
                            : resource.fhirJson.recordedDate
                              ? formatDate(resource.fhirJson.recordedDate)
                              : formatDate(resource.createdAt)}
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4">
                    <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                      {JSON.stringify(resource.fhirJson, null, 2)}
                    </pre>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
