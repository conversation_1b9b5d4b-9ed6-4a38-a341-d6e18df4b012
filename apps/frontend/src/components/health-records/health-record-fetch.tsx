"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Loader2,
  RefreshCw,
  AlertCircle,
  Download,
  Clock,
  List,
} from "lucide-react";
import { toast } from "sonner";
import { Fetch } from "@/services/fetch";
import { FhirResourceDisplay } from "./fhir-resource-display";

interface HealthRecordFetchProps {
  patientId: string;
  patientName: string;
}

export function HealthRecordFetch({
  patientId,
  patientName,
}: HealthRecordFetchProps) {
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [consents, setConsents] = useState<any[]>([]);
  const [selectedConsent, setSelectedConsent] = useState<string | null>(null);
  const [diagnosticReports, setDiagnosticReports] = useState<any[]>([]);
  const [prescriptions, setPrescriptions] = useState<any[]>([]);
  const [clinicalNotes, setClinicalNotes] = useState<any[]>([]);
  const [observations, setObservations] = useState<any[]>([]);
  const [activeView, setActiveView] = useState<string>("categorized");

  // Load active consents
  useEffect(() => {
    const loadConsents = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await Fetch.get(
          `/api/patients/${patientId}/consents?status=GRANTED`,
        );

        if (response && response.consents) {
          setConsents(response.consents);
          if (response.consents.length > 0) {
            setSelectedConsent(response.consents[0].consentId);
          }
        }
      } catch (error) {
        console.error("Error loading consents:", error);
        setError(
          error instanceof Error ? error.message : "Failed to load consents",
        );
      } finally {
        setLoading(false);
      }
    };

    loadConsents();
  }, [patientId]);

  // Load fetched records
  const loadFetchedRecords = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await Fetch.get(
        `/api/abdm/health-record/fetched?patientId=${patientId}`,
      );

      if (response && response.success) {
        // Set resources by type
        if (response.resourcesByType) {
          setDiagnosticReports(
            response.resourcesByType.diagnosticReports || [],
          );
          setPrescriptions(response.resourcesByType.prescriptions || []);
          setClinicalNotes(response.resourcesByType.clinicalNotes || []);
          setObservations(response.resourcesByType.observations || []);
        }
      }
    } catch (error) {
      console.error("Error loading fetched records:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to load fetched records",
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle fetch records
  const handleFetchRecords = async () => {
    if (!selectedConsent) {
      toast.error("Please select a consent");
      return;
    }

    try {
      setFetchLoading(true);
      setError(null);

      const response = await Fetch.post("/api/abdm/health-record/fetch", {
        consentId: selectedConsent,
      });

      if (response && response.success) {
        toast.success("Health records fetch initiated");

        // Wait for 5 seconds to allow for processing
        setTimeout(() => {
          loadFetchedRecords();
        }, 5000);
      }
    } catch (error) {
      console.error("Error fetching health records:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to fetch health records",
      );
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch health records",
      );
    } finally {
      setFetchLoading(false);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    loadFetchedRecords();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Fetch Health Records</CardTitle>
          <CardDescription>
            Fetch health records for {patientName} from ABDM
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-24">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : consents.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>No active consents</AlertTitle>
              <AlertDescription>
                There are no active consents for this patient. Please request
                consent before fetching health records.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Select Consent</label>
                  <Select
                    value={selectedConsent || ""}
                    onValueChange={setSelectedConsent}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select consent" />
                    </SelectTrigger>
                    <SelectContent>
                      {consents.map((consent) => (
                        <SelectItem
                          key={consent.consentId}
                          value={consent.consentId}
                        >
                          {consent.purpose} (
                          {new Date(consent.expiryDate).toLocaleDateString()})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button
                    onClick={handleFetchRecords}
                    disabled={fetchLoading || !selectedConsent}
                    className="w-full"
                  >
                    {fetchLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Fetching...
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-4 w-4" />
                        Fetch Records
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Fetched Health Records</CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/patients/${patientId}/health-records/timeline`}>
                  <Clock className="h-4 w-4 mr-2" />
                  Timeline View
                </Link>
              </Button>
            </div>
          </div>
          <CardDescription>
            View health records fetched from ABDM
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-24">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <Tabs value={activeView} onValueChange={setActiveView}>
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger value="categorized" className="flex items-center">
                  <List className="h-4 w-4 mr-2" />
                  Categorized View
                </TabsTrigger>
                <TabsTrigger value="timeline" className="flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  Timeline View
                </TabsTrigger>
              </TabsList>

              <TabsContent value="categorized" className="space-y-6">
                <FhirResourceDisplay
                  resources={diagnosticReports}
                  resourceType="DiagnosticReport"
                  title="Diagnostic Reports"
                />

                <FhirResourceDisplay
                  resources={prescriptions}
                  resourceType="MedicationRequest"
                  title="Prescriptions"
                />

                <FhirResourceDisplay
                  resources={clinicalNotes}
                  resourceType="Condition"
                  title="Clinical Notes"
                />

                <FhirResourceDisplay
                  resources={observations}
                  resourceType="Observation"
                  title="Observations"
                />
              </TabsContent>

              <TabsContent value="timeline">
                <div className="flex justify-center items-center py-8">
                  <div className="text-center">
                    <h3 className="text-lg font-medium mb-2">Timeline View</h3>
                    <p className="text-muted-foreground mb-4">
                      View all health records in chronological order
                    </p>
                    <Button asChild>
                      <Link
                        href={`/patients/${patientId}/health-records/timeline`}
                      >
                        Open Timeline View
                      </Link>
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
