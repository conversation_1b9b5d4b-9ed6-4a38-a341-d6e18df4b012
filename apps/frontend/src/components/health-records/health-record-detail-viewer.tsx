"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Loader2,
  AlertCircle,
  FileText,
  Copy,
  Download,
  ArrowLeft,
  Printer,
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import { Fetch } from "@/services/fetch";

interface HealthRecordDetailViewerProps {
  resourceId: string;
  onBack?: () => void;
}

export function HealthRecordDetailViewer({
  resourceId,
  onBack,
}: HealthRecordDetailViewerProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resource, setResource] = useState<any | null>(null);
  const [activeTab, setActiveTab] = useState("formatted");
  const [copied, setCopied] = useState(false);

  // Fetch resource details
  useEffect(() => {
    const fetchResourceDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await Fetch.get(
          `/api/abdm/health-record/resources/${resourceId}`,
        );

        if (response && response.resource) {
          setResource(response.resource);
        } else {
          setError("Resource not found");
        }
      } catch (error) {
        console.error("Error fetching resource details:", error);
        setError(
          error instanceof Error
            ? error.message
            : "Failed to fetch resource details",
        );
      } finally {
        setLoading(false);
      }
    };

    if (resourceId) {
      fetchResourceDetails();
    }
  }, [resourceId]);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "PPP p");
    } catch (e) {
      return dateString;
    }
  };

  // Handle copy to clipboard
  const handleCopy = () => {
    if (!resource) return;

    navigator.clipboard.writeText(JSON.stringify(resource.fhirJson, null, 2));
    setCopied(true);
    toast.success("Copied to clipboard");

    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  // Handle download
  const handleDownload = () => {
    if (!resource) return;

    const dataStr = JSON.stringify(resource.fhirJson, null, 2);
    const dataUri =
      "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);

    const downloadAnchorNode = document.createElement("a");
    downloadAnchorNode.setAttribute("href", dataUri);
    downloadAnchorNode.setAttribute(
      "download",
      `${resource.resourceType}-${resource.resourceId}.json`,
    );
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();

    toast.success("Downloaded JSON file");
  };

  // Handle print
  const handlePrint = () => {
    if (!resource) return;

    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast.error(
        "Unable to open print window. Please check your popup settings.",
      );
      return;
    }

    const resourceType = resource.resourceType;
    const title = getResourceTitle();
    const date =
      resource.fhirJson.effectiveDateTime ||
      resource.fhirJson.authoredOn ||
      resource.fhirJson.recordedDate ||
      resource.createdAt;
    const formattedDate = date ? formatDate(date) : "Unknown date";
    const status = resource.fhirJson.status || "unknown";
    const details = getResourceDetails();

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>${resourceType} - ${title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              margin: 20px;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
              padding-bottom: 10px;
              border-bottom: 1px solid #ddd;
            }
            .section {
              margin-bottom: 15px;
            }
            .label {
              font-weight: bold;
              margin-right: 5px;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 12px;
              color: #666;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${resourceType}: ${title}</h1>
            <p>Date: ${formattedDate}</p>
          </div>
          
          <div class="section">
            <span class="label">Status:</span> ${status}
          </div>
          
          <div class="section">
            <span class="label">Details:</span>
            <div>${details}</div>
          </div>
          
          <div class="footer">
            <p>Printed from Aran Care HIMS on ${new Date().toLocaleString()}</p>
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    // Print after a short delay to ensure content is loaded
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  // Get resource title
  const getResourceTitle = () => {
    if (!resource) return "Unknown";

    const fhirJson = resource.fhirJson;
    const resourceType = resource.resourceType;

    switch (resourceType) {
      case "DiagnosticReport":
        return (
          fhirJson.code?.text ||
          fhirJson.code?.coding?.[0]?.display ||
          "Diagnostic Report"
        );
      case "MedicationRequest":
        return (
          fhirJson.medicationCodeableConcept?.text ||
          fhirJson.medicationCodeableConcept?.coding?.[0]?.display ||
          "Medication"
        );
      case "Condition":
        return (
          fhirJson.code?.text ||
          fhirJson.code?.coding?.[0]?.display ||
          "Condition"
        );
      case "Observation":
        return (
          fhirJson.code?.text ||
          fhirJson.code?.coding?.[0]?.display ||
          "Observation"
        );
      default:
        return "Health Record";
    }
  };

  // Get resource details
  const getResourceDetails = () => {
    if (!resource) return "No details available";

    const fhirJson = resource.fhirJson;
    const resourceType = resource.resourceType;

    switch (resourceType) {
      case "DiagnosticReport":
        return fhirJson.conclusion || "No details available";
      case "MedicationRequest":
        return (
          fhirJson.dosageInstruction?.[0]?.text ||
          "No dosage instructions available"
        );
      case "Condition":
        return fhirJson.note?.[0]?.text || "No notes available";
      case "Observation":
        if (fhirJson.valueQuantity) {
          return `${fhirJson.valueQuantity.value} ${fhirJson.valueQuantity.unit || ""}`;
        }
        if (fhirJson.valueString) {
          return fhirJson.valueString;
        }
        if (fhirJson.valueCodeableConcept) {
          return (
            fhirJson.valueCodeableConcept.text ||
            fhirJson.valueCodeableConcept.coding?.[0]?.display ||
            "No value available"
          );
        }
        return "No value available";
      default:
        return "No details available";
    }
  };

  // Get resource provider
  const getResourceProvider = () => {
    if (!resource) return "Unknown provider";

    const fhirJson = resource.fhirJson;

    if (fhirJson.performer && fhirJson.performer.length > 0) {
      return fhirJson.performer[0].display || "Unknown provider";
    }

    if (fhirJson.recorder) {
      return fhirJson.recorder.display || "Unknown provider";
    }

    if (fhirJson.requester) {
      return fhirJson.requester.display || "Unknown provider";
    }

    return "Unknown provider";
  };

  // Render diagnostic report details
  const renderDiagnosticReportDetails = () => {
    if (!resource) return null;
    const fhirJson = resource.fhirJson;

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Test</h3>
            <p className="font-medium">
              {fhirJson.code?.text ||
                fhirJson.code?.coding?.[0]?.display ||
                "Unknown Test"}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="font-medium">
              {fhirJson.effectiveDateTime
                ? formatDate(fhirJson.effectiveDateTime)
                : fhirJson.issued
                  ? formatDate(fhirJson.issued)
                  : "Unknown Date"}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
          <Badge
            variant={
              fhirJson.status === "final"
                ? "default"
                : fhirJson.status === "preliminary"
                  ? "secondary"
                  : "outline"
            }
          >
            {fhirJson.status || "Unknown"}
          </Badge>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">
            Provider
          </h3>
          <p>{getResourceProvider()}</p>
        </div>

        {fhirJson.conclusion && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Conclusion
            </h3>
            <p className="whitespace-pre-wrap">{fhirJson.conclusion}</p>
          </div>
        )}

        {fhirJson.result && fhirJson.result.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Results
            </h3>
            <div className="space-y-2 mt-2">
              {fhirJson.result.map((result: any, index: number) => (
                <div key={index} className="p-2 border rounded-md">
                  <p className="font-medium">
                    {result.display || "Result " + (index + 1)}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render medication request details
  const renderMedicationRequestDetails = () => {
    if (!resource) return null;
    const fhirJson = resource.fhirJson;

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Medication
            </h3>
            <p className="font-medium">
              {fhirJson.medicationCodeableConcept?.text ||
                fhirJson.medicationCodeableConcept?.coding?.[0]?.display ||
                "Unknown Medication"}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="font-medium">
              {fhirJson.authoredOn
                ? formatDate(fhirJson.authoredOn)
                : "Unknown Date"}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
          <Badge
            variant={
              fhirJson.status === "active"
                ? "default"
                : fhirJson.status === "completed"
                  ? "secondary"
                  : "outline"
            }
          >
            {fhirJson.status || "Unknown"}
          </Badge>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">
            Provider
          </h3>
          <p>{getResourceProvider()}</p>
        </div>

        {fhirJson.dosageInstruction &&
          fhirJson.dosageInstruction.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Dosage Instructions
              </h3>
              <p className="whitespace-pre-wrap">
                {fhirJson.dosageInstruction[0].text ||
                  "No instructions provided"}
              </p>
            </div>
          )}

        {fhirJson.note && fhirJson.note.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p className="whitespace-pre-wrap">
              {fhirJson.note[0].text || "No notes provided"}
            </p>
          </div>
        )}
      </div>
    );
  };

  // Render condition details
  const renderConditionDetails = () => {
    if (!resource) return null;
    const fhirJson = resource.fhirJson;

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Condition
            </h3>
            <p className="font-medium">
              {fhirJson.code?.text ||
                fhirJson.code?.coding?.[0]?.display ||
                "Unknown Condition"}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="font-medium">
              {fhirJson.onsetDateTime
                ? formatDate(fhirJson.onsetDateTime)
                : fhirJson.recordedDate
                  ? formatDate(fhirJson.recordedDate)
                  : "Unknown Date"}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">
            Clinical Status
          </h3>
          <Badge
            variant={
              fhirJson.clinicalStatus?.coding?.[0]?.code === "active"
                ? "default"
                : fhirJson.clinicalStatus?.coding?.[0]?.code === "resolved"
                  ? "secondary"
                  : "outline"
            }
          >
            {fhirJson.clinicalStatus?.coding?.[0]?.code || "Unknown"}
          </Badge>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">
            Provider
          </h3>
          <p>{getResourceProvider()}</p>
        </div>

        {fhirJson.note && fhirJson.note.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p className="whitespace-pre-wrap">
              {fhirJson.note[0].text || "No notes provided"}
            </p>
          </div>
        )}
      </div>
    );
  };

  // Render observation details
  const renderObservationDetails = () => {
    if (!resource) return null;
    const fhirJson = resource.fhirJson;

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Observation
            </h3>
            <p className="font-medium">
              {fhirJson.code?.text ||
                fhirJson.code?.coding?.[0]?.display ||
                "Unknown Observation"}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="font-medium">
              {fhirJson.effectiveDateTime
                ? formatDate(fhirJson.effectiveDateTime)
                : fhirJson.issued
                  ? formatDate(fhirJson.issued)
                  : "Unknown Date"}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
          <Badge variant={fhirJson.status === "final" ? "default" : "outline"}>
            {fhirJson.status || "Unknown"}
          </Badge>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">
            Provider
          </h3>
          <p>{getResourceProvider()}</p>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Value</h3>
          {fhirJson.valueQuantity ? (
            <p>
              {fhirJson.valueQuantity.value} {fhirJson.valueQuantity.unit || ""}
            </p>
          ) : fhirJson.valueString ? (
            <p>{fhirJson.valueString}</p>
          ) : fhirJson.valueCodeableConcept ? (
            <p>
              {fhirJson.valueCodeableConcept.text ||
                fhirJson.valueCodeableConcept.coding?.[0]?.display ||
                "No value available"}
            </p>
          ) : (
            <p>No value available</p>
          )}
        </div>

        {fhirJson.note && fhirJson.note.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p className="whitespace-pre-wrap">
              {fhirJson.note[0].text || "No notes provided"}
            </p>
          </div>
        )}
      </div>
    );
  };

  // Render resource details based on type
  const renderResourceDetails = () => {
    if (!resource) return null;

    switch (resource.resourceType) {
      case "DiagnosticReport":
        return renderDiagnosticReportDetails();
      case "MedicationRequest":
        return renderMedicationRequestDetails();
      case "Condition":
        return renderConditionDetails();
      case "Observation":
        return renderObservationDetails();
      default:
        return (
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Resource Type
              </h3>
              <p className="font-medium">{resource.resourceType}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Resource ID
              </h3>
              <p className="font-medium">{resource.resourceId}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Created At
              </h3>
              <p className="font-medium">{formatDate(resource.createdAt)}</p>
            </div>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Details</CardTitle>
          <CardDescription>Loading resource details...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Details</CardTitle>
          <CardDescription>Error loading resource details</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!resource) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Record Details</CardTitle>
          <CardDescription>Resource not found</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Not Found</AlertTitle>
            <AlertDescription>
              The requested health record could not be found.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5 text-primary" />
              {getResourceTitle()}
            </CardTitle>
            <CardDescription>
              {resource.resourceType} - {formatDate(resource.createdAt)}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            {onBack && (
              <Button variant="outline" size="sm" onClick={onBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue="formatted"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="formatted">Formatted</TabsTrigger>
            <TabsTrigger value="raw">Raw JSON</TabsTrigger>
          </TabsList>

          <TabsContent value="formatted" className="space-y-4">
            {renderResourceDetails()}
          </TabsContent>

          <TabsContent value="raw">
            <pre className="bg-muted p-4 rounded-md overflow-auto text-xs max-h-[500px]">
              {JSON.stringify(resource.fhirJson, null, 2)}
            </pre>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleCopy}>
            <Copy className="h-4 w-4 mr-2" />
            {copied ? "Copied" : "Copy"}
          </Button>
          <Button variant="outline" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
        <Button variant="default" size="sm" onClick={handlePrint}>
          <Printer className="h-4 w-4 mr-2" />
          Print
        </Button>
      </CardFooter>
    </Card>
  );
}
