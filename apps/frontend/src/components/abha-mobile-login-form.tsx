"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import { AlertCircle, CheckCircle2, Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";

interface AbhaMobileLoginFormProps {
  onLoginSuccess?: (xToken: string) => void;
  onCancel?: () => void;
}

export function AbhaMobileLoginForm({
  onLoginSuccess,
  onCancel,
}: AbhaMobileLoginFormProps) {
  // State for ABHA login flow
  const [mobile, setMobile] = useState("");
  const [otp, setOtp] = useState("");
  const [txnId, setTxnId] = useState("");
  const [step, setStep] = useState<"request-otp" | "verify-otp" | "success">(
    "request-otp",
  );
  const [isRequestingOtp, setIsRequestingOtp] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [xToken, setXToken] = useState<string | null>(null);

  // Function to request OTP
  const handleRequestOtp = async () => {
    // Validate mobile number
    if (!mobile || mobile.length !== 10 || !/^\\d+$/.test(mobile)) {
      setError("Please enter a valid 10-digit mobile number");
      return;
    }

    setError(null);
    setIsRequestingOtp(true);

    try {
      const response = await fetch("/api/abdm/mobile/login/request-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ mobile }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to request OTP");
      }

      setTxnId(data.txnId);
      setStep("verify-otp");
      toast.success("OTP sent successfully to your mobile number");
    } catch (error) {
      console.error("Error requesting OTP:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to request OTP. Please try again.",
      );
    } finally {
      setIsRequestingOtp(false);
    }
  };

  // Function to verify OTP
  const handleVerifyOtp = async () => {
    // Validate OTP
    if (!otp || otp.length !== 6 || !/^\\d+$/.test(otp)) {
      setError("Please enter a valid 6-digit OTP");
      return;
    }

    setError(null);
    setIsVerifyingOtp(true);

    try {
      console.log("Starting ABHA mobile login OTP verification with payload:", {
        otp: "REDACTED",
        txnId,
      });

      const response = await fetch("/api/abdm/mobile/login/verify-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          otp,
          txnId,
        }),
      });

      const data = await response.json();

      console.log("ABHA mobile login OTP verification response:", {
        ok: response.ok,
        status: response.status,
        hasData: !!data,
        hasToken: !!data?.xToken,
      });

      // Enhanced error checking
      if (!response.ok) {
        const errorMessage =
          data.error || data.message || "Failed to verify OTP";
        console.error(
          "ABHA mobile login OTP verification failed:",
          errorMessage,
        );
        throw new Error(errorMessage);
      }

      // Additional validation - ensure we have the required data
      if (!data || (data.authResult && data.authResult === "Failed")) {
        const errorMessage =
          data.message ||
          "OTP verification failed. Please check your OTP and try again.";
        console.error(
          "ABHA mobile login OTP verification failed with authResult:",
          data.authResult,
        );
        throw new Error(errorMessage);
      }

      // Ensure we have the token for successful verification
      if (!data.xToken) {
        console.error(
          "ABHA mobile login OTP verification response missing token:",
          data,
        );
        throw new Error(
          "Invalid response from verification service. Please try again.",
        );
      }

      console.log("ABHA mobile login OTP verification successful");

      // Store the token in component state
      setXToken(data.xToken);

      setStep("success");
      toast.success("Mobile verification successful");

      // Add a small delay to ensure all processing is complete before callback
      setTimeout(() => {
        // Call the onLoginSuccess callback if provided
        if (onLoginSuccess && data.xToken) {
          console.log("Calling onLoginSuccess callback after verification");
          onLoginSuccess(data.xToken);
        }
      }, 500); // Small delay to ensure API response is fully processed
    } catch (error) {
      console.error("Error verifying OTP:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to verify OTP. Please try again.",
      );
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Mobile Verification</CardTitle>
        <CardDescription>
          Verify your mobile number to access your ABHA account
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {step === "request-otp" && (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="mobile">Mobile Number</Label>
              <div className="flex">
                <div className="flex items-center justify-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground">
                  +91
                </div>
                <Input
                  id="mobile"
                  placeholder="Enter your 10-digit mobile number"
                  value={mobile}
                  onChange={(e) =>
                    setMobile(e.target.value.replace(/\\D/g, "").slice(0, 10))
                  }
                  maxLength={10}
                  disabled={isRequestingOtp}
                  className="rounded-l-none"
                />
              </div>
              <p className="text-sm text-muted-foreground">
                You will receive an OTP on this mobile number
              </p>
            </div>
            <Button
              onClick={handleRequestOtp}
              disabled={isRequestingOtp || mobile.length !== 10}
              className="w-full"
            >
              {isRequestingOtp && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Request OTP
            </Button>
          </div>
        )}

        {step === "verify-otp" && (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="otp">Enter OTP</Label>
              <Input
                id="otp"
                placeholder="Enter 6-digit OTP"
                value={otp}
                onChange={(e) =>
                  setOtp(e.target.value.replace(/\\D/g, "").slice(0, 6))
                }
                maxLength={6}
                disabled={isVerifyingOtp}
              />
              <p className="text-sm text-muted-foreground">
                OTP has been sent to your mobile number
              </p>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setStep("request-otp");
                  setOtp("");
                  setTxnId("");
                  setError(null);
                }}
                disabled={isVerifyingOtp}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                onClick={handleVerifyOtp}
                disabled={isVerifyingOtp || otp.length !== 6}
                className="flex-1"
              >
                {isVerifyingOtp && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Verify OTP
              </Button>
            </div>
          </div>
        )}

        {step === "success" && (
          <div className="space-y-4">
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Success</AlertTitle>
              <AlertDescription className="text-green-700">
                Your mobile number has been verified successfully
              </AlertDescription>
            </Alert>
          </div>
        )}
      </CardContent>
      {step === "success" && (
        <CardFooter>
          <Button
            onClick={() => onLoginSuccess && onLoginSuccess(xToken || "")}
            className="w-full"
          >
            Continue
          </Button>
        </CardFooter>
      )}
      {step !== "success" && onCancel && (
        <CardFooter>
          <Button
            variant="outline"
            onClick={onCancel}
            className="w-full"
            disabled={isRequestingOtp || isVerifyingOtp}
          >
            Cancel
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
