"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import { ResourceTypeConfig } from "@/lib/fhir/resource-config";

interface ResourceGroupProps {
  resourceType: string;
  resources: any[];
  config: ResourceTypeConfig;
  defaultExpanded?: boolean;
}

export function ResourceGroupDisplay({
  resourceType,
  resources,
  config,
  defaultExpanded = false,
}: ResourceGroupProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const IconComponent = config.icon;

  const getResourceTitle = (resource: any) => {
    switch (resource.resourceType) {
      case "Patient":
        return (
          resource.name?.[0]?.text ||
          `${resource.name?.[0]?.given?.join(" ") || ""} ${resource.name?.[0]?.family || ""}`.trim() ||
          "Patient"
        );
      case "Practitioner":
        return (
          resource.name?.[0]?.text ||
          `${resource.name?.[0]?.prefix?.join(" ") || ""} ${resource.name?.[0]?.given?.join(" ") || ""} ${resource.name?.[0]?.family || ""}`.trim() ||
          "Practitioner"
        );
      case "Organization":
        return resource.name || "Organization";
      case "Observation":
        return (
          resource.code?.text ||
          resource.code?.coding?.[0]?.display ||
          "Observation"
        );
      case "MedicationRequest":
      case "Medication":
        return (
          resource.medicationCodeableConcept?.text ||
          resource.medicationCodeableConcept?.coding?.[0]?.display ||
          resource.code?.text ||
          resource.code?.coding?.[0]?.display ||
          "Medication"
        );
      case "Condition":
        return (
          resource.code?.text ||
          resource.code?.coding?.[0]?.display ||
          "Condition"
        );
      case "DiagnosticReport":
        return (
          resource.code?.text ||
          resource.code?.coding?.[0]?.display ||
          "Diagnostic Report"
        );
      case "Immunization":
        return (
          resource.vaccineCode?.text ||
          resource.vaccineCode?.coding?.[0]?.display ||
          "Immunization"
        );
      case "DocumentReference":
        return (
          resource.type?.text ||
          resource.type?.coding?.[0]?.display ||
          resource.description ||
          "Document"
        );
      default:
        return resource.title || resource.name || resourceType;
    }
  };

  const getResourceSubtitle = (resource: any) => {
    switch (resource.resourceType) {
      case "Patient":
        return `${resource.gender || "Unknown"} • Born ${resource.birthDate ? format(new Date(resource.birthDate), "PPP") : "Unknown"}`;
      case "Observation":
        const value = resource.valueQuantity
          ? `${resource.valueQuantity.value} ${resource.valueQuantity.unit || resource.valueQuantity.code || ""}`
          : resource.valueString ||
            resource.valueCodeableConcept?.text ||
            "No value";
        return `${value} • ${resource.status}`;
      case "MedicationRequest":
        return `${resource.status} • ${resource.dosageInstruction?.[0]?.text || "No dosage"}`;
      case "Condition":
        return `${resource.clinicalStatus?.coding?.[0]?.code || "Unknown status"} • ${resource.onsetDateTime ? format(new Date(resource.onsetDateTime), "PPP") : "Unknown onset"}`;
      case "DiagnosticReport":
        return `${resource.status} • ${resource.effectiveDateTime ? format(new Date(resource.effectiveDateTime), "PPP") : "Unknown date"}`;
      default:
        return resource.status || resource.id || "No additional info";
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader
        className="cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className={`p-2 rounded-lg bg-${config.color}-100 text-${config.color}-800`}
            >
              <IconComponent className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">{config.label}</h3>
              <p className="text-sm text-muted-foreground capitalize">
                {config.category} • {resources.length}{" "}
                {resources.length === 1 ? "resource" : "resources"}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{resources.length}</Badge>
            {isExpanded ? (
              <ChevronDown className="h-5 w-5" />
            ) : (
              <ChevronRight className="h-5 w-5" />
            )}
          </div>
        </CardTitle>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <div className="space-y-3">
            {resources.map((resource, index) => (
              <div key={resource.id || index} className="border rounded-lg p-4">
                <div className="mb-2">
                  <div className="flex-1">
                    <h4 className="font-medium text-base">
                      {getResourceTitle(resource)}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {getResourceSubtitle(resource)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}
