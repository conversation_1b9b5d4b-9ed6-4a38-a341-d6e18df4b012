"use client";

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, AlertTriangle } from "lucide-react";
import { PdfPreview } from "./pdf-preview";
import {
  extractDocumentFromDocumentReference,
  extractDocumentFromBinary,
  type DocumentInfo,
} from "@/lib/fhir/pdf-utils";

interface DocumentReferenceViewerProps {
  documentReferences: any[];
  binaries: any[];
}

export function DocumentReferenceViewer({
  documentReferences,
  binaries,
}: DocumentReferenceViewerProps) {
  // Extract document info from DocumentReference resources
  const docRefDocuments: DocumentInfo[] = documentReferences
    .map(extractDocumentFromDocumentReference)
    .filter((doc): doc is DocumentInfo => doc !== null);

  // Extract document info from Binary resources
  const binaryDocuments: DocumentInfo[] = binaries
    .map(extractDocumentFromBinary)
    .filter((doc): doc is DocumentInfo => doc !== null);

  // Combine all documents
  const allDocuments = [...docRefDocuments, ...binaryDocuments];

  const hasDocumentReferences = docRefDocuments.length > 0;
  const hasBinaries = binaryDocuments.length > 0;

  if (allDocuments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Documents</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            <p className="text-sm text-yellow-700">
              No document references or binary resources found in this bundle.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Documents</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">
              {allDocuments.length} Document
              {allDocuments.length !== 1 ? "s" : ""}
            </Badge>
            {hasDocumentReferences && (
              <Badge variant="secondary">
                {docRefDocuments.length} Reference
                {docRefDocuments.length !== 1 ? "s" : ""}
              </Badge>
            )}
            {hasBinaries && (
              <Badge variant="secondary">{binaryDocuments.length} Binary</Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {allDocuments.map((doc) => (
            <PdfPreview key={doc.id} document={doc} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
