import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import {
  FileText,
  Calendar,
  Hash,
  Activity,
  Pill,
  Stethoscope,
  Shield,
  TestTube,
  Receipt,
  Heart,
  FileImage,
  LucideIcon,
} from "lucide-react";
import {
  getBundleTypeDisplayName,
  BUNDLE_TYPE_ICONS,
  BUNDLE_TYPE_COLORS,
} from "@/lib/fhir/bundle-display-utils";

const ICON_COMPONENTS: Record<string, LucideIcon> = {
  Pill: Pill,
  Stethoscope: Stethoscope,
  Shield: Shield,
  TestTube: TestTube,
  FileText: FileText,
  Heart: Heart,
  Receipt: Receipt,
  FileImage: FileImage,
};

interface BundleHeaderProps {
  bundleType: string;
  bundleId: string;
  timestamp: string;
  totalEntries: number;
  currentIndex: number;
  totalBundles: number;
}

export function BundleHeader({
  bundleType,
  bundleId,
  timestamp,
  totalEntries,
  currentIndex,
  totalBundles,
}: BundleHeaderProps) {
  const displayName = getBundleTypeDisplayName(bundleType);
  const iconName =
    BUNDLE_TYPE_ICONS[displayName as keyof typeof BUNDLE_TYPE_ICONS] ||
    "FileImage";
  const IconComponent = ICON_COMPONENTS[iconName] || FileImage;
  const colorClass =
    BUNDLE_TYPE_COLORS[displayName as keyof typeof BUNDLE_TYPE_COLORS] ||
    BUNDLE_TYPE_COLORS["Unknown"];

  return (
    <Card className="border-2">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${colorClass}`}>
              <IconComponent className="h-6 w-6" />
            </div>
            <div>
              <CardTitle className="text-xl">{displayName} Bundle</CardTitle>
              <p className="text-sm text-muted-foreground">
                Bundle {currentIndex + 1} of {totalBundles}
              </p>
            </div>
          </div>
          <Badge variant="outline" className="text-sm">
            {totalEntries} Resources
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <Hash className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-muted-foreground">Bundle ID</p>
              <p className="font-mono text-xs truncate">{bundleId}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-muted-foreground">Generated</p>
              <p className="font-medium">
                {timestamp !== "Unknown"
                  ? format(new Date(timestamp), "PPP")
                  : "Unknown"}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-muted-foreground">Status</p>
              <Badge variant="secondary">Active</Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
