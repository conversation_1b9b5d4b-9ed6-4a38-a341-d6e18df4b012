"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Download,
  Eye,
  FileText,
  AlertTriangle,
  Loader2,
  ExternalLink,
  MoreVertical,
} from "lucide-react";
import {
  createObjectURL,
  downloadDocument,
  formatFileSize,
  getFileExtension,
  isValidBase64,
  type DocumentInfo,
} from "@/lib/fhir/pdf-utils";
import { toast } from "sonner";

interface PdfPreviewProps {
  document: DocumentInfo;
}

export function PdfPreview({ document }: PdfPreviewProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    return () => {
      // Cleanup object URL on unmount
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const handlePreview = async () => {
    if (!document.base64Data) {
      setError("No base64 data available for preview");
      return;
    }

    if (!isValidBase64(document.base64Data)) {
      setError("Invalid base64 data");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const url = createObjectURL(document.base64Data, document.contentType);
      if (url) {
        setPreviewUrl(url);
      } else {
        setError("Failed to create preview URL");
      }
    } catch (err) {
      console.error("Error creating preview:", err);
      setError("Failed to create preview");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = () => {
    if (!document.base64Data) {
      toast.error("No data available for download");
      return;
    }

    if (!isValidBase64(document.base64Data)) {
      toast.error("Invalid document data");
      return;
    }

    const extension = getFileExtension(document.contentType);
    const filename = `${document.title.replace(/[^a-zA-Z0-9]/g, "_")}.${extension}`;

    try {
      downloadDocument(document.base64Data, filename, document.contentType);
      toast.success("Document downloaded successfully");
    } catch (err) {
      console.error("Error downloading document:", err);
      toast.error("Failed to download document");
    }
  };

  const handleOpenInNewTab = () => {
    if (previewUrl) {
      window.open(previewUrl, "_blank");
    }
  };

  const isPdf = document.contentType === "application/pdf";
  const isImage = document.contentType.startsWith("image/");
  const hasBase64Data = !!document.base64Data;

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FileText className="h-5 w-5 text-blue-600" />
            <div>
              <CardTitle className="text-lg">{document.title}</CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="outline">{document.contentType}</Badge>
                {document.size && (
                  <Badge variant="secondary">
                    {formatFileSize(document.size)}
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {(hasBase64Data || document.url) && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" disabled={isLoading}>
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <MoreVertical className="h-4 w-4" />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {hasBase64Data && (
                    <>
                      <DropdownMenuItem onClick={handlePreview}>
                        <Eye className="h-4 w-4 mr-2" />
                        Preview Document
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleDownload}>
                        <Download className="h-4 w-4 mr-2" />
                        Download Document
                      </DropdownMenuItem>
                    </>
                  )}
                  {document.url && (
                    <DropdownMenuItem
                      onClick={() => window.open(document.url, "_blank")}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open URL
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {document.description && (
          <p className="text-sm text-muted-foreground mb-4">
            {document.description}
          </p>
        )}

        {error && (
          <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        )}

        {!hasBase64Data && !document.url && (
          <div className="flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <span className="text-sm text-yellow-700">
              No document data available for preview
            </span>
          </div>
        )}

        {previewUrl && (
          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium">Document Preview</h4>
              <Button variant="ghost" size="sm" onClick={handleOpenInNewTab}>
                <ExternalLink className="h-4 w-4" />
                Open in New Tab
              </Button>
            </div>

            {isPdf ? (
              <iframe
                src={previewUrl}
                className="w-full h-96 border rounded-lg"
                title={document.title}
              />
            ) : isImage ? (
              <img
                src={previewUrl}
                alt={document.title}
                className="max-w-full h-auto border rounded-lg"
              />
            ) : (
              <div className="p-4 bg-gray-50 border rounded-lg text-center">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  Preview not available for this file type
                </p>
              </div>
            )}
          </div>
        )}

        {document.creationDate && (
          <div className="mt-4 pt-4 border-t">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Created</p>
                <p>{new Date(document.creationDate).toLocaleDateString()}</p>
              </div>
              {document.language && (
                <div>
                  <p className="text-muted-foreground">Language</p>
                  <p>{document.language}</p>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
