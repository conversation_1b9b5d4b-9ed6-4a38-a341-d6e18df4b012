import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  User,
  Stethoscope,
  Building,
  FileText,
  BarChart3,
  LucideIcon,
} from "lucide-react";

interface ResourceSummaryProps {
  summary: {
    coreResources: number;
    clinicalResources: number;
    administrativeResources: number;
    documentResources: number;
  };
  totalEntries: number;
}

interface SummaryItem {
  label: string;
  count: number;
  icon: LucideIcon;
  color: string;
  description: string;
}

export function ResourceSummary({
  summary,
  totalEntries,
}: ResourceSummaryProps) {
  const summaryItems: SummaryItem[] = [
    {
      label: "Core",
      count: summary.coreResources,
      icon: User,
      color: "bg-blue-100 text-blue-800",
      description: "Patient, Provider, Organization",
    },
    {
      label: "Clinical",
      count: summary.clinicalResources,
      icon: Stethoscope,
      color: "bg-green-100 text-green-800",
      description: "Observations, Medications, Conditions",
    },
    {
      label: "Administrative",
      count: summary.administrativeResources,
      icon: Building,
      color: "bg-purple-100 text-purple-800",
      description: "Encounters, Appointments, Invoices",
    },
    {
      label: "Documents",
      count: summary.documentResources,
      icon: FileText,
      color: "bg-orange-100 text-orange-800",
      description: "PDFs, Images, Attachments",
    },
  ];

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5" />
          <span>Resource Summary</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {summaryItems.map((item) => {
            const IconComponent = item.icon;
            return (
              <div key={item.label} className="text-center">
                <div
                  className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${item.color} mb-2`}
                >
                  <IconComponent className="h-6 w-6" />
                </div>
                <div className="space-y-1">
                  <p className="text-2xl font-bold">{item.count}</p>
                  <p className="text-sm font-medium">{item.label}</p>
                  <p className="text-xs text-muted-foreground">
                    {item.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Total Resources</span>
            <Badge variant="outline" className="text-base font-semibold">
              {totalEntries}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
