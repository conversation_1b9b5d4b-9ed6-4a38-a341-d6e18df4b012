"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { MapPin, Phone, Mail, Home, Building, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface BranchOnboardingFormProps {
  organizationId: string;
  organizationName: string;
  onComplete: () => void;
}

// Define the validation schema using Zod
const formSchema = z.object({
  name: z.string().min(1, { message: "Branch name is required" }),
  phone: z.string().min(1, { message: "Phone number is required" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  address: z.string().min(1, { message: "Address is required" }),
  city: z.string().min(1, { message: "City is required" }),
  state: z.string().min(1, { message: "State is required" }),
  pincode: z.string().min(1, { message: "Pincode is required" }),
  latitude: z.string().optional(),
  longitude: z.string().optional(),
});

// Define the form values type
type FormValues = z.infer<typeof formSchema>;

export function BranchOnboardingForm({
  organizationId,
  organizationName,
  onComplete,
}: BranchOnboardingFormProps) {
  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Initialize React Hook Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: organizationName,
      phone: "",
      email: "",
      address: "",
      city: "",
      state: "",
      pincode: "",
      latitude: "",
      longitude: "",
    },
  });

  const onSubmit = async (values: FormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/organizations/${organizationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: values.name,
          branchData: {
            name: values.name,
            phone: values.phone,
            email: values.email,
            address: values.address,
            city: values.city,
            state: values.state,
            pincode: values.pincode,
            latitude: values.latitude || undefined,
            longitude: values.longitude || undefined,
            isHeadOffice: true,
          },
          onboardingCompleted: true,
        }),
      });

      if (response.ok) {
        // Force a redirect to dashboard
        router.push("/dashboard");

        // Call the onComplete callback
        onComplete();
        router.refresh();
      } else {
        const data = await response.json();
        console.error("Error response:", data);
        setError(
          data.error || "An error occurred while saving your information",
        );
      }
    } catch (error) {
      console.error("Error updating organization:", error);
      setError("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto shadow-xl glass-card animate-fade-in">
      <CardHeader className="space-y-3 text-center pb-6">
        <CardTitle className="text-3xl font-bold gradient-text">
          Complete Your Setup
        </CardTitle>
        <CardDescription className="text-base">
          Please provide your branch information to complete the onboarding
          process
        </CardDescription>
      </CardHeader>

      {error && (
        <Alert variant="destructive" className="mx-6 mb-4 animate-fade-in">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <CardContent className="space-y-6">
            {/* Branch Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="font-medium text-base">
                    Branch Name
                  </FormLabel>
                  <div className="flex items-center relative group">
                    <Building className="absolute left-3 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors" />
                    <FormControl>
                      <Input
                        className="pl-10 h-12 rounded-lg border-input/50 focus:border-primary shadow-sm"
                        placeholder="Enter branch name"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Contact Information Section */}
            <div className="bg-muted/30 p-5 rounded-lg border border-border/50">
              <h3 className="text-lg font-semibold mb-2 flex items-center">
                <span className="bg-primary/10 p-1.5 rounded-md mr-2">
                  <Phone className="h-5 w-5 text-primary" />
                </span>
                Contact Information
              </h3>
              <p className="text-sm text-muted-foreground mb-5">
                Provide contact details for the branch
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="font-medium">Phone</FormLabel>
                      <div className="flex items-center relative group">
                        <Phone className="absolute left-3 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors" />
                        <FormControl>
                          <Input
                            className="pl-10 h-12 rounded-lg border-input/50 focus:border-primary shadow-sm"
                            placeholder="Enter phone"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="font-medium">Email</FormLabel>
                      <div className="flex items-center relative group">
                        <Mail className="absolute left-3 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors" />
                        <FormControl>
                          <Input
                            className="pl-10 h-12 rounded-lg border-input/50 focus:border-primary shadow-sm"
                            type="email"
                            placeholder="Enter email"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Address Information Section */}
            <div className="bg-muted/30 p-5 rounded-lg border border-border/50">
              <h3 className="text-lg font-semibold mb-2 flex items-center">
                <span className="bg-primary/10 p-1.5 rounded-md mr-2">
                  <Home className="h-5 w-5 text-primary" />
                </span>
                Address Information
              </h3>
              <p className="text-sm text-muted-foreground mb-5">
                Enter the physical location details
              </p>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="font-medium">Address</FormLabel>
                      <div className="flex items-center relative group">
                        <Home className="absolute left-3 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors" />
                        <FormControl>
                          <Input
                            className="pl-10 h-12 rounded-lg border-input/50 focus:border-primary shadow-sm"
                            placeholder="Enter address"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel className="font-medium">City</FormLabel>
                        <FormControl>
                          <Input
                            className="h-12 rounded-lg border-input/50 focus:border-primary shadow-sm"
                            placeholder="Enter city"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="state"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel className="font-medium">State</FormLabel>
                        <FormControl>
                          <Input
                            className="h-12 rounded-lg border-input/50 focus:border-primary shadow-sm"
                            placeholder="Enter state"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="pincode"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel className="font-medium">Pincode</FormLabel>
                        <FormControl>
                          <Input
                            className="h-12 rounded-lg border-input/50 focus:border-primary shadow-sm"
                            placeholder="Enter pincode"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Location Coordinates Section */}
            <div className="bg-muted/30 p-5 rounded-lg border border-border/50">
              <h3 className="text-lg font-semibold mb-2 flex items-center">
                <span className="bg-primary/10 p-1.5 rounded-md mr-2">
                  <MapPin className="h-5 w-5 text-primary" />
                </span>
                Location Coordinates
              </h3>
              <p className="text-sm text-muted-foreground mb-5">
                You can find coordinates by searching for the address on Google
                Maps and right-clicking on the location
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="latitude"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="font-medium">Latitude</FormLabel>
                      <div className="flex items-center relative group">
                        <MapPin className="absolute left-3 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors" />
                        <FormControl>
                          <Input
                            className="pl-10 h-12 rounded-lg border-input/50 focus:border-primary shadow-sm"
                            placeholder="Enter latitude (e.g. 12.9716)"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="longitude"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="font-medium">Longitude</FormLabel>
                      <div className="flex items-center relative group">
                        <MapPin className="absolute left-3 h-5 w-5 text-muted-foreground group-focus-within:text-primary transition-colors" />
                        <FormControl>
                          <Input
                            className="pl-10 h-12 rounded-lg border-input/50 focus:border-primary shadow-sm"
                            placeholder="Enter longitude (e.g. 77.5946)"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-end space-x-2 pt-8 pb-6">
            <Button
              type="submit"
              className="w-full md:w-auto text-base font-medium h-12 px-8 rounded-lg shadow-md hover:shadow-lg transition-all btn-hover-effect"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></span>
                  Saving...
                </>
              ) : (
                "Complete Setup"
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
