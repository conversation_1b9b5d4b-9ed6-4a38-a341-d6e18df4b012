"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, User, Calendar, FileText, Plus } from "lucide-react";
import { toast } from "sonner";
import { AddResultsDialog } from "./add-results-dialog";

interface LabTestRequest {
  id: string;
  testType: string;
  testName: string;
  priority: string;
  status: string;
  requestDate: string;
  requestedBy: string;
  notes?: string;
  expectedDate?: string;
  consultationId: string;
  patientId: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    phone: string;
    email?: string;
  };
  doctor: {
    id: string;
    user: {
      name: string;
      email: string;
    };
    specialization?: string;
  };
  consultation: {
    id: string;
    consultationDate: string;
    status: string;
  };
  branch: {
    id: string;
    name: string;
  };
}

export function PendingLabTests() {
  const [labTests, setLabTests] = useState<LabTestRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTest, setSelectedTest] = useState<LabTestRequest | null>(null);
  const [showAddResults, setShowAddResults] = useState(false);

  useEffect(() => {
    fetchPendingTests();
  }, []);

  const fetchPendingTests = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/lab-test-requests?status=pending");

      if (!response.ok) {
        throw new Error("Failed to fetch pending lab tests");
      }

      const data = await response.json();
      setLabTests(data.labTestRequests || []);

      // Update counts in parent component
      const pendingCount = data.labTestRequests?.length || 0;
      const pendingCountElement = document.getElementById("pending-count");
      const pendingTabCountElement =
        document.getElementById("pending-tab-count");

      if (pendingCountElement)
        pendingCountElement.textContent = pendingCount.toString();
      if (pendingTabCountElement)
        pendingTabCountElement.textContent = pendingCount.toString();

      // Also fetch completed tests to update total count
      try {
        const completedResponse = await fetch(
          "/api/lab-test-requests?status=completed",
        );
        if (completedResponse.ok) {
          const completedData = await completedResponse.json();
          const completedCount = completedData.labTestRequests?.length || 0;
          const totalCount = pendingCount + completedCount;

          const totalCountElement = document.getElementById("total-count");
          if (totalCountElement)
            totalCountElement.textContent = totalCount.toString();
        }
      } catch (error) {
        console.error("Error fetching completed count:", error);
      }
    } catch (error) {
      console.error("Error fetching pending lab tests:", error);
      toast.error("Failed to load pending lab tests");
    } finally {
      setLoading(false);
    }
  };

  const handleAddResults = (test: LabTestRequest) => {
    setSelectedTest(test);
    setShowAddResults(true);
  };

  const handleResultsAdded = () => {
    setShowAddResults(false);
    setSelectedTest(null);
    fetchPendingTests();
    toast.success("Lab test results added successfully");
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "urgent":
        return <Badge variant="destructive">Urgent</Badge>;
      case "stat":
        return <Badge className="bg-red-600 text-white">STAT</Badge>;
      default:
        return <Badge variant="outline">Routine</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (labTests.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No pending lab tests</h3>
        <p className="text-muted-foreground">
          All lab test requests have been completed or there are no pending
          tests.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4">
        {labTests.map((test) => (
          <Card key={test.id} className="border-2 border-orange-100 shadow-sm">
            <CardHeader className="bg-orange-50 pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg font-semibold text-orange-700">
                    {test.testName}
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge
                      variant="outline"
                      className="bg-orange-100 text-orange-800"
                    >
                      {test.testType}
                    </Badge>
                    {getPriorityBadge(test.priority)}
                  </div>
                </div>
                <Button
                  onClick={() => handleAddResults(test)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Results
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="bg-muted/30 p-3 rounded-md">
                    <p className="text-xs font-medium text-muted-foreground flex items-center">
                      <User className="h-3.5 w-3.5 mr-1" />
                      Patient Information
                    </p>
                    <p className="font-medium">
                      {test.patient.firstName} {test.patient.lastName}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {test.patient.phone}
                    </p>
                    {test.patient.email && (
                      <p className="text-sm text-muted-foreground">
                        {test.patient.email}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-muted/30 p-3 rounded-md">
                    <p className="text-xs font-medium text-muted-foreground flex items-center">
                      <Calendar className="h-3.5 w-3.5 mr-1" />
                      Request Details
                    </p>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Requested:</span>
                        <span className="text-sm">
                          {format(new Date(test.requestDate), "PPp")}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Doctor:</span>
                        <span className="text-sm">{test.doctor.user.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Branch:</span>
                        <span className="text-sm">{test.branch.name}</span>
                      </div>
                      {test.expectedDate && (
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">Expected:</span>
                          <span className="text-sm">
                            {format(new Date(test.expectedDate), "PP")}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {test.notes && (
                <div className="mt-4 bg-blue-50 border border-blue-200 p-3 rounded-md">
                  <p className="text-sm font-medium text-blue-700">Notes:</p>
                  <p className="text-sm text-blue-600">{test.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedTest && (
        <AddResultsDialog
          open={showAddResults}
          onOpenChange={setShowAddResults}
          labTestRequest={selectedTest}
          onSuccess={handleResultsAdded}
        />
      )}
    </div>
  );
}
