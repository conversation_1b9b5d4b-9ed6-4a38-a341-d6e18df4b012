"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { AzureFhirUpload } from "@/components/azure-fhir-upload";
import { UploadedFilesList } from "@/components/uploaded-files-list";

const addResultsSchema = z.object({
  reportType: z.string().min(1, "Report type is required"),
  category: z.string().optional(),
  code: z.string().min(1, "Code is required"),
  codeDisplay: z.string().min(1, "Code display is required"),
  conclusion: z.string().optional(),
  performer: z.string().optional(),
  specimen: z.string().optional(),
  result: z.string().optional(),
});

type AddResultsFormData = z.infer<typeof addResultsSchema>;

interface LabTestRequest {
  id: string;
  testType: string;
  testName: string;
  consultationId: string;
  patientId: string;
  patient: {
    firstName: string;
    lastName: string;
  };
}

interface AddResultsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  labTestRequest: LabTestRequest;
  onSuccess: () => void;
}

export function AddResultsDialog({
  open,
  onOpenChange,
  labTestRequest,
  onSuccess,
}: AddResultsDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<AddResultsFormData>({
    resolver: zodResolver(addResultsSchema),
    defaultValues: {
      reportType: labTestRequest.testType,
      category: "LAB",
      code: "",
      codeDisplay: labTestRequest.testName,
      conclusion: "",
      performer: "",
      specimen: "",
      result: "",
    },
  });

  const onSubmit = async (data: AddResultsFormData) => {
    try {
      setIsSubmitting(true);

      // Prepare the result data as JSON if provided
      let resultJson = null;
      if (data.result) {
        try {
          resultJson = JSON.parse(data.result);
        } catch {
          // If not valid JSON, store as simple text
          resultJson = { text: data.result };
        }
      }

      const response = await fetch(
        `/api/lab-test-requests/${labTestRequest.id}/add-results`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...data,
            result: resultJson,
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add results");
      }

      toast.success("Lab test results added successfully");
      onSuccess();
      form.reset();
    } catch (error) {
      console.error("Error adding results:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to add results",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Lab Test Results</DialogTitle>
          <DialogDescription>
            Add diagnostic results for {labTestRequest.testName} for patient{" "}
            {labTestRequest.patient.firstName} {labTestRequest.patient.lastName}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="reportType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Report Type</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., Blood Test, X-Ray" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="LAB">Laboratory</SelectItem>
                        <SelectItem value="RAD">Radiology</SelectItem>
                        <SelectItem value="PATH">Pathology</SelectItem>
                        <SelectItem value="MICRO">Microbiology</SelectItem>
                        <SelectItem value="CHEM">Chemistry</SelectItem>
                        <SelectItem value="HEMA">Hematology</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Test Code</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., CBC, XRAY-CHEST" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* <FormField
                control={form.control}
                name="codeDisplay"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Test Display Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., Complete Blood Count"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              /> */}
            </div>

            <FormField
              control={form.control}
              name="conclusion"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Conclusion</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Enter the diagnostic conclusion or interpretation..."
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* <FormField
                control={form.control}
                name="performer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Performed By</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Lab technician or doctor name"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              /> */}

            {/* <FormField
                control={form.control}
                name="specimen"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Specimen</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., Blood, Urine, Tissue"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              /> */}

            {/* <FormField
              control={form.control}
              name="result"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Detailed Results (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Enter detailed test results, values, or JSON data..."
                      rows={4}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            /> */}

            {/* Document Upload Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Lab Report Document Upload (Optional)
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Upload a PDF lab report document that will be included in the
                FHIR bundle when results are saved.
              </p>
              <AzureFhirUpload
                consultationId={labTestRequest.consultationId}
                patientId={labTestRequest.patientId}
                bundleType="DiagnosticReport"
                title="Upload Lab Report Document"
                description="Upload a PDF lab report document"
                onUploadSuccess={(_result) => {
                  toast.success(
                    "Lab report document uploaded successfully. It will be included when results are saved.",
                  );
                }}
                hideWhenFilesExist={false}
              />
              <UploadedFilesList
                consultationId={labTestRequest.consultationId}
                bundleType="DiagnosticReport"
                title="Uploaded Lab Report Documents"
                description="Documents that will be included in the FHIR bundle"
                showOnlyGenerated={false}
                className="mt-4"
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding Results...
                  </>
                ) : (
                  "Add Results"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
