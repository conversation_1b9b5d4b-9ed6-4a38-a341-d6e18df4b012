"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Loader2, User, Calendar, FileText, Eye, Upload, Download } from "lucide-react";
import { toast } from "sonner";
import { Fetch } from "@/services/fetch";
import { UploadedFilesList } from "@/components/uploaded-files-list";

interface DiagnosticReport {
  id: string;
  reportType: string;
  status: string;
  reportDate: string;
  conclusion?: string;
}

interface LabTestRequest {
  id: string;
  testType: string;
  testName: string;
  priority: string;
  status: string;
  requestDate: string;
  completedDate?: string;
  requestedBy: string;
  notes?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    phone: string;
    email?: string;
  };
  doctor: {
    id: string;
    user: {
      name: string;
      email: string;
    };
    specialization?: string;
  };
  consultation: {
    id: string;
    consultationDate: string;
    status: string;
  };
  branch: {
    id: string;
    name: string;
  };
  diagnosticReports: DiagnosticReport[];
}

export function CompletedLabTests() {
  const [labTests, setLabTests] = useState<LabTestRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploadingIds, setUploadingIds] = useState<Set<string>>(new Set());
  const [downloadingIds, setDownloadingIds] = useState<Set<string>>(new Set());

  // Upload dialog state
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState<DiagnosticReport | null>(
    null,
  );
  const [selectedTest, setSelectedTest] = useState<LabTestRequest | null>(null);
  const [fetchRequests, setFetchRequests] = useState<any[]>([]);
  const [selectedFetchRequest, setSelectedFetchRequest] = useState<string>("");
  const [loadingFetchRequests, setLoadingFetchRequests] = useState(false);

  useEffect(() => {
    fetchCompletedTests();
  }, []);

  const fetchCompletedTests = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/lab-test-requests?status=completed");

      if (!response.ok) {
        throw new Error("Failed to fetch completed lab tests");
      }

      const data = await response.json();
      setLabTests(data.labTestRequests || []);

      // Update counts in parent component
      const completedCount = data.labTestRequests?.length || 0;
      const completedCountElement = document.getElementById("completed-count");
      const completedTabCountElement = document.getElementById(
        "completed-tab-count",
      );

      if (completedCountElement)
        completedCountElement.textContent = completedCount.toString();
      if (completedTabCountElement)
        completedTabCountElement.textContent = completedCount.toString();
    } catch (error) {
      console.error("Error fetching completed lab tests:", error);
      toast.error("Failed to load completed lab tests");
    } finally {
      setLoading(false);
    }
  };

  const handleViewReport = (test: LabTestRequest) => {
    // Navigate to consultation view to see the diagnostic report
    window.open(`/consultations/${test.consultation.id}`, "_blank");
  };

  // Handle upload button click - show dialog to select HI request
  const handleUploadClick = async (
    report: DiagnosticReport,
    test: LabTestRequest,
  ) => {
    setSelectedReport(report);
    setSelectedTest(test);
    setLoadingFetchRequests(true);
    setUploadDialogOpen(true);

    try {
      // Load HI requests using the same logic as discharge summary uploads
      const url = test.consultation.id
        ? `/api/patients/${test.patient.id}/hi-requests?consultationId=${test.consultation.id}`
        : `/api/patients/${test.patient.id}/hi-requests`;

      const response = await Fetch.get(url);
      if (response.error) {
        throw new Error(response.error);
      }
      setFetchRequests(response.hiRequests || []);
    } catch (error) {
      console.error("Error loading HI requests:", error);
      toast.error("Failed to load health record requests");
      setFetchRequests([]);
    } finally {
      setLoadingFetchRequests(false);
    }
  };

  // Handle actual upload to selected HI request
  const handleUpload = async () => {
    if (!selectedReport || !selectedTest || !selectedFetchRequest) {
      toast.error("Please select a health record request");
      return;
    }

    try {
      setUploadingIds((prev) => new Set(prev).add(selectedReport.id));

      // Upload lab report using the new API endpoint
      const uploadResponse = await Fetch.post("/api/abdm/upload-lab-reports", {
        patientId: selectedTest.patient.id,
        hiRequestId: selectedFetchRequest,
        consultationId: selectedTest.consultation.id,
        diagnosticReportId: selectedReport.id,
      });

      if (uploadResponse.error) {
        throw new Error(uploadResponse.error);
      }

      toast.success("Lab report uploaded to ABDM successfully!");
      setUploadDialogOpen(false);
      setSelectedReport(null);
      setSelectedTest(null);
      setSelectedFetchRequest("");
    } catch (error) {
      console.error("Error uploading lab report:", error);
      toast.error(
        `Failed to upload lab report: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    } finally {
      setUploadingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(selectedReport?.id || "");
        return newSet;
      });
    }
  };

  // Handle download PDF button click
  const handleDownloadClick = async (
    report: DiagnosticReport,
    test: LabTestRequest,
  ) => {
    try {
      console.log("🔽 Download button clicked", { reportId: report.id, consultationId: test.consultation.id });
      setDownloadingIds((prev) => new Set(prev).add(report.id));

      // Generate lab report PDF using the same pattern as immunization records
      const apiUrl = `/api/pdf/lab-reports?consultationId=${test.consultation.id}&diagnosticReportId=${report.id}`;
      console.log("📡 Making request to:", apiUrl);

      const response = await fetch(apiUrl);
      console.log("📥 Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ Response error:", errorText);
        throw new Error(`Failed to generate lab report PDF: ${response.status} ${errorText}`);
      }

      // Get the PDF blob and download it
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `Lab_Report_${test.patient.firstName}_${test.patient.lastName}_${format(new Date(report.reportDate), "yyyy-MM-dd")}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("Lab report PDF downloaded successfully!");
    } catch (error) {
      console.error("Error downloading lab report PDF:", error);
      toast.error(
        `Failed to download lab report PDF: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    } finally {
      setDownloadingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(report.id);
        return newSet;
      });
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "urgent":
        return <Badge variant="destructive">Urgent</Badge>;
      case "stat":
        return <Badge className="bg-red-600 text-white">STAT</Badge>;
      default:
        return <Badge variant="outline">Routine</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (labTests.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No completed lab tests</h3>
        <p className="text-muted-foreground">
          No lab tests have been completed yet.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4">
        {labTests.map((test) => (
          <Card key={test.id} className="border-2 border-green-100 shadow-sm">
            <CardHeader className="bg-green-50 pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg font-semibold text-green-700">
                    {test.testName}
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge
                      variant="outline"
                      className="bg-green-100 text-green-800"
                    >
                      {test.testType}
                    </Badge>
                    {getPriorityBadge(test.priority)}
                    <Badge className="bg-green-600 text-white">Completed</Badge>
                  </div>
                </div>
                <Button
                  onClick={() => handleViewReport(test)}
                  variant="outline"
                  className="border-green-600 text-green-600 hover:bg-green-50"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View in Consultation
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="bg-muted/30 p-3 rounded-md">
                    <p className="text-xs font-medium text-muted-foreground flex items-center">
                      <User className="h-3.5 w-3.5 mr-1" />
                      Patient Information
                    </p>
                    <p className="font-medium">
                      {test.patient.firstName} {test.patient.lastName}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {test.patient.phone}
                    </p>
                    {test.patient.email && (
                      <p className="text-sm text-muted-foreground">
                        {test.patient.email}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-muted/30 p-3 rounded-md">
                    <p className="text-xs font-medium text-muted-foreground flex items-center">
                      <Calendar className="h-3.5 w-3.5 mr-1" />
                      Timeline
                    </p>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Requested:</span>
                        <span className="text-sm">
                          {format(new Date(test.requestDate), "PPp")}
                        </span>
                      </div>
                      {test.completedDate && (
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">
                            Completed:
                          </span>
                          <span className="text-sm">
                            {format(new Date(test.completedDate), "PPp")}
                          </span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Doctor:</span>
                        <span className="text-sm">{test.doctor.user.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Branch:</span>
                        <span className="text-sm">{test.branch.name}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Diagnostic Reports Summary */}
              {test.diagnosticReports && test.diagnosticReports.length > 0 && (
                <div className="mt-4 bg-green-50 border border-green-200 p-3 rounded-md">
                  <p className="text-sm font-medium text-green-700 mb-2">
                    Diagnostic Reports ({test.diagnosticReports.length}):
                  </p>
                  <div className="space-y-3">
                    {test.diagnosticReports.map((report) => (
                      <div
                        key={report.id}
                        className="flex justify-between items-center p-3 bg-white border border-green-200 rounded-md"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium text-green-600">
                              {report.reportType}
                            </span>
                            <Badge
                              variant="outline"
                              className="bg-green-100 text-green-800"
                            >
                              {report.status}
                            </Badge>
                          </div>
                          <span className="text-xs text-green-500">
                            {format(new Date(report.reportDate), "PP")}
                          </span>
                          {report.conclusion && (
                            <p className="text-xs text-muted-foreground mt-1">
                              {report.conclusion}
                            </p>
                          )}
                        </div>
                        <div className="flex gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownloadClick(report, test)}
                            disabled={downloadingIds.has(report.id)}
                            className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                          >
                            {downloadingIds.has(report.id) ? (
                              <>
                                <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                Downloading...
                              </>
                            ) : (
                              <>
                                <Download className="mr-1 h-3 w-3" />
                                Download PDF
                              </>
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleUploadClick(report, test)}
                            disabled={uploadingIds.has(report.id)}
                            className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
                          >
                            {uploadingIds.has(report.id) ? (
                              <>
                                <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                Uploading...
                              </>
                            ) : (
                              <>
                                <Upload className="mr-1 h-3 w-3" />
                                Upload to ABDM
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Uploaded Documents Section */}
              <div className="mt-4 bg-green-50 border border-green-200 p-3 rounded-md">
                <p className="text-sm font-medium text-green-700 mb-2">
                  Uploaded Documents:
                </p>
                <UploadedFilesList
                  consultationId={test.consultation.id}
                  bundleType="DiagnosticReport"
                  title=""
                  description=""
                  className="border-0 shadow-none bg-transparent p-0"
                  showOnlyGenerated={true}
                />
              </div>

              {test.notes && (
                <div className="mt-4 bg-blue-50 border border-blue-200 p-3 rounded-md">
                  <p className="text-sm font-medium text-blue-700">Notes:</p>
                  <p className="text-sm text-blue-600">{test.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Upload Lab Report to ABDM</DialogTitle>
            <DialogDescription>
              Select a health record request to upload this lab report to ABDM.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {selectedTest && selectedReport && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Patient</Label>
                <p className="text-sm text-muted-foreground">
                  {selectedTest.patient.firstName}{" "}
                  {selectedTest.patient.lastName}
                </p>
                <Label className="text-sm font-medium">Lab Report</Label>
                <p className="text-sm text-muted-foreground">
                  {selectedReport.reportType} -{" "}
                  {format(new Date(selectedReport.reportDate), "PP")}
                </p>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="fetch-request">Health Record Request</Label>
              {loadingFetchRequests ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">
                    Loading requests...
                  </span>
                </div>
              ) : fetchRequests.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No active health record requests found for this patient.
                </p>
              ) : (
                <Select
                  value={selectedFetchRequest}
                  onValueChange={setSelectedFetchRequest}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a health record request" />
                  </SelectTrigger>
                  <SelectContent>
                    {fetchRequests.map((request) => (
                      <SelectItem key={request.id} value={request.id}>
                        <div className="flex flex-col">
                          <span>
                            Request{" "}
                            {request.transactionId?.slice(-8) ||
                              request.id.slice(-8)}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(
                              request.requestTimestamp,
                            ).toLocaleDateString()}{" "}
                            - {request.status}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setUploadDialogOpen(false);
                setSelectedReport(null);
                setSelectedTest(null);
                setSelectedFetchRequest("");
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              disabled={
                !selectedFetchRequest ||
                uploadingIds.has(selectedReport?.id || "")
              }
            >
              {uploadingIds.has(selectedReport?.id || "") ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload to ABDM
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
