"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Flask<PERSON><PERSON>al, Clock, CheckCircle } from "lucide-react";
import { PendingLabTests } from "./pending-lab-tests";
import { CompletedLabTests } from "./completed-lab-tests";

export function LabReportsContent() {
  const [activeTab, setActiveTab] = useState("pending");

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-2 border-orange-100 shadow-sm">
          <CardHeader className="bg-orange-50 pb-3">
            <CardTitle className="flex items-center text-orange-700">
              <Clock className="h-5 w-5 mr-2" />
              Pending Tests
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-orange-600">
              <span id="pending-count">-</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Tests awaiting results
            </p>
          </CardContent>
        </Card>

        <Card className="border-2 border-green-100 shadow-sm">
          <CardHeader className="bg-green-50 pb-3">
            <CardTitle className="flex items-center text-green-700">
              <CheckCircle className="h-5 w-5 mr-2" />
              Completed Tests
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-green-600">
              <span id="completed-count">-</span>
            </div>
            <p className="text-sm text-muted-foreground">Tests with results</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-blue-100 shadow-sm">
          <CardHeader className="bg-blue-50 pb-3">
            <CardTitle className="flex items-center text-blue-700">
              <FlaskConical className="h-5 w-5 mr-2" />
              Total Tests
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-blue-600">
              <span id="total-count">-</span>
            </div>
            <p className="text-sm text-muted-foreground">
              All lab test requests
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Lab Reports Tabs */}
      <Card className="border-2 border-primary/10 shadow-md">
        <CardHeader className="bg-muted/30 pb-4">
          <CardTitle className="text-xl font-bold text-primary">
            Lab Test Management
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="pending" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Pending Tests
                <Badge
                  variant="outline"
                  className="bg-orange-100 text-orange-800"
                >
                  <span id="pending-tab-count">0</span>
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="completed"
                className="flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Completed Tests
                <Badge
                  variant="outline"
                  className="bg-green-100 text-green-800"
                >
                  <span id="completed-tab-count">0</span>
                </Badge>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="pending" className="mt-6">
              <PendingLabTests />
            </TabsContent>

            <TabsContent value="completed" className="mt-6">
              <CompletedLabTests />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
