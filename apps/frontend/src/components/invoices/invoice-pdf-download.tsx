"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Download, FileText, Loader2 } from "lucide-react";
import { toast } from "sonner";

interface InvoicePdfDownloadProps {
  consultationId: string;
  invoiceNumber: string;
  disabled?: boolean;
  variant?:
    | "default"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function InvoicePdfDownload({
  consultationId,
  invoiceNumber,
  disabled = false,
  variant = "outline",
  size = "default",
  className = "",
}: InvoicePdfDownloadProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    if (disabled || isLoading) return;

    setIsLoading(true);

    try {
      // Generate PDF and get download URL
      const response = await fetch("/api/pdf/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          consultationId,
          type: "invoice",
          format: "download",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate invoice PDF");
      }

      // Check if response is PDF
      const contentType = response.headers.get("content-type");
      if (contentType === "application/pdf") {
        // Create blob and download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `Invoice_${invoiceNumber}_${new Date().toISOString().split("T")[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success("Invoice PDF downloaded successfully");
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error("Error downloading invoice PDF:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to download invoice PDF",
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={disabled || isLoading}
      variant={variant}
      size={size}
      className={className}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Generating...
        </>
      ) : (
        <>
          <Download className="mr-2 h-4 w-4" />
          Download PDF
        </>
      )}
    </Button>
  );
}

// Alternative component for use in cards or detailed views
export function InvoicePdfDownloadCard({
  consultationId,
  invoiceNumber,
  disabled = false,
}: {
  consultationId: string;
  invoiceNumber: string;
  disabled?: boolean;
}) {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    if (disabled || isLoading) return;

    setIsLoading(true);

    try {
      const response = await fetch("/api/pdf/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          consultationId,
          type: "invoice",
          format: "download",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate invoice PDF");
      }

      const contentType = response.headers.get("content-type");
      if (contentType === "application/pdf") {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `Invoice_${invoiceNumber}_${new Date().toISOString().split("T")[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success("Invoice PDF downloaded successfully");
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error("Error downloading invoice PDF:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to download invoice PDF",
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 border border-orange-200 rounded-lg bg-orange-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-orange-100 rounded-lg">
            <FileText className="h-5 w-5 text-orange-600" />
          </div>
          <div>
            <h4 className="font-medium text-gray-900">Invoice PDF</h4>
            <p className="text-sm text-gray-600">
              Download a PDF copy of invoice #{invoiceNumber}
            </p>
          </div>
        </div>

        <Button
          onClick={handleDownload}
          disabled={disabled || isLoading}
          variant="outline"
          size="sm"
          className="bg-white hover:bg-orange-50 border-orange-300"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Download
            </>
          )}
        </Button>
      </div>

      {isLoading && (
        <div className="mt-3 flex items-center space-x-2 text-sm text-orange-600">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>Generating invoice PDF...</span>
        </div>
      )}
    </div>
  );
}
