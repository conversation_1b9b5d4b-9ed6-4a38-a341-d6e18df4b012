"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Plus,
  Trash2,
  Save,
  ArrowLeft,
  Calculator,
  FileText,
  User,
  Building,
} from "lucide-react";
import { toast } from "sonner";

interface InvoiceFormProps {
  consultation: any;
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface InvoiceItem {
  serviceCode?: string;
  serviceDisplay: string;
  category: string;
  quantity: number;
  unitPrice: number;
  mrp?: number;
  discountAmount: number;
  taxRate: number;
  notes?: string;
}

export function InvoiceForm({
  consultation,
  onSuccess,
  onCancel,
}: InvoiceFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<InvoiceItem[]>([
    {
      serviceDisplay: "Consultation Fee",
      category: "service",
      quantity: 1,
      unitPrice: consultation.doctor?.consultationFee || 500,
      discountAmount: 0,
      taxRate: 18,
    },
  ]);

  const [formData, setFormData] = useState({
    dueDate: "",
    type: "consultation",
    paymentTerms: "Net 30",
    notes: "",
  });

  // Calculate totals
  const calculateTotals = () => {
    let subtotal = 0;
    let totalDiscountAmount = 0;
    let totalTaxAmount = 0;

    items.forEach((item) => {
      const lineSubtotal = item.quantity * item.unitPrice;
      const lineDiscountAmount = item.discountAmount;
      const lineAfterDiscount = lineSubtotal - lineDiscountAmount;
      const lineTaxAmount = (lineAfterDiscount * item.taxRate) / 100;

      subtotal += lineSubtotal;
      totalDiscountAmount += lineDiscountAmount;
      totalTaxAmount += lineTaxAmount;
    });

    const totalAmount = subtotal - totalDiscountAmount + totalTaxAmount;

    return {
      subtotal: subtotal.toFixed(2),
      totalDiscountAmount: totalDiscountAmount.toFixed(2),
      totalTaxAmount: totalTaxAmount.toFixed(2),
      totalAmount: totalAmount.toFixed(2),
    };
  };

  const totals = calculateTotals();

  const addItem = () => {
    setItems([
      ...items,
      {
        serviceDisplay: "",
        category: "service",
        quantity: 1,
        unitPrice: 0,
        discountAmount: 0,
        taxRate: 18,
      },
    ]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  const updateItem = (index: number, field: keyof InvoiceItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    setItems(updatedItems);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate items
      const validItems = items.filter(
        (item) => item.serviceDisplay.trim() !== "",
      );
      if (validItems.length === 0) {
        toast.error("At least one service item is required");
        return;
      }

      const invoiceData = {
        consultationId: consultation.id,
        patientId: consultation.patientId,
        doctorId: consultation.doctorId,
        organizationId: consultation.organizationId,
        branchId: consultation.branchId,
        dueDate: formData.dueDate || null,
        type: formData.type,
        paymentTerms: formData.paymentTerms,
        notes: formData.notes,
        items: validItems,
      };

      const response = await fetch("/api/invoices", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(invoiceData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create invoice");
      }

      const result = await response.json();
      toast.success("Invoice created successfully");

      // Generate FHIR bundle for the invoice
      try {
        await fetch("/api/fhir/bundles/invoice", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            invoiceId: result.invoice.id,
          }),
        });
      } catch (fhirError) {
        console.error("Error generating FHIR bundle:", fhirError);
        // Don't fail the invoice creation if FHIR bundle generation fails
      }

      if (onSuccess) {
        onSuccess();
      } else {
        router.push(`/consultations/${consultation.id}`);
      }
    } catch (error) {
      console.error("Error creating invoice:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create invoice",
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="bg-primary/5">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl font-bold text-primary flex items-center gap-2">
                <FileText className="h-6 w-6" />
                Create Invoice
              </CardTitle>
              <CardDescription className="text-base mt-1">
                Generate invoice for consultation on{" "}
                {format(new Date(consultation.consultationDate), "PPP")}
              </CardDescription>
            </div>
            <Badge variant="outline" className="text-sm">
              {consultation.status}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Patient & Doctor Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <User className="h-5 w-5" />
              Patient Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="font-medium">
                {consultation.patient?.firstName}{" "}
                {consultation.patient?.lastName}
              </p>
              <p className="text-sm text-muted-foreground">
                {consultation.patient?.phone}
              </p>
              {consultation.patient?.email && (
                <p className="text-sm text-muted-foreground">
                  {consultation.patient.email}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Building className="h-5 w-5" />
              Provider Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="font-medium">
                {consultation.doctor?.user?.name || "Dr. Unknown"}
              </p>
              <p className="text-sm text-muted-foreground">
                {consultation.doctor?.specialization}
              </p>
              <p className="text-sm text-muted-foreground">
                {consultation.branch?.name}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Invoice Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Invoice Details */}
        <Card>
          <CardHeader>
            <CardTitle>Invoice Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="dueDate">Due Date (Optional)</Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) =>
                    setFormData({ ...formData, dueDate: e.target.value })
                  }
                />
              </div>
              <div>
                <Label htmlFor="type">Invoice Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) =>
                    setFormData({ ...formData, type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="consultation">Consultation</SelectItem>
                    <SelectItem value="pharmacy">Pharmacy</SelectItem>
                    <SelectItem value="lab">Laboratory</SelectItem>
                    <SelectItem value="procedure">Procedure</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="paymentTerms">Payment Terms</Label>
                <Select
                  value={formData.paymentTerms}
                  onValueChange={(value) =>
                    setFormData({ ...formData, paymentTerms: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Due on receipt">
                      Due on receipt
                    </SelectItem>
                    <SelectItem value="Net 15">Net 15</SelectItem>
                    <SelectItem value="Net 30">Net 30</SelectItem>
                    <SelectItem value="Net 60">Net 60</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes or instructions..."
                value={formData.notes}
                onChange={(e) =>
                  setFormData({ ...formData, notes: e.target.value })
                }
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Line Items */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Service Items</CardTitle>
            <Button type="button" onClick={addItem} size="sm" variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            {items.map((item, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Item {index + 1}</h4>
                  {items.length > 1 && (
                    <Button
                      type="button"
                      onClick={() => removeItem(index)}
                      size="sm"
                      variant="ghost"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="lg:col-span-2">
                    <Label>Service Description *</Label>
                    <Input
                      value={item.serviceDisplay}
                      onChange={(e) =>
                        updateItem(index, "serviceDisplay", e.target.value)
                      }
                      placeholder="Enter service description"
                      required
                    />
                  </div>
                  <div>
                    <Label>Category</Label>
                    <Select
                      value={item.category}
                      onValueChange={(value) =>
                        updateItem(index, "category", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="service">Service</SelectItem>
                        <SelectItem value="medication">Medication</SelectItem>
                        <SelectItem value="lab">Laboratory</SelectItem>
                        <SelectItem value="procedure">Procedure</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Quantity</Label>
                    <Input
                      type="number"
                      min="0.01"
                      step="0.01"
                      value={item.quantity}
                      onChange={(e) =>
                        updateItem(
                          index,
                          "quantity",
                          parseFloat(e.target.value) || 0,
                        )
                      }
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <Label>Unit Price (₹)</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.unitPrice}
                      onChange={(e) =>
                        updateItem(
                          index,
                          "unitPrice",
                          parseFloat(e.target.value) || 0,
                        )
                      }
                    />
                  </div>
                  <div>
                    <Label>MRP (₹)</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.mrp || ""}
                      onChange={(e) =>
                        updateItem(
                          index,
                          "mrp",
                          e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        )
                      }
                      placeholder="Optional"
                    />
                  </div>
                  <div>
                    <Label>Discount (₹)</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.discountAmount}
                      onChange={(e) =>
                        updateItem(
                          index,
                          "discountAmount",
                          parseFloat(e.target.value) || 0,
                        )
                      }
                    />
                  </div>
                  <div>
                    <Label>Tax Rate (%)</Label>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      value={item.taxRate}
                      onChange={(e) =>
                        updateItem(
                          index,
                          "taxRate",
                          parseFloat(e.target.value) || 0,
                        )
                      }
                    />
                  </div>
                </div>

                <div>
                  <Label>Item Notes</Label>
                  <Input
                    value={item.notes || ""}
                    onChange={(e) => updateItem(index, "notes", e.target.value)}
                    placeholder="Optional notes for this item"
                  />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Totals */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Invoice Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>₹{totals.subtotal}</span>
              </div>
              <div className="flex justify-between">
                <span>Discount:</span>
                <span>-₹{totals.totalDiscountAmount}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax:</span>
                <span>₹{totals.totalTaxAmount}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-bold text-lg">
                <span>Total Amount:</span>
                <span>₹{totals.totalAmount}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel || (() => router.back())}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              "Creating..."
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Invoice
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
