"use client";

import { useState } from "react";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FileText,
  Download,
  Edit,
  Trash2,
  User,
  Building,
  Calendar,
  CreditCard,
  Receipt,
  Eye,
} from "lucide-react";
import { toast } from "sonner";
import { InvoicePdfDownload } from "./invoice-pdf-download";

interface InvoiceDisplayProps {
  invoice: any;
  onEdit?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
}

export function InvoiceDisplay({
  invoice,
  onEdit,
  onDelete,
  showActions = true,
}: InvoiceDisplayProps) {
  const [loading, setLoading] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800";
      case "issued":
        return "bg-blue-100 text-blue-800";
      case "overdue":
        return "bg-red-100 text-red-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800";
      case "partial":
        return "bg-yellow-100 text-yellow-800";
      case "pending":
        return "bg-orange-100 text-orange-800";
      case "failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleDownloadFhirBundle = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/fhir/bundles/invoice?invoiceId=${invoice.id}`,
      );

      if (!response.ok) {
        throw new Error("Failed to fetch FHIR bundle");
      }

      const data = await response.json();

      // Download as JSON file
      const blob = new Blob([JSON.stringify(data.bundle, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `invoice-${invoice.invoiceNumber}-fhir-bundle.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success("FHIR bundle downloaded successfully");
    } catch (error) {
      console.error("Error downloading FHIR bundle:", error);
      toast.error("Failed to download FHIR bundle");
    } finally {
      setLoading(false);
    }
  };

  const handleViewFhirBundle = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/fhir/bundles/invoice?invoiceId=${invoice.id}`,
      );

      if (!response.ok) {
        throw new Error("Failed to fetch FHIR bundle");
      }

      const data = await response.json();

      // Open in new window for viewing
      const newWindow = window.open("", "_blank");
      if (newWindow) {
        newWindow.document.write(`
          <html>
            <head>
              <title>FHIR Bundle - Invoice ${invoice.invoiceNumber}</title>
              <style>
                body { font-family: monospace; margin: 20px; }
                pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow: auto; }
              </style>
            </head>
            <body>
              <h1>FHIR Bundle - Invoice ${invoice.invoiceNumber}</h1>
              <pre>${JSON.stringify(data.bundle, null, 2)}</pre>
            </body>
          </html>
        `);
        newWindow.document.close();
      }

      toast.success("FHIR bundle opened in new window");
    } catch (error) {
      console.error("Error viewing FHIR bundle:", error);
      toast.error("Failed to view FHIR bundle");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="bg-primary/5">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl font-bold text-primary flex items-center gap-2">
                <FileText className="h-6 w-6" />
                Invoice {invoice.invoiceNumber}
              </CardTitle>
              <CardDescription className="text-base mt-1">
                Created on {format(new Date(invoice.invoiceDate), "PPP")}
                {invoice.dueDate && (
                  <> • Due on {format(new Date(invoice.dueDate), "PPP")}</>
                )}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(invoice.status)}>
                {invoice.status}
              </Badge>
              <Badge className={getPaymentStatusColor(invoice.paymentStatus)}>
                {invoice.paymentStatus}
              </Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Invoice Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Patient Information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <User className="h-5 w-5" />
              Bill To
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="font-medium text-lg">
                {invoice.patient?.firstName} {invoice.patient?.lastName}
              </p>
              <p className="text-sm text-muted-foreground">
                Phone: {invoice.patient?.phone}
              </p>
              {invoice.patient?.email && (
                <p className="text-sm text-muted-foreground">
                  Email: {invoice.patient.email}
                </p>
              )}
              {invoice.patient?.address && (
                <div className="text-sm text-muted-foreground mt-2">
                  <p>{invoice.patient.address}</p>
                  {invoice.patient.city && (
                    <p>
                      {invoice.patient.city}
                      {invoice.patient.state && `, ${invoice.patient.state}`}
                      {invoice.patient.pincode &&
                        ` - ${invoice.patient.pincode}`}
                    </p>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Provider Information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Building className="h-5 w-5" />
              From
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="font-medium text-lg">
                {invoice.organization?.name}
              </p>
              <p className="text-sm text-muted-foreground">
                {invoice.branch?.name}
              </p>
              <p className="text-sm text-muted-foreground">
                Dr. {invoice.doctor?.user?.name || "Unknown"}
              </p>
              {invoice.doctor?.specialization && (
                <p className="text-sm text-muted-foreground">
                  {invoice.doctor.specialization}
                </p>
              )}
              {invoice.branch?.address && (
                <div className="text-sm text-muted-foreground mt-2">
                  <p>{invoice.branch.address}</p>
                  {invoice.branch.city && (
                    <p>
                      {invoice.branch.city}
                      {invoice.branch.state && `, ${invoice.branch.state}`}
                      {invoice.branch.pincode && ` - ${invoice.branch.pincode}`}
                    </p>
                  )}
                  {invoice.branch.phone && <p>Phone: {invoice.branch.phone}</p>}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Invoice Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Invoice Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Invoice Date</p>
                <p className="text-sm text-muted-foreground">
                  {format(new Date(invoice.invoiceDate), "PPP")}
                </p>
              </div>
            </div>
            {invoice.dueDate && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Due Date</p>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(invoice.dueDate), "PPP")}
                  </p>
                </div>
              </div>
            )}
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Payment Terms</p>
                <p className="text-sm text-muted-foreground">
                  {invoice.paymentTerms || "N/A"}
                </p>
              </div>
            </div>
          </div>
          {invoice.notes && (
            <div className="mt-4">
              <p className="text-sm font-medium">Notes</p>
              <p className="text-sm text-muted-foreground mt-1">
                {invoice.notes}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Line Items */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Services & Items</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Description</TableHead>
                <TableHead>Category</TableHead>
                <TableHead className="text-right">Qty</TableHead>
                <TableHead className="text-right">Unit Price</TableHead>
                <TableHead className="text-right">Discount</TableHead>
                <TableHead className="text-right">Tax</TableHead>
                <TableHead className="text-right">Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoice.items?.map((item: any) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{item.serviceDisplay}</p>
                      {item.notes && (
                        <p className="text-sm text-muted-foreground">
                          {item.notes}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{item.category}</Badge>
                  </TableCell>
                  <TableCell className="text-right">{item.quantity}</TableCell>
                  <TableCell className="text-right">
                    ₹{parseFloat(item.unitPrice).toFixed(2)}
                  </TableCell>
                  <TableCell className="text-right">
                    ₹{parseFloat(item.discountAmount).toFixed(2)}
                  </TableCell>
                  <TableCell className="text-right">{item.taxRate}%</TableCell>
                  <TableCell className="text-right font-medium">
                    ₹{parseFloat(item.totalAmount).toFixed(2)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Totals */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            Invoice Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-w-sm ml-auto">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>₹{parseFloat(invoice.subtotal).toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Discount:</span>
              <span>-₹{parseFloat(invoice.discountAmount).toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Tax:</span>
              <span>₹{parseFloat(invoice.taxAmount).toFixed(2)}</span>
            </div>
            <Separator />
            <div className="flex justify-between font-bold text-lg">
              <span>Total Amount:</span>
              <span>₹{parseFloat(invoice.totalAmount).toFixed(2)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      {showActions && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-wrap gap-2">
              <InvoicePdfDownload
                consultationId={invoice.consultationId}
                invoiceNumber={invoice.invoiceNumber}
                variant="outline"
              />
              <Button
                onClick={handleViewFhirBundle}
                variant="outline"
                disabled={loading}
              >
                <Eye className="h-4 w-4 mr-2" />
                View FHIR Bundle
              </Button>
              <Button
                onClick={handleDownloadFhirBundle}
                variant="outline"
                disabled={loading}
              >
                <Download className="h-4 w-4 mr-2" />
                Download FHIR Bundle
              </Button>
              {onEdit && (
                <Button onClick={onEdit} variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Invoice
                </Button>
              )}
              {onDelete && (
                <Button
                  onClick={onDelete}
                  variant="outline"
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Invoice
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
