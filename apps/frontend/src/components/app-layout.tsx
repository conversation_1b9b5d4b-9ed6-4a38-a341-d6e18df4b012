"use client";

import React, { useState, useEffect } from "react";
import { UserNav } from "@/components/user-nav";

import ContextSwitcher from "@/components/context-switcher";
import { MoonIcon, SunIcon } from "lucide-react";
import { MobileMenuTrigger } from "@/components/sidebar/role-based-sidebar";
import { Button } from "@/components/ui/button";
import { useTheme } from "next-themes";
import { BranchProvider } from "@/contexts/branch-context";
import { SocketProvider } from "@/components/providers/socket-provider";
import { OrganizationHeaderSwitcher } from "./organization-header-switcher";
// Context providers are now used only in specific page layouts

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // After mounting, we can safely show the UI that depends on the theme
  useEffect(() => {
    setMounted(true);
  }, []);

  // Toggle dark mode
  const toggleDarkMode = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  if (!mounted) {
    return null;
  }

  return (
    <BranchProvider>
      <SocketProvider>
        <div className="flex h-screen bg-background dark:bg-gray-950">
          {/* Sidebar */}
          {/* <SidebarNav /> */}

          {/* Main content */}
          <div className="flex flex-col flex-1 overflow-hidden">
            {/* Header */}
            <header className="sticky top-0 z-10 flex items-center justify-between h-16 px-6 border-b bg-background dark:bg-gray-900">
              <div className="flex items-center space-x-4">
                <MobileMenuTrigger />
                <OrganizationHeaderSwitcher />
              </div>
              <div className="flex items-center space-x-4">
                <ContextSwitcher />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleDarkMode}
                  aria-label="Toggle theme"
                >
                  {theme === "dark" ? (
                    <SunIcon className="h-5 w-5" />
                  ) : (
                    <MoonIcon className="h-5 w-5" />
                  )}
                </Button>
                <UserNav />
              </div>
            </header>

            {/* Page content */}
            <main className="flex-1 overflow-y-auto p-6 crm-scrollbar">
              {children}
            </main>
          </div>
        </div>
      </SocketProvider>
    </BranchProvider>
  );
}
