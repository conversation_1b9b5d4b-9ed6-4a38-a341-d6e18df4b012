"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { Fetch } from "@/services/fetch";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { format } from "date-fns";

// Define the form schema
const formSchema = z.object({
  purpose: z.enum([
    "Care Management",
    "Break the Glass",
    "Public Health",
    "Healthcare Payment",
    "Disease Specific Healthcare Research",
    "Self Requested",
  ]),
  hiTypes: z.array(z.string()).min(1, {
    message: "Select at least one health information type.",
  }),
  accessMode: z.enum(["VIEW", "STORE"]),
  dateRange: z
    .object({
      from: z.date({
        required_error: "From date is required",
        invalid_type_error: "Please enter a valid date and time",
      }),
      to: z.date({
        required_error: "To date is required",
        invalid_type_error: "Please enter a valid date and time",
      }),
    })
    .refine((data) => data.to >= data.from, {
      message: "To date must be after from date",
      path: ["to"],
    })
    .refine((data) => data.to <= new Date(), {
      message: "To date cannot be in the future",
      path: ["to"],
    }),
  dataEraseAt: z
    .date({
      required_error: "Data erase date is required",
      invalid_type_error: "Please enter a valid date and time",
    })
    .refine((date) => date > new Date(), {
      message: "Data erase date must be in the future",
    }),
});

// Health information types
const healthInfoTypes = [
  { id: "DiagnosticReport", label: "Diagnostic Report" },
  { id: "DischargeSummary", label: "Discharge Summary" },
  { id: "HealthDocumentRecord", label: "Health Document Record" },
  { id: "ImmunizationRecord", label: "Immunization Record" },
  { id: "OPConsultation", label: "OP Consultation" },
  { id: "Prescription", label: "Prescription" },
  { id: "WellnessRecord", label: "Wellness Record" },
];

interface ConsentRequestFormProps {
  patientId: string;
  onSuccess?: (consentId: string) => void;
}

export function ConsentRequestForm({
  patientId,
  onSuccess,
}: ConsentRequestFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      purpose: "Care Management",
      hiTypes: [],
      accessMode: "VIEW",
      dateRange: {
        from: new Date(new Date().setMonth(new Date().getMonth() - 1)), // One month back
        to: new Date(), // Today's date
      },
      dataEraseAt: new Date(
        new Date().setFullYear(new Date().getFullYear() + 1),
      ), // Default to 1 year
    },
  });

  // Helper function to format date for ABDM API
  const formatDateForABDM = (date: Date): string => {
    // Set time to specific values to ensure consistency
    // For 'from' dates, set to start of day (00:00:00)
    // For 'to' dates, set to end of day (23:59:59)
    // This ensures we cover the full day in the request
    const d = new Date(date);

    // Format as ISO string with timezone offset
    // ABDM API expects dates in ISO format
    return d.toISOString();
  };

  // Helper function to ensure date is valid
  const ensureValidDate = (date: any): Date => {
    if (!date) return new Date();
    if (typeof date === "string") return new Date(date);
    return date;
  };

  // Helper function to validate date input
  const validateDateInput = (
    value: string,
    fieldName: string,
  ): string | null => {
    if (!value) return "Date and time are required";

    const date = new Date(value);
    if (isNaN(date.getTime())) return "Please enter a valid date and time";

    const now = new Date();

    if (fieldName === "dataEraseAt") {
      if (date <= now) return "Data erase date must be in the future";

      // Check if date is too far in the future (more than 10 years)
      const maxFutureDate = new Date();
      maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 10);
      if (date > maxFutureDate)
        return "Data erase date cannot be more than 10 years in the future";
    } else if (fieldName === "dateRange.from" || fieldName === "dateRange.to") {
      if (date > now) return "Date cannot be in the future";

      // Check if date is too far in the past (more than 10 years)
      const minPastDate = new Date();
      minPastDate.setFullYear(minPastDate.getFullYear() - 10);
      if (date < minPastDate)
        return "Date cannot be more than 10 years in the past";
    }

    return null;
  };

  // Form submission handler
  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsSubmitting(true);

      // Ensure dates are valid and format the request payload
      const fromDate = ensureValidDate(values.dateRange.from);
      const toDate = ensureValidDate(values.dateRange.to);
      const eraseDate = ensureValidDate(values.dataEraseAt);

      // Format the request payload
      const payload = {
        patientId,
        purpose: values.purpose,
        hiTypes: values.hiTypes,
        permission: {
          accessMode: values.accessMode,
          dateRange: {
            from: formatDateForABDM(fromDate),
            to: formatDateForABDM(toDate),
          },
          dataEraseAt: formatDateForABDM(eraseDate),
        },
        careContexts: [], // This would be populated based on patient's care contexts
      };

      // Send the request to the new endpoint
      const response = await Fetch.post(
        "/api/abdm/consent/request/new",
        payload,
      );

      if (response.success) {
        toast.success("Consent request sent");

        // Call the success callback if provided
        if (onSuccess && response.consentId) {
          onSuccess(response.consentId);
        }

        // Refresh the page
        router.refresh();
      } else {
        toast.error(response.error || "Failed to send consent request");
      }
    } catch (error) {
      console.error("Error submitting consent request:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader className="pb-4">
        <CardTitle>Request Patient Consent</CardTitle>
        <CardDescription>
          Request consent from the patient to access their health records
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="purpose"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Purpose</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select purpose" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Care Management">
                        Care Management
                      </SelectItem>
                      <SelectItem value="Break the Glass">
                        Break the Glass
                      </SelectItem>
                      <SelectItem value="Public Health">
                        Public Health
                      </SelectItem>
                      <SelectItem value="Healthcare Payment">
                        Healthcare Payment
                      </SelectItem>
                      <SelectItem value="Disease Specific Healthcare Research">
                        Disease Specific Healthcare Research
                      </SelectItem>
                      <SelectItem value="Self Requested">
                        Self Requested
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Select the purpose for requesting health information
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="hiTypes"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel>Health Information Types</FormLabel>
                    <FormDescription>
                      Select the types of health information you need
                    </FormDescription>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    {healthInfoTypes.map((type) => (
                      <FormField
                        key={type.id}
                        control={form.control}
                        name="hiTypes"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={type.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(type.id)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([
                                          ...field.value,
                                          type.id,
                                        ])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== type.id,
                                          ),
                                        );
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {type.label}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="accessMode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Access Mode</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select access mode" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="VIEW">View Only</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose whether to only view or also store the health records
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <FormLabel>Date Range</FormLabel>
              <div className="flex items-center gap-4">
                <FormField
                  control={form.control}
                  name="dateRange.from"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="text-xs">From</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          value={
                            field.value
                              ? format(field.value, "yyyy-MM-dd'T'HH:mm")
                              : ""
                          }
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value) {
                              const validationError = validateDateInput(
                                value,
                                "dateRange.from",
                              );
                              if (validationError) {
                                form.setError("dateRange.from", {
                                  message: validationError,
                                });
                              } else {
                                form.clearErrors("dateRange.from");
                                field.onChange(new Date(value));
                              }
                            } else {
                              field.onChange(null);
                            }
                          }}
                          max={format(new Date(), "yyyy-MM-dd'T'HH:mm")} // Prevent selecting future dates
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="dateRange.to"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="text-xs">To</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          value={
                            field.value
                              ? format(field.value, "yyyy-MM-dd'T'HH:mm")
                              : ""
                          }
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value) {
                              const validationError = validateDateInput(
                                value,
                                "dateRange.to",
                              );
                              const fromDate = form.getValues("dateRange.from");

                              if (validationError) {
                                form.setError("dateRange.to", {
                                  message: validationError,
                                });
                              } else if (
                                fromDate &&
                                new Date(value) < fromDate
                              ) {
                                form.setError("dateRange.to", {
                                  message: "To date must be after from date",
                                });
                              } else {
                                form.clearErrors("dateRange.to");
                                field.onChange(new Date(value));
                              }
                            } else {
                              field.onChange(null);
                            }
                          }}
                          min={
                            form.getValues("dateRange.from")
                              ? format(
                                  form.getValues("dateRange.from"),
                                  "yyyy-MM-dd'T'HH:mm",
                                )
                              : ""
                          }
                          max={format(new Date(), "yyyy-MM-dd'T'HH:mm")} // Prevent selecting future dates
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormDescription>
                Select the date and time range for the health records you need.
                Dates cannot be in the future.
              </FormDescription>
            </div>

            <FormField
              control={form.control}
              name="dataEraseAt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data Erase Date</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      value={
                        field.value
                          ? format(field.value, "yyyy-MM-dd'T'HH:mm")
                          : ""
                      }
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value) {
                          const validationError = validateDateInput(
                            value,
                            "dataEraseAt",
                          );
                          if (validationError) {
                            form.setError("dataEraseAt", {
                              message: validationError,
                            });
                          } else {
                            form.clearErrors("dataEraseAt");
                            field.onChange(new Date(value));
                          }
                        } else {
                          field.onChange(null);
                        }
                      }}
                      min={format(new Date(), "yyyy-MM-dd'T'HH:mm")} // Prevent selecting past dates for erase date
                    />
                  </FormControl>
                  <FormDescription>
                    Date and time when the data should be automatically erased.
                    Must be in the future.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="mt-8 mb-2">
              <Button type="submit" disabled={isSubmitting} className="w-full">
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  "Request Consent"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
