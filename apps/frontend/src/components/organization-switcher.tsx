"use client";

import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Building,
  ChevronDown,
  Plus,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import { useOrganization } from "@/contexts/organization-context";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

export function OrganizationSwitcher() {
  const {
    currentOrganization,
    organizations,
    setCurrentOrganization,
    isLoading,
    refreshOrganizations,
  } = useOrganization();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newOrgName, setNewOrgName] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const router = useRouter();

  // Refresh organizations when component mounts
  useEffect(() => {
    // If we're loading or have no organization, try to refresh
    if ((isLoading || !currentOrganization) && organizations.length === 0) {
      refreshOrganizations();
    }
  }, [
    isLoading,
    currentOrganization,
    organizations.length,
    refreshOrganizations,
  ]);

  const handleSwitchOrganization = async (orgId: string) => {
    const org = organizations.find((o) => o.id === orgId);
    if (org) {
      // Check if organization is active
      if (org.status === "inactive") {
        // Redirect to organization deactivated page
        window.location.href = `/organization-deactivated?orgId=${org.id}&orgName=${encodeURIComponent(org.name)}${org.logo ? `&orgLogo=${encodeURIComponent(org.logo)}` : ""}`;
        return;
      }

      try {
        const response = await fetch("/api/user/switch-context", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ organizationId: org.id }),
        });

        if (response.ok) {
          const responseData = await response.json();
          setCurrentOrganization(org);

          // Update the user-info cookie immediately for instant UI updates
          if (responseData.context?.organization) {
            const currentUserInfo = document.cookie
              .split('; ')
              .find(row => row.startsWith('user-info='));

            if (currentUserInfo) {
              try {
                const userInfoValue = decodeURIComponent(currentUserInfo.split('=')[1]);
                const userInfo = JSON.parse(userInfoValue);
                const updatedUserInfo = {
                  ...userInfo,
                  organizationId: responseData.context.organization.id,
                  organizationName: responseData.context.organization.name,
                  organizationSlug: responseData.context.organization.slug,
                };

                // Update the cookie immediately
                document.cookie = `user-info=${encodeURIComponent(JSON.stringify(updatedUserInfo))}; path=/; max-age=${30 * 24 * 60 * 60}`;

                // Dispatch custom event with the updated organization info
                window.dispatchEvent(new CustomEvent("organizationChanged", {
                  detail: {
                    organization: responseData.context.organization,
                    userInfo: updatedUserInfo
                  }
                }));

                // Small delay to allow UI updates, then reload
                setTimeout(() => {
                  window.location.reload();
                }, 100);
              } catch (error) {
                console.error("Error updating user info cookie:", error);
                window.location.reload();
              }
            } else {
              window.location.reload();
            }
          } else {
            window.location.reload();
          }
        } else {
          const errorData = await response.json();
          if (errorData.organizationDeactivated) {
            // Redirect to organization deactivated page
            window.location.href = `/organization-deactivated?orgId=${errorData.organizationId}&orgName=${encodeURIComponent(errorData.organizationName)}`;
          } else {
            console.error("Failed to switch organization:", errorData);
          }
        }
      } catch (error) {
        console.error("Error switching organization:", error);
      }
    }
  };

  const handleCreateOrganization = async () => {
    if (!newOrgName.trim()) return;

    setIsCreating(true);
    try {
      const response = await fetch("/api/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: newOrgName }),
      });

      if (response.ok) {
        await response.json();
        setIsDialogOpen(false);
        setNewOrgName("");

        // Refresh the organizations list
        await refreshOrganizations();
        router.refresh();
      }
    } catch (error) {
      console.error("Error creating organization:", error);
    } finally {
      setIsCreating(false);
    }
  };

  if (isLoading || !currentOrganization) {
    return (
      <Button variant="outline" size="sm" className="h-9 gap-1 opacity-70">
        <Building className="h-4 w-4" />
        <span className="max-w-[150px] truncate">Loading...</span>
      </Button>
    );
  }

  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="h-9 gap-1">
            <Building className="h-4 w-4" />
            <span className="max-w-[150px] truncate">
              {currentOrganization.name}
            </span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[200px]">
          <DropdownMenuLabel>Organizations</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {organizations.map((org) => (
            <DropdownMenuItem
              key={org.id}
              onClick={() => handleSwitchOrganization(org.id)}
              className={`${
                org.id === currentOrganization.id
                  ? "bg-accent text-accent-foreground"
                  : ""
              } ${org.status === "inactive" ? "opacity-60" : ""}`}
            >
              <Building className="mr-2 h-4 w-4" />
              <div className="flex-1 flex items-center justify-between">
                <span className="truncate">{org.name}</span>
                <div className="flex items-center gap-1 ml-2">
                  {org.status === "active" ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-3 w-3 text-orange-500" />
                  )}
                  <span
                    className={`text-xs ${
                      org.status === "active"
                        ? "text-green-600"
                        : "text-orange-600"
                    }`}
                  >
                    {org.status === "active" ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </DropdownMenuItem>
          ))}
          <DropdownMenuSeparator />
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <Plus className="mr-2 h-4 w-4" />
                Create Organization
              </DropdownMenuItem>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Organization</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Organization Name</Label>
                  <Input
                    id="name"
                    placeholder="Acme Inc."
                    value={newOrgName}
                    onChange={(e) => setNewOrgName(e.target.value)}
                  />
                </div>
                <Button
                  onClick={handleCreateOrganization}
                  disabled={!newOrgName.trim() || isCreating}
                  className="w-full"
                >
                  {isCreating ? "Creating..." : "Create Organization"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
