"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { DatePicker } from "@/components/ui/date-picker";
import { toast } from "sonner";
import { Fetch } from "@/services/fetch";
import { AzureFhirUpload } from "@/components/azure-fhir-upload";
import { UploadedFilesList } from "@/components/uploaded-files-list";

// Define the protocol applied schema for dose number
const protocolAppliedSchema = z.object({
  doseNumber: z.string().min(1, "Dose number is required"),
});

// Define the simplified form schema with only 4 required fields
const formSchema = z.object({
  patientId: z.string(),
  doctorId: z.string(),
  consultationId: z.string().optional(),
  status: z.string(),
  vaccineCode: z.string(),
  vaccineDisplay: z.string().min(1, "Vaccination name is required"),
  occurrenceDateTime: z.date(),
  recorded: z.date(),
  primarySource: z.boolean(),
  manufacturer: z.string().min(1, "Manufacturer is required"),
  lotNumber: z.string().min(1, "Lot number is required"),
  protocolApplied: z.array(protocolAppliedSchema),
});

type FormValues = z.infer<typeof formSchema>;

interface ImmunizationFormProps {
  patientId: string;
  doctorId: string;
  consultationId?: string;
  onCancel?: () => void;
  onSuccess?: () => void;
}

export function ImmunizationForm({
  patientId,
  doctorId,
  consultationId,
  onCancel,
  onSuccess,
}: ImmunizationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      patientId,
      doctorId,
      consultationId,
      status: "completed",
      vaccineCode: "",
      vaccineDisplay: "",
      occurrenceDateTime: new Date(),
      recorded: new Date(),
      primarySource: true,
      manufacturer: "",
      lotNumber: "",
      protocolApplied: [{ doseNumber: "1" }],
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      const response = await Fetch.post("/api/immunization", data);

      if (response.error) {
        throw new Error(response.error);
      }

      toast.success("Immunization record created successfully");
      form.reset();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error creating immunization record:", error);
      toast.error("Failed to create immunization record");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 1. Vaccination Name */}
          <FormField
            control={form.control}
            name="vaccineDisplay"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Vaccination Name *</FormLabel>
                <FormControl>
                  <Input placeholder="Enter vaccination name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 2. Manufacturer */}
          <FormField
            control={form.control}
            name="manufacturer"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Manufacturer *</FormLabel>
                <FormControl>
                  <Input placeholder="Enter manufacturer name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 3. Lot Number */}
          <FormField
            control={form.control}
            name="lotNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Lot Number *</FormLabel>
                <FormControl>
                  <Input placeholder="Enter lot number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 4. Dose Number */}
          <FormField
            control={form.control}
            name="protocolApplied.0.doseNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Dose Number *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter dose number (e.g., 1, 2, 3)"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* 5. Occurrence Date */}
          <FormField
            control={form.control}
            name="occurrenceDateTime"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Occurance Date *</FormLabel>
                <DatePicker date={field.value} setDate={field.onChange} />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Document Upload Section */}
        {consultationId && (
          <div className="bg-muted/20 p-4 rounded-lg border border-muted">
            <h3 className="text-sm font-medium mb-3 text-muted-foreground">
              Immunization Document Upload (Optional)
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              Upload a PDF immunization document that will be included in the
              FHIR bundle when immunization record is saved.
            </p>
            <AzureFhirUpload
              consultationId={consultationId}
              patientId={patientId}
              bundleType="ImmunizationRecord"
              title="Upload Immunization Document"
              description="Upload a PDF immunization document"
              onUploadSuccess={(_result) => {
                toast.success(
                  "Immunization document uploaded successfully. It will be included when immunization record is saved.",
                );
              }}
              hideWhenFilesExist={false}
            />
            <UploadedFilesList
              consultationId={consultationId}
              bundleType="ImmunizationRecord"
              title="Uploaded Immunization Documents"
              description="Documents that will be included in the FHIR bundle"
              showOnlyGenerated={false}
              className="mt-4"
            />
          </div>
        )}

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Immunization Record"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
