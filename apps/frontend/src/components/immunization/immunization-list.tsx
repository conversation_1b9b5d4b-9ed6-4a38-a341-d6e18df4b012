"use client";

import { useState, useEffect } from "react";
import { Fetch } from "@/services/fetch";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Loader2, FileText, Download, Share, Syringe } from "lucide-react";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";
import { UploadedFilesList } from "@/components/uploaded-files-list";

interface Reaction {
  date?: string;
  detail?: string;
  reported?: boolean;
}

interface ProtocolApplied {
  series?: string;
  authority?: string;
  targetDisease?: string;
  doseNumber?: string;
  seriesDoses?: string;
}

interface Immunization {
  id: string;
  status: string;
  statusReason?: string;
  vaccineCode: string;
  vaccineDisplay: string;
  occurrenceDateTime: string;
  recorded: string;
  primarySource: boolean;
  reportOrigin?: string;
  location?: string;
  manufacturer?: string;
  lotNumber?: string;
  expirationDate?: string;
  site?: string;
  route?: string;
  doseQuantity?: string;
  performer?: string;
  note?: string;
  reasonCode?: string;
  reasonDisplay?: string;
  isSubpotent?: boolean;
  subpotentReason?: string;
  programEligibility?: string;
  fundingSource?: string;
  reaction?: Reaction[];
  protocolApplied?: ProtocolApplied[];
  createdAt: string;
  updatedAt: string;
}

interface ImmunizationListProps {
  patientId: string;
  consultationId?: string;
  refreshTrigger?: number;
}

export function ImmunizationList({
  patientId,
  consultationId,
  refreshTrigger = 0,
}: ImmunizationListProps) {
  const [immunizations, setImmunizations] = useState<Immunization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImmunization, setSelectedImmunization] =
    useState<Immunization | null>(null);

  useEffect(() => {
    const fetchImmunizations = async () => {
      setLoading(true);
      setError(null);
      try {
        const url = consultationId
          ? `/api/immunization?consultationId=${consultationId}`
          : `/api/immunization?patientId=${patientId}`;

        const response = await Fetch.get(url);

        if (response.error) {
          throw new Error(response.error);
        }

        setImmunizations(response.immunizations || []);
      } catch (err) {
        console.error("Error fetching immunizations:", err);
        setError("Failed to load immunizations");
        toast.error("Failed to load immunizations");
      } finally {
        setLoading(false);
      }
    };

    fetchImmunizations();
  }, [patientId, consultationId, refreshTrigger]);

  const handleExportFhir = async (immunizationId: string) => {
    try {
      const response = await Fetch.get(
        `/api/immunization/${immunizationId}/fhir`,
      );

      if (response.error) {
        throw new Error(response.error);
      }

      // Create a downloadable JSON file
      const blob = new Blob([JSON.stringify(response, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `immunization-${immunizationId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success("FHIR resource exported successfully");
    } catch (err) {
      console.error("Error exporting FHIR resource:", err);
      toast.error("Failed to export FHIR resource");
    }
  };

  const handleShareWithAbdm = async (immunizationId: string) => {
    try {
      const response = await Fetch.post("/api/abdm/health-record/package", {
        patientId,
        recordType: "immunization",
        recordId: immunizationId,
      });

      if (response.error) {
        throw new Error(response.error);
      }

      toast.success("Immunization packaged for ABDM sharing");
    } catch (err) {
      console.error("Error packaging immunization for ABDM:", err);
      toast.error("Failed to package immunization for ABDM");
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "completed":
        return "default";
      case "entered-in-error":
        return "destructive";
      case "not-done":
        return "secondary";
      default:
        return "outline";
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  if (immunizations.length === 0) {
    return (
      <div className="p-4 text-center">
        <p className="text-muted-foreground">No immunization records found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Uploaded Documents Section - Only show for consultation-specific lists */}
      {consultationId && (
        <div className="mb-4">
          <h4 className="font-medium text-blue-700 mb-3 flex items-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
            Uploaded Immunization Documents
          </h4>
          <div className="bg-white/60 p-4 rounded-lg border border-blue-100">
            <UploadedFilesList
              consultationId={consultationId}
              bundleType="ImmunizationRecord"
              title=""
              description=""
              className="border-0 shadow-none bg-transparent p-0"
              showOnlyGenerated={true}
            />
          </div>
        </div>
      )}

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Vaccine</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Dose</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {immunizations.map((immunization) => (
            <TableRow key={immunization.id}>
              <TableCell className="font-medium">
                {immunization.vaccineDisplay}
              </TableCell>
              <TableCell>
                {formatDate(new Date(immunization.occurrenceDateTime))}
              </TableCell>
              <TableCell>
                <Badge variant={getStatusBadgeVariant(immunization.status)}>
                  {immunization.status}
                </Badge>
              </TableCell>
              <TableCell>
                {immunization.protocolApplied &&
                immunization.protocolApplied.length > 0 &&
                immunization.protocolApplied[0].doseNumber
                  ? `Dose ${immunization.protocolApplied[0].doseNumber}${
                      immunization.protocolApplied[0].seriesDoses
                        ? ` of ${immunization.protocolApplied[0].seriesDoses}`
                        : ""
                    }`
                  : immunization.doseQuantity || "N/A"}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedImmunization(immunization)}
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle className="flex items-center">
                          <Syringe className="h-5 w-5 mr-2 text-primary" />
                          Immunization Details
                        </DialogTitle>
                        <DialogDescription>
                          {immunization.vaccineDisplay} -{" "}
                          {formatDate(
                            new Date(immunization.occurrenceDateTime),
                          )}
                        </DialogDescription>
                      </DialogHeader>
                      {selectedImmunization && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h4 className="text-sm font-medium">Vaccine</h4>
                              <p>{selectedImmunization.vaccineDisplay}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Code</h4>
                              <p>{selectedImmunization.vaccineCode}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Status</h4>
                              <p className="capitalize">
                                {selectedImmunization.status}
                              </p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">
                                Date Given
                              </h4>
                              <p>
                                {formatDate(
                                  new Date(
                                    selectedImmunization.occurrenceDateTime,
                                  ),
                                )}
                              </p>
                            </div>
                            {selectedImmunization.manufacturer && (
                              <div>
                                <h4 className="text-sm font-medium">
                                  Manufacturer
                                </h4>
                                <p>{selectedImmunization.manufacturer}</p>
                              </div>
                            )}
                            {selectedImmunization.lotNumber && (
                              <div>
                                <h4 className="text-sm font-medium">
                                  Lot Number
                                </h4>
                                <p>{selectedImmunization.lotNumber}</p>
                              </div>
                            )}
                            {selectedImmunization.expirationDate && (
                              <div>
                                <h4 className="text-sm font-medium">
                                  Expiration Date
                                </h4>
                                <p>
                                  {formatDate(
                                    new Date(
                                      selectedImmunization.expirationDate,
                                    ),
                                  )}
                                </p>
                              </div>
                            )}
                            {selectedImmunization.site && (
                              <div>
                                <h4 className="text-sm font-medium">Site</h4>
                                <p className="capitalize">
                                  {selectedImmunization.site.replace(/-/g, " ")}
                                </p>
                              </div>
                            )}
                            {selectedImmunization.route && (
                              <div>
                                <h4 className="text-sm font-medium">Route</h4>
                                <p className="capitalize">
                                  {selectedImmunization.route}
                                </p>
                              </div>
                            )}
                            {selectedImmunization.doseQuantity && (
                              <div>
                                <h4 className="text-sm font-medium">
                                  Dose Quantity
                                </h4>
                                <p>{selectedImmunization.doseQuantity}</p>
                              </div>
                            )}
                            {selectedImmunization.performer && (
                              <div>
                                <h4 className="text-sm font-medium">
                                  Performer
                                </h4>
                                <p>{selectedImmunization.performer}</p>
                              </div>
                            )}
                            {selectedImmunization.location && (
                              <div>
                                <h4 className="text-sm font-medium">
                                  Location
                                </h4>
                                <p>{selectedImmunization.location}</p>
                              </div>
                            )}
                            {selectedImmunization.reasonDisplay && (
                              <div>
                                <h4 className="text-sm font-medium">Reason</h4>
                                <p>{selectedImmunization.reasonDisplay}</p>
                              </div>
                            )}
                            <div>
                              <h4 className="text-sm font-medium">
                                Primary Source
                              </h4>
                              <p>
                                {selectedImmunization.primarySource
                                  ? "Yes"
                                  : "No"}
                              </p>
                            </div>
                            {selectedImmunization.isSubpotent && (
                              <div>
                                <h4 className="text-sm font-medium">
                                  Subpotent Dose
                                </h4>
                                <p>
                                  {selectedImmunization.isSubpotent
                                    ? "Yes"
                                    : "No"}
                                </p>
                              </div>
                            )}
                          </div>

                          {selectedImmunization.note && (
                            <div>
                              <h4 className="text-sm font-medium">Notes</h4>
                              <p className="whitespace-pre-wrap">
                                {selectedImmunization.note}
                              </p>
                            </div>
                          )}

                          {selectedImmunization.protocolApplied &&
                            selectedImmunization.protocolApplied.length > 0 && (
                              <div>
                                <h4 className="text-sm font-medium mb-2">
                                  Protocol Applied
                                </h4>
                                <div className="space-y-4">
                                  {selectedImmunization.protocolApplied.map(
                                    (protocol, index) => (
                                      <div
                                        key={index}
                                        className="border rounded-md p-3 bg-muted/30"
                                      >
                                        <div className="grid grid-cols-2 gap-2">
                                          {protocol.series && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Series
                                              </h5>
                                              <p>{protocol.series}</p>
                                            </div>
                                          )}
                                          {protocol.targetDisease && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Target Disease
                                              </h5>
                                              <p>{protocol.targetDisease}</p>
                                            </div>
                                          )}
                                          {protocol.doseNumber && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Dose Number
                                              </h5>
                                              <p>{protocol.doseNumber}</p>
                                            </div>
                                          )}
                                          {protocol.seriesDoses && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Series Doses
                                              </h5>
                                              <p>{protocol.seriesDoses}</p>
                                            </div>
                                          )}
                                          {protocol.authority && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Authority
                                              </h5>
                                              <p>{protocol.authority}</p>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    ),
                                  )}
                                </div>
                              </div>
                            )}

                          {selectedImmunization.reaction &&
                            selectedImmunization.reaction.length > 0 && (
                              <div>
                                <h4 className="text-sm font-medium mb-2">
                                  Reactions
                                </h4>
                                <div className="space-y-4">
                                  {selectedImmunization.reaction.map(
                                    (reaction, index) => (
                                      <div
                                        key={index}
                                        className="border rounded-md p-3 bg-muted/30"
                                      >
                                        <div className="grid grid-cols-2 gap-2">
                                          {reaction.date && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Date
                                              </h5>
                                              <p>
                                                {formatDate(
                                                  new Date(reaction.date),
                                                )}
                                              </p>
                                            </div>
                                          )}
                                          {reaction.reported !== undefined && (
                                            <div>
                                              <h5 className="text-xs font-medium">
                                                Reported
                                              </h5>
                                              <p>
                                                {reaction.reported
                                                  ? "Yes"
                                                  : "No"}
                                              </p>
                                            </div>
                                          )}
                                        </div>
                                        {reaction.detail && (
                                          <div className="mt-2">
                                            <h5 className="text-xs font-medium">
                                              Detail
                                            </h5>
                                            <p className="whitespace-pre-wrap">
                                              {reaction.detail}
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    ),
                                  )}
                                </div>
                              </div>
                            )}

                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              onClick={() =>
                                handleExportFhir(selectedImmunization.id)
                              }
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Export FHIR
                            </Button>
                            <Button
                              onClick={() =>
                                handleShareWithAbdm(selectedImmunization.id)
                              }
                            >
                              <Share className="h-4 w-4 mr-1" />
                              Share with ABDM
                            </Button>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExportFhir(immunization.id)}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    FHIR
                  </Button>

                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleShareWithAbdm(immunization.id)}
                  >
                    <Share className="h-4 w-4 mr-1" />
                    ABDM
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
