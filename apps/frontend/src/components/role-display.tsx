"use client";

import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ChevronDown,
  User,
  Shield,
  UserCheck,
  Stethoscope,
  Users,
  Building2,
} from "lucide-react";
import {
  UserRole,
  getRoleDisplayName,
  getRoleColor,
} from "@/lib/organization-roles";

interface UserInfo {
  id: string;
  name: string;
  email: string;
  role: string; // Legacy role field
  organizationId: string;
  organizationName?: string;
}

interface OrganizationRoles {
  roles: UserRole[];
  organizationName: string;
}

export function RoleDisplay() {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [organizationRoles, setOrganizationRoles] =
    useState<OrganizationRoles | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const getUserInfo = async () => {
      try {
        // Get user info from cookie
        const userInfoCookie = document.cookie
          .split("; ")
          .find((row) => row.startsWith("user-info="));

        if (userInfoCookie) {
          const cookieValue = userInfoCookie.split("=")[1];
          const decodedValue = decodeURIComponent(cookieValue);
          const parsedUserInfo = JSON.parse(decodedValue);
          setUserInfo(parsedUserInfo);

          // Fetch organization-level roles
          if (parsedUserInfo.organizationId) {
            try {
              const response = await fetch(
                `/api/user/organization-roles?organizationId=${parsedUserInfo.organizationId}`,
              );
              if (response.ok) {
                const rolesData = await response.json();
                setOrganizationRoles(rolesData);
              }
            } catch (error) {
              console.error("Error fetching organization roles:", error);
              // Fallback to legacy role
              setOrganizationRoles({
                roles: [parsedUserInfo.role as UserRole],
                organizationName:
                  parsedUserInfo.organizationName || "Organization",
              });
            }
          }
        }
      } catch (error) {
        console.error("Error parsing user info:", error);
      } finally {
        setIsLoading(false);
      }
    };

    getUserInfo();
  }, []);

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case "hospitalAdmin":
        return <Shield className="h-4 w-4 text-blue-600" />;
      case "branchAdmin":
        return <UserCheck className="h-4 w-4 text-green-600" />;
      case "doctor":
        return <Stethoscope className="h-4 w-4 text-purple-600" />;
      case "nurse":
        return <Users className="h-4 w-4 text-pink-600" />;
      case "staff":
      case "receptionist":
      case "technician":
        return <Users className="h-4 w-4 text-orange-600" />;
      case "pharmacist":
        return <Building2 className="h-4 w-4 text-indigo-600" />;
      default:
        return <User className="h-4 w-4 text-gray-600" />;
    }
  };

  const getPrimaryRole = (roles: UserRole[]): UserRole => {
    // Return the highest priority role
    const rolePriority: Record<UserRole, number> = {
      superAdmin: 1000,
      hospitalAdmin: 100,
      branchAdmin: 80,
      doctor: 60,
      nurse: 40,
      pharmacist: 35,
      staff: 30,
      receptionist: 20,
      technician: 10,
    };

    return roles.reduce((highest, current) => {
      return (rolePriority[current] || 0) > (rolePriority[highest] || 0)
        ? current
        : highest;
    }, roles[0]);
  };

  if (isLoading || !userInfo) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="h-9 gap-1 opacity-70"
        disabled
      >
        <User className="h-4 w-4" />
        <span className="max-w-[120px] truncate">Loading...</span>
      </Button>
    );
  }

  // Use organization roles if available, fallback to legacy role
  const displayRoles = organizationRoles?.roles || [userInfo.role as UserRole];
  const primaryRole = getPrimaryRole(displayRoles);
  const hasMultipleRoles = displayRoles.length > 1;

  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={`h-9 gap-1 hover-card btn-hover-effect ${getRoleColor(primaryRole)}`}
          >
            {getRoleIcon(primaryRole)}
            <span className="max-w-[120px] truncate font-medium">
              {getRoleDisplayName(primaryRole)}
              {hasMultipleRoles && (
                <span className="ml-1 text-xs">+{displayRoles.length - 1}</span>
              )}
            </span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[280px]">
          <DropdownMenuLabel>User Information</DropdownMenuLabel>
          <DropdownMenuSeparator />

          <div className="px-2 py-2 space-y-3">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <div className="flex flex-col">
                <span className="text-sm font-medium">{userInfo.name}</span>
                <span className="text-xs text-muted-foreground">
                  {userInfo.email}
                </span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                {getRoleIcon(primaryRole)}
                <span className="text-sm font-medium">
                  {hasMultipleRoles ? "Roles" : "Role"}
                </span>
              </div>
              <div className="flex flex-wrap gap-1">
                {displayRoles.map((role, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className={`text-xs ${getRoleColor(role)}`}
                  >
                    {getRoleDisplayName(role)}
                  </Badge>
                ))}
              </div>
            </div>

            {(organizationRoles?.organizationName ||
              userInfo.organizationName) && (
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-muted-foreground" />
                <div className="flex flex-col">
                  <span className="text-sm font-medium">Organization</span>
                  <span className="text-xs text-muted-foreground">
                    {organizationRoles?.organizationName ||
                      userInfo.organizationName}
                  </span>
                </div>
              </div>
            )}
          </div>

          <DropdownMenuSeparator />

          <DropdownMenuItem disabled className="text-xs text-muted-foreground">
            {hasMultipleRoles
              ? `${displayRoles.length} roles in current organization`
              : "Role-based access is active"}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
