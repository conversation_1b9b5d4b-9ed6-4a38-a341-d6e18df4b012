"use client";

import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface ConsentStatusBadgeProps {
  status: string;
  className?: string;
}

export function ConsentStatusBadge({
  status,
  className,
}: ConsentStatusBadgeProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "REQUESTED":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
      case "GRANTED":
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case "DENIED":
        return "bg-red-100 text-red-800 hover:bg-red-100";
      case "EXPIRED":
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
      case "REVOKED":
        return "bg-purple-100 text-purple-800 hover:bg-purple-100";
      default:
        return "bg-blue-100 text-blue-800 hover:bg-blue-100";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "REQUESTED":
        return "Pending";
      case "GRANTED":
        return "Approved";
      case "DENIED":
        return "Rejected";
      case "EXPIRED":
        return "Expired";
      case "REVOKED":
        return "Revoked";
      default:
        return status;
    }
  };

  return (
    <Badge
      variant="outline"
      className={cn(getStatusColor(status), "font-medium", className)}
    >
      {getStatusText(status)}
    </Badge>
  );
}
