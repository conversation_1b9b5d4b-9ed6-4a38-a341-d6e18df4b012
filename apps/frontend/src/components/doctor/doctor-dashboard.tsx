"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
// Separator removed as it's not used
import {
  Loader2,
  Calendar,
  Clock,
  User,
  FileText,
  Activity,
  Pill,
  CheckCircle,
  PauseCircle,
  RefreshCw,
  Download,
  Stethoscope,
  ArrowRight,
  Building,
  History,
  Lock,
  Unlock,
} from "lucide-react";
import { toast } from "sonner";
import { useBranch } from "@/contexts/branch-context";
import { useSocket } from "@/components/providers/socket-provider";
import { joinBranch, joinOrganization } from "@/lib/socket-client";
import { useOrganization } from "@/contexts/organization-context";
import {
  Select,
  SelectContent,
  SelectI<PERSON>,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Appointment {
  id: string;
  appointmentDate: string;
  startTime: string;
  endTime: string;
  status: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    mrn: string;
    dateOfBirth?: string;
    gender?: string;
    phone?: string;
    email?: string;
  };
  doctor: {
    id: string;
    user: {
      name: string;
    };
    specialization: string;
  };
  branch: {
    id: string;
    name: string;
  };
  queueStatus?: {
    id: string;
    status: string;
    queueNumber: number;
  };
}

interface Consultation {
  id: string;
  consultationDate: string;
  status: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    mrn: string;
    abhaProfile?: {
      abhaNumber?: string;
      abhaAddress?: string;
      xToken?: string;
      abhaStatus?: string;
    };
  };
  doctor: {
    id: string;
    user: {
      name: string;
    };
  };
  vitals: any[];
  clinicalNotes: any[];
  prescriptions: any[];
  careContexts?: any[];
  consentStatus?: string;
}

export function DoctorDashboard() {
  const router = useRouter();
  const { currentBranch } = useBranch();
  const { currentOrganization } = useOrganization();
  const { socket, isConnected } = useSocket();
  const [todayAppointments, setTodayAppointments] = useState<Appointment[]>([]);
  const [waitingAppointments, setWaitingAppointments] = useState<Appointment[]>(
    [],
  );
  const [inConsultationAppointment, setInConsultationAppointment] =
    useState<Appointment | null>(null);
  const [activePatient, setActivePatient] = useState<any>(null);
  const [activeConsultation, setActiveConsultation] =
    useState<Consultation | null>(null);
  const [abhaRecords, setAbhaRecords] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [consultationLoading, setConsultationLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("vitals");
  const [availableBranches, setAvailableBranches] = useState<any[]>([]);
  const [selectedBranchId, setSelectedBranchId] = useState<string>("");
  const [abhaLoading, setAbhaLoading] = useState(false);
  const [consentStatus, setConsentStatus] = useState<string | null>(null);
  const [careContexts, setCareContexts] = useState<any[]>([]);
  const [ehrTimeline, setEhrTimeline] = useState<any[]>([]);
  const [showEhrTimeline, setShowEhrTimeline] = useState(false);

  // Fetch available branches for the doctor
  useEffect(() => {
    const fetchDoctorBranches = async () => {
      try {
        const response = await fetch("/api/doctors/branches");
        const data = await response.json();

        if (response.ok) {
          setAvailableBranches(data.branches || []);

          // Set selected branch to current branch or first available branch
          if (currentBranch?.id) {
            setSelectedBranchId(currentBranch.id);
          } else if (data.branches && data.branches.length > 0) {
            setSelectedBranchId(data.branches[0].id);
          }
        } else {
          console.error("Error fetching doctor branches:", data.error);
          toast.error(data.error || "Failed to fetch assigned branches");
        }
      } catch (error) {
        console.error("Error fetching doctor branches:", error);
        toast.error("Failed to fetch assigned branches");
      }
    };

    fetchDoctorBranches();
  }, [currentBranch?.id]);

  // Connect to WebSocket when component mounts
  useEffect(() => {
    if (socket && isConnected && currentOrganization?.id) {
      // Join organization room
      joinOrganization(currentOrganization.id);

      // Join branch room if available
      if (currentBranch?.id) {
        joinBranch(currentBranch.id);
      }

      // Listen for queue updates
      socket.on("queue-status-updated", (data) => {
        console.log("Queue status updated:", data);
        fetchTodayAppointments();
      });

      return () => {
        socket.off("queue-status-updated");
      };
    }
  }, [socket, isConnected, currentOrganization?.id, currentBranch?.id]);

  // Fetch today's appointments
  useEffect(() => {
    fetchTodayAppointments();

    // Set up polling as fallback for real-time updates
    const intervalId = setInterval(fetchTodayAppointments, 30000); // Poll every 30 seconds

    return () => clearInterval(intervalId);
  }, [currentBranch?.id, selectedBranchId]);

  // Function to fetch today's appointments
  const fetchTodayAppointments = async () => {
    setLoading(true);
    try {
      const today = format(new Date(), "yyyy-MM-dd");
      let url = `/api/appointments?date=${today}`;

      // Use selected branch ID if available, otherwise use current branch
      const branchId = selectedBranchId || currentBranch?.id;
      if (branchId) {
        url += `&branchId=${branchId}`;
      }

      const response = await fetch(url);
      const data = await response.json();

      if (response.ok) {
        const appointments = data.appointments || [];
        setTodayAppointments(appointments);

        // Filter waiting appointments
        const waiting = appointments.filter(
          (app: Appointment) =>
            app.queueStatus?.status === "waiting" &&
            app.status !== "cancelled" &&
            app.status !== "completed",
        );
        setWaitingAppointments(waiting);

        // Find in-consultation appointment
        const inConsultation = appointments.find(
          (app: Appointment) => app.queueStatus?.status === "in-consultation",
        );
        setInConsultationAppointment(inConsultation || null);

        // If there's an active consultation, fetch its details
        if (inConsultation) {
          setActivePatient(inConsultation.patient);
          fetchActiveConsultation(inConsultation.patient.id);
        }
      } else {
        toast.error(data.error || "Failed to fetch appointments");
      }
    } catch (error) {
      console.error("Error fetching appointments:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  // Fetch active consultation details
  const fetchActiveConsultation = async (patientId: string) => {
    setConsultationLoading(true);
    try {
      const response = await fetch(
        `/api/consultations?patientId=${patientId}&status=in-progress`,
      );

      if (!response.ok) {
        throw new Error("Failed to fetch consultation");
      }

      const data = await response.json();
      const consultations = data.consultations || [];

      // Get the most recent consultation
      if (consultations.length > 0) {
        const consultation = consultations[0];
        setActiveConsultation(consultation);

        // Fetch care contexts if available
        fetchCareContexts(patientId);

        // Also fetch ABHA records if available
        if (consultation.patient?.abhaProfile?.abhaNumber) {
          fetchAbhaRecords(
            consultation.patient.abhaProfile.abhaNumber,
            patientId,
          );
        }
      } else {
        setActiveConsultation(null);
        setCareContexts([]);
        setAbhaRecords([]);
      }
    } catch (error) {
      console.error("Error fetching consultation:", error);
      // toast.error("Failed to load patient consultation");
    } finally {
      setConsultationLoading(false);
    }
  };

  // Fetch care contexts for a patient
  const fetchCareContexts = async (patientId: string) => {
    try {
      const response = await fetch(`/api/patients/${patientId}/care-contexts`);

      if (!response.ok) {
        throw new Error("Failed to fetch care contexts");
      }

      const data = await response.json();
      setCareContexts(data.careContexts || []);
      setConsentStatus(data.consentStatus || null);
    } catch (error) {
      console.error("Error fetching care contexts:", error);
      setCareContexts([]);
      setConsentStatus(null);
    }
  };

  // Fetch ABHA records
  const fetchAbhaRecords = async (_abhaNumber: string, patientId: string) => {
    setAbhaLoading(true);
    try {
      // Check if patient has an active ABHA session
      const sessionResponse = await fetch(
        `/api/patients/${patientId}/abha/session-status`,
      );
      const sessionData = await sessionResponse.json();

      if (!sessionResponse.ok || !sessionData.active) {
        // No active session, need to login first
        setAbhaRecords([]);
        return;
      }

      // Fetch ABHA records
      const response = await fetch(`/api/patients/${patientId}/abha/records`);

      if (!response.ok) {
        throw new Error("Failed to fetch ABHA records");
      }

      const data = await response.json();
      setAbhaRecords(data.records || []);
    } catch (error) {
      console.error("Error fetching ABHA records:", error);
      setAbhaRecords([]);
    } finally {
      setAbhaLoading(false);
    }
  };

  // Handle starting a consultation with a patient
  const handleStartConsultation = async (appointment: Appointment) => {
    try {
      // Update queue status to in-consultation
      const response = await fetch(`/api/queue/${appointment.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "in-consultation",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update queue status");
      }

      // Create a new consultation if one doesn't exist
      const consultationResponse = await fetch("/api/consultations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          patientId: appointment.patient.id,
          doctorId: appointment.doctor.id,
          branchId: appointment.branch.id,
          consultationDate: new Date().toISOString(),
          status: "in-progress",
        }),
      });

      if (!consultationResponse.ok) {
        throw new Error("Failed to create consultation");
      }

      toast.success("Consultation started");

      // Update state
      setInConsultationAppointment(appointment);
      setActivePatient(appointment.patient);

      // Remove from waiting list
      setWaitingAppointments((prev) =>
        prev.filter((app) => app.id !== appointment.id),
      );

      // Fetch the consultation details
      fetchActiveConsultation(appointment.patient.id);
    } catch (error) {
      console.error("Error starting consultation:", error);
      toast.error("Failed to start consultation");
    }
  };

  // Handle completing a consultation
  const handleCompleteConsultation = async () => {
    if (!activeConsultation || !inConsultationAppointment) return;

    try {
      // Update consultation status
      const consultationResponse = await fetch(
        `/api/consultations/${activeConsultation.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "completed",
          }),
        },
      );

      if (!consultationResponse.ok) {
        throw new Error("Failed to complete consultation");
      }

      // Update queue status
      const queueResponse = await fetch(
        `/api/queue/${inConsultationAppointment.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "completed",
          }),
        },
      );

      if (!queueResponse.ok) {
        throw new Error("Failed to update queue status");
      }

      toast.success("Consultation completed");

      // Update state
      setInConsultationAppointment(null);
      setActivePatient(null);
      setActiveConsultation(null);

      // Refresh the page to show updated status
      router.refresh();

      // Refresh appointments
      const today = format(new Date(), "yyyy-MM-dd");
      const response = await fetch(
        `/api/appointments?date=${today}&branchId=${currentBranch?.id}`,
      );
      const data = await response.json();

      if (response.ok) {
        setTodayAppointments(data.appointments || []);
        setWaitingAppointments(
          data.appointments.filter(
            (app: Appointment) =>
              app.queueStatus?.status === "waiting" &&
              app.status !== "cancelled" &&
              app.status !== "completed",
          ),
        );
      }
    } catch (error) {
      console.error("Error completing consultation:", error);
      toast.error("Failed to complete consultation");
    }
  };

  // Handle pausing a consultation
  const handlePauseConsultation = async () => {
    if (!inConsultationAppointment) return;

    try {
      // Update queue status
      const response = await fetch(
        `/api/queue/${inConsultationAppointment.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "paused",
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to pause consultation");
      }

      toast.success("Consultation paused");

      // Update state
      const updatedAppointment = { ...inConsultationAppointment };
      if (updatedAppointment.queueStatus) {
        updatedAppointment.queueStatus.status = "paused";
      }
      setInConsultationAppointment(updatedAppointment);
    } catch (error) {
      console.error("Error pausing consultation:", error);
      toast.error("Failed to pause consultation");
    }
  };

  // Handle resuming a consultation
  const handleResumeConsultation = async () => {
    if (!inConsultationAppointment) return;

    try {
      // Update queue status
      const response = await fetch(
        `/api/queue/${inConsultationAppointment.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "in-consultation",
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to resume consultation");
      }

      toast.success("Consultation resumed");

      // Update state
      const updatedAppointment = { ...inConsultationAppointment };
      if (updatedAppointment.queueStatus) {
        updatedAppointment.queueStatus.status = "in-consultation";
      }
      setInConsultationAppointment(updatedAppointment);
    } catch (error) {
      console.error("Error resuming consultation:", error);
      toast.error("Failed to resume consultation");
    }
  };

  // Handle fetching ABHA records
  const handleFetchRecords = async () => {
    if (!activePatient) return;

    try {
      setAbhaLoading(true);

      // First, check if patient has ABHA profile
      if (!activePatient.abhaProfile?.abhaNumber) {
        toast.error("Patient does not have an ABHA profile");
        return;
      }

      // Request consent if needed
      const consentResponse = await fetch(
        `/api/patients/${activePatient.id}/abha/request-consent`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            purpose: "Care Management",
            hiTypes: [
              "DiagnosticReport",
              "Prescription",
              "DischargeSummary",
              "OPConsultation",
            ],
            dateRange: {
              from: new Date(
                new Date().setFullYear(new Date().getFullYear() - 1),
              ).toISOString(),
              to: new Date().toISOString(),
            },
          }),
        },
      );

      if (!consentResponse.ok) {
        const errorData = await consentResponse.json();
        toast.error(errorData.error || "Failed to request consent");
        return;
      }

      const consentData = await consentResponse.json();
      toast.success("Consent requested successfully");

      // Fetch records after consent is granted
      if (consentData.status === "GRANTED") {
        await fetchAbhaRecords(
          activePatient.abhaProfile.abhaNumber,
          activePatient.id,
        );
        toast.success("ABHA records fetched successfully");
      } else {
        toast.info("Waiting for patient to grant consent");
      }
    } catch (error) {
      console.error("Error fetching ABHA records:", error);
      toast.error("Failed to fetch ABHA records");
    } finally {
      setAbhaLoading(false);
    }
  };

  // Handle viewing EHR timeline
  const handleViewEhrTimeline = async () => {
    if (!activePatient) return;

    try {
      // Toggle timeline view
      setShowEhrTimeline(!showEhrTimeline);

      // If we're showing the timeline and it's empty, fetch it
      if (!showEhrTimeline && ehrTimeline.length === 0) {
        const response = await fetch(
          `/api/patients/${activePatient.id}/ehr-timeline`,
        );

        if (!response.ok) {
          throw new Error("Failed to fetch EHR timeline");
        }

        const data = await response.json();
        setEhrTimeline(data.timeline || []);
      }
    } catch (error) {
      console.error("Error fetching EHR timeline:", error);
      toast.error("Failed to fetch EHR timeline");
      setShowEhrTimeline(false);
    }
  };

  // Navigate to create new clinical record
  const handleCreateRecord = (recordType: string) => {
    if (!activeConsultation) return;

    router.push(`/consultations/${activeConsultation.id}?tab=${recordType}`);
  };

  // Handle branch change
  const handleBranchChange = (branchId: string) => {
    setSelectedBranchId(branchId);
    // Reset state
    setActivePatient(null);
    setActiveConsultation(null);
    setAbhaRecords([]);
    setCareContexts([]);
    setConsentStatus(null);
    // Fetch appointments for the new branch
    fetchTodayAppointments();
  };

  // Get queue status badge
  const getQueueStatusBadge = (status: string) => {
    switch (status) {
      case "waiting":
        return <Badge>Waiting</Badge>;
      case "in-consultation":
        return <Badge variant="secondary">In Consultation</Badge>;
      case "paused":
        return <Badge variant="warning">Paused</Badge>;
      case "completed":
        return <Badge variant="success">Completed</Badge>;
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Branch Selector */}
      {availableBranches.length > 1 && (
        <div className="flex items-center space-x-2 bg-muted/30 p-3 rounded-md">
          <Building className="h-5 w-5 text-muted-foreground" />
          <span className="text-sm font-medium">Branch:</span>
          <Select value={selectedBranchId} onValueChange={handleBranchChange}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select branch" />
            </SelectTrigger>
            <SelectContent>
              {availableBranches.map((branch) => (
                <SelectItem key={branch.id} value={branch.id}>
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Action Bar */}
      {inConsultationAppointment && activeConsultation && (
        <div className="flex items-center justify-between bg-primary/5 p-4 rounded-md border border-primary/20">
          <div className="flex items-center space-x-2">
            <User className="h-5 w-5 text-primary" />
            <span className="font-medium">
              {activePatient?.firstName} {activePatient?.lastName}
            </span>
            {inConsultationAppointment.queueStatus && (
              <Badge variant="outline" className="ml-2">
                #{inConsultationAppointment.queueStatus.queueNumber}
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {inConsultationAppointment.queueStatus?.status ===
            "in-consultation" ? (
              <Button
                size="sm"
                variant="outline"
                onClick={handlePauseConsultation}
              >
                <PauseCircle className="h-4 w-4 mr-1" />
                Pause
              </Button>
            ) : inConsultationAppointment.queueStatus?.status === "paused" ? (
              <Button
                size="sm"
                variant="outline"
                onClick={handleResumeConsultation}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Resume
              </Button>
            ) : null}

            <Button
              size="sm"
              variant="default"
              onClick={handleCompleteConsultation}
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              Complete
            </Button>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleFetchRecords}
                    disabled={
                      abhaLoading || !activePatient?.abhaProfile?.abhaNumber
                    }
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Fetch Records
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {!activePatient?.abhaProfile?.abhaNumber
                    ? "Patient does not have an ABHA profile"
                    : "Fetch patient's ABHA-linked records"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Today's Appointments */}
        <Card className="lg:col-span-1">
          <CardHeader className="bg-muted/30">
            <CardTitle className="flex items-center text-lg">
              <Calendar className="mr-2 h-5 w-5 text-primary" />
              Today's Queue
            </CardTitle>
            <CardDescription>
              {format(new Date(), "EEEE, MMMM d, yyyy")}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y">
              {/* Current Patient */}
              {inConsultationAppointment && (
                <div className="p-4 bg-primary/5 border-l-4 border-primary">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold text-primary">
                      Current Patient
                    </h3>
                    {inConsultationAppointment.queueStatus && (
                      <div className="flex items-center">
                        {getQueueStatusBadge(
                          inConsultationAppointment.queueStatus.status,
                        )}
                        <span className="ml-2 text-sm font-medium">
                          #{inConsultationAppointment.queueStatus.queueNumber}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center mb-1">
                    <User className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="font-medium">
                      {inConsultationAppointment.patient.firstName}{" "}
                      {inConsultationAppointment.patient.lastName}
                    </span>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground mb-1">
                    <span className="mr-2">
                      MRN: {inConsultationAppointment.patient.mrn}
                    </span>
                    {inConsultationAppointment.patient.gender && (
                      <span className="mr-2 capitalize">
                        {inConsultationAppointment.patient.gender}
                      </span>
                    )}
                    {inConsultationAppointment.patient.dateOfBirth && (
                      <span>
                        {format(
                          new Date(
                            inConsultationAppointment.patient.dateOfBirth,
                          ),
                          "yyyy",
                        )}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>
                      {inConsultationAppointment.startTime} -{" "}
                      {inConsultationAppointment.endTime}
                    </span>
                  </div>

                  <div className="flex space-x-2 mt-3">
                    {inConsultationAppointment.queueStatus?.status ===
                    "in-consultation" ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handlePauseConsultation}
                      >
                        <PauseCircle className="h-4 w-4 mr-1" />
                        Pause
                      </Button>
                    ) : inConsultationAppointment.queueStatus?.status ===
                      "paused" ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleResumeConsultation}
                      >
                        <RefreshCw className="h-4 w-4 mr-1" />
                        Resume
                      </Button>
                    ) : null}

                    <Button
                      size="sm"
                      variant="default"
                      onClick={handleCompleteConsultation}
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Complete
                    </Button>
                  </div>
                </div>
              )}

              {/* Waiting Patients */}
              <div className="p-4">
                <h3 className="font-semibold mb-2">
                  Waiting ({waitingAppointments.length})
                </h3>
                {waitingAppointments.length === 0 ? (
                  <p className="text-sm text-muted-foreground">
                    No patients waiting
                  </p>
                ) : (
                  <div className="space-y-3">
                    {waitingAppointments.map((appointment) => (
                      <div
                        key={appointment.id}
                        className="p-3 border rounded-md"
                      >
                        <div className="flex justify-between items-start mb-1">
                          <span className="font-medium">
                            {appointment.patient.firstName}{" "}
                            {appointment.patient.lastName}
                          </span>
                          {appointment.queueStatus && (
                            <Badge variant="outline">
                              #{appointment.queueStatus.queueNumber}
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground mb-1">
                          MRN: {appointment.patient.mrn}
                        </div>
                        <div className="text-sm text-muted-foreground mb-2">
                          <Clock className="h-3 w-3 inline mr-1" />
                          {appointment.startTime} - {appointment.endTime}
                        </div>
                        <Button
                          size="sm"
                          className="w-full"
                          onClick={() => handleStartConsultation(appointment)}
                        >
                          Start Consultation
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Other Appointments */}
              <div className="p-4">
                <h3 className="font-semibold mb-2">
                  All Appointments ({todayAppointments.length})
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => router.push("/appointments")}
                >
                  View All Appointments
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Center Column - Active Patient Clinical Record */}
        <Card className="lg:col-span-1">
          <CardHeader className="bg-muted/30">
            <CardTitle className="flex items-center text-lg">
              <Stethoscope className="mr-2 h-5 w-5 text-primary" />
              Clinical Record
            </CardTitle>
            <CardDescription>
              {activePatient
                ? `${activePatient.firstName} ${activePatient.lastName}`
                : "No active patient"}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {!activePatient ? (
              <div className="p-6 text-center">
                <p className="text-muted-foreground mb-4">
                  Select a patient from the queue to view their clinical record
                </p>
              </div>
            ) : consultationLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            ) : !activeConsultation ? (
              <div className="p-6 text-center">
                <p className="text-muted-foreground mb-4">
                  No active consultation found for this patient
                </p>
                <Button
                  onClick={() =>
                    handleStartConsultation(inConsultationAppointment!)
                  }
                >
                  Create Consultation
                </Button>
              </div>
            ) : (
              <div>
                <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
                  <div className="border-b">
                    <TabsList className="w-full justify-start rounded-none h-12 bg-transparent border-b px-4">
                      <TabsTrigger
                        value="vitals"
                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                      >
                        <Activity className="h-4 w-4 mr-2" />
                        Vitals
                      </TabsTrigger>
                      <TabsTrigger
                        value="notes"
                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Notes
                      </TabsTrigger>
                      <TabsTrigger
                        value="prescriptions"
                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
                      >
                        <Pill className="h-4 w-4 mr-2" />
                        Prescriptions
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <TabsContent value="vitals" className="p-4">
                    {activeConsultation.vitals &&
                    activeConsultation.vitals.length > 0 ? (
                      <div className="space-y-4">
                        {activeConsultation.vitals.map((vital) => (
                          <div key={vital.id} className="border rounded-md p-3">
                            <div className="text-sm text-muted-foreground mb-2">
                              {format(new Date(vital.recordedAt), "PPp")}
                            </div>
                            <div className="grid grid-cols-2 gap-2">
                              <div className="p-2 bg-muted/20 rounded">
                                <div className="text-xs text-muted-foreground">
                                  BP
                                </div>
                                <div className="font-medium">
                                  {vital.bloodPressureSystolic}/
                                  {vital.bloodPressureDiastolic} mmHg
                                </div>
                              </div>
                              <div className="p-2 bg-muted/20 rounded">
                                <div className="text-xs text-muted-foreground">
                                  Pulse
                                </div>
                                <div className="font-medium">
                                  {vital.pulse} bpm
                                </div>
                              </div>
                              <div className="p-2 bg-muted/20 rounded">
                                <div className="text-xs text-muted-foreground">
                                  Temp
                                </div>
                                <div className="font-medium">
                                  {vital.temperature} °C
                                </div>
                              </div>
                              <div className="p-2 bg-muted/20 rounded">
                                <div className="text-xs text-muted-foreground">
                                  SpO2
                                </div>
                                <div className="font-medium">
                                  {vital.oxygenSaturation}%
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <p className="text-muted-foreground mb-4">
                          No vitals recorded
                        </p>
                        <Button onClick={() => handleCreateRecord("vitals")}>
                          <Activity className="mr-2 h-4 w-4" />
                          Record Vitals
                        </Button>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="notes" className="p-4">
                    {activeConsultation.clinicalNotes &&
                    activeConsultation.clinicalNotes.length > 0 ? (
                      <div className="space-y-4">
                        {activeConsultation.clinicalNotes.map((note) => (
                          <div key={note.id} className="border rounded-md p-3">
                            <div className="flex justify-between mb-2">
                              <Badge variant="outline">{note.noteType}</Badge>
                              <span className="text-sm text-muted-foreground">
                                {format(new Date(note.createdAt), "PPp")}
                              </span>
                            </div>
                            <p className="whitespace-pre-wrap">
                              {note.content}
                            </p>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <p className="text-muted-foreground mb-4">
                          No clinical notes added
                        </p>
                        <Button onClick={() => handleCreateRecord("notes")}>
                          <FileText className="mr-2 h-4 w-4" />
                          Add Clinical Note
                        </Button>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="prescriptions" className="p-4">
                    {activeConsultation.prescriptions &&
                    activeConsultation.prescriptions.length > 0 ? (
                      <div className="space-y-4">
                        {activeConsultation.prescriptions.map(
                          (prescription) => (
                            <div
                              key={prescription.id}
                              className="border rounded-md p-3"
                            >
                              <div className="flex justify-between mb-2">
                                <span className="font-medium">
                                  Prescription
                                </span>
                                <span className="text-sm text-muted-foreground">
                                  {format(
                                    new Date(prescription.prescriptionDate),
                                    "PPp",
                                  )}
                                </span>
                              </div>
                              <div className="space-y-2">
                                {prescription.items.map((item: any) => (
                                  <div
                                    key={item.id}
                                    className="p-2 bg-muted/20 rounded"
                                  >
                                    <div className="font-medium">
                                      {item.medicationName}
                                    </div>
                                    <div className="text-sm">
                                      {item.dosage}, {item.frequency}, for{" "}
                                      {item.duration}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ),
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <p className="text-muted-foreground mb-4">
                          No prescriptions added
                        </p>
                        <Button
                          onClick={() => handleCreateRecord("prescriptions")}
                        >
                          <Pill className="mr-2 h-4 w-4" />
                          Create Prescription
                        </Button>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>

                <div className="p-4 border-t">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() =>
                      router.push(`/consultations/${activeConsultation.id}`)
                    }
                  >
                    View Full Consultation
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Right Column - ABHA History */}
        <Card className="lg:col-span-1">
          <CardHeader className="bg-muted/30">
            <CardTitle className="flex items-center text-lg">
              <FileText className="mr-2 h-5 w-5 text-primary" />
              ABHA Records
            </CardTitle>
            <CardDescription>External health records</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {!activePatient ? (
              <div className="p-6 text-center">
                <p className="text-muted-foreground">
                  Select a patient to view ABHA records
                </p>
              </div>
            ) : abhaLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            ) : (
              <div className="p-6">
                <div className="mb-4">
                  <h3 className="font-medium mb-2">ABHA Status</h3>
                  {activePatient.abhaProfile?.abhaNumber ? (
                    <div className="space-y-2">
                      <Badge
                        variant="outline"
                        className="bg-green-100 text-green-800"
                      >
                        Linked
                      </Badge>
                      <div className="text-sm">
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground">
                            ABHA Number:
                          </span>
                          <span className="font-medium">
                            {activePatient.abhaProfile.abhaNumber}
                          </span>
                        </div>
                        {activePatient.abhaProfile.abhaAddress && (
                          <div className="flex items-center justify-between">
                            <span className="text-muted-foreground">
                              ABHA Address:
                            </span>
                            <span className="font-medium">
                              {activePatient.abhaProfile.abhaAddress}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <Badge
                      variant="outline"
                      className="bg-yellow-100 text-yellow-800"
                    >
                      Not Linked
                    </Badge>
                  )}
                </div>

                {/* Care Context Status */}
                {activePatient.abhaProfile?.abhaNumber && (
                  <div className="mb-4">
                    <h3 className="font-medium mb-2">Care Context</h3>
                    <div className="space-y-2">
                      {careContexts.length > 0 ? (
                        <div>
                          <Badge
                            variant="outline"
                            className="bg-blue-100 text-blue-800 mb-2"
                          >
                            {careContexts.length} Context
                            {careContexts.length !== 1 ? "s" : ""}
                          </Badge>
                          <div className="text-sm space-y-1">
                            {careContexts.slice(0, 3).map((context, index) => (
                              <div
                                key={index}
                                className="p-2 bg-muted/20 rounded"
                              >
                                <div className="font-medium">
                                  {context.display}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {context.consultationId}
                                </div>
                              </div>
                            ))}
                            {careContexts.length > 3 && (
                              <div className="text-xs text-muted-foreground text-center">
                                +{careContexts.length - 3} more
                              </div>
                            )}
                          </div>
                        </div>
                      ) : (
                        <Badge
                          variant="outline"
                          className="bg-gray-100 text-gray-800"
                        >
                          No Care Contexts
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Consent Status */}
                {activePatient.abhaProfile?.abhaNumber && (
                  <div className="mb-4">
                    <h3 className="font-medium mb-2">Consent Status</h3>
                    {consentStatus ? (
                      <Badge
                        variant="outline"
                        className={
                          consentStatus === "GRANTED"
                            ? "bg-green-100 text-green-800"
                            : consentStatus === "REQUESTED"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-gray-100 text-gray-800"
                        }
                      >
                        {consentStatus === "GRANTED" ? (
                          <>
                            <Unlock className="h-3 w-3 mr-1" />
                            Granted
                          </>
                        ) : consentStatus === "REQUESTED" ? (
                          <>
                            <Clock className="h-3 w-3 mr-1" />
                            Requested
                          </>
                        ) : (
                          <>
                            <Lock className="h-3 w-3 mr-1" />
                            {consentStatus}
                          </>
                        )}
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="bg-gray-100 text-gray-800"
                      >
                        <Lock className="h-3 w-3 mr-1" />
                        No Consent
                      </Badge>
                    )}
                  </div>
                )}

                {/* ABHA Records */}
                <div>
                  <h3 className="font-medium mb-2">Health Records</h3>
                  {abhaRecords.length > 0 ? (
                    <div className="space-y-2">
                      {abhaRecords.map((record, index) => (
                        <div key={index} className="p-2 border rounded-md">
                          <div className="flex justify-between">
                            <Badge variant="outline">{record.type}</Badge>
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(record.date), "MMM d, yyyy")}
                            </span>
                          </div>
                          <div className="mt-1 text-sm">
                            {record.description}
                          </div>
                          <div className="mt-1 text-xs text-muted-foreground">
                            {record.provider}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : activePatient.abhaProfile?.abhaNumber ? (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground mb-4">
                        No ABHA records available
                      </p>
                      <Button
                        variant="outline"
                        onClick={handleFetchRecords}
                        disabled={abhaLoading}
                        className="w-full"
                      >
                        {abhaLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Fetching...
                          </>
                        ) : (
                          <>
                            <Download className="mr-2 h-4 w-4" />
                            Fetch Records
                          </>
                        )}
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground">
                        Patient does not have an ABHA profile
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="border-t p-4">
            <Button
              variant="outline"
              className="w-full"
              disabled={!activePatient || ehrTimeline.length === 0}
              onClick={handleViewEhrTimeline}
            >
              <History className="mr-2 h-4 w-4" />
              {showEhrTimeline ? "Hide" : "View"} EHR Timeline
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* EHR Timeline */}
      {showEhrTimeline && activePatient && (
        <Card className="mt-6">
          <CardHeader className="bg-muted/30">
            <CardTitle className="flex items-center text-lg">
              <History className="mr-2 h-5 w-5 text-primary" />
              EHR Timeline
            </CardTitle>
            <CardDescription>
              Comprehensive health record timeline
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4">
            {ehrTimeline.length === 0 ? (
              <div className="text-center py-6">
                <p className="text-muted-foreground">
                  No timeline data available
                </p>
              </div>
            ) : (
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-muted-foreground/20"></div>

                <div className="space-y-6">
                  {ehrTimeline.map((event, index) => (
                    <div key={index} className="relative pl-10">
                      {/* Timeline dot */}
                      <div className="absolute left-0 top-1.5 w-6 h-6 rounded-full bg-primary/10 border border-primary/30 flex items-center justify-center">
                        {event.type === "consultation" ? (
                          <Stethoscope className="h-3 w-3 text-primary" />
                        ) : event.type === "prescription" ? (
                          <Pill className="h-3 w-3 text-primary" />
                        ) : event.type === "vitals" ? (
                          <Activity className="h-3 w-3 text-primary" />
                        ) : event.type === "abha" ? (
                          <FileText className="h-3 w-3 text-primary" />
                        ) : (
                          <FileText className="h-3 w-3 text-primary" />
                        )}
                      </div>

                      <div className="border rounded-md p-3">
                        <div className="flex justify-between items-start mb-1">
                          <div>
                            <Badge
                              variant="outline"
                              className={
                                event.source === "internal"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-green-100 text-green-800"
                              }
                            >
                              {event.source === "internal"
                                ? "Internal"
                                : "ABHA"}
                            </Badge>
                            <Badge variant="outline" className="ml-2">
                              {event.type}
                            </Badge>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {format(new Date(event.date), "MMM d, yyyy h:mm a")}
                          </span>
                        </div>

                        <h4 className="font-medium">{event.title}</h4>
                        <p className="text-sm text-muted-foreground mt-1">
                          {event.description}
                        </p>

                        {event.provider && (
                          <div className="text-xs text-muted-foreground mt-2">
                            Provider: {event.provider}
                          </div>
                        )}

                        {event.details && (
                          <div className="mt-2 p-2 bg-muted/20 rounded text-sm">
                            <pre className="whitespace-pre-wrap text-xs">
                              {JSON.stringify(event.details, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
