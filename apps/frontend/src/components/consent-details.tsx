"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ConsentStatusBadge } from "@/components/consent-status-badge";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import { Loader2, RefreshCw } from "lucide-react";
import { format } from "date-fns";
import { Separator } from "@/components/ui/separator";
// import {
//   Accordion,
//   AccordionContent,
//   AccordionItem,
//   AccordionTrigger,
// } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import {
  getEffectiveConsentStatus,
  isConsentExpired,
} from "@/utils/consent-utils";
import { useConsentTimeRemaining } from "@/hooks/use-consent-expiry-monitor";

interface ConsentDetailsProps {
  consentId: string;
}

export function ConsentDetails({ consentId }: ConsentDetailsProps) {
  const [consent, setConsent] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  // Removed acknowledging state

  // Get time remaining until expiration (must be called before any conditional returns)
  const timeRemaining = useConsentTimeRemaining(consent);

  // Function to update consent status to expired
  const updateConsentToExpired = async () => {
    try {
      console.log("Updating consent status to EXPIRED for consent:", consentId);

      const response = await Fetch.post(
        `/api/abdm/consent/${consentId}/update-status`,
        {
          status: "EXPIRED",
          reason: "Consent automatically expired based on data erase date",
        },
      );

      console.log("Update response:", response);

      if (response.success) {
        // Refetch consent details to get the updated status
        await fetchConsentDetails();
        toast.success("Consent status updated to expired");
      } else {
        console.error("Failed to update consent status:", response.error);
        toast.error("Failed to update consent status");
      }
    } catch (error) {
      console.error("Error updating consent status:", error);
      toast.error("Error updating consent status");
    }
  };

  // Fetch consent details
  const fetchConsentDetails = async () => {
    try {
      setLoading(true);
      // Use the new endpoint that fetches directly from the database
      const response = await Fetch.get(
        `/api/abdm/consent/by-id?id=${consentId}`,
      );
      if (response.success && response.data) {
        setConsent(response.data);
      } else {
        toast.error(response.error || "Failed to fetch consent details");
      }
    } catch (error) {
      console.error("Error fetching consent details:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Refresh consent status
  const refreshConsentStatus = async () => {
    try {
      setRefreshing(true);

      // 1. Check consent status
      const response = await Fetch.post("/api/abdm/consent/status", {
        consentId,
      });

      console.log("🔍 Consent status response:", response);

      if (response.success) {
        // 2. If consent is GRANTED, trigger consent fetch API to get detailed info
        // Note: response.status is the consent status ("GRANTED", "DENIED", etc.), not HTTP status
        const consentStatus = (response as any).status; // The consent status from API response
        console.log("📊 Consent status:", consentStatus);

        if (consentStatus === "GRANTED") {
          try {
            console.log("🔄 Consent is GRANTED - triggering consent fetch API...");
            console.log("📋 Calling trigger-fetch with consentId:", consentId);

            // Use the dedicated trigger-fetch endpoint
            const fetchResponse = await Fetch.post("/api/abdm/consent/trigger-fetch", {
              consentId: consentId,
            });

            console.log("📡 Trigger-fetch response:", fetchResponse);

            if (fetchResponse.success) {
              const { results } = fetchResponse;
              console.log("✅ Consent fetch API triggered successfully:", results);

              if (results.successful > 0) {
                toast.success(`Status updated and ${results.successful} consent artifact(s) fetched`);
              } else {
                toast.success("Status updated (no artifacts to fetch)");
              }
            } else {
              console.warn("⚠️ Consent fetch API failed, but status was updated");
              toast.success("Status updated (detailed info fetch failed)");
            }
          } catch (fetchError) {
            console.error("Error triggering consent fetch API:", fetchError);
            toast.success("Status updated (detailed info fetch failed)");
          }
        } else {
          console.log(`ℹ️ Consent status is ${consentStatus}, not GRANTED - skipping fetch API`);
          toast.success("Status updated");

          // 🧪 TEMPORARY: Always try trigger-fetch for testing
          try {
            console.log("🧪 TESTING: Triggering fetch API regardless of status...");
            const testFetchResponse = await Fetch.post("/api/abdm/consent/trigger-fetch", {
              consentId: consentId,
            });
            console.log("🧪 TEST trigger-fetch response:", testFetchResponse);
          } catch (testError) {
            console.log("🧪 TEST trigger-fetch error:", testError);
          }
        }

        // 3. Refetch consent details to get the updated status
        await fetchConsentDetails();

        // 4. Reload the page to ensure all components are updated
        window.location.reload();
      } else {
        toast.error(response.error || "Failed to refresh consent status");
      }
    } catch (error) {
      console.error("Error refreshing consent status:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setRefreshing(false);
    }
  };

  // Acknowledge consent notification function removed

  // Fetch consent details on mount
  useEffect(() => {
    fetchConsentDetails();
  }, [consentId]);

  // Check if consent is expired and update status automatically
  useEffect(() => {
    if (
      consent &&
      (consent.status === "GRANTED" || consent.status === "REQUESTED")
    ) {
      const expired = isConsentExpired(consent);
      console.log("Checking consent expiration:", {
        consentId: consent.id,
        status: consent.status,
        expired,
        permission: consent.permission,
      });

      if (expired) {
        console.log("Consent is expired, updating status...");
        updateConsentToExpired();
      }
    }
  }, [consent]);

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6 flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  if (!consent) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            Consent details not found
          </p>
        </CardContent>
      </Card>
    );
  }

  // Format permission data for display
  const permission =
    typeof consent.permission === "string"
      ? JSON.parse(consent.permission)
      : consent.permission;

  // Format care contexts for display
  // const careContexts =
  //   typeof consent.careContexts === "string"
  //     ? JSON.parse(consent.careContexts)
  //     : consent.careContexts;

  // Format consent artifact for display
  // const consentArtifact = consent.consentArtifact
  //   ? typeof consent.consentArtifact === "string"
  //     ? JSON.parse(consent.consentArtifact)
  //     : consent.consentArtifact
  //   : null;

  // Check if consent is expired
  const isExpired = isConsentExpired(consent);
  const effectiveStatus = getEffectiveConsentStatus(consent);

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-xl">Consent Details</CardTitle>
          <CardDescription>
            Consent request for {consent.patient?.firstName}{" "}
            {consent.patient?.lastName}
          </CardDescription>
          {(consent.patient?.abhaProfile?.abhaAddress ||
            consent.patient?.abhaProfile?.abhaNumber) && (
            <div className="mt-2 space-y-1">
              {consent.patient?.abhaProfile?.abhaAddress && (
                <div className="text-xs text-muted-foreground">
                  <span className="font-medium">ABHA Address:</span>{" "}
                  {consent.patient.abhaProfile.abhaAddress}
                </div>
              )}
              {consent.patient?.abhaProfile?.abhaNumber && (
                <div className="text-xs text-muted-foreground">
                  <span className="font-medium">ABHA ID:</span>{" "}
                  {consent.patient.abhaProfile.abhaNumber}
                </div>
              )}
            </div>
          )}
        </div>
        <ConsentStatusBadge status={getEffectiveConsentStatus(consent)} />
      </CardHeader>
      <CardContent className="pt-4">
        {isExpired && effectiveStatus === "EXPIRED" && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800 font-medium">
              ⚠️ This consent has expired based on the data erase date and is no
              longer valid for accessing health records.
            </p>
          </div>
        )}
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground">
                Request ID
              </h4>
              <p className="text-sm">{consent.consentRequestId}</p>
            </div>
            {consent.consentId && (
              <div>
                <h4 className="text-sm font-medium text-muted-foreground">
                  Consent ID
                </h4>
                <p className="text-sm">{consent.consentId}</p>
              </div>
            )}
            <div>
              <h4 className="text-sm font-medium text-muted-foreground">
                Created
              </h4>
              <p className="text-sm">
                {format(new Date(consent.createdAt), "PPP p")}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-muted-foreground">
                Last Updated
              </h4>
              <p className="text-sm">
                status: {consent.status} updated by patient at:{" "}
                {format(new Date(consent.updatedAt), "PPP p")}
              </p>
            </div>
          </div>

          <Separator />

          <div>
            <h4 className="text-sm font-medium mb-1">Purpose</h4>
            <p className="text-sm">{consent.purpose}</p>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-1">
              {effectiveStatus === "GRANTED" ? "Granted Health Information Types" : "Requested Health Information Types"}
            </h4>
            <div className="flex flex-wrap gap-2">
              {consent.hiTypes.map((type: string) => (
                <Badge key={type} variant="outline">
                  {type}
                </Badge>
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <h4 className="text-sm font-medium mb-1">Permission Details</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Access Mode:</span>{" "}
                {permission.accessMode}
              </div>
              <div>
                <span className="text-muted-foreground">Date Range:</span>{" "}
                {permission.dateRange && (
                  <>
                    {format(new Date(permission.dateRange.from), "PPP p")} to{" "}
                    {format(new Date(permission.dateRange.to), "PPP p")}
                  </>
                )}
              </div>
              <div>
                <span className="text-muted-foreground">Data Expire At:</span>{" "}
                {permission.dataEraseAt &&
                  format(new Date(permission.dataEraseAt), "PPP p")}
              </div>
              {timeRemaining && effectiveStatus === "GRANTED" && (
                <div className="col-span-2">
                  <span className="text-muted-foreground">Time Remaining:</span>{" "}
                  <span
                    className={`font-medium ${timeRemaining.days < 7 ? "text-orange-600" : timeRemaining.days < 1 ? "text-red-600" : "text-green-600"}`}
                  >
                    {timeRemaining.formatted}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* {careContexts && careContexts.length > 0 && (
            <>
              <Separator />
              <div>
                <h4 className="text-sm font-medium mb-1">Care Contexts</h4>
                <div className="space-y-2">
                  {careContexts.map((context: any, index: number) => (
                    <div key={index} className="text-sm">
                      <span className="text-muted-foreground">
                        Patient Reference:
                      </span>{" "}
                      {context.patientReference}
                      <br />
                      <span className="text-muted-foreground">
                        Care Context Reference:
                      </span>{" "}
                      {context.careContextReference}
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {consentArtifact && (
            <>
              <Separator />
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="artifact">
                  <AccordionTrigger>Consent Artifact</AccordionTrigger>
                  <AccordionContent>
                    <pre className="text-xs bg-muted p-2 rounded-md overflow-auto max-h-[300px]">
                      {JSON.stringify(consentArtifact, null, 2)}
                    </pre>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </>
          )} */}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between gap-4">
        <Button
          variant="outline"
          className="flex-1"
          onClick={refreshConsentStatus}
          disabled={refreshing}
        >
          {refreshing ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="mr-2 h-4 w-4" />
          )}
          Refresh Status
        </Button>

        {/* Acknowledge button removed */}
      </CardFooter>
    </Card>
  );
}
