"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Building,
  Upload,
  MapPin,
  Phone,
  Mail,
  Home,
  Users,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Stethoscope,
  Pill,
  Activity,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";

interface MultiStepOnboardingProps {
  organizationId: string;
  organizationName: string;
  onComplete: () => void;
}

export function MultiStepOnboarding({
  organizationId,
  organizationName,
  onComplete,
}: MultiStepOnboardingProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;
  const progress = (currentStep / totalSteps) * 100;

  // Facility Information
  const [name, setName] = useState(organizationName);
  const [domain, setDomain] = useState("");
  const [description, setDescription] = useState("");
  const [logo, setLogo] = useState<string | null>(null);
  const [facilityType, setFacilityType] = useState("clinic");

  // Contact Information
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [address, setAddress] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [pincode, setPincode] = useState("");
  const [latitude, setLatitude] = useState("");
  const [longitude, setLongitude] = useState("");

  // Services
  const [services, setServices] = useState<string[]>([]);
  const [specialties, setSpecialties] = useState<string[]>([]);
  const [acceptsInsurance, setAcceptsInsurance] = useState(false);
  const [insuranceProviders, setInsuranceProviders] = useState<string[]>([]);

  // Team
  const [teamSize, setTeamSize] = useState("");
  const [hasDoctors, setHasDoctors] = useState(false);
  const [hasNurses, setHasNurses] = useState(false);
  const [hasAdminStaff, setHasAdminStaff] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch(`/api/organizations/${organizationId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          domain,
          description,
          logo,
          branchData: {
            name,
            phone,
            email,
            address,
            city,
            state,
            pincode,
            latitude: latitude || undefined,
            longitude: longitude || undefined,
            facilityType,
            isHeadOffice: true,
            services: services.join(","),
            specialties: specialties.join(","),
            acceptsInsurance,
            insuranceProviders: insuranceProviders.join(","),
            teamSize: parseInt(teamSize) || 0,
            hasDoctors,
            hasNurses,
            hasAdminStaff,
          },
        }),
      });

      if (response.ok) {
        onComplete();
        router.refresh();
      } else {
        const data = await response.json();
        console.error("Error updating organization:", data.error);
      }
    } catch (error) {
      console.error("Error updating organization:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogo(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleServiceToggle = (service: string) => {
    if (services.includes(service)) {
      setServices(services.filter((s) => s !== service));
    } else {
      setServices([...services, service]);
    }
  };

  const handleSpecialtyToggle = (specialty: string) => {
    if (specialties.includes(specialty)) {
      setSpecialties(specialties.filter((s) => s !== specialty));
    } else {
      setSpecialties([...specialties, specialty]);
    }
  };

  const handleInsuranceProviderToggle = (provider: string) => {
    if (insuranceProviders.includes(provider)) {
      setInsuranceProviders(insuranceProviders.filter((p) => p !== provider));
    } else {
      setInsuranceProviders([...insuranceProviders, provider]);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Welcome to Aran Care</h1>
        <p className="text-muted-foreground">
          Let's set up your healthcare facility profile
        </p>
      </div>

      <div className="mb-8">
        <div className="flex justify-between mb-2">
          <span className="text-sm font-medium">
            Step {currentStep} of {totalSteps}
          </span>
          <span className="text-sm font-medium">{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      <Card className="border shadow-sm">
        <CardHeader>
          <CardTitle>
            {currentStep === 1 && "Facility Information"}
            {currentStep === 2 && "Contact Information"}
            {currentStep === 3 && "Services & Specialties"}
            {currentStep === 4 && "Team & Final Setup"}
          </CardTitle>
          <CardDescription>
            {currentStep === 1 && "Tell us about your healthcare facility"}
            {currentStep === 2 && "How can patients reach you?"}
            {currentStep === 3 && "What services and specialties do you offer?"}
            {currentStep === 4 &&
              "Tell us about your team and complete your setup"}
          </CardDescription>
        </CardHeader>

        <form onSubmit={handleSubmit}>
          <CardContent>
            {/* Step 1: Facility Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Facility Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="facilityType">Facility Type</Label>
                  <Select value={facilityType} onValueChange={setFacilityType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select facility type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="clinic">Clinic</SelectItem>
                      <SelectItem value="hospital">Hospital</SelectItem>
                      <SelectItem value="laboratory">Laboratory</SelectItem>
                      <SelectItem value="pharmacy">Pharmacy</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="domain">Website Domain (Optional)</Label>
                  <Input
                    id="domain"
                    placeholder="example.com"
                    value={domain}
                    onChange={(e) => setDomain(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Tell us about your healthcare facility"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="logo">Facility Logo</Label>
                  <div className="flex items-center space-x-4">
                    <div className="h-20 w-20 rounded-md border flex items-center justify-center bg-muted">
                      {logo ? (
                        <img
                          src={logo}
                          alt="Facility logo"
                          className="h-18 w-18 object-contain"
                        />
                      ) : (
                        <Building className="h-10 w-10 text-muted-foreground" />
                      )}
                    </div>
                    <div className="flex-1">
                      <Input
                        id="logo"
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById("logo")?.click()}
                        className="w-full"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Logo
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Contact Information */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                      <Input
                        id="phone"
                        placeholder="+****************"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Contact Email</Label>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <div className="flex items-center">
                    <Home className="h-4 w-4 mr-2 text-muted-foreground" />
                    <Input
                      id="address"
                      placeholder="123 Healthcare St"
                      value={address}
                      onChange={(e) => setAddress(e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      placeholder="City"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      placeholder="State"
                      value={state}
                      onChange={(e) => setState(e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="pincode">Pincode/ZIP</Label>
                    <Input
                      id="pincode"
                      placeholder="123456"
                      value={pincode}
                      onChange={(e) => setPincode(e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="latitude">Latitude (Optional)</Label>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                      <Input
                        id="latitude"
                        placeholder="37.7749"
                        value={latitude}
                        onChange={(e) => setLatitude(e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="longitude">Longitude (Optional)</Label>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                      <Input
                        id="longitude"
                        placeholder="-122.4194"
                        value={longitude}
                        onChange={(e) => setLongitude(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Services & Specialties */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <Tabs defaultValue="services" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="services">Services</TabsTrigger>
                    <TabsTrigger value="specialties">Specialties</TabsTrigger>
                  </TabsList>
                  <TabsContent value="services" className="pt-4">
                    <div className="space-y-4">
                      <Label>What services do you offer?</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {[
                          {
                            id: "general-checkup",
                            label: "General Check-up",
                            icon: <Stethoscope className="h-4 w-4 mr-2" />,
                          },
                          {
                            id: "vaccination",
                            label: "Vaccination",
                            icon: <Pill className="h-4 w-4 mr-2" />,
                          },
                          {
                            id: "lab-tests",
                            label: "Laboratory Tests",
                            icon: <Activity className="h-4 w-4 mr-2" />,
                          },
                          {
                            id: "x-ray",
                            label: "X-Ray & Imaging",
                            icon: <Activity className="h-4 w-4 mr-2" />,
                          },
                          {
                            id: "emergency",
                            label: "Emergency Care",
                            icon: <Activity className="h-4 w-4 mr-2" />,
                          },
                          {
                            id: "surgery",
                            label: "Surgery",
                            icon: <Stethoscope className="h-4 w-4 mr-2" />,
                          },
                          {
                            id: "dental",
                            label: "Dental Care",
                            icon: <Stethoscope className="h-4 w-4 mr-2" />,
                          },
                          {
                            id: "physical-therapy",
                            label: "Physical Therapy",
                            icon: <Activity className="h-4 w-4 mr-2" />,
                          },
                        ].map((service) => (
                          <div
                            key={service.id}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={service.id}
                              checked={services.includes(service.id)}
                              onCheckedChange={() =>
                                handleServiceToggle(service.id)
                              }
                            />
                            <Label
                              htmlFor={service.id}
                              className="flex items-center cursor-pointer"
                            >
                              {service.icon}
                              {service.label}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                  <TabsContent value="specialties" className="pt-4">
                    <div className="space-y-4">
                      <Label>What specialties do you offer?</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {[
                          "Cardiology",
                          "Dermatology",
                          "Endocrinology",
                          "Gastroenterology",
                          "Neurology",
                          "Obstetrics & Gynecology",
                          "Oncology",
                          "Ophthalmology",
                          "Orthopedics",
                          "Pediatrics",
                          "Psychiatry",
                          "Urology",
                        ].map((specialty) => (
                          <div
                            key={specialty}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`specialty-${specialty}`}
                              checked={specialties.includes(specialty)}
                              onCheckedChange={() =>
                                handleSpecialtyToggle(specialty)
                              }
                            />
                            <Label
                              htmlFor={`specialty-${specialty}`}
                              className="cursor-pointer"
                            >
                              {specialty}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="space-y-4 pt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="accepts-insurance"
                      checked={acceptsInsurance}
                      onCheckedChange={(checked) =>
                        setAcceptsInsurance(!!checked)
                      }
                    />
                    <Label htmlFor="accepts-insurance" className="font-medium">
                      We accept insurance
                    </Label>
                  </div>

                  {acceptsInsurance && (
                    <div className="pl-6 space-y-3">
                      <Label>Which insurance providers do you accept?</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {[
                          "Medicare",
                          "Medicaid",
                          "Blue Cross Blue Shield",
                          "Aetna",
                          "Cigna",
                          "UnitedHealthcare",
                          "Humana",
                          "Kaiser Permanente",
                        ].map((provider) => (
                          <div
                            key={provider}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`insurance-${provider}`}
                              checked={insuranceProviders.includes(provider)}
                              onCheckedChange={() =>
                                handleInsuranceProviderToggle(provider)
                              }
                            />
                            <Label
                              htmlFor={`insurance-${provider}`}
                              className="cursor-pointer"
                            >
                              {provider}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Step 4: Team & Final Setup */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="teamSize">Team Size</Label>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                    <Input
                      id="teamSize"
                      type="number"
                      min="1"
                      placeholder="Number of team members"
                      value={teamSize}
                      onChange={(e) => setTeamSize(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Label>Team Composition</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="has-doctors"
                        checked={hasDoctors}
                        onCheckedChange={(checked) => setHasDoctors(!!checked)}
                      />
                      <Label htmlFor="has-doctors" className="cursor-pointer">
                        Doctors
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="has-nurses"
                        checked={hasNurses}
                        onCheckedChange={(checked) => setHasNurses(!!checked)}
                      />
                      <Label htmlFor="has-nurses" className="cursor-pointer">
                        Nurses
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="has-admin"
                        checked={hasAdminStaff}
                        onCheckedChange={(checked) =>
                          setHasAdminStaff(!!checked)
                        }
                      />
                      <Label htmlFor="has-admin" className="cursor-pointer">
                        Administrative Staff
                      </Label>
                    </div>
                  </div>
                </div>

                <div className="rounded-lg bg-muted p-4 mt-6">
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-primary mt-0.5 mr-3" />
                    <div>
                      <h3 className="font-medium">You're almost done!</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        Review your information and click "Complete Setup" to
                        finish setting up your healthcare facility profile.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>

          <CardFooter className="flex justify-between border-t pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={currentStep === 1 ? onComplete : handlePrevious}
              disabled={isLoading}
            >
              {currentStep === 1 ? (
                "Skip for now"
              ) : (
                <>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </>
              )}
            </Button>

            {currentStep < totalSteps ? (
              <Button type="button" onClick={handleNext}>
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : "Complete Setup"}
              </Button>
            )}
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
