"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  FileText,
  ExternalLink,
  RefreshCw,
  Calendar,
  FileType,
} from "lucide-react";
import { format } from "date-fns";

interface UploadedFile {
  id: string;
  bundleId: string;
  bundleType: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  consultationId: string;
  patientId: string;
  document: {
    url: string | null;
    title: string | null;
    size: number | null;
  };
}

interface UploadedFilesListProps {
  consultationId: string;
  bundleType?: string;
  title?: string;
  description?: string;
  className?: string;
  onRefresh?: () => void;
  showOnlyGenerated?: boolean; // New prop to filter out document_uploaded entries
}

export function UploadedFilesList({
  consultationId,
  bundleType,
  title = "Uploaded Files",
  description = "Previously uploaded files and generated FHIR bundles",
  className,
  onRefresh,
  showOnlyGenerated = false,
}: UploadedFilesListProps) {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUploadedFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        consultationId,
      });

      if (bundleType) {
        params.append("bundleType", bundleType);
      }

      const response = await fetch(`/api/fhir/uploaded-bundles?${params}`);

      if (!response.ok) {
        throw new Error("Failed to fetch uploaded files");
      }

      const data = await response.json();
      let filteredFiles = data.bundles || [];

      // Filter out document_uploaded entries if showOnlyGenerated is true
      if (showOnlyGenerated) {
        filteredFiles = filteredFiles.filter(
          (file: UploadedFile) => file.status !== "document_uploaded",
        );
      }

      setFiles(filteredFiles);
    } catch (error) {
      console.error("Error fetching uploaded files:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch uploaded files",
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUploadedFiles();
  }, [consultationId, bundleType]);

  const handleRefresh = () => {
    fetchUploadedFiles();
    onRefresh?.();
  };

  const formatFileSize = (bytes: number | null): string => {
    if (!bytes) return "Unknown size";
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getBundleTypeColor = (type: string): string => {
    switch (type) {
      case "HealthDocumentRecord":
        return "bg-purple-100 text-purple-800";
      case "WellnessRecord":
        return "bg-green-100 text-green-800";
      case "WellnessDocument":
        return "bg-green-100 text-green-800";
      case "OPConsultNote":
        return "bg-blue-100 text-blue-800";
      case "DischargeSummary":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getBundleTypeLabel = (type: string): string => {
    switch (type) {
      case "HealthDocumentRecord":
        return "Health Document";
      case "WellnessRecord":
        return "Wellness Record";
      case "WellnessDocument":
        return "Wellness Document";
      case "OPConsultNote":
        return "OP Consultation";
      case "DischargeSummary":
        return "Discharge Summary";
      case "DiagnosticReport":
        return "Diagnostic Report";
      case "Prescription":
        return "Prescription";
      case "OPConsultationRecord":
        return "OP Consultation";
      case "DischargeSummary":
        return "Discharge Summary";
      case "ImmunizationRecord":
        return "Immunization Record";
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">
              Loading uploaded files...
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertDescription>
              {error}
              <Button
                onClick={handleRefresh}
                variant="outline"
                size="sm"
                className="ml-2"
              >
                Try Again
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              {title}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {files.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No uploaded files found</p>
            <p className="text-sm">Upload a file to see it here</p>
          </div>
        ) : (
          <div className="space-y-4">
            {files.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className="p-2 rounded-full bg-muted">
                    <FileText className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <p className="font-medium">
                        {file.document.title ||
                          `${getBundleTypeLabel(file.bundleType)} Document`}
                      </p>
                      <Badge className={getBundleTypeColor(file.bundleType)}>
                        {getBundleTypeLabel(file.bundleType)}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {format(new Date(file.createdAt), "MMM dd, yyyy HH:mm")}
                      </div>
                      {file.document.size && (
                        <div className="flex items-center">
                          <FileType className="h-3 w-3 mr-1" />
                          {formatFileSize(file.document.size)}
                        </div>
                      )}
                      <Badge variant="outline" className="text-xs">
                        {file.status}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {file.document.url && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(file.document.url!, "_blank")}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View File
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
