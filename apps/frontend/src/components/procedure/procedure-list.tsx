"use client";

import { useState, useEffect } from "react";
import { Fetch } from "@/services/fetch";
import { Button } from "@/components/ui/button";
// Card components are not used in this file
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Loader2, FileText, Download, Share } from "lucide-react";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";

interface Procedure {
  id: string;
  procedureDate: string;
  status: string;
  category?: string;
  code: string;
  codeDisplay: string;
  bodySite?: string;
  outcome?: string;
  complication?: string;
  followUp?: string;
  notes?: string;
  performer?: string;
  location?: string;
  reasonCode?: string;
  reasonDisplay?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProcedureListProps {
  patientId: string;
  consultationId?: string;
  refreshTrigger?: number;
}

export function ProcedureList({
  patientId,
  consultationId,
  refreshTrigger = 0,
}: ProcedureListProps) {
  const [procedures, setProcedures] = useState<Procedure[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProcedure, setSelectedProcedure] = useState<Procedure | null>(
    null,
  );

  useEffect(() => {
    const fetchProcedures = async () => {
      setLoading(true);
      setError(null);
      try {
        const url = consultationId
          ? `/api/procedure?consultationId=${consultationId}`
          : `/api/procedure?patientId=${patientId}`;

        const response = await Fetch.get(url);

        if (response.error) {
          throw new Error(response.error);
        }

        // Make sure we're setting an array of procedures
        if (Array.isArray(response.data)) {
          setProcedures(response.data);
        } else if (Array.isArray(response)) {
          setProcedures(response);
        } else {
          console.error("Unexpected response format:", response);
          throw new Error("Unexpected response format from server");
        }
      } catch (err) {
        console.error("Error fetching procedures:", err);
        setError("Failed to load procedures");
        toast.error("Failed to load procedures");
      } finally {
        setLoading(false);
      }
    };

    fetchProcedures();
  }, [patientId, consultationId, refreshTrigger]);

  const handleExportFhir = async (procedureId: string) => {
    try {
      const response = await Fetch.get(`/api/procedure/${procedureId}/fhir`);

      if (response.error) {
        throw new Error(response.error);
      }

      // Create a downloadable JSON file
      const blob = new Blob([JSON.stringify(response, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `procedure-${procedureId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success("FHIR resource exported successfully");
    } catch (err) {
      console.error("Error exporting FHIR resource:", err);
      toast.error("Failed to export FHIR resource");
    }
  };

  const handleShareWithAbdm = async (procedureId: string) => {
    try {
      const response = await Fetch.post("/api/abdm/health-record/package", {
        patientId,
        recordType: "procedure",
        recordId: procedureId,
      });

      if (response.error) {
        throw new Error(response.error);
      }

      toast.success("Procedure packaged for ABDM sharing");
    } catch (err) {
      console.error("Error packaging procedure for ABDM:", err);
      toast.error("Failed to package procedure for ABDM");
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "completed":
        return "default";
      case "in-progress":
        return "secondary";
      case "preparation":
        return "outline";
      case "not-done":
      case "on-hold":
      case "stopped":
        return "destructive";
      case "entered-in-error":
        return "destructive";
      default:
        return "outline";
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  if (procedures.length === 0) {
    return (
      <div className="p-4 text-center">
        <p className="text-muted-foreground">No procedures found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Procedure</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Category</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {procedures.map((procedure) => (
            <TableRow key={procedure.id}>
              <TableCell>
                {formatDate(new Date(procedure.procedureDate))}
              </TableCell>
              <TableCell>{procedure.codeDisplay}</TableCell>
              <TableCell>
                <Badge variant={getStatusBadgeVariant(procedure.status)}>
                  {procedure.status}
                </Badge>
              </TableCell>
              <TableCell>{procedure.category || "N/A"}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedProcedure(procedure)}
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle>Procedure Details</DialogTitle>
                        <DialogDescription>
                          {procedure.codeDisplay} -{" "}
                          {formatDate(new Date(procedure.procedureDate))}
                        </DialogDescription>
                      </DialogHeader>
                      {selectedProcedure && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h4 className="text-sm font-medium">Status</h4>
                              <p>{selectedProcedure.status}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Category</h4>
                              <p>{selectedProcedure.category || "N/A"}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Code</h4>
                              <p>{selectedProcedure.code}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Body Site</h4>
                              <p>{selectedProcedure.bodySite || "N/A"}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Performer</h4>
                              <p>{selectedProcedure.performer || "N/A"}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Location</h4>
                              <p>{selectedProcedure.location || "N/A"}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Reason</h4>
                              <p>
                                {selectedProcedure.reasonDisplay ||
                                  selectedProcedure.reasonCode ||
                                  "N/A"}
                              </p>
                            </div>
                          </div>

                          {selectedProcedure.outcome && (
                            <div>
                              <h4 className="text-sm font-medium">Outcome</h4>
                              <p className="whitespace-pre-wrap">
                                {selectedProcedure.outcome}
                              </p>
                            </div>
                          )}

                          {selectedProcedure.complication && (
                            <div>
                              <h4 className="text-sm font-medium">
                                Complications
                              </h4>
                              <p className="whitespace-pre-wrap">
                                {selectedProcedure.complication}
                              </p>
                            </div>
                          )}

                          {selectedProcedure.followUp && (
                            <div>
                              <h4 className="text-sm font-medium">
                                Follow-up Instructions
                              </h4>
                              <p className="whitespace-pre-wrap">
                                {selectedProcedure.followUp}
                              </p>
                            </div>
                          )}

                          {selectedProcedure.notes && (
                            <div>
                              <h4 className="text-sm font-medium">
                                Additional Notes
                              </h4>
                              <p className="whitespace-pre-wrap">
                                {selectedProcedure.notes}
                              </p>
                            </div>
                          )}

                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              onClick={() =>
                                handleExportFhir(selectedProcedure.id)
                              }
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Export FHIR
                            </Button>
                            <Button
                              onClick={() =>
                                handleShareWithAbdm(selectedProcedure.id)
                              }
                            >
                              <Share className="h-4 w-4 mr-1" />
                              Share with ABDM
                            </Button>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExportFhir(procedure.id)}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    FHIR
                  </Button>

                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleShareWithAbdm(procedure.id)}
                  >
                    <Share className="h-4 w-4 mr-1" />
                    ABDM
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
