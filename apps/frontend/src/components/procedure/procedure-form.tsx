"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { toast } from "sonner";
import { Fetch } from "@/services/fetch";

// Define the form schema
const formSchema = z.object({
  consultationId: z.string(),
  patientId: z.string(),
  doctorId: z.string(),
  procedureDate: z.date(),
  status: z.string(), // Make status required without default
  category: z.string().optional(),
  code: z.string(),
  codeDisplay: z.string(),
  bodySite: z.string().optional(),
  outcome: z.string().optional(),
  complication: z.string().optional(),
  followUp: z.string().optional(),
  notes: z.string().optional(),
  performer: z.string().optional(),
  location: z.string().optional(),
  reasonCode: z.string().optional(),
  reasonDisplay: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface ProcedureFormProps {
  consultationId: string;
  patientId: string;
  doctorId: string;
  onCancel?: () => void;
  onSuccess?: () => void;
}

export function ProcedureForm({
  consultationId,
  patientId,
  doctorId,
  onCancel,
  onSuccess,
}: ProcedureFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      consultationId,
      patientId,
      doctorId,
      procedureDate: new Date(),
      status: "completed",
      category: "procedure",
      code: "",
      codeDisplay: "",
      bodySite: "",
      outcome: "",
      complication: "",
      followUp: "",
      notes: "",
      performer: "",
      location: "",
      reasonCode: "",
      reasonDisplay: "",
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      const response = await Fetch.post("/api/procedure", data);

      if (response.error) {
        throw new Error(response.error);
      }

      toast.success("Procedure created successfully");
      form.reset();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error creating procedure:", error);
      toast.error("Failed to create procedure");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="procedureDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Procedure Date</FormLabel>
                <DatePicker date={field.value} setDate={field.onChange} />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="preparation">Preparation</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="not-done">Not Done</SelectItem>
                    <SelectItem value="on-hold">On Hold</SelectItem>
                    <SelectItem value="stopped">Stopped</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="entered-in-error">
                      Entered in Error
                    </SelectItem>
                    <SelectItem value="unknown">Unknown</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="procedure">Procedure</SelectItem>
                    <SelectItem value="investigation">Investigation</SelectItem>
                    <SelectItem value="therapy">Therapy</SelectItem>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="surgery">Surgery</SelectItem>
                    <SelectItem value="diagnostic">Diagnostic</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Code</FormLabel>
                <FormControl>
                  <Input placeholder="SNOMED CT or CPT code" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="codeDisplay"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Procedure Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Display name for the procedure"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bodySite"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Body Site</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Body site where procedure was performed"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="performer"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Performer</FormLabel>
                <FormControl>
                  <Input placeholder="Name of the performer" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Location where procedure was performed"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="reasonCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reason Code</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Reason code for the procedure"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="reasonDisplay"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reason</FormLabel>
                <FormControl>
                  <Input placeholder="Reason for the procedure" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="outcome"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Outcome</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Outcome of the procedure"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="complication"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Complications</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Complications during the procedure"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="followUp"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Follow-up Instructions</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Follow-up instructions"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Additional Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Additional notes"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Procedure"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
