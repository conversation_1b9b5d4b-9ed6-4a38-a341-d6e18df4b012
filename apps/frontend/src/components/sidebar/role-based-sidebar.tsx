"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { MenuIcon } from "lucide-react";
import { useUserRole } from "@/hooks/use-user-role";
import {
  dashboardItem,
  getMenuByRole,
  isRouteActive,
  UserRole,
} from "@/config/roleBasedSidebarConfig";

interface RoleBasedSidebarProps {
  id?: string;
  className?: string;
}

// Export the MobileMenuTrigger component for use in the header
export const MobileMenuTrigger = () => {
  const { role } = useUserRole();

  if (!role) return null;

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <MenuIcon className="h-6 w-6" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[300px] sm:w-[350px] p-0">
        <div className="h-full overflow-y-auto">
          <SidebarContent />
        </div>
      </SheetContent>
    </Sheet>
  );
};

// Extract the sidebar content to a separate component for reuse
export const SidebarContent = () => {
  const pathname = usePathname();
  const { role } = useUserRole();

  // If no role is available, don't render the sidebar
  if (!role) return null;

  // Get menu groups for the current user role
  const menuGroups = getMenuByRole(role as UserRole);

  return (
    <>
      <div className="p-6">
        <h2 className="text-2xl font-bold text-primary">Aran Care</h2>
      </div>

      <nav className="px-4 space-y-4">
        {/* Dashboard */}
        <Button
          variant={
            isRouteActive(pathname || "", dashboardItem.href)
              ? "secondary"
              : "ghost"
          }
          className={cn(
            "w-full justify-start",
            isRouteActive(pathname || "", dashboardItem.href) && "font-medium",
          )}
          asChild
        >
          <Link href={dashboardItem.href}>
            <dashboardItem.icon className="mr-2 h-5 w-5" />
            {dashboardItem.title}
          </Link>
        </Button>

        {/* Menu Groups */}
        {menuGroups.map((group) => (
          <div key={group.title} className="space-y-2 mt-8 first:mt-4">
            {/* Group Header - Now just a label */}
            <div className="px-2 py-2">
              <h3 className="text-xs font-bold text-muted-foreground uppercase tracking-wider">
                {group.title}
              </h3>
            </div>

            {/* Group Items - Always visible */}
            <div className="space-y-1.5 pl-2">
              {group.items.map((item) => (
                <Button
                  key={item.title}
                  variant={
                    isRouteActive(pathname || "", item.href)
                      ? "secondary"
                      : "ghost"
                  }
                  className={cn(
                    "w-full justify-start",
                    item.indented && "pl-8",
                    isRouteActive(pathname || "", item.href) && "font-medium",
                  )}
                  asChild
                >
                  <Link href={item.href}>
                    <item.icon className="mr-2 h-5 w-5" />
                    <span>{item.title}</span>
                  </Link>
                </Button>
              ))}
            </div>
          </div>
        ))}
      </nav>
    </>
  );
};

export function RoleBasedSidebar({ id, className }: RoleBasedSidebarProps) {
  // Return the desktop sidebar
  return (
    <div
      className={cn(
        "hidden md:block w-64 h-full bg-background dark:bg-gray-900 border-r overflow-y-auto crm-scrollbar",
        className,
      )}
      id={id}
    >
      <SidebarContent />
    </div>
  );
}
