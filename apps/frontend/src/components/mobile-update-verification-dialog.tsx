"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Loader2, Phone, Shield, RefreshCw } from "lucide-react";
import { toast } from "sonner";

interface MobileUpdateVerificationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userMobile: string;
  abhaResponseMobile: string;
  txnId: string;
  abhaNumber: string;
  abhaAddress: string;
  healthIdNumber: string;
  patientId?: string;
  xToken: string;
  onVerificationSuccess: (result: any) => void;
}

export function MobileUpdateVerificationDialog({
  open,
  onOpenChange,
  userMobile,
  abhaResponseMobile,
  txnId,
  abhaNumber,
  abhaAddress,
  healthIdNumber,
  patientId,
  xToken,
  onVerificationSuccess,
}: MobileUpdateVerificationDialogProps) {
  const [otp, setOtp] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Resend OTP state
  const [isResendingOtp, setIsResendingOtp] = useState(false);
  const [resendAttempts, setResendAttempts] = useState(0);
  const [resendTimeRemaining, setResendTimeRemaining] = useState(0);
  const [isResendTimerActive, setIsResendTimerActive] = useState(false);

  // Timer refs
  const resendTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Format time for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Resend timer effect
  useEffect(() => {
    if (isResendTimerActive && resendTimeRemaining > 0) {
      resendTimerRef.current = setTimeout(() => {
        setResendTimeRemaining((prev) => prev - 1);
      }, 1000);
    } else if (resendTimeRemaining === 0) {
      setIsResendTimerActive(false);
    }

    return () => {
      if (resendTimerRef.current) {
        clearTimeout(resendTimerRef.current);
      }
    };
  }, [isResendTimerActive, resendTimeRemaining]);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (resendTimerRef.current) {
        clearTimeout(resendTimerRef.current);
      }
    };
  }, []);

  // Function to resend OTP
  const handleResendOtp = async () => {
    // Don't allow resend if timer is still active or maximum attempts reached
    if (isResendTimerActive) {
      toast.error(
        `Please wait ${resendTimeRemaining} seconds before resending OTP`,
        {
          duration: 3000,
          position: "top-center",
        },
      );
      return;
    }

    if (resendAttempts >= 2) {
      toast.error("Maximum resend attempts reached", {
        duration: 3000,
        position: "top-center",
      });
      return;
    }

    setError(null);
    setIsResendingOtp(true);

    try {
      const response = await fetch(
        "/api/abdm/abha-create/mobile-update/request-otp",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            userMobile,
            txnId,
            xToken,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to resend OTP");
      }

      // Increment resend attempts
      const newResendAttempts = resendAttempts + 1;
      setResendAttempts(newResendAttempts);

      // Start the resend cooldown timer (60 seconds)
      setResendTimeRemaining(60);
      setIsResendTimerActive(true);

      // Show appropriate toast message based on remaining attempts
      const remainingAttempts = 2 - newResendAttempts;
      const successMessage =
        remainingAttempts > 0
          ? `OTP resent successfully. ${remainingAttempts} resend attempt${remainingAttempts === 1 ? "" : "s"} remaining.`
          : "OTP resent successfully. No more resend attempts available.";

      toast.success(successMessage, {
        duration: 4000,
        position: "top-center",
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to resend OTP";
      setError(errorMessage);
      toast.error(errorMessage, {
        duration: 4000,
        position: "top-center",
      });
    } finally {
      setIsResendingOtp(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (otp.length !== 6) {
      setError("Please enter a valid 6-digit OTP");
      return;
    }

    setIsVerifying(true);
    setError(null);

    try {
      const response = await fetch(
        "/api/abdm/abha-create/mobile-update/verify-otp",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            otp,
            txnId,
            userMobile,
            abhaNumber,
            abhaAddress,
            healthIdNumber,
            patientId,
            xToken,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to verify OTP");
      }

      toast.success("Mobile number updated successfully");
      onVerificationSuccess(data);
      onOpenChange(false);
      setOtp("");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to verify OTP";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsVerifying(false);
    }
  };

  const formatPhoneNumber = (phone: string) => {
    if (phone.length === 10) {
      return `+91 ${phone.slice(0, 5)} ${phone.slice(5)}`;
    }
    return phone;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5 text-blue-600" />
            Communication Mobile Verification
          </DialogTitle>
          <DialogDescription>
            Please verify the OTP sent to your communication mobile number to
            complete the setup. This number will be used for all future
            communications while keeping your Aadhaar-linked mobile separate.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Mobile number comparison */}
          <div className="space-y-2">
            <div className="space-y-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="text-sm">
                <div className="font-medium text-blue-800 mb-2">
                  Dual Mobile Number System
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-blue-700">
                    <Shield className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-muted-foreground">
                      Aadhaar Linked:
                    </span>
                    <span className="font-medium">
                      {abhaResponseMobile
                        ? formatPhoneNumber(abhaResponseMobile)
                        : "Not available"}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-blue-700">
                    <Phone className="h-3 w-3 text-blue-600" />
                    <span className="text-xs text-muted-foreground">
                      Communication:
                    </span>
                    <span className="font-medium">
                      {formatPhoneNumber(userMobile)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* New mobile number display */}
          <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
            <Phone className="h-4 w-4 text-green-600" />
            <div className="text-sm">
              <div className="font-medium text-green-800">
                {!abhaResponseMobile ? "Adding mobile number:" : "Updating to:"}
              </div>
              <div className="text-green-700 font-medium">
                {formatPhoneNumber(userMobile)}
              </div>
            </div>
          </div>

          {/* OTP Input */}
          <div className="space-y-2">
            <Label htmlFor="mobile-update-otp">Enter verification code</Label>
            <Input
              id="mobile-update-otp"
              type="text"
              placeholder="000000"
              value={otp}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, "").slice(0, 6);
                setOtp(value);
                if (error) setError(null);
              }}
              maxLength={6}
              className="text-center text-lg tracking-widest"
              disabled={isVerifying}
            />
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>

          <div className="text-xs text-gray-500">
            An OTP has been sent to {formatPhoneNumber(userMobile)} for
            verification.
          </div>

          {/* Resend OTP Section */}
          <div className="flex justify-center mt-4">
            {isResendTimerActive ? (
              <span className="text-sm text-muted-foreground italic">
                Wait {formatTime(resendTimeRemaining)} to resend (
                {2 - resendAttempts} attempt
                {2 - resendAttempts === 1 ? "" : "s"} left)
              </span>
            ) : resendAttempts >= 2 ? (
              <span className="text-sm text-red-500 italic">
                Maximum resend attempts reached
              </span>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleResendOtp}
                disabled={
                  isResendingOtp ||
                  isVerifying ||
                  isResendTimerActive ||
                  resendAttempts >= 2
                }
                className="h-8 px-3 text-xs font-medium"
              >
                {isResendingOtp ? (
                  <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 h-3 w-3" />
                )}
                Resend OTP
              </Button>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isVerifying}
          >
            Cancel
          </Button>
          <Button
            onClick={handleVerifyOTP}
            disabled={otp.length !== 6 || isVerifying}
          >
            {isVerifying ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              "Verify & Update"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
