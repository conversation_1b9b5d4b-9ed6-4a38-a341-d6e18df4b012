"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Mail } from "lucide-react";
import { toast } from "sonner";

interface SendUILOtpEmailButtonProps {
  patientId: string;
  disabled?: boolean;
  variant?:
    | "default"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

/**
 * Button component to send UIL OTP via email
 */
export function SendUILOtpEmailButton({
  patientId,
  disabled = false,
  variant = "outline",
  size = "sm",
  className = "",
}: SendUILOtpEmailButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleSendEmail = async () => {
    if (isLoading || disabled) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/patients/${patientId}/uil-otps/send-email`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to send OTP email");
      }

      toast.success("OTP sent successfully to patient's email", {
        description: `Email sent to ${data.email}. Expires at ${new Date(data.expiresAt).toLocaleTimeString()}`,
        duration: 5000,
      });
    } catch (error) {
      console.error("Error sending UIL OTP email:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to send OTP email",
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleSendEmail}
      disabled={disabled || isLoading}
      className={className}
    >
      <Mail className="h-4 w-4 mr-2" />
      {isLoading ? "Sending..." : "Send OTP via Email"}
    </Button>
  );
}
