"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { SendUILOtpEmailButton } from "./send-email-button";
import { Clock, AlertCircle } from "lucide-react";
import { toast } from "sonner";

interface ActiveOtpCardProps {
  patientId: string;
  className?: string;
}

interface ActiveOtp {
  id: string;
  linkRefNumber: string;
  otp: string;
  transactionId: string;
  patientId: string;
  expiresAt: string;
  verified: boolean;
  createdAt: string;
}

/**
 * Card component to display active UIL OTP with option to send via email
 */
export function ActiveOtpCard({
  patientId,
  className = "",
}: ActiveOtpCardProps) {
  const [activeOtp, setActiveOtp] = useState<ActiveOtp | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState<string>("");

  // Fetch active OTP
  const fetchActiveOtp = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(
        `/api/patients/${patientId}/uil-otps/active`,
      );

      if (!response.ok) {
        if (response.status === 404) {
          // No active OTP is not an error, just set activeOtp to null
          setActiveOtp(null);
          return;
        }
        const data = await response.json();
        throw new Error(data.error || "Failed to fetch active OTP");
      }

      const data = await response.json();
      setActiveOtp(data);
    } catch (error) {
      console.error("Error fetching active OTP:", error);
      setError(
        error instanceof Error ? error.message : "Failed to fetch active OTP",
      );
      toast.error(
        error instanceof Error ? error.message : "Failed to fetch active OTP",
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate time left until OTP expires
  const calculateTimeLeft = () => {
    if (!activeOtp) return "";

    const now = new Date();
    const expiresAt = new Date(activeOtp.expiresAt);
    const diffMs = expiresAt.getTime() - now.getTime();

    if (diffMs <= 0) {
      // OTP has expired, refetch to get updated state
      fetchActiveOtp();
      return "Expired";
    }

    const minutes = Math.floor(diffMs / 60000);
    const seconds = Math.floor((diffMs % 60000) / 1000);

    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  // Fetch active OTP on component mount
  useEffect(() => {
    fetchActiveOtp();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [patientId]);

  // Update time left every second
  useEffect(() => {
    if (!activeOtp) return;

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    setTimeLeft(calculateTimeLeft());

    return () => clearInterval(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeOtp]);

  if (isLoading) {
    return (
      <Card
        className={`${className} min-h-[200px] flex items-center justify-center`}
      >
        <CardContent>
          <div className="text-center">Loading active OTP...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`${className} border-red-200`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-red-500 flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-red-500">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!activeOtp) {
    return (
      <Card className={`${className}`}>
        <CardHeader className="pb-2">
          <CardTitle>UIL OTP</CardTitle>
          <CardDescription>No active OTP found</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            There is no active OTP for this patient. OTPs are generated during
            the User Initiated Linking flow.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle>UIL OTP</CardTitle>
          <Badge
            variant={timeLeft === "Expired" ? "destructive" : "secondary"}
            className="ml-2"
          >
            <Clock className="h-3 w-3 mr-1" />
            {timeLeft}
          </Badge>
        </div>
        <CardDescription>Active OTP for User Initiated Linking</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div>
            <p className="text-sm font-medium">OTP</p>
            <p className="text-2xl font-bold tracking-wider">{activeOtp.otp}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Link Reference</p>
            <p className="text-sm text-muted-foreground truncate">
              {activeOtp.linkRefNumber}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Expires At</p>
            <p className="text-sm">
              {new Date(activeOtp.expiresAt).toLocaleString()}
            </p>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <SendUILOtpEmailButton
          patientId={patientId}
          disabled={timeLeft === "Expired"}
          className="w-full"
        />
      </CardFooter>
    </Card>
  );
}
