"use client";

import { useState, useEffect } from "react";
import { Fetch } from "@/services/fetch";
import { Button } from "@/components/ui/button";
// Card components are not used in this file
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Loader2, FileText, Download, Share } from "lucide-react";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";

interface DiagnosticReport {
  id: string;
  reportType: string;
  reportDate: string;
  status: string;
  category?: string;
  code: string;
  codeDisplay: string;
  conclusion?: string;
  performer?: string;
  specimen?: string;
  effectiveDate: string;
  issuedDate: string;
  createdAt: string;
  updatedAt: string;
}

interface DiagnosticReportListProps {
  patientId: string;
  consultationId?: string;
  refreshTrigger?: number;
}

export function DiagnosticReportList({
  patientId,
  consultationId,
  refreshTrigger = 0,
}: DiagnosticReportListProps) {
  const [reports, setReports] = useState<DiagnosticReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedReport, setSelectedReport] = useState<DiagnosticReport | null>(
    null,
  );

  useEffect(() => {
    const fetchReports = async () => {
      setLoading(true);
      setError(null);
      try {
        const url = consultationId
          ? `/api/diagnostic-report?consultationId=${consultationId}`
          : `/api/diagnostic-report?patientId=${patientId}`;

        const response = await Fetch.get(url);

        if (response.error) {
          throw new Error(response.error);
        }

        setReports(response.reports || []);
      } catch (err) {
        console.error("Error fetching diagnostic reports:", err);
        setError("Failed to load diagnostic reports");
        toast.error("Failed to load diagnostic reports");
      } finally {
        setLoading(false);
      }
    };

    fetchReports();
  }, [patientId, consultationId, refreshTrigger]);

  const handleExportFhir = async (reportId: string) => {
    try {
      const response = await Fetch.get(
        `/api/diagnostic-report/${reportId}/fhir`,
      );

      if (response.error) {
        throw new Error(response.error);
      }

      // Create a downloadable JSON file
      const blob = new Blob([JSON.stringify(response, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `diagnostic-report-${reportId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success("FHIR resource exported successfully");
    } catch (err) {
      console.error("Error exporting FHIR resource:", err);
      toast.error("Failed to export FHIR resource");
    }
  };

  const handleShareWithAbdm = async (reportId: string) => {
    try {
      const response = await Fetch.post("/api/abdm/health-record/package", {
        patientId,
        recordType: "diagnosticReport",
        recordId: reportId,
      });

      if (response.error) {
        throw new Error(response.error);
      }

      toast.success("Diagnostic report packaged for ABDM sharing");
    } catch (err) {
      console.error("Error packaging diagnostic report for ABDM:", err);
      toast.error("Failed to package diagnostic report for ABDM");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  if (reports.length === 0) {
    return (
      <div className="p-4 text-center">
        <p className="text-muted-foreground">No diagnostic reports found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Code</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {reports.map((report) => (
            <TableRow key={report.id}>
              <TableCell>{formatDate(new Date(report.reportDate))}</TableCell>
              <TableCell>
                <Badge variant="outline">{report.reportType}</Badge>
              </TableCell>
              <TableCell>
                <Badge
                  variant={
                    report.status === "final"
                      ? "default"
                      : report.status === "preliminary"
                        ? "secondary"
                        : report.status === "cancelled" ||
                            report.status === "entered-in-error"
                          ? "destructive"
                          : "outline"
                  }
                >
                  {report.status}
                </Badge>
              </TableCell>
              <TableCell>{report.codeDisplay}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedReport(report)}
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle>Diagnostic Report</DialogTitle>
                        <DialogDescription>
                          {selectedReport?.codeDisplay} -{" "}
                          {formatDate(
                            new Date(selectedReport?.reportDate || ""),
                          )}
                        </DialogDescription>
                      </DialogHeader>
                      {selectedReport && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h4 className="text-sm font-medium">
                                Report Type
                              </h4>
                              <p>{selectedReport.reportType}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Category</h4>
                              <p>{selectedReport.category || "N/A"}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Status</h4>
                              <p>{selectedReport.status}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Code</h4>
                              <p>
                                {selectedReport.code} (
                                {selectedReport.codeDisplay})
                              </p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Performer</h4>
                              <p>{selectedReport.performer || "N/A"}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">Specimen</h4>
                              <p>{selectedReport.specimen || "N/A"}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">
                                Effective Date
                              </h4>
                              <p>
                                {formatDate(
                                  new Date(selectedReport.effectiveDate),
                                )}
                              </p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">
                                Issued Date
                              </h4>
                              <p>
                                {formatDate(
                                  new Date(selectedReport.issuedDate),
                                )}
                              </p>
                            </div>
                          </div>

                          {selectedReport.conclusion && (
                            <div>
                              <h4 className="text-sm font-medium">
                                Conclusion
                              </h4>
                              <p className="whitespace-pre-wrap">
                                {selectedReport.conclusion}
                              </p>
                            </div>
                          )}

                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              onClick={() =>
                                handleExportFhir(selectedReport.id)
                              }
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Export FHIR
                            </Button>
                            <Button
                              onClick={() =>
                                handleShareWithAbdm(selectedReport.id)
                              }
                            >
                              <Share className="h-4 w-4 mr-1" />
                              Share with ABDM
                            </Button>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExportFhir(report.id)}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    FHIR
                  </Button>

                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleShareWithAbdm(report.id)}
                  >
                    <Share className="h-4 w-4 mr-1" />
                    ABDM
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
