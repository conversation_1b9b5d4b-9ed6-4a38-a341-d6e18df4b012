"use client";

import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  CheckCircle2,
  Clock,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";

interface ConsentStatusProps {
  consentId: string;
  initialStatus?: string;
  onStatusChange?: (status: string) => void;
}

export function ConsentStatus({
  consentId,
  initialStatus = "REQUESTED",
  onStatusChange,
}: ConsentStatusProps) {
  const [status, setStatus] = useState(initialStatus);
  const [isLoading, setIsLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date>(new Date());

  // Function to check consent status
  const checkStatus = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/abdm/consent/status", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ consentId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to check consent status");
      }

      // Update status
      setStatus(data.status);
      setLastChecked(new Date());

      // Call onStatusChange callback if provided
      if (onStatusChange && data.status !== status) {
        onStatusChange(data.status);
      }

      // Reload the page to ensure all components are updated
      window.location.reload();

      return data.status;
    } catch (error) {
      console.error("Error checking consent status:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to check consent status",
      );
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Check status on initial load
  useEffect(() => {
    checkStatus();

    // Set up polling for status updates (every 30 seconds)
    const intervalId = setInterval(() => {
      // Only poll if status is still REQUESTED
      if (status === "REQUESTED") {
        checkStatus();
      }
    }, 30000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [consentId, status]);

  // Function to revoke consent
  const revokeConsent = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/abdm/consent/revoke", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ consentId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to revoke consent");
      }

      // Update status
      setStatus("REVOKED");
      setLastChecked(new Date());

      // Call onStatusChange callback if provided
      if (onStatusChange) {
        onStatusChange("REVOKED");
      }

      toast.success("Consent revoked successfully");
    } catch (error) {
      console.error("Error revoking consent:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to revoke consent",
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Render status badge
  const renderStatusBadge = () => {
    switch (status) {
      case "REQUESTED":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case "GRANTED":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            Approved
          </Badge>
        );
      case "DENIED":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </Badge>
        );
      case "REVOKED":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700">
            <XCircle className="mr-1 h-3 w-3" />
            Revoked
          </Badge>
        );
      case "EXPIRED":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Expired
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <Clock className="mr-1 h-3 w-3" />
            {status}
          </Badge>
        );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Consent Status</span>
          {renderStatusBadge()}
        </CardTitle>
        <CardDescription>
          Last checked: {formatDistanceToNow(lastChecked, { addSuffix: true })}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-500">
          {status === "REQUESTED" &&
            "Waiting for patient to approve or deny this consent request."}
          {status === "GRANTED" &&
            "Patient has approved this consent request. You can now access their health records."}
          {status === "DENIED" &&
            "Patient has denied this consent request. You cannot access their health records."}
          {status === "REVOKED" &&
            "This consent has been revoked. You can no longer access the patient's health records."}
          {status === "EXPIRED" &&
            "This consent has expired. You can no longer access the patient's health records."}
        </p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={checkStatus}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="mr-2 h-4 w-4" />
          )}
          Refresh Status
        </Button>
        {status === "GRANTED" && (
          <Button
            variant="destructive"
            size="sm"
            onClick={revokeConsent}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <XCircle className="mr-2 h-4 w-4" />
            )}
            Revoke Consent
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
