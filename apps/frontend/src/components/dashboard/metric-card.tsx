"use client";

import { ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowUpRight, ArrowDownRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface MetricCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  description?: string;
  trend?: number;
  trendLabel?: string;
  loading?: boolean;
  className?: string;
  valueClassName?: string;
  onClick?: () => void;
}

export function MetricCard({
  title,
  value,
  icon,
  description,
  trend,
  trendLabel,
  loading = false,
  className,
  valueClassName,
  onClick,
}: MetricCardProps) {
  return (
    <Card
      className={cn(
        onClick ? "cursor-pointer transition-all hover:shadow-md" : "",
        className,
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon && <div className="h-4 w-4 text-muted-foreground">{icon}</div>}
      </CardHeader>
      <CardContent>
        {loading ? (
          <>
            <Skeleton className="h-8 w-24 mb-1" />
            {(description || trend !== undefined) && (
              <Skeleton className="h-4 w-32 mt-1" />
            )}
          </>
        ) : (
          <>
            <div className={cn("text-2xl font-bold", valueClassName)}>
              {value}
            </div>
            {description && (
              <div className="text-xs text-muted-foreground mt-1">
                {description}
              </div>
            )}
            {trend !== undefined && (
              <div className="flex items-center pt-1">
                <span
                  className={`text-xs ${trend > 0 ? "text-green-500" : trend < 0 ? "text-red-500" : "text-gray-500"}`}
                >
                  {trend > 0 ? (
                    <ArrowUpRight className="h-3 w-3 inline mr-1" />
                  ) : trend < 0 ? (
                    <ArrowDownRight className="h-3 w-3 inline mr-1" />
                  ) : null}
                  {isNaN(trend) ? 0 : Math.abs(trend)}%
                </span>
                {trendLabel && (
                  <span className="text-xs text-muted-foreground ml-1">
                    {trendLabel}
                  </span>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
