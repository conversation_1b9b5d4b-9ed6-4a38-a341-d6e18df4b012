"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { DateRange } from "react-day-picker";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon, Filter, X } from "lucide-react";
import { cn } from "@/lib/utils";

// Time range options
export type TimeRange =
  | "today"
  | "yesterday"
  | "week"
  | "month"
  | "quarter"
  | "year"
  | "custom";

interface TimeRangeOption {
  value: TimeRange;
  label: string;
}

const TIME_RANGE_OPTIONS: TimeRangeOption[] = [
  { value: "today", label: "Today" },
  { value: "yesterday", label: "Yesterday" },
  { value: "week", label: "Last 7 days" },
  { value: "month", label: "This month" },
  { value: "quarter", label: "Last 3 months" },
  { value: "year", label: "This year" },
  { value: "custom", label: "Custom range" },
];

// Branch filter
interface BranchOption {
  value: string;
  label: string;
}

// Dashboard filters props
interface DashboardFiltersProps {
  timeRange: TimeRange;
  onTimeRangeChange: (range: TimeRange) => void;
  branches?: BranchOption[];
  selectedBranchId?: string;
  onBranchChange?: (branchId: string) => void;
  customDateRange?: {
    from: Date | undefined;
    to: Date | undefined;
  };
  onCustomDateRangeChange?: (range: {
    from: Date | undefined;
    to: Date | undefined;
  }) => void;
  onApplyFilters?: () => void;
  onResetFilters?: () => void;
  className?: string;
}

export function DashboardFilters({
  timeRange,
  onTimeRangeChange,
  branches,
  selectedBranchId,
  onBranchChange,
  customDateRange,
  onCustomDateRangeChange,
  onApplyFilters,
  onResetFilters,
  className,
}: DashboardFiltersProps) {
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>(customDateRange || { from: undefined, to: undefined });
  const [isCustomDateOpen, setIsCustomDateOpen] = useState(false);

  // Update local date range when prop changes
  useEffect(() => {
    if (customDateRange) {
      setDateRange(customDateRange);
    }
  }, [customDateRange]);

  // Handle time range change
  const handleTimeRangeChange = (value: string) => {
    const newRange = value as TimeRange;
    onTimeRangeChange(newRange);

    // If custom range, open the date picker
    if (newRange === "custom") {
      setIsCustomDateOpen(true);
    } else {
      setIsCustomDateOpen(false);
    }
  };

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range) {
      const newRange = { from: range.from, to: range.to };
      setDateRange(newRange);
      if (onCustomDateRangeChange) {
        onCustomDateRangeChange(newRange);
      }
    }
  };

  // Format date range for display
  const formatDateRange = () => {
    if (!dateRange.from) return "Select dates";
    if (!dateRange.to) return format(dateRange.from, "MMM d, yyyy");
    return `${format(dateRange.from, "MMM d, yyyy")} - ${format(dateRange.to, "MMM d, yyyy")}`;
  };

  // Apply filters
  const handleApplyFilters = () => {
    if (onApplyFilters) {
      onApplyFilters();
    }
    setIsCustomDateOpen(false);
  };

  // Reset filters
  const handleResetFilters = () => {
    if (onResetFilters) {
      onResetFilters();
    }
    setDateRange({ from: undefined, to: undefined });
    setIsCustomDateOpen(false);
  };

  return (
    <div
      className={cn(
        "flex flex-col sm:flex-row items-start sm:items-center gap-2",
        className,
      )}
    >
      {/* Time Range Filter */}
      <div className="flex-1 min-w-[150px]">
        <Select value={timeRange} onValueChange={handleTimeRangeChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            {TIME_RANGE_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Custom Date Range */}
      {timeRange === "custom" && (
        <Popover open={isCustomDateOpen} onOpenChange={setIsCustomDateOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "justify-start text-left font-normal",
                !dateRange.from && "text-muted-foreground",
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {formatDateRange()}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={dateRange.from}
              selected={dateRange}
              onSelect={handleDateRangeChange}
              numberOfMonths={2}
            />
            <div className="flex items-center justify-between p-3 border-t">
              <Button variant="ghost" size="sm" onClick={handleResetFilters}>
                Reset
              </Button>
              <Button size="sm" onClick={handleApplyFilters}>
                Apply
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      )}

      {/* Branch Filter */}
      {branches && branches.length > 0 && onBranchChange && (
        <div className="flex-1 min-w-[150px]">
          <Select
            value={selectedBranchId || "all"}
            onValueChange={onBranchChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select branch" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Branches</SelectItem>
              {branches.map((branch) => (
                <SelectItem key={branch.value} value={branch.value}>
                  {branch.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Apply/Reset Buttons */}
      {(onApplyFilters || onResetFilters) && (
        <div className="flex items-center space-x-2">
          {onApplyFilters && (
            <Button size="sm" onClick={onApplyFilters}>
              <Filter className="h-4 w-4 mr-1" />
              Apply
            </Button>
          )}
          {onResetFilters && (
            <Button variant="outline" size="sm" onClick={onResetFilters}>
              <X className="h-4 w-4 mr-1" />
              Reset
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
