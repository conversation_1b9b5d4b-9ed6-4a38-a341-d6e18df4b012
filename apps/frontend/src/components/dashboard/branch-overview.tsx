"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>it<PERSON>,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowRight,
  Building,
  Users,
  Calendar,
  Stethoscope,
  ShieldCheck,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

interface BranchMetric {
  id: string;
  name: string;
  patients: number;
  appointments: number;
  consultations: number;
  abhaLinked: number;
  abhaPercentage: number;
  doctors: number;
  staff: number;
}

interface BranchOverviewProps {
  branches: BranchMetric[];
  loading?: boolean;
  onSelectBranch?: (branchId: string) => void;
}

export function BranchOverview({
  branches,
  loading = false,
  onSelectBranch,
}: BranchOverviewProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-[250px]" />
          <Skeleton className="h-4 w-[300px]" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="p-4 border rounded-lg">
              <div className="flex justify-between items-center mb-4">
                <Skeleton className="h-6 w-[200px]" />
                <Skeleton className="h-6 w-[100px]" />
              </div>
              <div className="grid grid-cols-4 gap-4">
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (!branches || branches.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Branch Overview</CardTitle>
          <CardDescription>
            Performance metrics across all branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Building className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No branches found</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Add branches to see performance metrics here
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Branch Overview</CardTitle>
        <CardDescription>Performance metrics for each branch</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {branches.map((branch) => (
          <div
            key={branch.id}
            className="p-4 border rounded-lg hover:border-primary/50 hover:bg-muted/50 transition-colors"
          >
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                  <Building className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-medium">{branch.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {branch.doctors} doctors, {branch.staff} staff
                  </p>
                </div>
              </div>
              <Badge variant={branch.appointments > 0 ? "default" : "outline"}>
                {branch.appointments > 0 ? "Active" : "Inactive"}
              </Badge>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-1">
                <div className="text-sm font-medium flex items-center">
                  <Users className="h-4 w-4 mr-1 text-muted-foreground" />
                  Patients
                </div>
                <div className="text-2xl font-bold">
                  {branch.patients.toLocaleString()}
                </div>
              </div>

              <div className="space-y-1">
                <div className="text-sm font-medium flex items-center">
                  <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                  Appointments
                </div>
                <div className="text-2xl font-bold">
                  {branch.appointments.toLocaleString()}
                </div>
              </div>

              <div className="space-y-1">
                <div className="text-sm font-medium flex items-center">
                  <Stethoscope className="h-4 w-4 mr-1 text-muted-foreground" />
                  Consultations
                </div>
                <div className="text-2xl font-bold">
                  {branch.consultations.toLocaleString()}
                </div>
              </div>

              <div className="space-y-1">
                <div className="text-sm font-medium flex items-center">
                  <ShieldCheck className="h-4 w-4 mr-1 text-muted-foreground" />
                  ABHA Linked
                </div>
                <div className="flex items-center">
                  <div className="text-2xl font-bold mr-2">
                    {isNaN(branch.abhaPercentage) ? 0 : branch.abhaPercentage}%
                  </div>
                  <Progress
                    value={
                      isNaN(branch.abhaPercentage) ? 0 : branch.abhaPercentage
                    }
                    className="h-2 flex-1"
                  />
                </div>
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSelectBranch?.(branch.id)}
              >
                View Details
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full">
          Manage Branches
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
}
