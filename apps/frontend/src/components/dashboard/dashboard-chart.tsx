"use client";

import { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
} from "recharts";
import {
  <PERSON><PERSON><PERSON>3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  LineChart as LineChartIcon,
  AreaChart as AreaChartIcon,
  Download,
  RefreshCw,
} from "lucide-react";
import { downloadCSV } from "@/lib/export-utils";

// Chart types
type ChartType = "bar" | "pie" | "line" | "area";

// Chart props
interface DashboardChartProps {
  title: string;
  description?: string;
  data: any[];
  loading?: boolean;
  type?: ChartType;
  xAxisKey?: string;
  dataKey?: string;
  nameKey?: string;
  valueKey?: string;
  categories?: { key: string; name: string; color?: string }[];
  colors?: string[];
  height?: number;
  showLegend?: boolean;
  allowTypeChange?: boolean;
  exportable?: boolean;
  exportFilename?: string;
  onRefresh?: () => void;
  emptyMessage?: string;
}

// Default colors
const DEFAULT_COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#8dd1e1",
  "#a4de6c",
  "#d0ed57",
];

export function DashboardChart({
  title,
  description,
  data,
  loading = false,
  type = "bar",
  xAxisKey,
  dataKey,
  nameKey = "name",
  valueKey = "value",
  categories,
  colors = DEFAULT_COLORS,
  height = 300,
  showLegend = true,
  allowTypeChange = true,
  exportable = true,
  exportFilename = "chart-data",
  onRefresh,
  emptyMessage = "No data available",
}: DashboardChartProps) {
  const [chartType, setChartType] = useState<ChartType>(type);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle export
  const handleExport = () => {
    if (!exportable || data.length === 0) return;

    // Download CSV
    downloadCSV(
      data,
      `${exportFilename}_${new Date().toISOString().split("T")[0]}.csv`,
    );
  };

  // Handle refresh
  const handleRefresh = async () => {
    if (!onRefresh) return;

    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  // Render chart based on type
  const renderChart = () => {
    if (loading) {
      return <Skeleton className="w-full h-[300px] rounded-md" />;
    }

    if (!data || data.length === 0) {
      return (
        <div className="flex items-center justify-center h-[300px] text-muted-foreground">
          {emptyMessage}
        </div>
      );
    }

    switch (chartType) {
      case "bar":
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey={xAxisKey || nameKey}
                tick={{ fontSize: 12 }}
                tickLine={false}
              />
              <YAxis tick={{ fontSize: 12 }} tickLine={false} />
              <Tooltip />
              {showLegend && <Legend />}
              {categories ? (
                categories.map((category, index) => (
                  <Bar
                    key={category.key}
                    dataKey={category.key}
                    name={category.name}
                    fill={category.color || colors[index % colors.length]}
                  />
                ))
              ) : (
                <Bar
                  dataKey={dataKey || valueKey}
                  fill={colors[0]}
                  radius={[4, 4, 0, 0]}
                />
              )}
            </BarChart>
          </ResponsiveContainer>
        );

      case "pie":
        // Create a simple custom pie chart with divs if data is available
        if (data && data.length > 0) {
          const total = data.reduce(
            (sum, item) => sum + (item[valueKey] || 0),
            0,
          );

          // If no data, show empty message
          if (total === 0) {
            return (
              <div className="flex items-center justify-center h-full">
                <p className="text-muted-foreground">{emptyMessage}</p>
              </div>
            );
          }

          return (
            <div className="w-full h-full flex flex-col items-center justify-center">
              {/* Pie chart visualization */}
              <div className="relative w-48 h-48 mb-4">
                {data.map((entry, index) => {
                  const value = entry[valueKey] || 0;
                  const percentage = total > 0 ? (value / total) * 100 : 0;
                  const color = colors[index % colors.length];

                  // For simplicity, just show two segments side by side
                  return (
                    <div
                      key={`segment-${index}`}
                      className="absolute top-0 left-0 w-full h-full flex items-center justify-center"
                      style={{
                        clipPath:
                          index === 0
                            ? `polygon(50% 50%, 50% 0%, ${50 + percentage / 2}% 0%, 100% 0%, 100% 100%, 50% 100%, 50% 50%)`
                            : `polygon(50% 50%, 50% 0%, 0% 0%, 0% 100%, 50% 100%, 50% 50%)`,
                        backgroundColor: color,
                        zIndex: 10 - index,
                      }}
                    >
                      {percentage > 20 && (
                        <span className="text-white font-bold text-lg">
                          {Math.round(percentage)}%
                        </span>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Legend */}
              {showLegend && (
                <div className="flex flex-wrap justify-center gap-4 mt-2">
                  {data.map((entry, index) => (
                    <div key={`legend-${index}`} className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{
                          backgroundColor: colors[index % colors.length],
                        }}
                      />
                      <span className="text-sm">
                        {entry[nameKey]} ({entry[valueKey] || 0})
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        }

        // Fallback to empty message
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-muted-foreground">{emptyMessage}</p>
          </div>
        );

      case "line":
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey={xAxisKey || nameKey}
                tick={{ fontSize: 12 }}
                tickLine={false}
              />
              <YAxis tick={{ fontSize: 12 }} tickLine={false} />
              <Tooltip />
              {showLegend && <Legend />}
              {categories ? (
                categories.map((category, index) => (
                  <Line
                    key={category.key}
                    type="monotone"
                    dataKey={category.key}
                    name={category.name}
                    stroke={category.color || colors[index % colors.length]}
                    activeDot={{ r: 8 }}
                  />
                ))
              ) : (
                <Line
                  type="monotone"
                  dataKey={dataKey || valueKey}
                  stroke={colors[0]}
                  activeDot={{ r: 8 }}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        );

      case "area":
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey={xAxisKey || nameKey}
                tick={{ fontSize: 12 }}
                tickLine={false}
              />
              <YAxis tick={{ fontSize: 12 }} tickLine={false} />
              <Tooltip />
              {showLegend && <Legend />}
              {categories ? (
                categories.map((category, index) => (
                  <Area
                    key={category.key}
                    type="monotone"
                    dataKey={category.key}
                    name={category.name}
                    fill={category.color || colors[index % colors.length]}
                    stroke={category.color || colors[index % colors.length]}
                    fillOpacity={0.3}
                  />
                ))
              ) : (
                <Area
                  type="monotone"
                  dataKey={dataKey || valueKey}
                  fill={colors[0]}
                  stroke={colors[0]}
                  fillOpacity={0.3}
                />
              )}
            </AreaChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </div>
        <div className="flex items-center space-x-2">
          {onRefresh && (
            <Button
              variant="ghost"
              size="icon"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw
                className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
              />
            </Button>
          )}
          {exportable && (
            <Button
              variant="ghost"
              size="icon"
              onClick={handleExport}
              disabled={!data || data.length === 0}
            >
              <Download className="h-4 w-4" />
            </Button>
          )}
          {allowTypeChange && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  {chartType === "bar" ? (
                    <BarChart3 className="h-4 w-4" />
                  ) : chartType === "pie" ? (
                    <PieChartIcon className="h-4 w-4" />
                  ) : chartType === "line" ? (
                    <LineChartIcon className="h-4 w-4" />
                  ) : (
                    <AreaChartIcon className="h-4 w-4" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setChartType("bar")}>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Bar Chart
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setChartType("pie")}>
                  <PieChartIcon className="h-4 w-4 mr-2" />
                  Pie Chart
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setChartType("line")}>
                  <LineChartIcon className="h-4 w-4 mr-2" />
                  Line Chart
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setChartType("area")}>
                  <AreaChartIcon className="h-4 w-4 mr-2" />
                  Area Chart
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>
      <CardContent>{renderChart()}</CardContent>
    </Card>
  );
}
