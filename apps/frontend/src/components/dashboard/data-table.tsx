"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
// Dropdown menu removed as it's not used
import { Skeleton } from "@/components/ui/skeleton";
import { downloadCSV } from "@/lib/export-utils";
import {
  Search,
  Download,
  ChevronDown,
  ChevronUp,
  RefreshCw,
} from "lucide-react";

interface Column<T> {
  key: keyof T | string;
  title: string;
  render?: (row: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  align?: "left" | "center" | "right";
  exportable?: boolean;
  exportKey?: keyof T;
  exportLabel?: string;
}

interface DataTableProps<T extends Record<string, any>> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  searchable?: boolean;
  searchPlaceholder?: string;
  exportable?: boolean;
  exportFilename?: string;
  pagination?: boolean;
  pageSize?: number;
  onRefresh?: () => void;
  emptyMessage?: string;
  className?: string;
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  searchable = true,
  searchPlaceholder = "Search...",
  exportable = true,
  exportFilename = "export",
  pagination = true,
  pageSize = 10,
  onRefresh,
  emptyMessage = "No data available",
  className,
}: DataTableProps<T>) {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortKey, setSortKey] = useState<keyof T | string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [currentPage, setCurrentPage] = useState(1);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Reset pagination when data changes
  useEffect(() => {
    setCurrentPage(1);
  }, [data]);

  // Handle search
  const filteredData =
    searchable && searchQuery
      ? data.filter((row) =>
          Object.entries(row).some(([_, value]) => {
            // Skip non-string values
            if (typeof value !== "string" && typeof value !== "number") {
              return false;
            }
            return String(value)
              .toLowerCase()
              .includes(searchQuery.toLowerCase());
          }),
        )
      : data;

  // Handle sorting
  const sortedData = sortKey
    ? [...filteredData].sort((a, b) => {
        const aValue = a[sortKey as keyof T];
        const bValue = b[sortKey as keyof T];

        // Handle different types
        if (typeof aValue === "string" && typeof bValue === "string") {
          return sortDirection === "asc"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        } else if (
          (typeof aValue === "number" && typeof bValue === "number") ||
          (typeof aValue === "boolean" && typeof bValue === "boolean")
        ) {
          return sortDirection === "asc"
            ? aValue > bValue
              ? 1
              : -1
            : aValue < bValue
              ? 1
              : -1;
        } else if (
          aValue &&
          bValue &&
          typeof aValue === "object" &&
          typeof bValue === "object" &&
          "getTime" in aValue &&
          "getTime" in bValue &&
          typeof aValue.getTime === "function" &&
          typeof bValue.getTime === "function"
        ) {
          const aTime = (aValue as unknown as Date).getTime();
          const bTime = (bValue as unknown as Date).getTime();
          return sortDirection === "asc" ? aTime - bTime : bTime - aTime;
        }

        // Default comparison
        return 0;
      })
    : filteredData;

  // Handle pagination
  const totalPages = pagination ? Math.ceil(sortedData.length / pageSize) : 1;
  const paginatedData = pagination
    ? sortedData.slice((currentPage - 1) * pageSize, currentPage * pageSize)
    : sortedData;

  // Handle sort
  const handleSort = (key: keyof T | string) => {
    if (sortKey === key) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortKey(key);
      setSortDirection("asc");
    }
  };

  // Handle export
  const handleExport = () => {
    if (!exportable || sortedData.length === 0) return;

    // Prepare export headers
    const exportHeaders = columns
      .filter((col) => col.exportable !== false)
      .map((col) => ({
        key: col.exportKey || (col.key as keyof T),
        label: col.exportLabel || col.title,
      }));

    // Download CSV
    downloadCSV(
      sortedData,
      `${exportFilename}_${new Date().toISOString().split("T")[0]}.csv`,
      exportHeaders as { key: keyof T; label: string }[],
    );
  };

  // Handle refresh
  const handleRefresh = async () => {
    if (!onRefresh) return;

    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className={className}>
      {/* Table Controls */}
      {(searchable || exportable || onRefresh) && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
          {searchable && (
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
          )}
          <div className="flex items-center space-x-2 ml-auto">
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw
                  className={`h-4 w-4 mr-1 ${
                    isRefreshing ? "animate-spin" : ""
                  }`}
                />
                Refresh
              </Button>
            )}
            {exportable && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                disabled={sortedData.length === 0}
              >
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Table */}
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead
                  key={column.key.toString()}
                  className={`${column.width ? column.width : ""} ${
                    column.align ? `text-${column.align}` : ""
                  }`}
                >
                  {column.sortable ? (
                    <button
                      className="flex items-center space-x-1 focus:outline-none"
                      onClick={() => handleSort(column.key)}
                    >
                      <span>{column.title}</span>
                      {sortKey === column.key ? (
                        sortDirection === "asc" ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )
                      ) : (
                        <ChevronDown className="h-4 w-4 opacity-0 group-hover:opacity-50" />
                      )}
                    </button>
                  ) : (
                    column.title
                  )}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              // Loading state
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`loading-${index}`}>
                  {columns.map((column) => (
                    <TableCell
                      key={`loading-${index}-${column.key.toString()}`}
                      className={column.align ? `text-${column.align}` : ""}
                    >
                      <Skeleton className="h-5 w-full" />
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : paginatedData.length > 0 ? (
              // Data rows
              paginatedData.map((row, rowIndex) => (
                <TableRow key={`row-${rowIndex}`}>
                  {columns.map((column) => (
                    <TableCell
                      key={`cell-${rowIndex}-${column.key.toString()}`}
                      className={column.align ? `text-${column.align}` : ""}
                    >
                      {column.render
                        ? column.render(row)
                        : row[column.key as keyof T] !== undefined
                          ? String(row[column.key as keyof T])
                          : ""}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              // Empty state
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {emptyMessage}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && totalPages > 1 && (
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * pageSize + 1} to{" "}
            {Math.min(currentPage * pageSize, sortedData.length)} of{" "}
            {sortedData.length} entries
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around current page
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }
              return pageNum;
            }).map((pageNum) => (
              <Button
                key={`page-${pageNum}`}
                variant={currentPage === pageNum ? "default" : "outline"}
                size="sm"
                onClick={() => setCurrentPage(pageNum)}
              >
                {pageNum}
              </Button>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
