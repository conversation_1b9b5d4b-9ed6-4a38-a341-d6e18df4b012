"use client";

import { useState, useRef } from "react";
import React from "react";
import { CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, Download, RefreshCw } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";
import html2canvas from "html2canvas";

interface AbhaCardIframeProps {
  abhaNumber: string;
  patientId?: string;
  onError?: (error: string) => void;
  onTokenExpired?: () => void;
  fullPage?: boolean;
}

export function AbhaCardIframe({
  abhaNumber,
  patientId,
  onError,
  onTokenExpired,
  fullPage = false,
}: AbhaCardIframeProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeKey, setIframeKey] = useState(Date.now()); // Used to force iframe refresh

  // Function to handle iframe load events
  const handleIframeLoad = () => {
    setLoading(false);

    // Check if the iframe loaded successfully
    try {
      // If we can access the contentDocument, it means the iframe loaded from the same origin
      const iframeDoc = iframeRef.current?.contentDocument;
      if (iframeDoc) {
        // Check if the document contains an image or PDF embed
        const hasImage = iframeDoc.querySelector('img[src^="data:image"]');
        const hasPdf = iframeDoc.querySelector(
          'embed[src^="data:application/pdf"]',
        );

        if (hasImage || hasPdf) {
          // Successfully loaded image or PDF
          console.log("ABHA card loaded successfully");
          return;
        }

        // Try to extract error message from the document
        const errorText = iframeDoc.body.textContent || "";
        console.error("ABHA card iframe error:", errorText);

        if (errorText) {
          // Check for token expiration
          if (
            errorText.includes("TOKEN_EXPIRED") ||
            errorText.includes("ABDM-1094") ||
            errorText.toLowerCase().includes("token expired") ||
            errorText.toLowerCase().includes("x-token expired")
          ) {
            setError("Your ABHA session has expired. Please login again.");
            onTokenExpired?.();
          } else if (errorText.includes("PNG") || errorText.includes("�PNG")) {
            // Raw PNG data detected
            setError("Invalid response format. Please try again.");
            onError?.("Raw PNG data received instead of HTML");
          } else {
            setError("Failed to load ABHA card. Please try again.");
            onError?.(errorText);
          }
        }
      }
    } catch (e) {
      // If we get a cross-origin error, it means the iframe loaded from the ABDM API
      // which is expected and good
      console.log("ABHA card iframe loaded successfully (cross-origin)");
    }
  };

  // Function to handle iframe errors
  const handleIframeError = () => {
    setLoading(false);
    setError("Failed to load ABHA card. Please try again.");
    onError?.("Iframe load error");
  };

  // Function to refresh the iframe
  const refreshIframe = () => {
    setLoading(true);
    setError(null);
    setIframeKey(Date.now()); // Change the key to force a complete reload
  };

  // Function to save the ABHA card as an image
  const saveAsImage = async () => {
    if (!iframeRef.current) {
      toast.error("Cannot capture ABHA card. Please try again.");
      return;
    }

    try {
      toast.info("Preparing ABHA card for download...");

      // Call our API to get the card data
      const response = await fetch(
        `/api/abdm/abha-card/download-stream?patientId=${patientId}`,
      );

      if (!response.ok) {
        const errorData = await response.json();

        // Check for token expiration
        if (errorData.code === "TOKEN_EXPIRED") {
          toast.error("Your ABHA session has expired. Please login again.");
          onTokenExpired?.();
          return;
        }

        toast.error(errorData.error || "Failed to download ABHA card");
        return;
      }

      const data = await response.json();

      if (!data.cardData) {
        toast.error("Failed to download ABHA card. No data received.");
        return;
      }

      // Create a link element to trigger the browser's native save dialog
      const link = document.createElement("a");
      link.href = data.cardData;
      link.download = `ABHA_Card_${abhaNumber}.${
        data.contentType.includes("pdf") ? "pdf" : "png"
      }`;

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();

      // Small delay before removing the link
      setTimeout(() => {
        document.body.removeChild(link);
      }, 100);

      toast.success("ABHA card downloaded successfully");

      // Fallback for browsers that don't support the download attribute
      setTimeout(() => {
        // If the download didn't start, try to capture as image
        try {
          // First try to capture the iframe content directly
          const iframeDocument = iframeRef.current?.contentDocument;
          if (iframeDocument && iframeDocument.body) {
            html2canvas(iframeDocument.body, {
              allowTaint: true,
              useCORS: true,
              logging: false,
            })
              .then((canvas) => {
                // Convert canvas to data URL and trigger download
                const imageUrl = canvas.toDataURL("image/png");
                const imgLink = document.createElement("a");
                imgLink.href = imageUrl;
                imgLink.download = `ABHA_Card_${abhaNumber}.png`;
                document.body.appendChild(imgLink);
                imgLink.click();
                document.body.removeChild(imgLink);
              })
              .catch((err) => {
                console.error("Error capturing iframe content:", err);
                // Try fallback method
                captureIframeContainer();
              });
          } else {
            captureIframeContainer();
          }
        } catch (error) {
          console.log("Error in fallback capture:", error);
          // No need to show error to user as this is just a fallback
        }
      }, 3000); // Wait 3 seconds to see if the download started
    } catch (error) {
      console.error("Error downloading ABHA card:", error);
      toast.error("Failed to download ABHA card");
    }
  };

  // Helper function to capture the iframe container as fallback
  const captureIframeContainer = async () => {
    try {
      const iframeContainer = iframeRef.current?.parentElement;
      if (iframeContainer) {
        const canvas = await html2canvas(iframeContainer, {
          allowTaint: true,
          useCORS: true,
          logging: false,
        });

        // Convert canvas to data URL and trigger download
        const imageUrl = canvas.toDataURL("image/png");
        const link = document.createElement("a");
        link.href = imageUrl;
        link.download = `ABHA_Card_${abhaNumber}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Error in container capture fallback:", error);
    }
  };

  // Add event listener for postMessage to handle download requests from parent
  React.useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data === "download") {
        saveAsImage();
      }
    };

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, []);

  return (
    <>
      {error ? (
        <CardContent className="p-4">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={refreshIframe} className="w-full">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      ) : (
        <>
          <div
            className="relative w-full"
            style={{ height: fullPage ? "100%" : "500px" }}
          >
            {loading && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
                  <p className="text-sm text-muted-foreground">
                    Loading ABHA card...
                  </p>
                </div>
              </div>
            )}
            <iframe
              key={iframeKey}
              ref={iframeRef}
              src={`/api/abdm/abha-card/download?patientId=${encodeURIComponent(
                patientId || "",
              )}`}
              className="w-full h-full border-0"
              onLoad={handleIframeLoad}
              onError={() => handleIframeError()}
              sandbox="allow-same-origin allow-scripts"
              referrerPolicy="no-referrer"
            />
          </div>
          {!fullPage && (
            <div className="p-3 border-t flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={saveAsImage}
                className="border-primary/30 hover:border-primary hover:bg-primary/5"
              >
                <Download className="h-4 w-4 mr-2" />
                Save ABHA Card
              </Button>
            </div>
          )}
        </>
      )}
    </>
  );
}
