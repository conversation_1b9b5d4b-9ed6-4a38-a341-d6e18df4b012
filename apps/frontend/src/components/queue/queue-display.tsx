"use client";

import { useQueue } from "@/hooks/useQueue";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  AlertCircle,
  CheckCircle2,
  Clock,
  Pause,
  Play,
  FileText,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface QueueDisplayProps {
  doctorId?: string;
  showControls?: boolean;
}

export function QueueDisplay({
  doctorId,
  showControls = false,
}: QueueDisplayProps) {
  const { queueItems, isLoading, error, updateQueueItem } = useQueue(doctorId);
  const router = useRouter();
  const [consultations, setConsultations] = useState<Record<string, string>>(
    {},
  );

  // Fetch consultations for appointments in the queue
  useEffect(() => {
    if (!queueItems.length) return;

    const fetchConsultations = async () => {
      try {
        // Fetch consultations for appointments in progress
        const response = await fetch(`/api/consultations?status=in-progress`);
        if (response.ok) {
          const data = await response.json();
          const consultationMap: Record<string, string> = {};

          // Create a map of appointmentId -> consultationId
          data.consultations.forEach((consultation: any) => {
            if (consultation.appointmentId) {
              consultationMap[consultation.appointmentId] = consultation.id;
            }
          });

          setConsultations(consultationMap);
        }
      } catch (error) {
        console.error("Error fetching consultations:", error);
      }
    };

    fetchConsultations();
  }, [queueItems]);

  // Navigate to consultation
  const navigateToConsultation = (appointmentId: string) => {
    const consultationId = consultations[appointmentId];
    if (consultationId) {
      router.push(`/consultations/${consultationId}`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "waiting":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "in-consultation":
        return "bg-blue-100 text-blue-800 border-blue-300";
      case "paused":
        return "bg-purple-100 text-purple-800 border-purple-300";
      case "completed":
        return "bg-green-100 text-green-800 border-green-300";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "waiting":
        return <Clock className="h-4 w-4 mr-1" />;
      case "in-consultation":
        return <Play className="h-4 w-4 mr-1" />;
      case "paused":
        return <Pause className="h-4 w-4 mr-1" />;
      case "completed":
        return <CheckCircle2 className="h-4 w-4 mr-1" />;
      case "cancelled":
        return <AlertCircle className="h-4 w-4 mr-1" />;
      default:
        return null;
    }
  };

  if (error) {
    return (
      <Card className="border-red-300">
        <CardHeader>
          <CardTitle className="text-red-600">Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Queue Status
        </CardTitle>
        <CardDescription>
          {doctorId
            ? "Queue for the selected doctor"
            : "All patients in the queue for this branch"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="flex items-center justify-between p-4 border rounded-md"
              >
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[150px]" />
                </div>
                <Skeleton className="h-6 w-[100px]" />
              </div>
            ))}
          </div>
        ) : queueItems.length === 0 ? (
          <p className="text-center py-8 text-gray-500">
            No patients in the queue
          </p>
        ) : (
          <div className="space-y-4">
            {queueItems.map((item) => (
              <div
                key={item.id}
                className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-md"
              >
                <div className="space-y-1">
                  <p className="font-medium">{item.patientName}</p>
                  <div className="flex items-center">
                    <Badge
                      variant="outline"
                      className={`flex items-center ${getStatusColor(item.status)}`}
                    >
                      {getStatusIcon(item.status)}
                      {item.status.replace("-", " ")}
                    </Badge>
                    <span className="ml-2 text-sm text-gray-500">
                      Position: {item.position}
                    </span>
                  </div>
                </div>

                {showControls && (
                  <div className="flex gap-2 mt-3 sm:mt-0">
                    {item.status === "waiting" && (
                      <Button
                        size="sm"
                        onClick={() =>
                          updateQueueItem(item.id, "in-consultation")
                        }
                      >
                        Start
                      </Button>
                    )}
                    {item.status === "in-consultation" && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQueueItem(item.id, "paused")}
                        >
                          Pause
                        </Button>
                        <Button
                          size="sm"
                          variant="default"
                          onClick={() => updateQueueItem(item.id, "completed")}
                        >
                          Complete
                        </Button>
                        {consultations[item.appointmentId] && (
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() =>
                              navigateToConsultation(item.appointmentId)
                            }
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            View
                          </Button>
                        )}
                      </>
                    )}
                    {item.status === "paused" && (
                      <Button
                        size="sm"
                        onClick={() =>
                          updateQueueItem(item.id, "in-consultation")
                        }
                      >
                        Resume
                      </Button>
                    )}
                    {(item.status === "waiting" ||
                      item.status === "paused") && (
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => updateQueueItem(item.id, "cancelled")}
                      >
                        Cancel
                      </Button>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
