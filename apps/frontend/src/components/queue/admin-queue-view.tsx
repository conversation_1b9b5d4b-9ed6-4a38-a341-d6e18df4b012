"use client";

import { useState, useEffect } from "react";
import { QueueDashboard } from "./queue-dashboard";
import { QueueStats } from "./queue-stats";
import { QueueNotification } from "./queue-notification";
import { useQueue } from "@/hooks/useQueue";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { useBranch } from "@/contexts/branch-context";

interface Doctor {
  id: string;
  name: string;
}

export function AdminQueueView() {
  const router = useRouter();
  const { currentBranch } = useBranch();
  const [selectedDoctor, setSelectedDoctor] = useState<string | undefined>(
    undefined,
  );
  // Initialize with today's date, ensuring it's set to the local date
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(() => {
    const today = new Date();
    // Create a new date object with just the year, month, and day to avoid time issues
    return new Date(today.getFullYear(), today.getMonth(), today.getDate());
  });
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  // Using a loading state for doctors fetch
  const [, setIsLoading] = useState(true);
  const { queueItems, isLoading: queueLoading } = useQueue(
    selectedDoctor,
    selectedDate,
  );

  // Fetch doctors for the current branch
  useEffect(() => {
    if (!currentBranch?.id) return;

    const fetchDoctors = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(
          `/api/doctors?branchId=${currentBranch.id}`,
        );

        if (response.ok) {
          const data = await response.json();

          // Format doctors for select dropdown
          const formattedDoctors = data.doctors.map((doctor: any) => ({
            id: doctor.id,
            name: doctor.user.name,
          }));

          setDoctors(formattedDoctors);
        }
      } catch (error) {
        console.error("Error fetching doctors:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDoctors();
  }, [currentBranch?.id]);

  return (
    <div className="space-y-6">
      <QueueNotification doctorId={selectedDoctor} />
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Queue Management
          </h1>
          <p className="text-muted-foreground">
            Manage patient queue and track consultation status
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push("/queue/display")}
          >
            Public Display
          </Button>
          <Button onClick={() => router.push("/appointments/new")}>
            New Appointment
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Queue Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="w-full sm:w-1/3">
              <label className="text-sm font-medium mb-1 block">Doctor</label>
              <Select
                value={selectedDoctor}
                onValueChange={(value) =>
                  setSelectedDoctor(value === "all" ? undefined : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Doctors" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Doctors</SelectItem>
                  {doctors.map((doctor) => (
                    <SelectItem key={doctor.id} value={doctor.id}>
                      {doctor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-1/3">
              <label className="text-sm font-medium mb-1 block">Date</label>
              <div className="w-full">
                <DatePicker date={selectedDate} setDate={setSelectedDate} />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <QueueStats queueItems={queueItems} isLoading={queueLoading} />

      <QueueDashboard
        doctorId={selectedDoctor}
        showControls={true}
        isAdmin={true}
        selectedDate={selectedDate}
      />
    </div>
  );
}
