"use client";

import { useState, useEffect, useCallback } from "react";
import { useQueue, QueueItem } from "@/hooks/useQueue";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";

import {
  AlertCircle,
  CheckCircle2,
  Clock,
  Pause,
  Play,
  FileText,
  User,
  Calendar,
  MapPin,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useSocket } from "@/components/providers/socket-provider";
import { formatDistanceToNow, parseISO, format } from "date-fns";

interface QueueDashboardProps {
  doctorId?: string;
  showControls?: boolean;
  isAdmin?: boolean;
  selectedDate?: Date;
}

export function QueueDashboard({
  doctorId,
  // showControls is not used in this component
  // showControls = false,
  isAdmin = false,
  selectedDate,
}: QueueDashboardProps) {
  const { queueItems, isLoading, updateQueueItem } = useQueue(
    doctorId,
    selectedDate,
  );
  const router = useRouter();
  const { isConnected } = useSocket();
  const [consultations, setConsultations] = useState<Record<string, string>>(
    {},
  );
  const [activeTab, setActiveTab] = useState<string>("all");

  // Filter queue items based on active tab
  const filteredQueueItems = queueItems.filter((item) => {
    if (activeTab === "all") return true;
    return item.status === activeTab;
  });

  // Sort queue items: waiting first, then in-consultation, then paused, then completed, then cancelled
  const sortedQueueItems = [...filteredQueueItems].sort((a, b) => {
    const statusOrder: Record<string, number> = {
      waiting: 0,
      "in-consultation": 1,
      paused: 2,
      completed: 3,
      cancelled: 4,
    };

    // First sort by status
    const statusDiff = statusOrder[a.status] - statusOrder[b.status];
    if (statusDiff !== 0) return statusDiff;

    // For items with the same status, sort by time
    if (a.startTime && b.startTime) {
      // Convert time strings (e.g., "09:00 AM") to comparable values
      const timeA = a.startTime.replace(/\s/g, "").toLowerCase();
      const timeB = b.startTime.replace(/\s/g, "").toLowerCase();

      // Compare times
      if (timeA < timeB) return -1;
      if (timeA > timeB) return 1;
    }

    // If times are equal or not available, sort by position
    return a.position - b.position;
  });

  // Fetch consultations for appointments in the queue
  const fetchConsultations = useCallback(async () => {
    try {
      // Fetch consultations for appointments in progress
      const response = await fetch(`/api/consultations?status=in-progress`);
      if (response.ok) {
        const data = await response.json();
        console.log("Fetched consultations:", data.consultations);
        const consultationMap: Record<string, string> = {};

        // Create a map of appointmentId -> consultationId
        data.consultations.forEach((consultation: any) => {
          console.log(
            "Processing consultation:",
            consultation.id,
            "for appointment:",
            consultation.appointmentId,
          );
          if (consultation.appointmentId) {
            consultationMap[consultation.appointmentId] = consultation.id;
          }
        });

        console.log("Consultation map:", consultationMap);
        setConsultations(consultationMap);
      }
    } catch (error) {
      console.error("Error fetching consultations:", error);
    }
  }, []);

  useEffect(() => {
    if (!queueItems.length) return;
    fetchConsultations();
  }, [queueItems, fetchConsultations]);

  // Navigate to consultation
  const navigateToConsultation = (appointmentId: string) => {
    const consultationId = consultations[appointmentId];
    if (consultationId) {
      router.push(`/consultations/${consultationId}`);
    }
  };

  // Get status badge variant and class
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "waiting":
        return {
          variant: "secondary" as const,
          className: "bg-blue-100 text-blue-700 hover:bg-blue-100",
        };
      case "in-consultation":
        return {
          variant: "secondary" as const,
          className: "bg-green-100 text-green-700 hover:bg-green-100",
        };
      case "paused":
        return {
          variant: "secondary" as const,
          className: "bg-amber-100 text-amber-700 hover:bg-amber-100",
        };
      case "completed":
        return {
          variant: "secondary" as const,
          className: "bg-gray-100 text-gray-700 hover:bg-gray-100",
        };
      case "cancelled":
        return {
          variant: "secondary" as const,
          className: "bg-red-100 text-red-700 hover:bg-red-100",
        };
      default:
        return { variant: "outline" as const, className: "" };
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "waiting":
        return <Clock className="h-3 w-3 mr-1" />;
      case "in-consultation":
        return <Play className="h-3 w-3 mr-1" />;
      case "paused":
        return <Pause className="h-3 w-3 mr-1" />;
      case "completed":
        return <CheckCircle2 className="h-3 w-3 mr-1" />;
      case "cancelled":
        return <AlertCircle className="h-3 w-3 mr-1" />;
      default:
        return null;
    }
  };

  // Format estimated wait time
  const formatWaitTime = (estimatedStartTime: string | Date | undefined) => {
    if (!estimatedStartTime) return "Unknown";

    const estimatedDate =
      typeof estimatedStartTime === "string"
        ? parseISO(estimatedStartTime)
        : estimatedStartTime;

    const now = new Date();

    if (estimatedDate < now) {
      return "Now";
    }

    return formatDistanceToNow(estimatedDate, { addSuffix: true });
  };

  // Format appointment time
  const formatAppointmentTime = (item: QueueItem) => {
    if (!item.appointmentDate) return "";

    const date =
      typeof item.appointmentDate === "string"
        ? parseISO(item.appointmentDate)
        : item.appointmentDate;

    return `${format(date, "MMM d")} ${item.startTime || ""}`;
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Queue Status</CardTitle>
            <CardDescription>
              {doctorId
                ? "Queue for the selected doctor"
                : "All patients in the queue for this branch"}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <div
              className={`h-3 w-3 rounded-full ${
                isConnected ? "bg-green-500" : "bg-red-500"
              }`}
            ></div>
            <span className="text-xs text-muted-foreground">
              {isConnected ? "Live" : "Offline"}
            </span>
          </div>
        </div>

        <div className="mt-4 w-full">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="all" className="flex items-center">
                <span className="text-xs">All</span>
              </TabsTrigger>
              <TabsTrigger value="waiting" className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                <span className="text-xs">Waiting</span>
              </TabsTrigger>
              <TabsTrigger
                value="in-consultation"
                className="flex items-center"
              >
                <Play className="h-3 w-3 mr-1" />
                <span className="text-xs">In Progress</span>
              </TabsTrigger>
              <TabsTrigger value="paused" className="flex items-center">
                <Pause className="h-3 w-3 mr-1" />
                <span className="text-xs">Paused</span>
              </TabsTrigger>
              <TabsTrigger value="completed" className="flex items-center">
                <CheckCircle2 className="h-3 w-3 mr-1" />
                <span className="text-xs">Completed</span>
              </TabsTrigger>
              <TabsTrigger value="cancelled" className="flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                <span className="text-xs">Cancelled</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="grid grid-cols-1 gap-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <Card key={i} className="border-l-4 border-gray-200">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <Skeleton className="h-6 w-[120px]" />
                      <Skeleton className="h-4 w-[80px]" />
                    </div>
                    <Skeleton className="h-6 w-[60px]" />
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[100px]" />
                    <Skeleton className="h-4 w-[80px]" />
                    <Skeleton className="h-4 w-[90px]" />
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-8 w-[60px]" />
                    <Skeleton className="h-8 w-[70px]" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : sortedQueueItems.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No patients in the queue</p>
            {isAdmin && (
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => router.push("/appointments/new")}
              >
                Create New Appointment
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {sortedQueueItems.map((item) => {
              const statusConfig = {
                waiting: {
                  borderColor: "border-blue-500",
                },
                "in-consultation": {
                  borderColor: "border-green-500",
                },
                paused: {
                  borderColor: "border-amber-500",
                },
                completed: {
                  borderColor: "border-gray-500",
                },
                cancelled: {
                  borderColor: "border-red-500",
                },
              };

              const config =
                statusConfig[item.status] || statusConfig.cancelled;

              return (
                <Card
                  key={item.id}
                  className={`border-l-4 ${config.borderColor}`}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      {/* Left Section - Patient Info */}
                      <div className="flex items-center space-x-4 min-w-0 flex-1">
                        <div className="flex flex-col">
                          <div className="text-lg font-semibold text-gray-900">
                            {item.patientName}
                          </div>
                          <div className="text-sm text-gray-500">
                            Queue #{item.position}
                          </div>
                        </div>
                      </div>

                      {/* Middle Section - Details */}
                      <div className="flex items-center space-x-6 min-w-0 flex-1">
                        <div className="flex items-center text-sm text-gray-600">
                          <User className="h-4 w-4 mr-1 text-gray-400" />
                          <span>Dr. {item.doctorName}</span>
                        </div>

                        {item.branchName && !doctorId && (
                          <div className="flex items-center text-sm text-gray-600">
                            <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                            <span>{item.branchName}</span>
                          </div>
                        )}

                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="h-4 w-4 mr-1 text-gray-400" />
                          <span>{item.startTime || "Not set"}</span>
                        </div>

                        {formatAppointmentTime(item) && (
                          <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                            <span>{formatAppointmentTime(item)}</span>
                          </div>
                        )}
                      </div>

                      {/* Right Section - Status and Actions */}
                      <div className="flex items-center space-x-3 flex-shrink-0">
                        <Badge
                          variant={getStatusBadge(item.status).variant}
                          className={`flex items-center ${
                            getStatusBadge(item.status).className
                          }`}
                        >
                          {getStatusIcon(item.status)}
                          {item.status.replace("-", " ")}
                        </Badge>
                      </div>
                    </div>

                    {/* Actions Row */}
                    <div className="flex justify-end mt-3 pt-3 border-t border-gray-100">
                      <div className="flex gap-2">
                        {item.status === "waiting" && (
                          <Button
                            variant="default"
                            size="sm"
                            onClick={async () => {
                              await updateQueueItem(item.id, "in-consultation");
                              // Refetch consultations after starting consultation
                              setTimeout(() => {
                                fetchConsultations();
                              }, 1000); // Small delay to ensure consultation is created
                            }}
                          >
                            Start
                          </Button>
                        )}
                        {item.status === "in-consultation" && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateQueueItem(item.id, "paused")}
                            >
                              Pause
                            </Button>
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() =>
                                updateQueueItem(item.id, "completed")
                              }
                            >
                              Complete
                            </Button>
                            {consultations[item.appointmentId] && (
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={() =>
                                  navigateToConsultation(item.appointmentId)
                                }
                              >
                                <FileText className="h-4 w-4 mr-1" />
                                View
                              </Button>
                            )}
                          </>
                        )}
                        {item.status === "paused" && (
                          <>
                            <Button
                              variant="default"
                              size="sm"
                              onClick={async () => {
                                await updateQueueItem(
                                  item.id,
                                  "in-consultation",
                                );
                                // Refetch consultations after resuming consultation
                                setTimeout(() => {
                                  fetchConsultations();
                                }, 1000); // Small delay to ensure consultation is available
                              }}
                            >
                              Resume
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                updateQueueItem(item.id, "cancelled")
                              }
                            >
                              Cancel
                            </Button>
                          </>
                        )}
                        {(item.status === "completed" ||
                          item.status === "cancelled") && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateQueueItem(item.id, "waiting")}
                          >
                            Reopen
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Additional Info Row */}
                    {(item.estimatedStartTime && item.status === "waiting") ||
                    (item.status === "paused" && item.pauseReason) ? (
                      <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-100">
                        {item.estimatedStartTime &&
                          item.status === "waiting" && (
                            <div className="flex items-center text-sm text-blue-600 font-medium">
                              <Clock className="h-3 w-3 mr-1" />
                              <span>
                                Est. {formatWaitTime(item.estimatedStartTime)}
                              </span>
                            </div>
                          )}
                        {item.status === "paused" && item.pauseReason && (
                          <div className="text-sm text-amber-600">
                            <span className="font-medium">Reason:</span>{" "}
                            {item.pauseReason}
                          </div>
                        )}
                      </div>
                    ) : null}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
