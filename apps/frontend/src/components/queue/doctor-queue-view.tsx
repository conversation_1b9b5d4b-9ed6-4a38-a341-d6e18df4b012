"use client";

import { useState, useEffect } from "react";
import { QueueDashboard } from "./queue-dashboard";
import { QueueStats } from "./queue-stats";
import { QueueNotification } from "./queue-notification";
import { useQueue } from "@/hooks/useQueue";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
// Removed unused import

interface DoctorQueueViewProps {
  doctorId?: string;
}

export function DoctorQueueView({ doctorId }: DoctorQueueViewProps) {
  const router = useRouter();
  const [currentDoctorId, setCurrentDoctorId] = useState<string | undefined>(
    doctorId,
  );
  const [isCurrentDoctor, setIsCurrentDoctor] = useState(false);
  const { queueItems, isLoading: queueLoading } = useQueue(currentDoctorId);

  // Check if the current user is the doctor
  useEffect(() => {
    const checkCurrentDoctor = async () => {
      try {
        // For now, we'll assume the user is the doctor if doctorId is provided
        // In a real implementation, we would fetch the doctor profile and check
        if (doctorId) {
          setIsCurrentDoctor(true);
        }
      } catch (error) {
        console.error("Error checking current doctor:", error);
      }
    };

    if (doctorId) {
      checkCurrentDoctor();
    }
  }, [doctorId]);

  // If no doctorId is provided, try to get the current user's doctor ID
  useEffect(() => {
    if (!doctorId) {
      const fetchCurrentDoctor = async () => {
        try {
          // In a real implementation, we would fetch the doctor profile
          // For now, we'll just use a placeholder or fetch from an API
          const response = await fetch("/api/doctors/me");
          if (response.ok) {
            const data = await response.json();
            if (data.doctor?.id) {
              setCurrentDoctorId(data.doctor.id);
              setIsCurrentDoctor(true);
            }
          }
        } catch (error) {
          console.error("Error fetching current doctor:", error);
        }
      };

      fetchCurrentDoctor();
    }
  }, [doctorId]);

  return (
    <div className="space-y-6">
      <QueueNotification doctorId={currentDoctorId} />
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Queue</h1>
          <p className="text-muted-foreground">
            Manage your patient queue and track consultation status
          </p>
        </div>
        <Button onClick={() => router.push("/consultations")}>
          View Consultations
        </Button>
      </div>

      {currentDoctorId ? (
        <>
          <QueueStats queueItems={queueItems} isLoading={queueLoading} />

          <QueueDashboard
            doctorId={currentDoctorId}
            showControls={isCurrentDoctor}
          />
        </>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>No Doctor Profile</CardTitle>
            <CardDescription>
              You don't have a doctor profile associated with your account.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              Please contact your administrator to set up your doctor profile.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
