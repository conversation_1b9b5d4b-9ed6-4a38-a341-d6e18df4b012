"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Clock, Users, Activity, Timer } from "lucide-react";
import { QueueItem } from "@/hooks/useQueue";
import { parseISO } from "date-fns";

interface QueueStatsProps {
  queueItems: QueueItem[];
  isLoading: boolean;
}

export function QueueStats({ queueItems, isLoading }: QueueStatsProps) {
  const [stats, setStats] = useState({
    totalPatients: 0,
    waitingCount: 0,
    inConsultationCount: 0,
    averageWaitTime: 0,
  });

  useEffect(() => {
    if (isLoading || !queueItems.length) return;

    // Calculate statistics
    const waitingItems = queueItems.filter((item) => item.status === "waiting");
    const inConsultationItems = queueItems.filter(
      (item) => item.status === "in-consultation",
    );

    // Calculate average wait time for completed items
    const completedItems = queueItems.filter(
      (item) =>
        item.status === "completed" &&
        item.actualStartTime &&
        item.completionTime,
    );

    let totalWaitTimeMinutes = 0;

    if (completedItems.length > 0) {
      completedItems.forEach((item) => {
        if (item.actualStartTime && item.completionTime) {
          const startTime =
            typeof item.actualStartTime === "string"
              ? parseISO(item.actualStartTime)
              : item.actualStartTime;

          const endTime =
            typeof item.completionTime === "string"
              ? parseISO(item.completionTime)
              : item.completionTime;

          const waitTimeMinutes = Math.floor(
            (endTime.getTime() - startTime.getTime()) / (1000 * 60),
          );
          totalWaitTimeMinutes += waitTimeMinutes;
        }
      });
    }

    const averageWaitTime =
      completedItems.length > 0
        ? Math.floor(totalWaitTimeMinutes / completedItems.length)
        : 0;

    setStats({
      totalPatients: queueItems.length,
      waitingCount: waitingItems.length,
      inConsultationCount: inConsultationItems.length,
      averageWaitTime,
    });
  }, [queueItems, isLoading]);

  // Format minutes to human-readable time
  const formatMinutes = (minutes: number) => {
    if (minutes < 1) return "Less than a minute";
    if (minutes < 60) return `${minutes} minute${minutes !== 1 ? "s" : ""}`;

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) return `${hours} hour${hours !== 1 ? "s" : ""}`;
    return `${hours} hour${hours !== 1 ? "s" : ""} ${remainingMinutes} minute${remainingMinutes !== 1 ? "s" : ""}`;
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {isLoading ? "-" : stats.totalPatients}
          </div>
          <p className="text-xs text-muted-foreground">In queue today</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Waiting</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {isLoading ? "-" : stats.waitingCount}
          </div>
          <p className="text-xs text-muted-foreground">
            Patients in waiting room
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">In Consultation</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {isLoading ? "-" : stats.inConsultationCount}
          </div>
          <p className="text-xs text-muted-foreground">
            Currently with doctors
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Avg. Consultation Time
          </CardTitle>
          <Timer className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {isLoading ? "-" : formatMinutes(stats.averageWaitTime)}
          </div>
          <p className="text-xs text-muted-foreground">Per patient today</p>
        </CardContent>
      </Card>
    </div>
  );
}
