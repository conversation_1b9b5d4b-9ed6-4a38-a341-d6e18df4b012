"use client";

import { useEffect, useState } from "react";
import { useSocket } from "@/components/providers/socket-provider";
import { toast } from "sonner";
import { QueueItem } from "@/hooks/useQueue";
import { Bell } from "lucide-react";

interface QueueNotificationProps {
  doctorId?: string;
}

export function QueueNotification({ doctorId }: QueueNotificationProps) {
  const { socket, isConnected } = useSocket();
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);

  // Check if browser notifications are supported and permission is granted
  useEffect(() => {
    const checkNotificationPermission = async () => {
      if (!("Notification" in window)) {
        console.log("This browser does not support desktop notification");
        return;
      }

      if (Notification.permission === "granted") {
        setNotificationsEnabled(true);
      } else if (Notification.permission !== "denied") {
        const permission = await Notification.requestPermission();
        setNotificationsEnabled(permission === "granted");
      }
    };

    checkNotificationPermission();
  }, []);

  // Listen for queue updates and show notifications
  useEffect(() => {
    if (!isConnected || !socket || !notificationsEnabled) return;

    const handleQueueUpdate = (data: any) => {
      if (!data.formattedQueue) return;

      const queueItem = data.formattedQueue as QueueItem;

      // Only show notifications for specific doctor if doctorId is provided
      if (doctorId && queueItem.doctorId !== doctorId) return;

      // Show different notifications based on status
      switch (queueItem.status) {
        case "waiting":
          // New patient added to queue
          toast.info(
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <span>New patient in waiting room: {queueItem.patientName}</span>
            </div>,
          );

          if (notificationsEnabled) {
            new Notification("New Patient Waiting", {
              body: `${queueItem.patientName} has been added to your queue`,
              icon: "/favicon.ico",
            });
          }
          break;

        case "in-consultation":
          // Patient moved to consultation
          toast.success(
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <span>Consultation started with {queueItem.patientName}</span>
            </div>,
          );
          break;

        case "completed":
          // Consultation completed
          toast.success(
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <span>Consultation completed for {queueItem.patientName}</span>
            </div>,
          );
          break;
      }
    };

    socket.on("queue-status-updated", handleQueueUpdate);

    return () => {
      socket.off("queue-status-updated", handleQueueUpdate);
    };
  }, [isConnected, socket, notificationsEnabled, doctorId]);

  // This component doesn't render anything visible
  return null;
}
