"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CheckCircle, Phone, Shield, AlertCircle } from "lucide-react";
import { PhoneVerificationDialog } from "./phone-verification-dialog";
import { cn } from "@/lib/utils";

interface PhoneVerificationFormProps {
  phoneNumber: string;
  onPhoneChange: (phone: string) => void;
  isVerified: boolean;
  onVerificationChange: (verified: boolean) => void;
  disabled?: boolean;
  required?: boolean;
  purpose?: string;
  patientId?: string;
  skipVerification?: boolean; // For ABHA scenarios where verification is not needed
  onSkipVerification?: (skip: boolean) => void; // Callback for when user chooses to skip verification
}

export function PhoneVerificationForm({
  phoneNumber,
  onPhoneChange,
  isVerified,
  onVerificationChange,
  disabled = false,
  required = true,
  purpose = "phone-verification",
  patientId,
  skipVerification = false,
  onSkipVerification,
}: PhoneVerificationFormProps) {
  const [showVerificationDialog, setShowVerificationDialog] = useState(false);
  const [_hasTriggeredVerification, setHasTriggeredVerification] =
    useState(false);
  const [userSkippedVerification, setUserSkippedVerification] = useState(false);

  const handlePhoneChange = (value: string) => {
    // Only allow digits and limit to 10 characters
    const cleanValue = value.replace(/\D/g, "").slice(0, 10);
    onPhoneChange(cleanValue);

    // Reset verification status when phone changes
    if (cleanValue !== phoneNumber && (isVerified || userSkippedVerification)) {
      onVerificationChange(false);
      setHasTriggeredVerification(false);
      setUserSkippedVerification(false);
      if (onSkipVerification) {
        onSkipVerification(false);
      }
    }
  };

  const handleVerifyClick = () => {
    if (phoneNumber.length === 10) {
      setShowVerificationDialog(true);
      setHasTriggeredVerification(true);
    }
  };

  const handleVerificationSuccess = () => {
    onVerificationChange(true);
    setShowVerificationDialog(false);
    setUserSkippedVerification(false);
  };

  const handleSkipVerification = () => {
    setUserSkippedVerification(true);
    if (onSkipVerification) {
      onSkipVerification(true);
    }
  };

  const isPhoneValid = phoneNumber.length === 10;
  const showVerifyButton =
    isPhoneValid &&
    !isVerified &&
    !skipVerification &&
    !userSkippedVerification;
  const showSkipButton =
    isPhoneValid &&
    !isVerified &&
    !skipVerification &&
    !userSkippedVerification;
  const showVerifiedStatus =
    isVerified || skipVerification || userSkippedVerification;

  return (
    <div className="space-y-3">
      <Label htmlFor="phone" className="flex items-center gap-2">
        <Phone className="h-4 w-4" />
        Phone Number
        {required && <span className="text-red-500">*</span>}
      </Label>

      <div className="relative">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Input
              id="phone"
              type="tel"
              placeholder="Enter 10-digit phone number"
              value={phoneNumber}
              onChange={(e) => handlePhoneChange(e.target.value)}
              disabled={disabled}
              className={cn(
                "pr-10",
                showVerifiedStatus && "border-green-500 bg-green-50",
                !skipVerification &&
                  isPhoneValid &&
                  !isVerified &&
                  "border-orange-500",
              )}
              maxLength={10}
            />

            {/* Status icon */}
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              {showVerifiedStatus ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : !skipVerification && isPhoneValid && !isVerified ? (
                <AlertCircle className="h-4 w-4 text-orange-500" />
              ) : null}
            </div>
          </div>

          {/* Verify button */}
          {showVerifyButton && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleVerifyClick}
              disabled={disabled}
              className="shrink-0"
            >
              <Shield className="mr-1 h-3 w-3" />
              Verify
            </Button>
          )}

          {/* Skip verification button */}
          {showSkipButton && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleSkipVerification}
              disabled={disabled}
              className="shrink-0 text-muted-foreground hover:text-foreground"
            >
              Skip
            </Button>
          )}
        </div>

        {/* Status messages */}
        <div className="mt-1 min-h-[1.25rem]">
          {showVerifiedStatus ? (
            <p className="text-xs text-green-600 flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              {skipVerification
                ? "Phone number confirmed"
                : userSkippedVerification
                  ? "Phone verification skipped"
                  : "Phone number verified"}
            </p>
          ) : !skipVerification &&
            isPhoneValid &&
            !isVerified &&
            !userSkippedVerification ? (
            <p className="text-xs text-blue-600 flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              Phone verification recommended (optional)
            </p>
          ) : phoneNumber.length > 0 && phoneNumber.length < 10 ? (
            <p className="text-xs text-gray-500">
              Enter a valid 10-digit phone number
            </p>
          ) : null}
        </div>
      </div>

      {/* Verification Dialog */}
      <PhoneVerificationDialog
        open={showVerificationDialog}
        onOpenChange={setShowVerificationDialog}
        phoneNumber={phoneNumber}
        onVerificationSuccess={handleVerificationSuccess}
        purpose={purpose}
        patientId={patientId}
      />
    </div>
  );
}
