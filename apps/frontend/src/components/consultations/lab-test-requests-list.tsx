"use client";

import { format } from "date-fns";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  FlaskConical,
  Calendar,
  User,
  Eye,
  Clock,
  CheckCircle,
} from "lucide-react";

interface DiagnosticReport {
  id: string;
  reportType: string;
  status: string;
  reportDate: string;
  conclusion?: string;
}

interface LabTestRequest {
  id: string;
  testType: string;
  testName: string;
  priority: string;
  status: string;
  requestDate: string;
  completedDate?: string;
  requestedBy: string;
  notes?: string;
  expectedDate?: string;
  diagnosticReports: DiagnosticReport[];
}

interface LabTestRequestsListProps {
  consultationId: string;
  labTestRequests: LabTestRequest[];
  onRefresh: () => void;
}

export function LabTestRequestsList({
  labTestRequests,
}: LabTestRequestsListProps) {
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "urgent":
        return <Badge variant="destructive">Urgent</Badge>;
      case "stat":
        return <Badge className="bg-red-600 text-white">STAT</Badge>;
      default:
        return <Badge variant="outline">Routine</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case "in-progress":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            <FlaskConical className="h-3 w-3 mr-1" />
            In Progress
          </Badge>
        );
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleViewInLabReports = () => {
    window.open("/lab-reports", "_blank");
  };

  if (!labTestRequests || labTestRequests.length === 0) {
    return (
      <div className="text-center py-6">
        <FlaskConical className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
        <p className="text-muted-foreground">
          No lab test requests for this consultation
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {labTestRequests.map((request) => (
        <Card
          key={request.id}
          className="border-l-4 border-l-blue-500 shadow-sm"
        >
          <CardHeader className="pb-3">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg font-semibold text-blue-700">
                  {request.testName}
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge
                    variant="outline"
                    className="bg-blue-100 text-blue-800"
                  >
                    {request.testType}
                  </Badge>
                  {getPriorityBadge(request.priority)}
                  {getStatusBadge(request.status)}
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleViewInLabReports}
                className="text-blue-600 border-blue-600 hover:bg-blue-50"
              >
                <Eye className="h-4 w-4 mr-2" />
                View in Lab Reports
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="bg-muted/30 p-3 rounded-md">
                  <p className="text-xs font-medium text-muted-foreground flex items-center">
                    <Calendar className="h-3.5 w-3.5 mr-1" />
                    Request Timeline
                  </p>
                  <div className="space-y-1 mt-1">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Requested:</span>
                      <span className="text-sm">
                        {format(new Date(request.requestDate), "PPp")}
                      </span>
                    </div>
                    {request.expectedDate && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Expected:</span>
                        <span className="text-sm">
                          {format(new Date(request.expectedDate), "PP")}
                        </span>
                      </div>
                    )}
                    {request.completedDate && (
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Completed:</span>
                        <span className="text-sm">
                          {format(new Date(request.completedDate), "PPp")}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="bg-muted/30 p-3 rounded-md">
                  <p className="text-xs font-medium text-muted-foreground flex items-center">
                    <User className="h-3.5 w-3.5 mr-1" />
                    Request Details
                  </p>
                  <div className="space-y-1 mt-1">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Requested by:</span>
                      <span className="text-sm">{request.requestedBy}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Priority:</span>
                      <span className="text-sm">{request.priority}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Status:</span>
                      <span className="text-sm">{request.status}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Diagnostic Reports */}
            {request.diagnosticReports &&
              request.diagnosticReports.length > 0 && (
                <div className="mt-4 bg-green-50 border border-green-200 p-3 rounded-md">
                  <p className="text-sm font-medium text-green-700 mb-2">
                    Diagnostic Reports ({request.diagnosticReports.length}):
                  </p>
                  <div className="space-y-2">
                    {request.diagnosticReports.map((report) => (
                      <div
                        key={report.id}
                        className="flex justify-between items-start"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-green-600">
                              {report.reportType}
                            </span>
                            <Badge
                              variant="outline"
                              className="bg-green-100 text-green-800"
                            >
                              {report.status}
                            </Badge>
                          </div>
                          <p className="text-xs text-green-500">
                            {format(new Date(report.reportDate), "PPp")}
                          </p>
                          {report.conclusion && (
                            <p className="text-sm text-green-600 mt-1">
                              {report.conclusion}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

            {/* Notes */}
            {request.notes && (
              <div className="mt-4 bg-blue-50 border border-blue-200 p-3 rounded-md">
                <p className="text-sm font-medium text-blue-700">Notes:</p>
                <p className="text-sm text-blue-600">{request.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
