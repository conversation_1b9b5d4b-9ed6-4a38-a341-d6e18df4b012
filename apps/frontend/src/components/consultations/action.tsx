"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON>riangle,
  Link as LinkIcon,
  Loader2,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";

interface ConsultationActionProps {
  patientId: string;
  consultationId: string;
  appointmentId?: string;
  onSuccess?: () => void;
}

export function ConsultationAction({
  patientId,
  consultationId,
  appointmentId,
  onSuccess,
}: ConsultationActionProps) {
  const [hasAbhaProfile, setHasAbhaProfile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPendingToken, setIsPendingToken] = useState(false);
  const [hasLinkToken, setHasLinkToken] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isGeneratingToken, setIsGeneratingToken] = useState(false);
  const [branchId, setBranchId] = useState("");

  useEffect(() => {
    const checkAbhaStatus = async () => {
      if (!patientId) return;

      try {
        setIsLoading(true);
        setError(null);

        // Check if the patient has an ABHA profile
        const profileResponse = await fetch(
          `/api/patients/${patientId}/abha-profile`,
        );

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();
          setHasAbhaProfile(!!profileData.abhaProfile?.abhaNumber);

          // If they have an ABHA profile, check for link tokens
          if (profileData.abhaProfile?.abhaNumber) {
            // Get the current branch ID
            const branchResponse = await fetch("/api/user/current-branch");
            if (branchResponse.ok) {
              const branchData = await branchResponse.json();

              if (branchData.currentBranch?.id) {
                setBranchId(branchData.currentBranch.id);
                // Check for branch-specific link token
                const tokenResponse = await fetch(
                  `/api/patients/${patientId}/care-contexts/link-token?branchId=${branchData.currentBranch.id}`,
                  { method: "GET" },
                );

                if (tokenResponse.ok) {
                  const tokenData = await tokenResponse.json();
                  console.log("Token data:", tokenData); // Debug log
                  setIsPendingToken(!!tokenData.isPendingToken);
                  setHasLinkToken(!!tokenData.hasLinkToken);
                }
              }
            }
          }
        } else {
          setError("Failed to check ABHA status");
        }
      } catch (error) {
        console.error("Error checking ABHA status:", error);
        setError("An error occurred while checking ABHA status");
      } finally {
        setIsLoading(false);
      }
    };

    checkAbhaStatus();
  }, [patientId]);

  const handleOpenDialog = () => {
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  const handleGenerateToken = async () => {
    if (!patientId) {
      toast.error("Missing patient information");
      return;
    }

    console.log("Initial branchId:", branchId); // Debug log

    // If branchId is not set, try to get it again
    let currentBranchId = branchId;
    if (!currentBranchId) {
      try {
        console.log("Fetching current branch..."); // Debug log
        const branchResponse = await fetch("/api/user/current-branch");
        if (branchResponse.ok) {
          const branchData = await branchResponse.json();
          console.log("Branch data:", branchData); // Debug log

          if (branchData.currentBranch?.id) {
            currentBranchId = branchData.currentBranch.id;
            console.log("Setting branchId to:", currentBranchId); // Debug log
            setBranchId(currentBranchId);
          } else {
            console.log("No branch ID in response"); // Debug log
          }
        } else {
          console.log("Branch response not OK:", await branchResponse.text()); // Debug log
        }
      } catch (error) {
        console.error("Error fetching current branch:", error);
      }
    }

    console.log("Final currentBranchId:", currentBranchId); // Debug log

    if (!currentBranchId) {
      toast.error("Missing branch information. Please select a branch first.");
      return;
    }

    try {
      setIsGeneratingToken(true);

      // Create a link token
      console.log("Sending API request with branchId:", currentBranchId); // Debug log
      const requestBody = { branchId: currentBranchId, createEmpty: true };
      console.log("Request body:", requestBody); // Debug log

      const response = await fetch(
        `/api/patients/${patientId}/care-contexts/link-token`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText); // Debug log
        throw new Error(`Failed to generate link token: ${errorText}`);
      }

      const data = await response.json();
      console.log("API response data:", data); // Debug log

      // The API doesn't return a success property, but if we got here, it was successful
      // Check for message or token to confirm success
      if (data.message || data.token || data.tokenId || data.requestId) {
        toast.success(data.message || "Link token generation initiated");
        setIsPendingToken(true);
        handleCloseDialog();

        // Start polling for token status
        let attempts = 0;
        const maxAttempts = 30; // 30 attempts * 5 seconds = 150 seconds (2.5 minutes)
        const pollInterval = 5000; // 5 seconds

        const pollForToken = async () => {
          if (attempts >= maxAttempts) {
            toast.error("Token generation timed out. Please try again later.");
            setIsGeneratingToken(false);
            return;
          }

          attempts++;

          try {
            const tokenResponse = await fetch(
              `/api/patients/${patientId}/care-contexts/link-token?branchId=${currentBranchId}`,
              { method: "GET" },
            );

            if (tokenResponse.ok) {
              const tokenData = await tokenResponse.json();

              if (tokenData.hasLinkToken) {
                // Token is available
                setHasLinkToken(true);
                setIsPendingToken(false);
                toast.success("Link token is now available");
                setIsGeneratingToken(false);
                return;
              } else if (tokenData.isPendingToken) {
                // Token is pending
                setIsPendingToken(true);
                // Continue polling
              }
            }
          } catch (error) {
            console.error("Error polling for token:", error);
          }

          // Schedule next poll
          setTimeout(pollForToken, pollInterval);
        };

        // Start polling
        setTimeout(pollForToken, pollInterval);
      } else {
        console.error("Unexpected API response format:", data);
        throw new Error(
          data.message || data.error || "Failed to generate link token",
        );
      }
    } catch (error) {
      console.error("Error generating link token:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to generate link token",
      );
      setIsGeneratingToken(false);
    }
  };

  if (isLoading) {
    return (
      <Alert className="bg-blue-50 text-blue-800 border-blue-200">
        <AlertCircle className="h-4 w-4 text-blue-500" />
        <AlertTitle>Checking ABHA status</AlertTitle>
        <AlertDescription>
          Please wait while we check the patient's ABHA status...
        </AlertDescription>
      </Alert>
    );
  }

  if (error) {
    return (
      <Alert className="bg-red-50 text-red-800 border-red-200">
        <AlertCircle className="h-4 w-4 text-red-500" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!hasAbhaProfile) {
    return (
      <Alert className="bg-yellow-50 text-yellow-800 border-yellow-200">
        <AlertTriangle className="h-4 w-4 text-yellow-500" />
        <AlertTitle>ABHA Not Linked</AlertTitle>
        <AlertDescription>
          This patient does not have an ABHA profile. Link the patient to ABHA
          to enable care context creation.
        </AlertDescription>
      </Alert>
    );
  }

  // We don't show a banner for care context created as requested

  // Show different banners based on the state
  if (hasAbhaProfile) {
    if (isPendingToken) {
      // Return null if there's a pending token (the waiting banner will be shown by the parent component)
      return null;
    } else if (hasLinkToken) {
      // Show the "ABHA Linked" banner when there's a link token
      return (
        <>
          <Alert className="bg-blue-50 text-blue-800 border-blue-200">
            <div className="flex flex-col w-full">
              <div className="flex items-start">
                <AlertCircle className="h-4 w-4 text-blue-500 mt-0.5 mr-2" />
                <div className="flex-1">
                  <AlertTitle>ABHA Linked</AlertTitle>
                  <AlertDescription className="mb-3">
                    Patient has an ABHA profile. You can create a care context
                    for this consultation.
                  </AlertDescription>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Create care context
                      fetch(`/api/patients/${patientId}/care-contexts/link`, {
                        method: "POST",
                        headers: {
                          "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                          consultationId,
                          appointmentId,
                          branchId: branchId,
                        }),
                      })
                        .then((response) => {
                          if (!response.ok) {
                            throw new Error("Failed to create care context");
                          }
                          return response.json();
                        })
                        .then((data) => {
                          if (data.success) {
                            if (onSuccess) onSuccess();
                          } else {
                            throw new Error(
                              data.message || "Failed to create care context",
                            );
                          }
                        })
                        .catch((error) => {
                          console.error("Error creating care context:", error);
                          toast.error("Failed to create care context");
                        });
                    }}
                    className="bg-white hover:bg-blue-50"
                  >
                    <LinkIcon className="h-4 w-4 mr-2" />
                    Create Care Context
                  </Button>
                </div>
              </div>
            </div>
          </Alert>
        </>
      );
    } else {
      // Show the "Generate a link token" banner when there's no link token
      return (
        <>
          <Alert className="bg-blue-50 text-blue-800 border-blue-200">
            <div className="flex flex-col w-full">
              <div className="flex items-start">
                <AlertCircle className="h-4 w-4 text-blue-500 mt-0.5 mr-2" />
                <div className="flex-1">
                  <AlertTitle>Link Token Required</AlertTitle>
                  <AlertDescription className="mb-3">
                    A link token is required to create care contexts. Generate a
                    link token to proceed.
                  </AlertDescription>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleOpenDialog}
                    disabled={isGeneratingToken}
                    className="bg-white hover:bg-blue-50"
                  >
                    <LinkIcon className="h-4 w-4 mr-2" />
                    Generate Link Token
                  </Button>
                </div>
              </div>
            </div>
          </Alert>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Generate Link Token</DialogTitle>
                <DialogDescription>
                  This will generate a link token for the patient's ABHA
                  profile. The token will be used to create care contexts.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={handleCloseDialog}>
                  Cancel
                </Button>
                <Button
                  onClick={handleGenerateToken}
                  disabled={isGeneratingToken}
                >
                  {isGeneratingToken ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    "Generate Token"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      );
    }
  }

  // Return null for other cases
  return null;
}
