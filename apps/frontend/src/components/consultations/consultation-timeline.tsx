"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2, Calendar, Clock, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ConsultationCard } from "./consultation-card";

interface Consultation {
  id: string;
  consultationDate: string;
  status: string;
  startTime?: string;
  endTime?: string;
  followUpDate?: string;
  followUpNotes?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: string;
    phone: string;
    email?: string;
  };
  doctor: {
    id: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
    specialization?: string;
  };
  branch: {
    id: string;
    name: string;
  };
  vitals: any[];
  clinicalNotes: any[];
  prescriptions: any[];
  labTestRequests: any[];
}

interface ConsultationTimelineProps {
  patientId: string;
}

export function ConsultationTimeline({ patientId }: ConsultationTimelineProps) {
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchConsultations();
  }, [patientId]);

  const fetchConsultations = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/consultations?patientId=${patientId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch consultations");
      }

      const data = await response.json();
      setConsultations(data.consultations || []);
    } catch (error) {
      console.error("Error fetching consultations:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load consultations",
      );
    } finally {
      setLoading(false);
    }
  };

  // Group consultations by date
  const groupedConsultations = consultations.reduce<
    Record<string, Consultation[]>
  >((groups, consultation) => {
    const date = format(new Date(consultation.consultationDate), "yyyy-MM-dd");
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(consultation);
    return groups;
  }, {});

  // Sort dates in descending order (latest first)
  const sortedDates = Object.keys(groupedConsultations).sort(
    (a, b) => new Date(b).getTime() - new Date(a).getTime(),
  );

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Health Records Timeline</CardTitle>
          <CardDescription>Loading consultation history...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (consultations.length === 0) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>No consultations found</AlertTitle>
        <AlertDescription>
          This patient has no consultation history yet.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          Health Records Timeline
        </CardTitle>
        <CardDescription>
          View all consultations and health records in chronological order
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {sortedDates.map((date) => (
            <div key={date} className="space-y-4">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <h3 className="text-lg font-semibold">
                  {format(new Date(date), "EEEE, MMMM d, yyyy")}
                </h3>
              </div>
              <div className="ml-6 space-y-3 border-l-2 border-muted pl-6">
                {groupedConsultations[date].map((consultation) => (
                  <ConsultationCard
                    key={consultation.id}
                    consultation={consultation}
                    onRefresh={fetchConsultations}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
