"use client";

import { useState } from "react";
import { format } from "date-fns";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  ChevronDown,
  ChevronRight,
  Activity,
  FileText,
  Pill,
  Clock,
  Stethoscope,
  ExternalLink,
  FlaskConical,
} from "lucide-react";
import { useRouter } from "next/navigation";

interface Consultation {
  id: string;
  consultationDate: string;
  status: string;
  startTime?: string;
  endTime?: string;
  followUpDate?: string;
  followUpNotes?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: string;
    phone: string;
    email?: string;
  };
  doctor: {
    id: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
    specialization?: string;
  };
  branch: {
    id: string;
    name: string;
  };
  vitals: any[];
  clinicalNotes: any[];
  prescriptions: any[];
  labTestRequests: any[];
}

interface ConsultationCardProps {
  consultation: Consultation;
  onRefresh?: () => void;
}

export function ConsultationCard({ consultation }: ConsultationCardProps) {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 shadow-md";
      case "in-progress":
        return "bg-gradient-to-r from-blue-500 to-cyan-500 text-white border-0 shadow-md";
      case "cancelled":
        return "bg-gradient-to-r from-red-500 to-rose-500 text-white border-0 shadow-md";
      case "scheduled":
        return "bg-gradient-to-r from-yellow-500 to-amber-500 text-white border-0 shadow-md";
      default:
        return "bg-gradient-to-r from-gray-500 to-slate-500 text-white border-0 shadow-md";
    }
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return "Not specified";
    return timeString;
  };

  const handleViewFullConsultation = () => {
    router.push(`/consultations/${consultation.id}`);
  };

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <Card className="group relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-white to-gray-50/30">
        {/* Status indicator bar */}
        <div
          className={`absolute top-0 left-0 w-full h-1 ${
            consultation.status === "completed"
              ? "bg-gradient-to-r from-green-500 to-emerald-500"
              : consultation.status === "in-progress"
                ? "bg-gradient-to-r from-blue-500 to-cyan-500"
                : consultation.status === "cancelled"
                  ? "bg-gradient-to-r from-red-500 to-rose-500"
                  : "bg-gradient-to-r from-yellow-500 to-amber-500"
          }`}
        />

        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gradient-to-r hover:from-primary/5 hover:to-primary/10 transition-all duration-300 pb-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4 flex-1">
                {/* Doctor Avatar */}
                <div className="relative">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/30 flex items-center justify-center shadow-md">
                    <Stethoscope className="h-6 w-6 text-primary" />
                  </div>
                  <div
                    className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                      consultation.status === "completed"
                        ? "bg-green-500"
                        : consultation.status === "in-progress"
                          ? "bg-blue-500"
                          : consultation.status === "cancelled"
                            ? "bg-red-500"
                            : "bg-yellow-500"
                    }`}
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <CardTitle className="text-xl font-semibold text-gray-900 truncate">
                      Dr. {consultation.doctor.user.name}
                    </CardTitle>
                    {consultation.doctor.specialization && (
                      <Badge
                        variant="outline"
                        className="text-xs font-medium bg-primary/10 text-primary border-primary/20"
                      >
                        {consultation.doctor.specialization}
                      </Badge>
                    )}
                  </div>

                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">
                        {formatTime(consultation.startTime)} -{" "}
                        {formatTime(consultation.endTime)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 rounded-full bg-gray-300" />
                      <span>{consultation.branch.name}</span>
                    </div>
                  </div>

                  {/* Quick stats */}
                  <div className="flex items-center space-x-6 mt-3">
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Activity className="h-3 w-3 text-red-500" />
                      <span>{consultation.vitals?.length || 0} vitals</span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <FileText className="h-3 w-3 text-blue-500" />
                      <span>
                        {consultation.clinicalNotes?.length || 0} notes
                      </span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Pill className="h-3 w-3 text-green-500" />
                      <span>
                        {consultation.prescriptions?.length || 0} prescriptions
                      </span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <FlaskConical className="h-3 w-3 text-purple-500" />
                      <span>
                        {consultation.labTestRequests?.length || 0} lab tests
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3 ml-4">
                <Badge
                  className={`${getStatusColor(consultation.status)} px-3 py-1 text-xs font-semibold shadow-sm`}
                >
                  {consultation.status.charAt(0).toUpperCase() +
                    consultation.status.slice(1)}
                </Badge>
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 group-hover:bg-primary/10 transition-colors">
                  {isOpen ? (
                    <ChevronDown className="h-4 w-4 text-gray-600 group-hover:text-primary transition-colors" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-600 group-hover:text-primary transition-colors" />
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0 px-6 pb-6">
            {/* Divider */}
            <div className="w-full h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent mb-6" />

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
              {/* Vitals Section */}
              <div className="group/vitals">
                <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-red-50 to-rose-50/50 overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 to-rose-500" />
                  <CardHeader className="pb-4 pt-5">
                    <CardTitle className="text-base flex items-center text-red-700 font-semibold">
                      <div className="w-8 h-8 rounded-lg bg-red-100 flex items-center justify-center mr-3">
                        <Activity className="h-4 w-4 text-red-600" />
                      </div>
                      Vitals
                      <Badge
                        variant="secondary"
                        className="ml-auto text-xs bg-red-100 text-red-700 border-red-200"
                      >
                        {consultation.vitals?.length || 0}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0 pb-5">
                    {consultation.vitals && consultation.vitals.length > 0 ? (
                      <div className="space-y-3">
                        {consultation.vitals.slice(0, 2).map((vital, index) => (
                          <div
                            key={index}
                            className="bg-white/60 rounded-lg p-3 space-y-2 border border-red-100"
                          >
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600 font-medium">
                                  BP:
                                </span>
                                <span className="font-semibold text-gray-900">
                                  {vital.systolicBP}/{vital.diastolicBP}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600 font-medium">
                                  Pulse:
                                </span>
                                <span className="font-semibold text-gray-900">
                                  {vital.pulse}
                                </span>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600 font-medium">
                                  Temp:
                                </span>
                                <span className="font-semibold text-gray-900">
                                  {vital.temperature}°C
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600 font-medium">
                                  SpO2:
                                </span>
                                <span className="font-semibold text-gray-900">
                                  {vital.oxygenSaturation || "N/A"}%
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                        {consultation.vitals.length > 2 && (
                          <div className="text-center">
                            <Badge
                              variant="outline"
                              className="text-xs text-red-600 border-red-200 bg-red-50"
                            >
                              +{consultation.vitals.length - 2} more records
                            </Badge>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-2">
                          <Activity className="h-6 w-6 text-red-400" />
                        </div>
                        <p className="text-sm text-gray-500">
                          No vitals recorded
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Clinical Notes Section */}
              <div className="group/notes">
                <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-blue-50 to-sky-50/50 overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-sky-500" />
                  <CardHeader className="pb-4 pt-5">
                    <CardTitle className="text-base flex items-center text-blue-700 font-semibold">
                      <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
                        <FileText className="h-4 w-4 text-blue-600" />
                      </div>
                      Clinical Notes
                      <Badge
                        variant="secondary"
                        className="ml-auto text-xs bg-blue-100 text-blue-700 border-blue-200"
                      >
                        {consultation.clinicalNotes?.length || 0}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0 pb-5">
                    {consultation.clinicalNotes &&
                    consultation.clinicalNotes.length > 0 ? (
                      <div className="space-y-3">
                        {consultation.clinicalNotes
                          .slice(0, 2)
                          .map((note, index) => (
                            <div
                              key={index}
                              className="bg-white/60 rounded-lg p-3 border border-blue-100"
                            >
                              <div className="flex items-center justify-between mb-2">
                                <Badge
                                  variant="outline"
                                  className="text-xs font-medium bg-blue-50 text-blue-700 border-blue-200"
                                >
                                  {note.noteType}
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {new Date(
                                    note.createdAt,
                                  ).toLocaleDateString()}
                                </span>
                              </div>
                              <div className="text-sm text-gray-700 leading-relaxed space-y-1">
                                {note.chiefComplaints && (
                                  <p className="line-clamp-2">
                                    <span className="font-medium text-gray-600">
                                      Chief Complaints:
                                    </span>{" "}
                                    {note.chiefComplaints.substring(0, 80)}
                                    {note.chiefComplaints.length > 80
                                      ? "..."
                                      : ""}
                                  </p>
                                )}
                                {note.allergies && (
                                  <p className="line-clamp-1 text-red-600">
                                    <span className="font-medium">
                                      Allergies:
                                    </span>{" "}
                                    {note.allergies.substring(0, 60)}
                                    {note.allergies.length > 60 ? "..." : ""}
                                  </p>
                                )}
                                {note.content && !note.chiefComplaints && (
                                  <p className="line-clamp-3">
                                    {note.content.substring(0, 120)}
                                    {note.content.length > 120 ? "..." : ""}
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}
                        {consultation.clinicalNotes.length > 2 && (
                          <div className="text-center">
                            <Badge
                              variant="outline"
                              className="text-xs text-blue-600 border-blue-200 bg-blue-50"
                            >
                              +{consultation.clinicalNotes.length - 2} more
                              notes
                            </Badge>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                          <FileText className="h-6 w-6 text-blue-400" />
                        </div>
                        <p className="text-sm text-gray-500">
                          No clinical notes
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Prescriptions Section */}
              <div className="group/prescriptions">
                <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-green-50 to-emerald-50/50 overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-emerald-500" />
                  <CardHeader className="pb-4 pt-5">
                    <CardTitle className="text-base flex items-center text-green-700 font-semibold">
                      <div className="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center mr-3">
                        <Pill className="h-4 w-4 text-green-600" />
                      </div>
                      Prescriptions
                      <Badge
                        variant="secondary"
                        className="ml-auto text-xs bg-green-100 text-green-700 border-green-200"
                      >
                        {consultation.prescriptions?.length || 0}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0 pb-5">
                    {consultation.prescriptions &&
                    consultation.prescriptions.length > 0 ? (
                      <div className="space-y-3">
                        {consultation.prescriptions
                          .slice(0, 2)
                          .map((prescription, index) => (
                            <div
                              key={index}
                              className="bg-white/60 rounded-lg p-3 border border-green-100"
                            >
                              <div className="flex items-center justify-between mb-3">
                                <Badge
                                  variant="outline"
                                  className="text-xs font-medium bg-green-50 text-green-700 border-green-200"
                                >
                                  {prescription.items?.length || 0}{" "}
                                  medication(s)
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {new Date(
                                    prescription.createdAt,
                                  ).toLocaleDateString()}
                                </span>
                              </div>
                              {prescription.items &&
                                prescription.items.length > 0 && (
                                  <div className="space-y-2">
                                    {prescription.items
                                      .slice(0, 3)
                                      .map((item: any, itemIndex: number) => (
                                        <div
                                          key={itemIndex}
                                          className="flex items-start justify-between text-sm"
                                        >
                                          <div className="flex-1 min-w-0">
                                            <p className="font-medium text-gray-900 truncate">
                                              {item.medicationName}
                                            </p>
                                            <p className="text-xs text-gray-600">
                                              {item.dosage} • {item.frequency}
                                            </p>
                                          </div>
                                          <Badge
                                            variant="secondary"
                                            className="text-xs ml-2 bg-gray-100 text-gray-600"
                                          >
                                            {item.duration}
                                          </Badge>
                                        </div>
                                      ))}
                                    {prescription.items.length > 3 && (
                                      <div className="text-center pt-1">
                                        <Badge
                                          variant="outline"
                                          className="text-xs text-green-600 border-green-200 bg-green-50"
                                        >
                                          +{prescription.items.length - 3} more
                                          medications
                                        </Badge>
                                      </div>
                                    )}
                                  </div>
                                )}
                            </div>
                          ))}
                        {consultation.prescriptions.length > 2 && (
                          <div className="text-center">
                            <Badge
                              variant="outline"
                              className="text-xs text-green-600 border-green-200 bg-green-50"
                            >
                              +{consultation.prescriptions.length - 2} more
                              prescriptions
                            </Badge>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2">
                          <Pill className="h-6 w-6 text-green-400" />
                        </div>
                        <p className="text-sm text-gray-500">
                          No prescriptions
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Lab Tests Section */}
              <div className="group/labtests">
                <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-purple-50 to-violet-50/50 overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-violet-500" />
                  <CardHeader className="pb-4 pt-5">
                    <CardTitle className="text-base flex items-center text-purple-700 font-semibold">
                      <div className="w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center mr-3">
                        <FlaskConical className="h-4 w-4 text-purple-600" />
                      </div>
                      Lab Tests
                      <Badge
                        variant="secondary"
                        className="ml-auto text-xs bg-purple-100 text-purple-700 border-purple-200"
                      >
                        {consultation.labTestRequests?.length || 0}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0 pb-5">
                    {consultation.labTestRequests &&
                    consultation.labTestRequests.length > 0 ? (
                      <div className="space-y-3">
                        {consultation.labTestRequests
                          .slice(0, 2)
                          .map((labTest, index) => (
                            <div
                              key={index}
                              className="bg-white/60 rounded-lg p-3 border border-purple-100"
                            >
                              <div className="flex items-center justify-between mb-2">
                                <Badge
                                  variant="outline"
                                  className={`text-xs font-medium border-purple-200 ${
                                    labTest.status === "completed"
                                      ? "bg-green-50 text-green-700 border-green-200"
                                      : labTest.status === "in-progress"
                                        ? "bg-blue-50 text-blue-700 border-blue-200"
                                        : labTest.status === "pending"
                                          ? "bg-orange-50 text-orange-700 border-orange-200"
                                          : "bg-purple-50 text-purple-700"
                                  }`}
                                >
                                  {labTest.status}
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {new Date(
                                    labTest.requestDate,
                                  ).toLocaleDateString()}
                                </span>
                              </div>
                              <div className="space-y-1">
                                <p className="font-medium text-gray-900 text-sm">
                                  {labTest.testName}
                                </p>
                                <p className="text-xs text-gray-600">
                                  {labTest.testType}
                                </p>
                                {labTest.priority &&
                                  labTest.priority !== "routine" && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs bg-red-50 text-red-700 border-red-200"
                                    >
                                      {labTest.priority}
                                    </Badge>
                                  )}
                                {labTest.diagnosticReports &&
                                  labTest.diagnosticReports.length > 0 && (
                                    <div className="mt-2">
                                      <p className="text-xs text-green-600 font-medium">
                                        {labTest.diagnosticReports.length}{" "}
                                        report(s) available
                                      </p>
                                      {labTest.diagnosticReports
                                        .slice(0, 1)
                                        .map(
                                          (
                                            report: any,
                                            reportIndex: number,
                                          ) => (
                                            <p
                                              key={reportIndex}
                                              className="text-xs text-gray-600 truncate"
                                            >
                                              {report.reportType} -{" "}
                                              {report.conclusion?.substring(
                                                0,
                                                50,
                                              )}
                                              {report.conclusion &&
                                              report.conclusion.length > 50
                                                ? "..."
                                                : ""}
                                            </p>
                                          ),
                                        )}
                                    </div>
                                  )}
                              </div>
                            </div>
                          ))}
                        {consultation.labTestRequests.length > 2 && (
                          <div className="text-center">
                            <Badge
                              variant="outline"
                              className="text-xs text-purple-600 border-purple-200 bg-purple-50"
                            >
                              +{consultation.labTestRequests.length - 2} more
                              lab tests
                            </Badge>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mx-auto mb-2">
                          <FlaskConical className="h-6 w-6 text-purple-400" />
                        </div>
                        <p className="text-sm text-gray-500">
                          No lab tests requested
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Follow-up Information */}
            {consultation.followUpDate && (
              <div className="mt-6">
                <Card className="border-0 shadow-md bg-gradient-to-br from-amber-50 to-yellow-50/50 overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-amber-500 to-yellow-500" />
                  <CardContent className="pt-5 pb-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-lg bg-amber-100 flex items-center justify-center flex-shrink-0">
                        <Clock className="h-4 w-4 text-amber-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-base font-semibold text-amber-800 mb-1">
                          Follow-up Scheduled
                        </p>
                        <p className="text-sm text-gray-700 font-medium">
                          {format(
                            new Date(consultation.followUpDate),
                            "EEEE, MMMM d, yyyy",
                          )}
                        </p>
                        {consultation.followUpNotes && (
                          <p className="text-sm text-gray-600 mt-2 leading-relaxed">
                            {consultation.followUpNotes}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Action Button */}
            <div className="mt-6 flex justify-center">
              <Button
                variant="outline"
                size="lg"
                onClick={handleViewFullConsultation}
                className="group flex items-center space-x-2 px-6 py-3 bg-white hover:bg-primary hover:text-white border-2 border-primary/20 hover:border-primary shadow-md hover:shadow-lg transition-all duration-300"
              >
                <ExternalLink className="h-4 w-4 group-hover:scale-110 transition-transform" />
                <span className="font-medium">View Full Consultation</span>
              </Button>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  );
}
