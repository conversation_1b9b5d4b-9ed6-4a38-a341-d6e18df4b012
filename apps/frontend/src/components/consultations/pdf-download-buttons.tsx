"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Download,
  Activity,
  Pill,
  ClipboardList,
  Loader2,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";

interface PdfDownloadButtonsProps {
  consultationId: string;
  vitalsCount: number;
  prescriptionsCount: number;
  clinicalNotesCount: number;
}

interface AvailableTypes {
  vitals: boolean;
  prescription: boolean;
  "clinical-notes": boolean;
}

export function PdfDownloadButtons({
  consultationId,
  vitalsCount,
  prescriptionsCount,
  clinicalNotesCount,
}: PdfDownloadButtonsProps) {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    {},
  );
  const [availableTypes, setAvailableTypes] = useState<AvailableTypes>({
    vitals: false,
    prescription: false,
    "clinical-notes": false,
  });

  useEffect(() => {
    // Update available types based on data counts
    setAvailableTypes({
      vitals: vitalsCount > 0,
      prescription: prescriptionsCount > 0,
      "clinical-notes": clinicalNotesCount > 0,
    });
  }, [vitalsCount, prescriptionsCount, clinicalNotesCount]);

  const handleDownload = async (type: string, title: string) => {
    if (!availableTypes[type as keyof AvailableTypes]) {
      toast.error(`No ${title.toLowerCase()} data available for download`);
      return;
    }

    setLoadingStates((prev) => ({ ...prev, [type]: true }));

    try {
      // Generate PDF and get download URL
      const response = await fetch("/api/pdf/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          consultationId,
          type,
          format: "download",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate PDF");
      }

      // Check if response is PDF
      const contentType = response.headers.get("content-type");
      if (contentType === "application/pdf") {
        // Create blob and download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${title.replace(/\s+/g, "_")}_${new Date().toISOString().split("T")[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success(`${title} PDF downloaded successfully`);
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error(`Error downloading ${type} PDF:`, error);
      toast.error(
        error instanceof Error
          ? error.message
          : `Failed to download ${title.toLowerCase()} PDF`,
      );
    } finally {
      setLoadingStates((prev) => ({ ...prev, [type]: false }));
    }
  };

  const downloadButtons = [
    {
      type: "vitals",
      title: "Vitals PDF",
      description: "Patient vital signs and observations",
      icon: Activity,
      color: "bg-red-50 hover:bg-red-100 border-red-200",
      iconColor: "text-red-600",
      count: vitalsCount,
    },
    {
      type: "prescription",
      title: "Prescription PDF",
      description: "Medication prescriptions and dosage",
      icon: Pill,
      color: "bg-green-50 hover:bg-green-100 border-green-200",
      iconColor: "text-green-600",
      count: prescriptionsCount,
    },
    {
      type: "clinical-notes",
      title: "Clinical Notes PDF",
      description: "Doctor notes and diagnoses",
      icon: ClipboardList,
      color: "bg-blue-50 hover:bg-blue-100 border-blue-200",
      iconColor: "text-blue-600",
      count: clinicalNotesCount,
    },
  ];

  // Don't show the component if no data is available
  if (
    !availableTypes.vitals &&
    !availableTypes.prescription &&
    !availableTypes["clinical-notes"]
  ) {
    return null;
  }

  return (
    <Card className="border-2 border-orange-100 shadow-sm">
      <CardHeader className="bg-orange-50 pb-3">
        <CardTitle className="flex items-center text-orange-700">
          <Download className="h-5 w-5 mr-2" />
          Download PDF Reports
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {downloadButtons.map((button) => {
            const isAvailable =
              availableTypes[button.type as keyof AvailableTypes];
            const isLoading = loadingStates[button.type];

            return (
              <Button
                key={button.type}
                onClick={() => handleDownload(button.type, button.title)}
                disabled={!isAvailable || isLoading}
                variant="outline"
                className={`h-auto p-4 flex flex-col items-start space-y-2 ${
                  isAvailable
                    ? button.color
                    : "bg-gray-50 hover:bg-gray-50 border-gray-200"
                }`}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center space-x-2">
                    <button.icon
                      className={`h-5 w-5 ${
                        isAvailable ? button.iconColor : "text-gray-400"
                      }`}
                    />
                    <span
                      className={`font-medium ${
                        isAvailable ? "text-gray-900" : "text-gray-500"
                      }`}
                    >
                      {button.title}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
                    ) : isAvailable ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-gray-400" />
                    )}
                  </div>
                </div>

                <div className="text-left w-full">
                  <p
                    className={`text-sm ${
                      isAvailable ? "text-gray-600" : "text-gray-400"
                    }`}
                  >
                    {button.description}
                  </p>
                  <p
                    className={`text-xs mt-1 ${
                      isAvailable ? "text-gray-500" : "text-gray-400"
                    }`}
                  >
                    {isAvailable
                      ? `${button.count} record${button.count !== 1 ? "s" : ""} available`
                      : "No data available"}
                  </p>
                </div>

                {isLoading && (
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span>Generating PDF...</span>
                  </div>
                )}
              </Button>
            );
          })}
        </div>

        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-sm text-blue-700">
            <strong>Note:</strong> PDF reports are generated in real-time and
            include all current data.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
