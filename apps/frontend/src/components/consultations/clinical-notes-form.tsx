"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FileText, Loader2, Search, X } from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { AzureFhirUpload } from "@/components/azure-fhir-upload";
import { UploadedFilesList } from "@/components/uploaded-files-list";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
// import { cn } from "@/lib/utils";
import { useDebounce } from "@/hooks/use-debounce";

// Define the SNOMED CT diagnosis type
interface SnomedDiagnosis {
  code: string;
  term: string;
  definition: string;
}

const clinicalNotesFormSchema = z.object({
  // Chief complaints
  chiefComplaints: z.string().optional(),

  // Allergies
  allergies: z.string().optional(),

  // Medical History
  medicalHistory: z.string().optional(),

  // Investigation Advice
  investigationAdvice: z.string().optional(),

  // Procedure
  procedure: z.string().optional(),

  // Follow Up
  followUp: z.string().optional(),

  // Legacy fields
  content: z.string().optional(),
  noteType: z.string(),
  snomedTags: z.string().optional(),
});

type ClinicalNotesFormValues = z.infer<typeof clinicalNotesFormSchema>;

interface ClinicalNotesFormProps {
  consultationId: string;
  patientId: string;
  doctorId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

export function ClinicalNotesForm({
  consultationId,
  patientId,
  doctorId,
  onCancel,
  onSuccess,
}: ClinicalNotesFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [openSnomedSearch, setOpenSnomedSearch] = useState(false);
  const [snomedSearchQuery, setSnomedSearchQuery] = useState("");
  const [snomedSearchResults, setSnomedSearchResults] = useState<
    SnomedDiagnosis[]
  >([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedDiagnoses, setSelectedDiagnoses] = useState<SnomedDiagnosis[]>(
    [],
  );

  const debouncedSearchQuery = useDebounce(snomedSearchQuery, 300);

  const form = useForm<ClinicalNotesFormValues>({
    resolver: zodResolver(clinicalNotesFormSchema),
    defaultValues: {
      chiefComplaints: "",
      allergies: "",
      medicalHistory: "",
      investigationAdvice: "",
      procedure: "",
      followUp: "",
      content: "",
      noteType: "general",
      snomedTags: "",
    },
  });

  // Search for SNOMED CT diagnoses when the debounced search query changes
  useEffect(() => {
    const searchSnomedDiagnoses = async () => {
      if (!debouncedSearchQuery || debouncedSearchQuery.length < 2) {
        setSnomedSearchResults([]);
        return;
      }

      try {
        setIsSearching(true);
        const response = await fetch(
          `/api/snomed?query=${encodeURIComponent(debouncedSearchQuery)}`,
        );

        if (!response.ok) {
          throw new Error("Failed to search SNOMED CT diagnoses");
        }

        const data = await response.json();
        setSnomedSearchResults(data.results);
      } catch (error) {
        console.error("Error searching SNOMED CT diagnoses:", error);
        toast.error("Failed to search diagnoses");
      } finally {
        setIsSearching(false);
      }
    };

    searchSnomedDiagnoses();
  }, [debouncedSearchQuery]);

  // Add a diagnosis to the selected diagnoses
  const addDiagnosis = (diagnosis: SnomedDiagnosis) => {
    // Check if the diagnosis is already selected
    if (!selectedDiagnoses.some((d) => d.code === diagnosis.code)) {
      setSelectedDiagnoses([...selectedDiagnoses, diagnosis]);

      // Update the form field with the codes
      const codes = [...selectedDiagnoses, diagnosis]
        .map((d) => d.code)
        .join(",");
      form.setValue("snomedTags", codes);
    }

    // Close the popover and reset the search
    setOpenSnomedSearch(false);
    setSnomedSearchQuery("");
  };

  // Remove a diagnosis from the selected diagnoses
  const removeDiagnosis = (code: string) => {
    const updatedDiagnoses = selectedDiagnoses.filter((d) => d.code !== code);
    setSelectedDiagnoses(updatedDiagnoses);

    // Update the form field with the codes
    const codes = updatedDiagnoses.map((d) => d.code).join(",");
    form.setValue("snomedTags", codes);
  };

  const onSubmit = async (data: ClinicalNotesFormValues) => {
    try {
      setIsSubmitting(true);

      // Prepare SNOMED diagnoses data
      const snomedDiagnosesData = selectedDiagnoses.map((diagnosis) => ({
        code: diagnosis.code,
        term: diagnosis.term,
        definition: diagnosis.definition,
      }));

      const response = await fetch("/api/clinical-notes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          consultationId,
          patientId,
          doctorId,
          chiefComplaints: data.chiefComplaints,
          allergies: data.allergies,
          medicalHistory: data.medicalHistory,
          investigationAdvice: data.investigationAdvice,
          procedure: data.procedure,
          followUp: data.followUp,
          content: data.content,
          noteType: data.noteType,
          snomedTags: data.snomedTags
            ? data.snomedTags.split(",").map((tag) => tag.trim())
            : [],
          snomedDiagnoses: snomedDiagnosesData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create clinical note");
      }

      toast.success("Clinical note created successfully");
      onSuccess();
    } catch (error) {
      console.error("Error creating clinical note:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to create clinical note",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="border-2 border-primary/10 shadow-md">
      <CardHeader className="bg-muted/30 pb-4">
        <CardTitle className="text-xl font-bold flex items-center text-primary">
          <FileText className="h-5 w-5 mr-2" /> Add Clinical Note
        </CardTitle>
        <CardDescription>
          Record clinical observations, diagnoses, and treatment plans
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-6 pt-4">
            {/* Chief Complaints Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Chief Complaints
              </h3>
              <FormField
                control={form.control}
                name="chiefComplaints"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-foreground font-medium">
                      Chief Complaints
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter patient's primary complaints and symptoms..."
                        className="min-h-[100px] bg-background resize-y"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Record the main reasons for the patient's visit
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Allergies Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Allergies
              </h3>
              <FormField
                control={form.control}
                name="allergies"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-foreground font-medium">
                      Known Allergies
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="List any known allergies, drug reactions, or sensitivities..."
                        className="min-h-[100px] bg-background resize-y"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Include medications, foods, environmental allergens, etc.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Medical History Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Medical History
              </h3>
              <FormField
                control={form.control}
                name="medicalHistory"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-foreground font-medium">
                      Past Medical History
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter relevant past medical history, previous conditions, surgeries..."
                        className="min-h-[120px] bg-background resize-y"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Include chronic conditions, previous hospitalizations,
                      surgeries
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Investigation Advice Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Investigation Advice
              </h3>
              <FormField
                control={form.control}
                name="investigationAdvice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-foreground font-medium">
                      Recommended Investigations
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="List recommended tests, lab work, imaging studies..."
                        className="min-h-[100px] bg-background resize-y"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Include lab tests, imaging, diagnostic procedures
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Procedure Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Procedure
              </h3>
              <FormField
                control={form.control}
                name="procedure"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-foreground font-medium">
                      Procedures Performed
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe any procedures performed during this consultation..."
                        className="min-h-[100px] bg-background resize-y"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Include minor procedures, examinations, interventions
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Follow Up Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Follow Up
              </h3>
              <FormField
                control={form.control}
                name="followUp"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-foreground font-medium">
                      Follow-up Instructions
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter follow-up instructions, next appointment recommendations..."
                        className="min-h-[100px] bg-background resize-y"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Include when to return, warning signs to watch for
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Note Information Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Note Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="noteType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground font-medium">
                        Note Type
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-background">
                            <SelectValue placeholder="Select note type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="general">General</SelectItem>
                          <SelectItem value="diagnosis">Diagnosis</SelectItem>
                          <SelectItem value="treatment">Treatment</SelectItem>
                          <SelectItem value="followup">Follow-up</SelectItem>
                          <SelectItem value="lab">Lab Results</SelectItem>
                          <SelectItem value="imaging">
                            Imaging Results
                          </SelectItem>
                          <SelectItem value="procedure">Procedure</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the type of clinical note you are recording
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Additional Clinical Observations Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Additional Clinical Observations
              </h3>
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-foreground font-medium">
                      Additional Notes
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional clinical observations, findings, and treatment plans..."
                        className="min-h-[150px] bg-background resize-y"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Include any other relevant clinical information not
                      covered above
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Diagnoses (SNOMED CT)
              </h3>
              <div className="flex gap-2 mt-3 mb-3">
                <Popover
                  open={openSnomedSearch}
                  onOpenChange={setOpenSnomedSearch}
                >
                  <PopoverTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-9 bg-primary/5 border-primary/20 hover:bg-primary/10 text-primary"
                      onClick={() => setOpenSnomedSearch(true)}
                    >
                      <Search className="mr-2 h-4 w-4" />
                      Search SNOMED CT Diagnoses
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="p-0 w-[350px]"
                    side="bottom"
                    align="start"
                    alignOffset={0}
                  >
                    <Command>
                      <CommandInput
                        placeholder="Search SNOMED CT diagnoses..."
                        value={snomedSearchQuery}
                        onValueChange={setSnomedSearchQuery}
                        className="h-10"
                      />
                      <CommandList className="max-h-[300px]">
                        <CommandEmpty>
                          {isSearching ? (
                            <div className="flex items-center justify-center p-4">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              <span>Searching...</span>
                            </div>
                          ) : (
                            "No diagnoses found."
                          )}
                        </CommandEmpty>
                        <CommandGroup>
                          {snomedSearchResults.map((diagnosis) => (
                            <CommandItem
                              key={diagnosis.code}
                              value={diagnosis.term}
                              onSelect={() => addDiagnosis(diagnosis)}
                              className="cursor-pointer"
                            >
                              <div className="flex flex-col">
                                <span className="font-medium">
                                  {diagnosis.term}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  <span className="font-mono bg-muted px-1 py-0.5 rounded text-xs mr-1">
                                    {diagnosis.code}
                                  </span>
                                  {diagnosis.definition}
                                </span>
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              <FormField
                control={form.control}
                name="snomedTags"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className="text-foreground font-medium">
                      SNOMED CT Diagnoses
                    </FormLabel>
                    <div className="bg-background p-3 rounded-md border min-h-[60px]">
                      <div className="flex flex-wrap gap-2 mb-2">
                        {selectedDiagnoses.length === 0 && (
                          <p className="text-sm text-muted-foreground italic">
                            No diagnoses selected
                          </p>
                        )}
                        {selectedDiagnoses.map((diagnosis) => (
                          <Badge
                            key={diagnosis.code}
                            variant="secondary"
                            className="flex items-center gap-1 bg-blue-50 text-blue-700 hover:bg-blue-100 border border-blue-200"
                          >
                            {diagnosis.term}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 text-blue-500 hover:text-blue-700 hover:bg-transparent"
                              onClick={() => removeDiagnosis(diagnosis.code)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <FormControl>
                      <Input type="hidden" {...field} />
                    </FormControl>
                    <FormDescription>
                      Search and select relevant SNOMED CT diagnoses for this
                      clinical note
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Document Upload Section */}
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground">
                Clinical Notes Document Upload (Optional)
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Upload a PDF clinical notes document that will be included in
                the FHIR bundle when clinical notes are saved.
              </p>
              <AzureFhirUpload
                consultationId={consultationId}
                patientId={patientId}
                bundleType="OPConsultationRecord"
                title="Upload Clinical Notes Document"
                description="Upload a PDF clinical notes document"
                onUploadSuccess={(_result) => {
                  toast.success(
                    "Clinical notes document uploaded successfully. It will be included when clinical notes are saved.",
                  );
                }}
                hideWhenFilesExist={false}
              />
              <UploadedFilesList
                consultationId={consultationId}
                bundleType="OPConsultationRecord"
                title="Uploaded Clinical Notes Documents"
                description="Documents that will be included in the FHIR bundle"
                showOnlyGenerated={false}
                className="mt-4"
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between bg-muted/20 border-t pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="border-gray-300 hover:bg-gray-100"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  Save Clinical Note
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
