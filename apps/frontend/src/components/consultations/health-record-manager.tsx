"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle, FileText } from "lucide-react";
import { toast } from "sonner";
import {
  packageHealthRecord,
  uploadHealthRecord,
  uploadHealthRecordToDataPushUrl,
  getPatientFhirBundles,
  getHealthRecordFetchRequests,
  canShareHealthRecord,
} from "@/services/health-record-service";

interface ConsultationHealthRecordManagerProps {
  consultationId: string;
  patientId: string;
  vitals: any[];
  clinicalNotes: any[];
  prescriptions: any[];
}

export function ConsultationHealthRecordManager({
  consultationId,
  patientId,
  vitals,
  clinicalNotes,
  prescriptions,
}: ConsultationHealthRecordManagerProps) {
  const [selectedTab, setSelectedTab] = useState("vitals");
  const [selectedRecord, setSelectedRecord] = useState<string | null>(null);
  const [isPackaging, setIsPackaging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [bundleId, setBundleId] = useState<string | null>(null);
  const [bundles, setBundles] = useState<any[]>([]);
  const [fetchRequests, setFetchRequests] = useState<any[]>([]);
  const [selectedFetchRequest, setSelectedFetchRequest] = useState<
    string | null
  >(null);
  const [isUploadingToDataPushUrl, setIsUploadingToDataPushUrl] =
    useState(false);
  const [activeConsents, setActiveConsents] = useState<any[]>([]);
  const [selectedConsent, setSelectedConsent] = useState<string | null>(null);
  const [careContextReference, setCareContextReference] = useState("");
  const [recipientPublicKey, setRecipientPublicKey] = useState("");
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [showDataPushDialog, setShowDataPushDialog] = useState(false);

  // Load bundles and fetch requests on initial render
  useEffect(() => {
    loadBundles();
    loadFetchRequests();
  }, []);

  // Get record type based on selected tab
  const getRecordType = () => {
    switch (selectedTab) {
      case "vitals":
        return "vitals";
      case "prescriptions":
        return "prescription";
      case "clinicalNotes":
        return "clinicalNote";
      default:
        return "";
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Check if record can be shared
  const checkCanShare = async () => {
    try {
      const result = await canShareHealthRecord(patientId, getRecordType());
      setActiveConsents(result.activeConsents);

      if (!result.canShare) {
        toast.error(
          "No active consents found for sharing this type of health record",
        );
      }

      return result.canShare;
    } catch (error) {
      console.error("Error checking if health record can be shared:", error);
      if (
        error instanceof Error &&
        error.message.includes("Authentication error")
      ) {
        toast.error("Authentication error: Please log in again");
      } else {
        toast.error(
          `Failed to check if health record can be shared: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
        );
      }
      return false;
    }
  };

  // Load FHIR bundles
  const loadBundles = async () => {
    try {
      const bundles = await getPatientFhirBundles(patientId);
      setBundles(bundles);
    } catch (error) {
      console.error("Error loading FHIR bundles:", error);
      if (
        error instanceof Error &&
        error.message.includes("Authentication error")
      ) {
        toast.error("Authentication error: Please log in again");
      } else {
        toast.error(
          `Failed to load FHIR bundles: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
        );
      }
      throw error; // Re-throw to allow caller to handle
    }
  };

  // Load health record fetch requests
  const loadFetchRequests = async () => {
    try {
      const requests = await getHealthRecordFetchRequests(patientId);
      setFetchRequests(requests);
    } catch (error) {
      console.error("Error loading health record fetch requests:", error);
      if (
        error instanceof Error &&
        error.message.includes("Authentication error")
      ) {
        toast.error("Authentication error: Please log in again");
      } else {
        toast.error(
          `Failed to load health record fetch requests: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
        );
      }
      // Non-critical error, don't re-throw
    }
  };

  // Handle record selection
  const handleRecordSelect = (recordId: string) => {
    setSelectedRecord(recordId);
  };

  // Get care context reference for the consultation
  const getCareContextReference = async () => {
    try {
      const response = await fetch(
        `/api/consultations/${consultationId}/care-context`,
      );
      if (!response.ok) {
        if (response.status === 401) {
          toast.error("Authentication error: Please log in again");
          throw new Error("Authentication error");
        } else {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || "Failed to fetch care context");
        }
      }

      const data = await response.json();
      if (data.careContext) {
        return data.careContext.consultationId || consultationId;
      }
      return consultationId; // Fallback to consultationId if no care context found
    } catch (error) {
      console.error("Error fetching care context:", error);
      if (
        !(error instanceof Error && error.message === "Authentication error")
      ) {
        toast.error(
          `Error fetching care context: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
        );
      }
      return consultationId; // Fallback to consultationId
    }
  };

  // Get the most recent active consent
  const getActiveConsent = async () => {
    try {
      const result = await canShareHealthRecord(patientId, getRecordType());
      if (result.activeConsents && result.activeConsents.length > 0) {
        // Sort by creation date (newest first) and return the first one
        const sortedConsents = [...result.activeConsents].sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );
        return sortedConsents[0].consentId;
      }
      return null;
    } catch (error) {
      console.error("Error getting active consent:", error);
      if (
        error instanceof Error &&
        error.message.includes("Authentication error")
      ) {
        toast.error("Authentication error: Please log in again");
      } else {
        toast.error(
          `Error getting active consent: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
        );
      }
      return null;
    }
  };

  // Handle package button click
  const handlePackage = async () => {
    if (!selectedRecord) {
      toast.error("Please select a record to package");
      return;
    }

    try {
      setIsPackaging(true);

      // Check if record can be shared
      try {
        const canShareResult = await checkCanShare();
        if (!canShareResult) {
          setIsPackaging(false);
          return;
        }
      } catch (error) {
        // Error already handled in checkCanShare
        setIsPackaging(false);
        return;
      }

      // Package health record
      let result;
      try {
        result = await packageHealthRecord(
          patientId,
          getRecordType(),
          selectedRecord,
          consultationId, // Pass consultationId for context
        );
        setBundleId(result.bundleId);
        toast.success("Health record packaged successfully");
      } catch (error) {
        if (
          error instanceof Error &&
          error.message.includes("Authentication error")
        ) {
          toast.error("Authentication error: Please log in again");
        } else {
          toast.error(
            `Failed to package health record: ${
              error instanceof Error ? error.message : "Unknown error"
            }`,
          );
        }
        setIsPackaging(false);
        return;
      }

      // Load bundles
      try {
        await loadBundles();
      } catch (error) {
        // Non-critical error, continue with upload
        console.error("Error loading bundles:", error);
      }

      // Auto-populate required fields
      let consentId;
      try {
        consentId = await getActiveConsent();
        if (!consentId) {
          toast.error("No active consent found for automatic upload");
          setIsPackaging(false);
          return;
        }
      } catch (error) {
        // Error already handled in getActiveConsent
        setIsPackaging(false);
        return;
      }

      let careContextRef;
      try {
        careContextRef = await getCareContextReference();
      } catch (error) {
        // Error already handled in getCareContextReference
        setIsPackaging(false);
        return;
      }

      // Use a default public key or generate one
      // In a real system, this would be fetched from a secure source
      const defaultPublicKey =
        "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2AXWxqIgzm+wFOQR5F5nFaiNT1yy1Eb5GvrIJ5Ypz5zYbVQiNQQ4rK4X9aFKzR1Eb8rkQzN5PwMY5Q9SpQvCy3RyP9tZqGIBXYYQBJVpG3aRYE5EqUcKDnFAcLuN5b3BVEjcgGpYUoCCQpZRGc+BZY5ixE9zHuKpyNQJi6gIpUmQAUUSdNLeiOcXcY5a1PrJcC7iIGNzBQ0jIRNBLJqXYLUKoA9HgQKgpCgtsQaCF7qBSJQz6ZhRYtmJXq0Xn5aMsLLsfKi+UAzlI2VVKbzKcNotDtY9QiDYmhZ0kBsCCfMX3HQpDwJc+lDyOYczDrB9qJJAml6FVdz2YEFLExXVZwIDAQAB";

      // Directly upload the health record
      try {
        await uploadHealthRecord(
          result.bundleId,
          consentId,
          careContextRef,
          defaultPublicKey,
        );
        toast.success("Health record packaged and uploaded successfully");

        // Reset form
        setSelectedRecord(null);
        setBundleId(null);
      } catch (error) {
        if (
          error instanceof Error &&
          error.message.includes("Authentication error")
        ) {
          toast.error("Authentication error: Please log in again");
        } else {
          toast.error(
            `Failed to upload health record: ${
              error instanceof Error ? error.message : "Unknown error"
            }`,
          );
        }
      }
    } catch (error: any) {
      console.error("Error packaging/uploading health record:", error);
      toast.error(
        `Failed to process health record: ${error.message || "Unknown error"}`,
      );
    } finally {
      setIsPackaging(false);
    }
  };

  // Handle upload button click
  const handleUpload = async () => {
    if (!bundleId) {
      toast.error("No bundle ID found");
      return;
    }

    if (!selectedConsent) {
      toast.error("Please select a consent");
      return;
    }

    if (!careContextReference) {
      toast.error("Please enter a care context reference");
      return;
    }

    if (!recipientPublicKey) {
      toast.error("Please enter a recipient public key");
      return;
    }

    try {
      setIsUploading(true);

      // Upload health record
      await uploadHealthRecord(
        bundleId,
        selectedConsent,
        careContextReference,
        recipientPublicKey,
      );

      toast.success("Health record uploaded successfully");

      // Close upload dialog
      setShowUploadDialog(false);

      // Reset form
      setSelectedRecord(null);
      setBundleId(null);
      setSelectedConsent(null);
      setCareContextReference("");
      setRecipientPublicKey("");

      // Load bundles
      await loadBundles();
    } catch (error: any) {
      console.error("Error uploading health record:", error);
      toast.error(
        `Failed to upload health record: ${error.message || "Unknown error"}`,
      );
    } finally {
      setIsUploading(false);
    }
  };

  // Handle upload to dataPushUrl button click
  const handleUploadToDataPushUrl = async () => {
    if (!bundleId) {
      toast.error("No bundle ID found");
      return;
    }

    if (!selectedFetchRequest) {
      toast.error("Please select a fetch request");
      return;
    }

    try {
      setIsUploadingToDataPushUrl(true);

      // Upload health record to dataPushUrl
      await uploadHealthRecordToDataPushUrl(bundleId, selectedFetchRequest);

      toast.success("Health record uploaded to dataPushUrl successfully");

      // Close dataPush dialog
      setShowDataPushDialog(false);

      // Reset form
      setSelectedRecord(null);
      setBundleId(null);
      setSelectedFetchRequest(null);

      // Load bundles and fetch requests
      await loadBundles();
      await loadFetchRequests();
    } catch (error: any) {
      console.error("Error uploading health record to dataPushUrl:", error);
      toast.error(
        `Failed to upload health record to dataPushUrl: ${
          error.message || "Unknown error"
        }`,
      );
    } finally {
      setIsUploadingToDataPushUrl(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5 text-primary" />
              Health Record Management
            </CardTitle>
            <CardDescription>
              Package and upload health records to ABDM
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue="vitals"
          value={selectedTab}
          onValueChange={(value) => {
            setSelectedTab(value);
            setSelectedRecord(null);
          }}
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="vitals">Vitals</TabsTrigger>
            <TabsTrigger value="prescriptions">Prescriptions</TabsTrigger>
            <TabsTrigger value="clinicalNotes">Clinical Notes</TabsTrigger>
          </TabsList>
          <TabsContent value="vitals">
            <div className="space-y-4 mt-4">
              <h3 className="text-sm font-medium">Select Vitals Record</h3>
              {vitals.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>No vitals records found</AlertTitle>
                  <AlertDescription>
                    There are no vitals records available for this consultation.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="grid gap-2">
                  {vitals.map((record) => (
                    <div
                      key={record.id}
                      className={`p-3 border rounded-md cursor-pointer ${
                        selectedRecord === record.id
                          ? "border-primary bg-primary/5"
                          : "border-border"
                      }`}
                      onClick={() => handleRecordSelect(record.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">
                            Vitals - {formatDate(record.recordedAt)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {record.bloodPressureSystolic &&
                            record.bloodPressureDiastolic
                              ? `BP: ${record.bloodPressureSystolic}/${record.bloodPressureDiastolic} mmHg`
                              : ""}
                            {record.pulse
                              ? ` | Pulse: ${record.pulse} bpm`
                              : ""}
                            {record.temperature
                              ? ` | Temp: ${record.temperature}°C`
                              : ""}
                          </p>
                        </div>
                        {selectedRecord === record.id && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="prescriptions">
            <div className="space-y-4 mt-4">
              <h3 className="text-sm font-medium">Select Prescription</h3>
              {prescriptions.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>No prescriptions found</AlertTitle>
                  <AlertDescription>
                    There are no prescriptions available for this consultation.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="grid gap-2">
                  {prescriptions.map((record) => (
                    <div
                      key={record.id}
                      className={`p-3 border rounded-md cursor-pointer ${
                        selectedRecord === record.id
                          ? "border-primary bg-primary/5"
                          : "border-border"
                      }`}
                      onClick={() => handleRecordSelect(record.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">
                            Prescription - {formatDate(record.prescriptionDate)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {record.items?.length || 0} medications
                          </p>
                        </div>
                        {selectedRecord === record.id && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="clinicalNotes">
            <div className="space-y-4 mt-4">
              <h3 className="text-sm font-medium">Select Clinical Note</h3>
              {clinicalNotes.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>No clinical notes found</AlertTitle>
                  <AlertDescription>
                    There are no clinical notes available for this consultation.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="grid gap-2">
                  {clinicalNotes.map((record) => (
                    <div
                      key={record.id}
                      className={`p-3 border rounded-md cursor-pointer ${
                        selectedRecord === record.id
                          ? "border-primary bg-primary/5"
                          : "border-border"
                      }`}
                      onClick={() => handleRecordSelect(record.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">
                            Clinical Note - {formatDate(record.createdAt)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {record.content.substring(0, 50)}
                            {record.content.length > 50 ? "..." : ""}
                          </p>
                        </div>
                        {selectedRecord === record.id && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-6">
          <h3 className="text-sm font-medium mb-2">FHIR Bundles</h3>
          {bundles.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>No FHIR bundles found</AlertTitle>
              <AlertDescription>
                There are no FHIR bundles available for this patient.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid gap-2">
              {bundles.map((bundle) => (
                <div key={bundle.id} className="p-3 border rounded-md">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">
                        Bundle - {bundle.bundleType}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Created: {formatDate(bundle.createdAt)}
                      </p>
                    </div>
                    <Badge
                      variant={
                        bundle.status === "created"
                          ? "outline"
                          : bundle.status === "packaged"
                            ? "secondary"
                            : bundle.status === "uploaded"
                              ? "default"
                              : "destructive"
                      }
                    >
                      {bundle.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadBundles}>
            Refresh Bundles
          </Button>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowDataPushDialog(true)}
            disabled={!bundleId || fetchRequests.length === 0}
          >
            Upload to DataPushUrl
          </Button>
          <Button
            onClick={handlePackage}
            disabled={!selectedRecord || isPackaging}
          >
            {isPackaging ? "Processing..." : "Process Record"}
          </Button>
        </div>
      </CardFooter>

      {/* Upload Dialog */}
      <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Upload Health Record</DialogTitle>
            <DialogDescription>
              Upload the packaged health record to ABDM.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="consent" className="text-right">
                Consent
              </Label>
              <Select
                value={selectedConsent || ""}
                onValueChange={setSelectedConsent}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select consent" />
                </SelectTrigger>
                <SelectContent>
                  {activeConsents.map((consent) => (
                    <SelectItem
                      key={consent.consentId}
                      value={consent.consentId}
                    >
                      {consent.purpose} - {formatDate(consent.expiryDate)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="careContext" className="text-right">
                Care Context
              </Label>
              <Input
                id="careContext"
                value={careContextReference}
                onChange={(e) => setCareContextReference(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="publicKey" className="text-right">
                Public Key
              </Label>
              <Input
                id="publicKey"
                value={recipientPublicKey}
                onChange={(e) => setRecipientPublicKey(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowUploadDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              disabled={
                !bundleId ||
                !selectedConsent ||
                !careContextReference ||
                !recipientPublicKey ||
                isUploading
              }
            >
              {isUploading ? "Uploading..." : "Upload"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* DataPush Dialog */}
      <Dialog open={showDataPushDialog} onOpenChange={setShowDataPushDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Upload Health Record to DataPushUrl</DialogTitle>
            <DialogDescription>
              Upload the packaged health record to a dataPushUrl from a fetch
              request.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="fetchRequest" className="text-right">
                Fetch Request
              </Label>
              <Select
                value={selectedFetchRequest || ""}
                onValueChange={setSelectedFetchRequest}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select fetch request" />
                </SelectTrigger>
                <SelectContent>
                  {fetchRequests.map((request) => (
                    <SelectItem key={request.id} value={request.id}>
                      {request.transactionId} -{" "}
                      {new Date(request.requestTimestamp).toLocaleString()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDataPushDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUploadToDataPushUrl}
              disabled={
                !bundleId || !selectedFetchRequest || isUploadingToDataPushUrl
              }
            >
              {isUploadingToDataPushUrl ? "Uploading..." : "Upload"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
