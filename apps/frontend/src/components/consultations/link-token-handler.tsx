"use client";

import { useState, useEffect } from "react";
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, LinkIcon, Loader2 } from "lucide-react";
import { toast } from "sonner";

// Helper function to get cookie value by name
function getCookie(name: string): string | null {
  if (typeof document === "undefined") return null; // SSR check

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(";").shift() || null;
  }
  return null;
}

// Helper function to parse branch ID from cookies
function getBranchIdFromCookies(): string | null {
  // First check if we have default-branch cookie (contains full branch object)
  // const defaultBranchCookie = getCookie("default-branch");
  // if (defaultBranchCookie) {
  //   try {
  //     const branch = JSON.parse(decodeURIComponent(defaultBranchCookie));
  //     return branch.id;
  //   } catch (error) {
  //     console.error("Error parsing default-branch cookie:", error);
  //   }
  // }

  // Then check if we have current-branch cookie
  const currentBranchCookie = getCookie("current-branch");
  if (currentBranchCookie) {
    try {
      const branchInfo = JSON.parse(decodeURIComponent(currentBranchCookie));
      console.log("Branch info from cookie:", branchInfo);
      return branchInfo.id;
    } catch (error) {
      console.error("Error parsing current-branch cookie:", error);
    }
  }

  return null;
}

interface LinkTokenHandlerProps {
  patientId: string;
  consultationId: string;
  appointmentId?: string;
  hasCareContext?: boolean;
  onSuccess?: () => void;
}

export function LinkTokenHandler({
  patientId,
  consultationId,
  appointmentId,
  hasCareContext = false,
  onSuccess,
}: LinkTokenHandlerProps) {
  // DEPRECATED: This component is no longer used - care context is automatically created when consultation is completed
  return null;
  const [hasAbhaProfile, setHasAbhaProfile] = useState(false);
  const [hasLinkToken, setHasLinkToken] = useState(false);
  const [isPendingToken, setIsPendingToken] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneratingToken, setIsGeneratingToken] = useState(false);
  const [isCreatingCareContext, setIsCreatingCareContext] = useState(false);
  const [branchId, setBranchId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [_updatedAt, setUpdatedAt] = useState<string | null>(null);
  const [canRetry, setCanRetry] = useState(false);
  const [forceRegenerate, setForceRegenerate] = useState(false);
  const [retryCountdown, setRetryCountdown] = useState(0);

  // Check ABHA profile, link token status, and care context
  useEffect(() => {
    const checkStatus = async () => {
      if (!patientId || !consultationId) return;

      try {
        setIsLoading(true);
        setError(null);

        // First check if a care context already exists for this consultation
        if (hasCareContext) {
          // If we already know a care context exists, skip further checks
          console.log("hasCareContext prop is true, skipping checks");
          setIsLoading(false);
          return;
        }

        // Double-check with the API if a care context exists for this specific consultation
        console.log(
          `Checking care context for consultation: ${consultationId}`,
        );
        const careContextResponse = await fetch(
          `/api/consultations/${consultationId}/care-context`,
        );
        if (careContextResponse.ok) {
          const careContextData = await careContextResponse.json();
          console.log("Care context check response:", careContextData);

          if (careContextData.careContext) {
            // If a care context exists, update the state and skip further checks
            console.log("Care context found, hiding banner");
            if (onSuccess) {
              onSuccess(); // This will set hasCareContext to true in the parent component
            }
            setIsLoading(false);
            return;
          } else {
            console.log("No care context found for this consultation");
          }
        }

        // Get branch ID from cookies
        const cookieBranchId = getBranchIdFromCookies();
        console.log("Branch ID from cookies:", cookieBranchId);
        setBranchId(cookieBranchId);

        if (!cookieBranchId) {
          console.error("No branch ID found in cookies");
          setError("No branch selected. Please select a branch first.");
          setIsLoading(false);
          return;
        }

        // Check if the patient has an ABHA profile
        const profileResponse = await fetch(
          `/api/patients/${patientId}/abha-profile`,
        );

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();
          setHasAbhaProfile(!!profileData.abhaProfile?.abhaNumber);

          // If they have an ABHA profile, check for link token
          if (profileData.abhaProfile?.abhaNumber) {
            // Check for branch-specific link token
            const tokenResponse = await fetch(
              `/api/patients/${patientId}/care-contexts/link-token?branchId=${cookieBranchId}`,
              { method: "GET" },
            );

            if (tokenResponse.ok) {
              const tokenData = await tokenResponse.json();
              console.log("Token check response:", tokenData);
              setIsPendingToken(!!tokenData.isPendingToken);
              setHasLinkToken(!!tokenData.hasLinkToken);

              // Set updatedAt and canRetry if available
              if (tokenData.updatedAt) {
                setUpdatedAt(tokenData.updatedAt);
              }

              setCanRetry(!!tokenData.canRetry);

              // Calculate countdown if token is pending
              if (tokenData.isPendingToken && tokenData.updatedAt) {
                calculateCountdown(new Date(tokenData.updatedAt));
              }
            } else {
              console.error("Failed to check link token status");
            }
          }
        } else {
          setError("Failed to check ABHA status");
        }
      } catch (error) {
        console.error("Error checking status:", error);
        setError("An error occurred while checking status");
      } finally {
        setIsLoading(false);
      }
    };

    checkStatus();
  }, [patientId, consultationId, hasCareContext, onSuccess]);

  // Calculate countdown based on updatedAt time
  const calculateCountdown = (updatedTime: Date) => {
    const now = new Date();
    const fiveMinutesLater = new Date(updatedTime);
    fiveMinutesLater.setMinutes(fiveMinutesLater.getMinutes() + 5);

    // If 5 minutes have already passed
    if (now >= fiveMinutesLater) {
      setRetryCountdown(0);
      setCanRetry(true);
      return;
    }

    // Calculate remaining seconds
    const remainingMs = fiveMinutesLater.getTime() - now.getTime();
    const remainingSeconds = Math.ceil(remainingMs / 1000);

    setRetryCountdown(remainingSeconds);
    console.log(
      `Countdown set to ${Math.floor(remainingSeconds / 60)}:${(remainingSeconds % 60).toString().padStart(2, "0")}`,
    );
  };

  // Set up polling if we're waiting for a token
  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    if (isPendingToken && branchId) {
      intervalId = setInterval(async () => {
        try {
          const tokenResponse = await fetch(
            `/api/patients/${patientId}/care-contexts/link-token?branchId=${branchId}`,
            { method: "GET" },
          );

          if (tokenResponse.ok) {
            const tokenData = await tokenResponse.json();

            if (tokenData.hasLinkToken) {
              // Token is available
              setHasLinkToken(true);
              setIsPendingToken(false);
              toast.success("Link token is now available");
              clearInterval(intervalId!);
            } else if (tokenData.isPendingToken) {
              // Update the updatedAt timestamp if provided
              if (tokenData.updatedAt) {
                setUpdatedAt(tokenData.updatedAt);
                calculateCountdown(new Date(tokenData.updatedAt));
              }

              // Set canRetry based on server response
              setCanRetry(!!tokenData.canRetry);
            }
          }
        } catch (error) {
          console.error("Error polling for token:", error);
        }
      }, 5000); // Check every 5 seconds
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isPendingToken, branchId, patientId]);

  // Generate link token
  const handleGenerateToken = async () => {
    if (!patientId) {
      toast.error("Missing patient information");
      return;
    }

    if (!branchId) {
      toast.error("Missing branch information. Please select a branch first.");
      return;
    }

    try {
      setIsGeneratingToken(true);
      console.log("Generating link token with branchId:", branchId);

      // If we're retrying, we need to handle the existing pending token
      let requestBody: any = {
        branchId: branchId,
        createEmpty: true, // Create an empty record first
      };

      // If we're retrying or forcing regeneration, add the flag
      if ((isPendingToken && canRetry) || forceRegenerate) {
        requestBody.forceRegenerate = true; // Add flag to force regeneration
      }

      const response = await fetch(
        `/api/patients/${patientId}/care-contexts/link-token`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate link token");
      }

      const data = await response.json();
      console.log("Token generation response:", data);

      // Reset the retry state if we're retrying or forcing regeneration
      if ((isPendingToken && canRetry) || forceRegenerate) {
        setCanRetry(false);
        setForceRegenerate(false);
        setRetryCountdown(300);
      }

      toast.success(data.message || "Link token generation initiated");
      setIsPendingToken(true);
    } catch (error) {
      console.error("Error generating link token:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to generate link token",
      );
    } finally {
      setIsGeneratingToken(false);
    }
  };

  // Create care context
  const handleCreateCareContext = async () => {
    if (!patientId || !consultationId || !branchId) {
      toast.error("Missing required information");
      return;
    }

    try {
      setIsCreatingCareContext(true);

      // First check if a care context already exists for this consultation
      console.log(
        `Checking care context before creation for consultation: ${consultationId}`,
      );
      const careContextResponse = await fetch(
        `/api/consultations/${consultationId}/care-context`,
      );
      if (careContextResponse.ok) {
        const careContextData = await careContextResponse.json();
        if (careContextData.careContext) {
          // If a care context already exists, just update the state and return
          console.log("Care context already exists, skipping creation");
          toast.success("Care context already exists for this consultation");
          if (onSuccess) {
            onSuccess();
          }
          setIsCreatingCareContext(false);
          return;
        }
      }

      const response = await fetch(
        `/api/patients/${patientId}/care-contexts/link`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            consultationId,
            appointmentId,
            branchId,
            hiTypes: ["DiagnosticReport", "Prescription", "OPConsultation"],
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create care context");
      }

      const data = await response.json();

      // Notify about the care context
      const notifyResponse = await fetch(
        `/api/patients/${patientId}/care-contexts/notify`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            careContextId: data.careContext.id,
          }),
        },
      );

      if (!notifyResponse.ok) {
        const errorData = await notifyResponse.json();
        console.error("Error notifying care context:", errorData);
        // Continue anyway since the care context was created
      }

      toast.success("Care context created successfully");

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error creating care context:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to create care context",
      );
    } finally {
      setIsCreatingCareContext(false);
    }
  };

  // If a care context already exists, don't show any banner
  if (hasCareContext) {
    console.log("hasCareContext is true, not showing banner");
    return null;
  }

  console.log("hasCareContext is false, showing banner");

  if (isLoading) {
    return (
      <Alert className="bg-blue-50 text-blue-800 border-blue-200">
        <AlertCircle className="h-4 w-4 text-blue-500" />
        <AlertTitle>Checking ABHA status</AlertTitle>
        <AlertDescription>
          Please wait while we check the patient's ABHA status...
        </AlertDescription>
      </Alert>
    );
  }

  if (error) {
    return (
      <Alert className="bg-red-50 text-red-800 border-red-200">
        <AlertCircle className="h-4 w-4 text-red-500" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!hasAbhaProfile) {
    return (
      <Alert className="bg-yellow-50 text-yellow-800 border-yellow-200">
        <AlertCircle className="h-4 w-4 text-yellow-500" />
        <AlertTitle>ABHA Not Linked</AlertTitle>
        <AlertDescription>
          This patient does not have an ABHA profile. Link the patient to ABHA
          to enable care context creation.
        </AlertDescription>
      </Alert>
    );
  }

  if (isPendingToken) {
    return (
      <Alert className="bg-yellow-50 text-yellow-800 border-yellow-200">
        <div className="flex flex-col w-full">
          <div className="flex items-center">
            <Loader2 className="h-4 w-4 mr-2 animate-spin text-yellow-500" />
            <AlertTitle>Waiting for Link Token</AlertTitle>
          </div>
          <AlertDescription className="flex flex-col">
            <div className="mb-3">
              Waiting for link token from ABDM. This may take a few moments. The
              page will update automatically when the token is received.
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm">
                {canRetry
                  ? "You can now retry token generation."
                  : `You can retry in ${Math.floor(retryCountdown / 60)}:${(retryCountdown % 60).toString().padStart(2, "0")}`}
              </div>
              <Button
                onClick={() => {
                  // Set forceRegenerate to true if we're not in canRetry state
                  if (!canRetry) {
                    setForceRegenerate(true);
                  }
                  handleGenerateToken();
                }}
                disabled={isGeneratingToken}
                size="sm"
                variant="outline"
                className="ml-4 bg-white border-yellow-300 hover:bg-yellow-50 text-yellow-800"
              >
                {isGeneratingToken ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    Generating...
                  </>
                ) : canRetry ? (
                  <>Retry</>
                ) : (
                  <>Generate Link Token</>
                )}
              </Button>
            </div>
          </AlertDescription>
        </div>
      </Alert>
    );
  }

  if (hasLinkToken) {
    return (
      <Alert className="bg-green-50 text-green-800 border-green-200">
        <div className="flex flex-col w-full">
          <div className="flex items-start">
            <AlertCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2" />
            <div className="flex-1">
              <AlertTitle>ABHA Linked</AlertTitle>
              <AlertDescription className="mb-3">
                Patient has an ABHA profile and link token. You can create a
                care context for this consultation.
              </AlertDescription>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCreateCareContext}
                disabled={isCreatingCareContext}
                className="bg-white hover:bg-green-50"
              >
                {isCreatingCareContext ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <LinkIcon className="h-4 w-4 mr-2" />
                    Create Care Context
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </Alert>
    );
  }

  return (
    <Alert className="bg-blue-50 text-blue-800 border-blue-200">
      <div className="flex flex-col w-full">
        <div className="flex items-start">
          <AlertCircle className="h-4 w-4 text-blue-500 mt-0.5 mr-2" />
          <div className="flex-1">
            <AlertTitle>Link Token Required</AlertTitle>
            <AlertDescription className="mb-3">
              A link token is required to create care contexts. Generate a link
              token to proceed.
            </AlertDescription>
            <Button
              variant="outline"
              size="sm"
              onClick={handleGenerateToken}
              disabled={isGeneratingToken}
              className="bg-white hover:bg-blue-50"
            >
              {isGeneratingToken ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Generate Link Token
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </Alert>
  );
}
