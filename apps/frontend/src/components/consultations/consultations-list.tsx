"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { getBranchIdFromCookies } from "@/lib/client-cookies";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Loader2, Search, FileText } from "lucide-react";
// import { toast } from "sonner";

interface Consultation {
  id: string;
  consultationDate: string;
  status: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
  };
  doctor: {
    id: string;
    user: {
      name: string;
    };
    specialization: string;
  };
  branch: {
    id: string;
    name: string;
  };
}

export function ConsultationsList() {
  const router = useRouter();
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  // Initialize with today's date as the default filter
  const [dateFilter, setDateFilter] = useState(() => {
    const today = new Date();
    return format(today, "yyyy-MM-dd");
  });
  const [currentBranchId, setCurrentBranchId] = useState("");

  useEffect(() => {
    // Fetch the current branch first
    const fetchCurrentBranch = async () => {
      try {
        // Try to get branch ID from cookies first
        const branchId = getBranchIdFromCookies();
        if (branchId) {
          console.log("Got branch ID from cookies:", branchId);
          setCurrentBranchId(branchId);
          return;
        }

        // If not in cookies, try the API
        const response = await fetch("/api/user/current-branch");
        if (response.ok) {
          const data = await response.json();
          if (data.currentBranch?.id) {
            setCurrentBranchId(data.currentBranch.id);
          }
        }
      } catch (error) {
        console.error("Error fetching current branch:", error);
      }
    };

    fetchCurrentBranch().then(() => {
      fetchConsultations();
    });
  }, []);

  const fetchConsultations = async () => {
    try {
      setLoading(true);
      // Add branchId to the query if available
      const url = currentBranchId
        ? `/api/consultations?branchId=${currentBranchId}`
        : "/api/consultations";

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to fetch consultations");
      }

      const data = await response.json();
      setConsultations(data.consultations || []);
    } catch (error) {
      console.error("Error fetching consultations:", error);
      // toast.error("Failed to load consultations");
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "in-progress":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            In Progress
          </Badge>
        );
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Completed
          </Badge>
        );
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            Cancelled
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const filteredConsultations = consultations.filter((consultation) => {
    const patientName =
      `${consultation.patient.firstName} ${consultation.patient.lastName}`.toLowerCase();
    const doctorName = consultation.doctor.user.name.toLowerCase();
    const searchLower = searchTerm.toLowerCase();

    const matchesSearch =
      patientName.includes(searchLower) || doctorName.includes(searchLower);

    const matchesStatus =
      statusFilter && statusFilter !== "all"
        ? consultation.status === statusFilter
        : true;

    const matchesDate = dateFilter
      ? format(new Date(consultation.consultationDate), "yyyy-MM-dd") ===
        dateFilter
      : true;

    return matchesSearch && matchesStatus && matchesDate;
  });

  const handleViewConsultation = (id: string) => {
    router.push(`/consultations/${id}`);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>All Consultations</CardTitle>
        <CardDescription>View and manage patient consultations</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search by patient name or doctor..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="w-full md:w-[180px]">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full md:w-[200px]">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="dateFilter">Filter by Date</Label>
              <Input
                id="dateFilter"
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
              />
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : filteredConsultations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No consultations found.
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Patient</TableHead>
                  <TableHead>Doctor</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredConsultations.map((consultation) => (
                  <TableRow key={consultation.id}>
                    <TableCell className="font-medium">
                      {consultation.patient.firstName}{" "}
                      {consultation.patient.lastName}
                    </TableCell>
                    <TableCell>{consultation.doctor.user.name}</TableCell>
                    <TableCell>
                      {format(new Date(consultation.consultationDate), "PPP")}
                    </TableCell>
                    <TableCell>{getStatusBadge(consultation.status)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleViewConsultation(consultation.id)
                          }
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {filteredConsultations.length} of {consultations.length}{" "}
          consultations
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={fetchConsultations}>
            Refresh
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
