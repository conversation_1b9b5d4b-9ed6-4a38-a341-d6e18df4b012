"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

// Removed Tabs import as we're displaying everything in single view
import {
  Loader2,
  User,
  Calendar,
  Clock,
  Activity,
  Trash2,
  AlertCircle,
  CheckCircle,
  Receipt,
} from "lucide-react";
// LinkTokenHandler import removed - care context is now automatically created
import { toast } from "sonner";
import { VitalsForm } from "@/components/consultations/vitals-form";
import { ClinicalNotesForm } from "@/components/consultations/clinical-notes-form";
import { PrescriptionForm } from "@/components/consultations/prescription-form";
import { VitalsList } from "@/components/consultations/vitals-list";
import { ClinicalNotesList } from "@/components/consultations/clinical-notes-list";
import { PrescriptionsList } from "@/components/consultations/prescriptions-list";
import { LabTestRequestForm } from "@/components/consultations/lab-test-request-form";
import { LabTestRequestsList } from "@/components/consultations/lab-test-requests-list";
import { PdfDownloadButtons } from "@/components/consultations/pdf-download-buttons";
import { InvoiceDisplay } from "@/components/invoices/invoice-display";
// import { AzureFhirUpload } from "@/components/azure-fhir-upload";
// import { UploadedFilesList } from "@/components/uploaded-files-list";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { UploadedFilesList } from "../uploaded-files-list";
import { AzureFhirUpload } from "../azure-fhir-upload";
// import { LinkTokenAlert } from "../care-context/link-token-alert";

interface ConsultationDetailProps {
  id: string;
}

export function ConsultationDetail({ id }: ConsultationDetailProps) {
  const router = useRouter();
  const [consultation, setConsultation] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  // Removed activeTab state as we're not using tabs anymore
  const [showVitalsForm, setShowVitalsForm] = useState(false);
  const [showClinicalNotesForm, setShowClinicalNotesForm] = useState(false);
  const [showPrescriptionForm, setShowPrescriptionForm] = useState(false);
  const [showLabTestRequestForm, setShowLabTestRequestForm] = useState(false);
  const [hasCareContext, setHasCareContext] = useState(false);
  const [invoice, setInvoice] = useState<any>(null);
  const [, setBranchId] = useState<string | null>(null);

  useEffect(() => {
    fetchConsultation();
  }, [id]);

  // Get current branch ID for link token component
  useEffect(() => {
    const getCurrentBranch = async () => {
      try {
        const branchResponse = await fetch("/api/user/current-branch");
        if (branchResponse.ok) {
          const branchData = await branchResponse.json();
          if (branchData.currentBranch?.id) {
            setBranchId(branchData.currentBranch.id);
          }
        }
      } catch (error) {
        console.error("Error getting current branch:", error);
      }
    };

    getCurrentBranch();
  }, []);

  // Check if a care context exists for this consultation
  useEffect(() => {
    const checkCareContext = async () => {
      if (!consultation) return;

      try {
        console.log(`Checking care context for consultation: ${id}`);
        const response = await fetch(`/api/consultations/${id}/care-context`);

        if (response.ok) {
          const data = await response.json();
          console.log("Care context check response:", data);
          const hasCareContextValue = !!data.careContext;
          console.log(`Setting hasCareContext to: ${hasCareContextValue}`);
          setHasCareContext(hasCareContextValue);
        }
      } catch (error) {
        console.error("Error checking care context:", error);
      }
    };

    checkCareContext();
  }, [id, consultation]);

  // Function to manually check for care contexts
  // const checkCareContextManually = async () => {
  //   if (!id) return;

  //   try {
  //     console.log(`Manually checking care context for consultation: ${id}`);
  //     const response = await fetch(`/api/consultations/${id}/care-context`);

  //     if (response.ok) {
  //       const data = await response.json();
  //       console.log("Manual care context check response:", data);
  //       const hasCareContextValue = !!data.careContext;
  //       console.log(
  //         `Manually setting hasCareContext to: ${hasCareContextValue}`,
  //       );
  //       setHasCareContext(hasCareContextValue);
  //       return hasCareContextValue;
  //     }
  //   } catch (error) {
  //     console.error("Error manually checking care context:", error);
  //   }

  //   return false;
  // };

  // Link token handling is now done by the LinkTokenHandler component

  const fetchConsultation = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/consultations/${id}`);

      const data = await response.json();

      if (!response.ok) {
        console.error("API error:", data);
        throw new Error(data.error || "Failed to fetch consultation");
      }

      if (!data.consultation) {
        console.error("No consultation data returned:", data);
        throw new Error("No consultation data returned");
      }

      setConsultation(data.consultation);

      // Fetch invoice if consultation is completed
      if (true) {
        fetchInvoice(data.consultation.id);
      }
    } catch (error) {
      console.error("Error fetching consultation:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to load consultation details",
      );
      toast.error("View and manage consultation details.");
    } finally {
      setLoading(false);
    }
  };

  const fetchInvoice = async (consultationId: string) => {
    try {
      const response = await fetch(
        `/api/invoices?consultationId=${consultationId}`,
      );
      if (response.ok) {
        const data = await response.json();
        if (data.invoices && data.invoices.length > 0) {
          setInvoice(data.invoices[0]);
        }
      }
    } catch (error) {
      console.error("Error fetching invoice:", error);
    }
  };

  const handleCompleteConsultation = async () => {
    try {
      const response = await fetch(`/api/consultations/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "completed",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to complete consultation");
      }

      const data = await response.json();
      setConsultation(data.consultation);
      toast.success("Consultation marked as completed");

      // Refresh the page to show updated status
      router.refresh();

      // Fetch the consultation again to ensure all data is up to date
      fetchConsultation();
    } catch (error) {
      console.error("Error completing consultation:", error);
      toast.error("Failed to complete consultation");
    }
  };

  const handleDeleteConsultation = async () => {
    try {
      const response = await fetch(`/api/consultations/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete consultation");
      }

      toast.success("Consultation deleted successfully");
      router.push("/consultations");
    } catch (error) {
      console.error("Error deleting consultation:", error);
      toast.error("Failed to delete consultation");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusBadge = (() => {
      switch (status) {
        case "in-progress":
          return (
            <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
              In Progress
            </Badge>
          );
        case "completed":
          return (
            <Badge variant="outline" className="bg-green-100 text-green-800">
              Completed
            </Badge>
          );
        case "cancelled":
          return (
            <Badge variant="outline" className="bg-red-100 text-red-800">
              Cancelled
            </Badge>
          );
        default:
          return <Badge variant="outline">{status}</Badge>;
      }
    })();

    return (
      <div className="flex space-x-2">
        {statusBadge}
        {hasCareContext && (
          <Badge variant="outline" className="bg-purple-100 text-purple-800">
            ABHA Linked
          </Badge>
        )}
      </div>
    );
  };

  const handleVitalsAdded = () => {
    setShowVitalsForm(false);
    fetchConsultation();
  };

  const handleClinicalNoteAdded = () => {
    setShowClinicalNotesForm(false);
    fetchConsultation();
  };

  const handlePrescriptionAdded = () => {
    setShowPrescriptionForm(false);
    fetchConsultation();
  };

  const handleLabTestRequestAdded = () => {
    setShowLabTestRequestForm(false);
    fetchConsultation();
  };

  // Individual upload functionality removed - care context is now automatically created when consultation is completed

  // Function to handle uploading all consultation data to ABDM
  // const handleUploadToAbdm = async () => {
  //   if (!consultation || !consultation.id) {
  //     toast.error("Consultation data not available");
  //     return;
  //   }

  //   // Check if we have the necessary data
  //   if (!consultation.vitals || consultation.vitals.length === 0) {
  //     toast.error("No vitals data available to upload");
  //     return;
  //   }

  //   if (
  //     !consultation.prescriptions ||
  //     consultation.prescriptions.length === 0
  //   ) {
  //     toast.error("No prescription data available to upload");
  //     return;
  //   }

  //   if (
  //     !consultation.clinicalNotes ||
  //     consultation.clinicalNotes.length === 0
  //   ) {
  //     toast.error("No clinical notes available to upload");
  //     return;
  //   }

  //   try {

  //     // Call the new API to create and upload all FHIR bundles to ABDM
  //     const response = await fetch(`/api/abdm/upload-consultation-bundles`, {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify({
  //         consultationId: consultation.id,
  //       }),
  //     });

  //     const data = await response.json();

  //     if (!response.ok) {
  //       throw new Error(data.error || "Failed to upload data to ABDM");
  //     }

  //     // Check individual bundle results
  //     const successCount = data.results.filter(
  //       (result: any) => result.success,
  //     ).length;
  //     const totalBundles = data.results.length;

  //     if (successCount === totalBundles) {
  //       toast.success("All consultation data uploaded to ABDM successfully");
  //     } else if (successCount > 0) {
  //       toast.success(
  //         `${successCount} of ${totalBundles} bundles uploaded to ABDM successfully`,
  //       );
  //     } else {
  //       throw new Error("Failed to upload any bundles to ABDM");
  //     }
  //   } catch (error) {
  //     console.error("Error uploading to ABDM:", error);
  //     toast.error(
  //       error instanceof Error
  //         ? error.message
  //         : "Failed to upload data to ABDM",
  //     );
  //   } finally {
  //   }
  // };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!consultation) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-8 w-8 mx-auto text-red-500 mb-2" />
        <h3 className="text-lg font-medium">Consultation not found</h3>
        <p className="text-muted-foreground">
          The consultation you are looking for does not exist or you do not have
          permission to view it.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Show link token alert if patient has ABHA profile but no link token */}
      {/* {consultation.patient?.abhaProfile && branchId && !loading && (
        <LinkTokenAlert
          patientId={consultation.patient.id}
          branchId={branchId}
        />
      )} */}

      {/* Care context banner and upload buttons are now hidden - care context is automatically created when consultation is marked as completed */}
      <Card className="border-2 border-primary/10 shadow-md">
        <CardHeader className="flex flex-row items-start justify-between space-y-0 bg-muted/30 pb-4">
          <div>
            <CardTitle className="text-2xl font-bold text-primary">
              Consultation for {consultation.patient?.firstName || "Unknown"}{" "}
              {consultation.patient?.lastName || "Patient"}
            </CardTitle>
            <CardDescription className="text-base mt-1">
              <span className="font-medium">Date:</span>{" "}
              {format(new Date(consultation.consultationDate), "PPP")} •
              <span className="font-medium ml-2">Status:</span>{" "}
              <div className="flex space-x-2">
                <span
                  className={`ml-1 px-2 py-0.5 rounded-full text-xs font-medium ${
                    consultation.status === "completed"
                      ? "bg-green-100 text-green-800"
                      : consultation.status === "in-progress"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {consultation.status}
                </span>
                {hasCareContext && (
                  <span className="px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    ABHA Linked
                  </span>
                )}
              </div>
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            {consultation.status === "in-progress" && (
              <Button
                onClick={handleCompleteConsultation}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="mr-2 h-4 w-4" /> Mark as Completed
              </Button>
            )}
            {!invoice && (
              <Button
                onClick={() =>
                  router.push(
                    `/consultations/${consultation.id}/create-invoice`,
                  )
                }
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Receipt className="mr-2 h-4 w-4" /> Create Invoice
              </Button>
            )}
            {/* {consultation.status === "completed" &&
              consultation.vitals &&
              consultation.vitals.length > 0 &&
              consultation.prescriptions &&
              consultation.prescriptions.length > 0 &&
              consultation.clinicalNotes &&
              consultation.clinicalNotes.length > 0 &&
              hasCareContext && (
                <Button
                  onClick={handleUploadToAbdm}
                  disabled={isUploadingToAbdm}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isUploadingToAbdm ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />{" "}
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" /> Send To Patient
                    </>
                  )}
                </Button>
              )} */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    the consultation and all associated records including
                    vitals, clinical notes, and prescriptions.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDeleteConsultation}>
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardHeader>
        <CardContent>
          {/* Overview Section */}
          <div className="space-y-6">
            {/* Patient and Consultation Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="border-2 border-blue-100 shadow-sm">
                  <CardHeader className="bg-blue-50 pb-3">
                    <CardTitle className="flex items-center text-blue-700">
                      <User className="h-5 w-5 mr-2" />
                      Patient Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div>
                          <h3 className="text-lg font-semibold mb-1">
                            {consultation.patient?.firstName || "Unknown"}{" "}
                            {consultation.patient?.lastName || "Patient"}
                          </h3>
                        </div>

                        <div className="grid grid-cols-2 gap-2 pt-2">
                          <div className="bg-muted/30 p-2 rounded-md">
                            <p className="text-xs font-medium text-muted-foreground">
                              Date of Birth
                            </p>
                            <p className="font-medium">
                              {consultation.patient?.dateOfBirth
                                ? format(
                                    new Date(consultation.patient?.dateOfBirth),
                                    "PPP",
                                  )
                                : "N/A"}
                            </p>
                          </div>

                          <div className="bg-muted/30 p-2 rounded-md">
                            <p className="text-xs font-medium text-muted-foreground">
                              Gender
                            </p>
                            <p className="font-medium capitalize">
                              {consultation.patient?.gender || "N/A"}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="bg-muted/30 p-2 rounded-md">
                          <p className="text-xs font-medium text-muted-foreground">
                            Contact Information
                          </p>
                          <div className="space-y-1 mt-1">
                            <div className="flex items-center">
                              <span className="text-xs font-medium w-12">
                                Phone:
                              </span>
                              <span className="font-medium">
                                {consultation.patient?.phone || "N/A"}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <span className="text-xs font-medium w-12">
                                Email:
                              </span>
                              <span className="font-medium">
                                {consultation.patient?.email || "N/A"}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="border-2 border-green-100 shadow-sm">
                  <CardHeader className="bg-green-50 pb-3">
                    <CardTitle className="flex items-center text-green-700">
                      <Activity className="h-5 w-5 mr-2" />
                      Consultation Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-1 gap-4">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-muted/30 p-2 rounded-md">
                          <p className="text-xs font-medium text-muted-foreground flex items-center">
                            <Calendar className="h-3.5 w-3.5 mr-1 text-green-600" />{" "}
                            Date
                          </p>
                          <p className="font-medium">
                            {format(
                              new Date(consultation.consultationDate),
                              "PPP",
                            )}
                          </p>
                        </div>

                        <div className="bg-muted/30 p-2 rounded-md">
                          <p className="text-xs font-medium text-muted-foreground flex items-center">
                            <Clock className="h-3.5 w-3.5 mr-1 text-green-600" />{" "}
                            Time
                          </p>
                          <p className="font-medium">
                            {consultation.startTime || "N/A"} -{" "}
                            {consultation.endTime || "N/A"}
                          </p>
                        </div>
                      </div>

                      <div className="bg-muted/30 p-2 rounded-md">
                        <p className="text-xs font-medium text-muted-foreground">
                          Doctor Information
                        </p>
                        <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-1">
                          <div className="flex items-center">
                            <span className="text-xs font-medium w-24">
                              Name:
                            </span>
                            <span className="font-medium">
                              {consultation.doctor?.user?.name ||
                                "Unknown Doctor"}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-xs font-medium w-24">
                              Specialization:
                            </span>
                            <span className="font-medium">
                              {consultation.doctor?.specialization || "N/A"}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-xs font-medium w-24">
                              Branch:
                            </span>
                            <span className="font-medium">
                              {consultation.branch?.name || "N/A"}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-xs font-medium w-24">
                              Status:
                            </span>
                            <span>{getStatusBadge(consultation.status)}</span>
                          </div>
                        </div>
                      </div>

                      {consultation.followUpDate && (
                        <div className="bg-yellow-50 border border-yellow-200 p-2 rounded-md">
                          <p className="text-xs font-medium text-yellow-700 flex items-center">
                            <Calendar className="h-3.5 w-3.5 mr-1" /> Follow-up
                            Scheduled
                          </p>
                          <p className="font-medium text-yellow-800">
                            {format(new Date(consultation.followUpDate), "PPP")}
                          </p>
                          {consultation.followUpNotes && (
                            <p className="text-sm mt-1 text-yellow-700">
                              {consultation.followUpNotes}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* PDF Download Section */}
              <PdfDownloadButtons
                consultationId={consultation.id}
                vitalsCount={consultation.vitals?.length || 0}
                prescriptionsCount={consultation.prescriptions?.length || 0}
                clinicalNotesCount={consultation.clinicalNotes?.length || 0}
              />

              {/* Health Document Record Upload Section */}
              <AzureFhirUpload
                consultationId={consultation.id}
                patientId={consultation.patientId}
                bundleType="HealthDocumentRecord"
                title="Upload Health Document"
                description="Upload a PDF health document that will be stored in Azure Blob Storage and automatically generate a HealthDocumentRecord FHIR bundle for ABDM compliance."
                onUploadSuccess={(result) => {
                  console.log("Health document uploaded successfully:", result);
                  toast.success(
                    "Health document uploaded and FHIR bundle generated successfully",
                  );
                  // Refresh the page to update the UI
                  window.location.reload();
                }}
                onUploadError={(error) => {
                  console.error("Health document upload failed:", error);
                  toast.error("Failed to upload health document");
                }}
                hideWhenFilesExist={true}
                className="mb-6"
              />

              {/* Uploaded Health Documents List */}

              <UploadedFilesList
                consultationId={consultation.id}
                bundleType="HealthDocumentRecord"
                title="Uploaded Health Documents"
                description="Previously uploaded health documents and generated FHIR bundles"
                className="mb-6"
              />

              {/* Vitals Section */}
              <Card className="border-2 border-red-100 shadow-sm">
                <CardHeader className="bg-red-50 pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center text-red-700">
                      <Activity className="h-5 w-5 mr-2" />
                      Vitals Records
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button onClick={() => setShowVitalsForm(true)}>
                        Record New Vitals
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  {showVitalsForm ? (
                    <VitalsForm
                      consultationId={consultation.id}
                      patientId={consultation.patientId}
                      doctorId={consultation.doctorId}
                      onCancel={() => setShowVitalsForm(false)}
                      onSuccess={handleVitalsAdded}
                    />
                  ) : (
                    <>
                      <VitalsList
                        consultationId={consultation.id}
                        vitals={consultation.vitals || []}
                        onRefresh={fetchConsultation}
                      />
                    </>
                  )}
                </CardContent>
              </Card>
              {/* Clinical Notes Section */}
              <Card className="border-2 border-blue-100 shadow-sm">
                <CardHeader className="bg-blue-50 pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center text-blue-700">
                      <User className="h-5 w-5 mr-2" />
                      Clinical Note
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button onClick={() => setShowClinicalNotesForm(true)}>
                        Add New Note
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  {showClinicalNotesForm ? (
                    <ClinicalNotesForm
                      consultationId={consultation.id}
                      patientId={consultation.patientId}
                      doctorId={consultation.doctorId}
                      onCancel={() => setShowClinicalNotesForm(false)}
                      onSuccess={handleClinicalNoteAdded}
                    />
                  ) : (
                    <>
                      <ClinicalNotesList
                        consultationId={consultation.id}
                        clinicalNotes={consultation.clinicalNotes || []}
                        Consultation={consultation}
                        onRefresh={fetchConsultation}
                      />

                      {/* OP Consult Document Upload */}
                      {/*   SRIDHAR: --HIde for now
                      <div className="mt-4 pt-4 border-t border-blue-200">
                        <AzureFhirUpload
                          consultationId={consultation.id}
                          patientId={consultation.patientId}
                          bundleType="OPConsultNote"
                          title="Upload OP Consultation Document"
                          description="Upload a PDF OP consultation document that will be stored in Azure Blob Storage and automatically generate an OPConsultNote FHIR bundle with DocumentReference for ABDM compliance."
                          onUploadSuccess={(result) => {
                            console.log('OP consultation document uploaded successfully:', result);
                            toast.success('OP consultation document uploaded and FHIR bundle generated successfully');
                            window.location.reload();
                          }}
                          onUploadError={(error) => {
                            console.error('OP consultation document upload failed:', error);
                            toast.error('Failed to upload OP consultation document');
                          }}
                          hideWhenFilesExist={true}
                          className="mb-4"
                        /> */}

                      {/* Uploaded OP Consultation Documents */}
                      {/* SRIDHAR: --HIde for now
                         <UploadedFilesList
                          consultationId={consultation.id}
                          bundleType="OPConsultNote"
                          title="Uploaded OP Consultation Documents"
                          description="Previously uploaded OP consultation documents"
                          className="mb-4"
                        />
                      </div> */}
                    </>
                  )}
                </CardContent>
              </Card>

              {/* Prescriptions Section */}
              <Card className="border-2 border-green-100 shadow-sm">
                <CardHeader className="bg-green-50 pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center text-green-700">
                      <Activity className="h-5 w-5 mr-2" />
                      Prescriptions
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button onClick={() => setShowPrescriptionForm(true)}>
                        Create New Prescription
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  {showPrescriptionForm ? (
                    <PrescriptionForm
                      consultationId={consultation.id}
                      patientId={consultation.patientId}
                      doctorId={consultation.doctorId}
                      onCancel={() => setShowPrescriptionForm(false)}
                      onSuccess={handlePrescriptionAdded}
                    />
                  ) : (
                    <PrescriptionsList
                      consultationId={consultation.id}
                      prescriptions={consultation.prescriptions || []}
                      Consultation={consultation}
                      onRefresh={fetchConsultation}
                    />
                  )}
                </CardContent>
              </Card>

              {/* Lab Test Requests Section */}
              <Card className="border-2 border-purple-100 shadow-sm">
                <CardHeader className="bg-purple-50 pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center text-purple-700">
                      <Activity className="h-5 w-5 mr-2" />
                      Lab Test Requests
                    </CardTitle>
                    <Button onClick={() => setShowLabTestRequestForm(true)}>
                      Request Lab Test
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  {showLabTestRequestForm ? (
                    <LabTestRequestForm
                      consultationId={consultation.id}
                      patientId={consultation.patientId}
                      doctorId={consultation.doctorId}
                      onCancel={() => setShowLabTestRequestForm(false)}
                      onSuccess={handleLabTestRequestAdded}
                    />
                  ) : (
                    <LabTestRequestsList
                      consultationId={consultation.id}
                      labTestRequests={consultation.labTestRequests || []}
                      onRefresh={fetchConsultation}
                    />
                  )}
                </CardContent>
              </Card>

              {/* Invoice Section */}
              {invoice && (
                <Card className="border-2 border-green-100 shadow-sm">
                  <CardHeader className="bg-green-50 pb-3">
                    <div className="flex justify-between items-center">
                      <CardTitle className="flex items-center text-green-700">
                        <Receipt className="h-5 w-5 mr-2" />
                        Invoice
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <InvoiceDisplay
                      invoice={invoice}
                      showActions={true}
                      onEdit={() => {
                        // TODO: Implement edit functionality
                        toast.info("Edit functionality coming soon");
                      }}
                      onDelete={() => {
                        // TODO: Implement delete functionality
                        toast.info("Delete functionality coming soon");
                      }}
                    />
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
