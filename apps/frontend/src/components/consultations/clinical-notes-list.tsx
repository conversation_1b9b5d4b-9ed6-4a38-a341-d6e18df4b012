"use client";

import { useState } from "react";
import { format } from "date-fns";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, AlertCircle, Tag } from "lucide-react";
import { toast } from "sonner";
import { UploadedFilesList } from "@/components/uploaded-files-list";

interface ClinicalNotesListProps {
  consultationId: string;
  clinicalNotes: any[];
  Consultation: any;
  onRefresh: () => void;
}

export function ClinicalNotesList({
  // consultationId,
  clinicalNotes,
  Consultation,
  onRefresh,
}: ClinicalNotesListProps) {
  // const [loading, setLoading] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleDeleteNote = async (id: string) => {
    try {
      setDeletingId(id);

      const response = await fetch(`/api/clinical-notes/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete clinical note");
      }

      toast.success("Clinical note deleted successfully");
      onRefresh();
    } catch (error) {
      console.error("Error deleting clinical note:", error);
      toast.error("Failed to delete clinical note");
    } finally {
      setDeletingId(null);
    }
  };

  const getNoteTypeBadge = (noteType: string) => {
    switch (noteType) {
      case "diagnosis":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            Diagnosis
          </Badge>
        );
      case "treatment":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Treatment
          </Badge>
        );
      case "followup":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800">
            Follow-up
          </Badge>
        );
      case "lab":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            Lab Results
          </Badge>
        );
      case "imaging":
        return (
          <Badge variant="outline" className="bg-indigo-100 text-indigo-800">
            Imaging
          </Badge>
        );
      case "procedure":
        return (
          <Badge variant="outline" className="bg-pink-100 text-pink-800">
            Procedure
          </Badge>
        );
      default:
        return <Badge variant="outline">General</Badge>;
    }
  };

  if (clinicalNotes.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-6">
          <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-muted-foreground text-center">
            No clinical notes found for this consultation.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Uploaded Documents Section */}
      <div className="mb-4">
        <h4 className="font-medium text-blue-700 mb-3 flex items-center">
          <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
          Uploaded Clinical Notes Documents
        </h4>
        <div className="bg-white/60 p-4 rounded-lg border border-blue-100">
          <UploadedFilesList
            consultationId={Consultation.id}
            bundleType="OPConsultationRecord"
            title=""
            description=""
            className="border-0 shadow-none bg-transparent p-0"
            showOnlyGenerated={true}
          />
        </div>
      </div>

      {clinicalNotes.map((note) => (
        <Card key={note.id}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  {getNoteTypeBadge(note.noteType)}
                  <span className="text-sm text-muted-foreground">
                    {format(new Date(note.createdAt), "PPp")}
                  </span>
                </div>
                <CardTitle className="text-lg">
                  {Consultation.doctor?.user?.name || "Unknown Doc"}
                </CardTitle>
              </div>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => handleDeleteNote(note.id)}
                disabled={deletingId === note.id}
              >
                {deletingId === note.id ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  "Delete"
                )}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Structured Clinical Note Fields */}
            <div className="space-y-4">
              {note.chiefComplaints && (
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">
                    Chief Complaints
                  </h4>
                  <div className="whitespace-pre-wrap text-sm bg-muted/30 p-3 rounded-md">
                    {note.chiefComplaints}
                  </div>
                </div>
              )}

              {note.allergies && (
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">
                    Allergies
                  </h4>
                  <div className="whitespace-pre-wrap text-sm bg-red-50 p-3 rounded-md border border-red-100">
                    {note.allergies}
                  </div>
                </div>
              )}

              {note.medicalHistory && (
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">
                    Medical History
                  </h4>
                  <div className="whitespace-pre-wrap text-sm bg-muted/30 p-3 rounded-md">
                    {note.medicalHistory}
                  </div>
                </div>
              )}

              {note.investigationAdvice && (
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">
                    Investigation Advice
                  </h4>
                  <div className="whitespace-pre-wrap text-sm bg-blue-50 p-3 rounded-md border border-blue-100">
                    {note.investigationAdvice}
                  </div>
                </div>
              )}

              {note.procedure && (
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">
                    Procedure
                  </h4>
                  <div className="whitespace-pre-wrap text-sm bg-green-50 p-3 rounded-md border border-green-100">
                    {note.procedure}
                  </div>
                </div>
              )}

              {note.followUp && (
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">
                    Follow Up
                  </h4>
                  <div className="whitespace-pre-wrap text-sm bg-yellow-50 p-3 rounded-md border border-yellow-100">
                    {note.followUp}
                  </div>
                </div>
              )}

              {note.content && (
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">
                    Additional Clinical Observations
                  </h4>
                  <div className="whitespace-pre-wrap text-sm bg-muted/30 p-3 rounded-md">
                    {note.content}
                  </div>
                </div>
              )}
            </div>

            {note.snomedTags &&
              Array.isArray(note.snomedTags) &&
              note.snomedTags.length > 0 && (
                <div className="mt-4">
                  <div className="flex items-center text-sm text-muted-foreground mb-1">
                    <Tag className="h-4 w-4 mr-1" />
                    SNOMED CT Tags:
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {note.snomedTags.map((tag: string, index: number) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
