"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, FlaskConical } from "lucide-react";
import { toast } from "sonner";

const labTestRequestSchema = z.object({
  testType: z.string().min(1, "Test type is required"),
  testName: z.string().min(1, "Test name is required"),
  priority: z.enum(["routine", "urgent", "stat"]),
  notes: z.string().optional(),
  expectedDate: z.string().optional(),
});

type LabTestRequestFormData = z.infer<typeof labTestRequestSchema>;

interface LabTestRequestFormProps {
  consultationId: string;
  patientId: string;
  doctorId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

const commonLabTests = [
  { type: "BLOOD", name: "Complete Blood Count (CBC)" },
  { type: "BLOOD", name: "Basic Metabolic Panel (BMP)" },
  { type: "BLOOD", name: "Comprehensive Metabolic Panel (CMP)" },
  { type: "BLOOD", name: "Lipid Panel" },
  { type: "BLOOD", name: "Liver Function Tests (LFT)" },
  { type: "BLOOD", name: "Kidney Function Tests (KFT)" },
  { type: "BLOOD", name: "Thyroid Function Tests (TFT)" },
  { type: "BLOOD", name: "HbA1c (Diabetes)" },
  { type: "BLOOD", name: "Fasting Blood Sugar (FBS)" },
  { type: "BLOOD", name: "Random Blood Sugar (RBS)" },
  { type: "URINE", name: "Urine Routine & Microscopy" },
  { type: "URINE", name: "Urine Culture & Sensitivity" },
  { type: "IMAGING", name: "Chest X-Ray" },
  { type: "IMAGING", name: "Abdominal Ultrasound" },
  { type: "IMAGING", name: "ECG (Electrocardiogram)" },
  { type: "MICRO", name: "Blood Culture" },
  { type: "MICRO", name: "Sputum Culture" },
  { type: "SEROLOGY", name: "Hepatitis B Surface Antigen" },
  { type: "SEROLOGY", name: "HIV Test" },
  { type: "SEROLOGY", name: "VDRL/RPR (Syphilis)" },
];

export function LabTestRequestForm({
  consultationId,
  patientId,
  doctorId,
  onCancel,
  onSuccess,
}: LabTestRequestFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<LabTestRequestFormData>({
    resolver: zodResolver(labTestRequestSchema),
    defaultValues: {
      testType: "",
      testName: "",
      priority: "routine",
      notes: "",
      expectedDate: "",
    },
  });

  const selectedTestType = form.watch("testType");

  const onSubmit = async (data: LabTestRequestFormData) => {
    try {
      setIsSubmitting(true);

      const response = await fetch("/api/lab-test-requests", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          consultationId,
          patientId,
          doctorId,
          ...data,
          expectedDate: data.expectedDate
            ? new Date(data.expectedDate).toISOString()
            : null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create lab test request");
      }

      toast.success("Lab test request created successfully");
      onSuccess();
      form.reset();
    } catch (error) {
      console.error("Error creating lab test request:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to create lab test request",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredTests = commonLabTests.filter(
    (test) => !selectedTestType || test.type === selectedTestType,
  );

  return (
    <Card className="border-2 border-blue-100 shadow-sm">
      <CardHeader className="bg-blue-50 pb-3">
        <CardTitle className="flex items-center text-blue-700">
          <FlaskConical className="h-5 w-5 mr-2" />
          Request Lab Test
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="testType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Test Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select test category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="BLOOD">Blood Tests</SelectItem>
                        <SelectItem value="URINE">Urine Tests</SelectItem>
                        <SelectItem value="IMAGING">Imaging</SelectItem>
                        <SelectItem value="MICRO">Microbiology</SelectItem>
                        <SelectItem value="SEROLOGY">Serology</SelectItem>
                        <SelectItem value="PATHOLOGY">Pathology</SelectItem>
                        <SelectItem value="OTHER">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="routine">Routine</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                        <SelectItem value="stat">STAT</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="testName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Test Name</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select or type test name" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {filteredTests.map((test) => (
                        <SelectItem key={test.name} value={test.name}>
                          {test.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="expectedDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expected Completion Date (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      min={new Date().toISOString().split("T")[0]}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Any special instructions or notes for the lab..."
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Request...
                  </>
                ) : (
                  "Create Lab Test Request"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
