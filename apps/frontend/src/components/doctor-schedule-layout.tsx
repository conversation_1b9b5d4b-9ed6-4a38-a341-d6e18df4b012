"use client";

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/contexts/doctor-context";
import { DepartmentProvider } from "@/contexts/department-context";

interface DoctorScheduleLayoutProps {
  children: React.ReactNode;
}

export function DoctorScheduleLayout({ children }: DoctorScheduleLayoutProps) {
  return (
    <DepartmentProvider>
      <DoctorProvider>{children}</DoctorProvider>
    </DepartmentProvider>
  );
}
