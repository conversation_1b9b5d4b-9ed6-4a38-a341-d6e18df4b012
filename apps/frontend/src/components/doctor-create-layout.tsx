"use client";

import React from "react";
import { DepartmentProvider } from "@/contexts/department-context";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/contexts/doctor-context";
import { DoctorInvitationProvider } from "@/contexts/doctor-invitation-context";

interface DoctorCreateLayoutProps {
  children: React.ReactNode;
}

export function DoctorCreateLayout({ children }: DoctorCreateLayoutProps) {
  return (
    <DepartmentProvider>
      <DoctorProvider>
        <DoctorInvitationProvider>{children}</DoctorInvitationProvider>
      </DoctorProvider>
    </DepartmentProvider>
  );
}
