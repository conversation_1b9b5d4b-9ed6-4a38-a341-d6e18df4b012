"use client";

import { useRouter } from "next/navigation";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ShieldCheck,
  ShieldPlus,
  UserPlus,
  X,
  ArrowRight,
  Phone,
  IdCard,
} from "lucide-react";
import { SearchBannerState } from "@/hooks/use-patient-search-with-abha";

interface PatientSearchBannerProps {
  banner: SearchBannerState;
  onHide: () => void;
}

interface AbhaEntry {
  ABHANumber: string;
  name: string;
  gender: string;
  kycVerified: string;
  authMethods: string[];
}

export function PatientSearchBanner({
  banner,
  onHide,
}: PatientSearchBannerProps) {
  const router = useRouter();

  if (!banner || !banner.show || !banner.type) return null;

  const navigate = (url: string) => {
    router.push(url);
  };

  const actions = {
    "verify-abha": () =>
      banner.patient?.id &&
      navigate(`/patients/${banner.patient.id}/abha/verify`),

    "create-abha": () =>
      banner.patient?.id &&
      navigate(`/patients/${banner.patient.id}/abha/create`),

    "verify-register": () => {
      const abhaList: AbhaEntry[] = banner.abhaData?.[0]?.ABHA || [];
      const patientName =
        `${banner.patient?.firstName ?? ""} ${banner.patient?.lastName ?? ""}`
          .toLowerCase()
          .trim();

      const selectedIndex = abhaList.findIndex((entry) =>
        entry.name?.toLowerCase().includes(patientName),
      );

      const url =
        selectedIndex !== -1
          ? `/abdm/abha-verification?phone=${banner.phone}&fromPatientSearch=true&selectedIndex=${selectedIndex}`
          : `/abdm/abha-verification?phone=${banner.phone}&fromPatientSearch=true`;

      if (banner.phone) navigate(url);
    },

    "create-register": () =>
      banner.phone &&
      navigate(
        `/abdm/abha-creation?phone=${banner.phone}&fromPatientSearch=true`,
      ),

    "register-without-abha": () =>
      banner.phone && navigate(`/patients/register?phone=${banner.phone}`),
  };

  const configMap = {
    "verify-abha": {
      icon: ShieldCheck,
      bgColor: "bg-blue-50 border-blue-200",
      iconColor: "text-blue-600",
      titleColor: "text-blue-800",
      descColor: "text-blue-700",
      actions: [
        {
          label: "Verify ABHA",
          onClick: actions["verify-abha"],
          variant: "default" as const,
          icon: ShieldCheck,
        },
      ],
    },
    "create-abha": {
      icon: ShieldPlus,
      bgColor: "bg-orange-50 border-orange-200",
      iconColor: "text-orange-600",
      titleColor: "text-orange-800",
      descColor: "text-orange-700",
      actions: [
        {
          label: "Create ABHA",
          onClick: actions["create-abha"],
          variant: "default" as const,
          icon: ShieldPlus,
        },
      ],
    },
    "verify-register": {
      icon: UserPlus,
      bgColor: "bg-green-50 border-green-200",
      iconColor: "text-green-600",
      titleColor: "text-green-800",
      descColor: "text-green-700",
      actions: [
        {
          label: "Verify & Register",
          onClick: actions["verify-register"],
          variant: "default" as const,
          icon: ShieldCheck,
        },
        {
          label: "Register without ABHA",
          onClick: actions["register-without-abha"],
          variant: "outline" as const,
          icon: UserPlus,
        },
      ],
    },
    "create-register": {
      icon: UserPlus,
      bgColor: "bg-purple-50 border-purple-200",
      iconColor: "text-purple-600",
      titleColor: "text-purple-800",
      descColor: "text-purple-700",
      actions: [
        {
          label: "Create ABHA & Register",
          onClick: actions["create-register"],
          variant: "default" as const,
          icon: ShieldPlus,
        },
        {
          label: "Register without ABHA",
          onClick: actions["register-without-abha"],
          variant: "outline" as const,
          icon: UserPlus,
        },
      ],
    },
  };

  const config = configMap[banner.type];
  if (!config) return null;

  const IconComponent = config.icon;

  const abhaName =
    banner.abhaData?.[0]?.ABHA?.[0]?.name ||
    banner.abhaData?.[0]?.name ||
    "N/A";

  const name =
    banner.name ||
    `${banner.patient?.firstName || ""} ${banner.patient?.lastName || ""}`.trim() ||
    abhaName;

  return (
    <Alert className={`${config.bgColor} relative`}>
      <IconComponent className={`h-4 w-4 ${config.iconColor}`} />

      <Button
        variant="ghost"
        size="sm"
        className="absolute right-2 top-2 h-6 w-6 p-0 hover:bg-black/10"
        onClick={onHide}
      >
        <X className="h-3 w-3" />
      </Button>

      <AlertTitle className={config.titleColor}>{banner.message}</AlertTitle>

      <AlertDescription className={`${config.descColor} space-y-3`}>
        <div className="flex items-center gap-2 text-sm flex-wrap">
          <Phone className="h-3 w-3" />
          <span>Phone: {banner.phone}</span>
          <span>•</span>
          <span>Name: {name}</span>

          {banner.patient && (
            <>
              <span>•</span>
              <span>
                Patient: {banner.patient.firstName} {banner.patient.lastName}
              </span>
            </>
          )}

          {banner.abhaData && banner.abhaData.length > 0 && (
            <>
              <span>•</span>
              <Badge variant="secondary" className="text-xs">
                <IdCard className="h-3 w-3 mr-1" />
                ABHA Available
              </Badge>
            </>
          )}
        </div>

        <div className="flex flex-wrap gap-2 pt-2">
          {config.actions.map((action, idx) => {
            const ActionIcon = action.icon;
            return (
              <Button
                key={idx}
                variant={action.variant}
                size="sm"
                onClick={action.onClick}
                className="h-8"
              >
                <ActionIcon className="h-3 w-3 mr-2" />
                {action.label}
                <ArrowRight className="h-3 w-3 ml-2" />
              </Button>
            );
          })}
        </div>
      </AlertDescription>
    </Alert>
  );
}
