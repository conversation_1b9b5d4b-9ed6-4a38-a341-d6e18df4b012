"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { XCircle, Mail } from "lucide-react";
import Link from "next/link";
import logger from "@/lib/logger";

interface EmailVerificationCheckProps {
  email: string;
  onVerified: () => void;
}

export function EmailVerificationCheck({
  email,
  onVerified,
}: EmailVerificationCheckProps) {
  const [isChecking, setIsChecking] = useState(true);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSending, setIsSending] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  useEffect(() => {
    const checkVerification = async () => {
      try {
        logger.log("Checking email verification status for:", email);

        // Check if the user's email is verified
        const response = await fetch("/api/auth/check-email-verified", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email }),
        });

        const data = await response.json();
        logger.log("Email verification status:", data);

        if (response.ok && data.verified) {
          setIsVerified(true);
          onVerified(); // Notify parent component that email is verified
        } else {
          setError(
            data.message ||
              "Your email is not verified. Please verify your email to continue.",
          );
        }
      } catch (error) {
        logger.error("Error checking email verification:", error);
        setError(
          "An error occurred while checking your email verification status.",
        );
      } finally {
        setIsChecking(false);
      }
    };

    checkVerification();
  }, [email, onVerified]);

  const handleResendVerification = async () => {
    setIsSending(true);
    setError(null);
    setEmailSent(false);

    try {
      const response = await fetch("/api/resend-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setEmailSent(true);
      } else {
        setError(data.error || "Failed to resend verification email");
      }
    } catch (error) {
      logger.error("Error resending verification email:", error);
      setError("An unexpected error occurred");
    } finally {
      setIsSending(false);
    }
  };

  if (isVerified) {
    return null; // Don't render anything if email is verified
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            Aran Care
          </h1>
          <h2 className="mt-6 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
            Email Verification Required
          </h2>
        </div>

        <Card className="border shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl">Verify Your Email</CardTitle>
          </CardHeader>
          <CardContent className="text-center py-6">
            {isChecking ? (
              <div className="flex flex-col items-center justify-center">
                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mb-6"></div>
                <h3 className="text-xl font-semibold mb-2">
                  Checking Verification Status
                </h3>
                <p className="text-muted-foreground">
                  Please wait while we check your email verification status.
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center">
                <XCircle className="h-16 w-16 text-amber-500 mb-4" />
                <h3 className="text-xl font-semibold mb-2">
                  Email Not Verified
                </h3>
                <p className="text-muted-foreground">
                  {error ||
                    "Your email is not verified. Please verify your email to continue."}
                </p>

                {emailSent ? (
                  <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                    <p className="text-green-700">
                      Verification email has been sent to{" "}
                      <strong>{email}</strong>. Please check your inbox and
                      click the verification link.
                    </p>
                  </div>
                ) : (
                  <div className="mt-4">
                    <Button
                      onClick={handleResendVerification}
                      disabled={isSending}
                      className="flex items-center"
                    >
                      {isSending ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Sending...
                        </>
                      ) : (
                        <>
                          <Mail className="h-4 w-4 mr-2" />
                          Resend Verification Email
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-center border-t pt-6">
            <Button variant="outline" asChild>
              <Link href="/sign-in">Back to Sign In</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
