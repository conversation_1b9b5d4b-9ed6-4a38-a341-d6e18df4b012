import * as React from "react";

export function MainContainer({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body style={main}>
        <div style={container}>
          <div style={header}>
            <h1 style={headerText}>Aran Care HIMS</h1>
          </div>
          <div style={content}>{children}</div>
          <div style={footer}>
            <p style={footerText}>
              © {new Date().getFullYear()} Aran Care HIMS. All rights reserved.
            </p>
            <p style={footerText}>
              This email was sent to you as part of your Aran Care HIMS account.
            </p>
          </div>
        </div>
      </body>
    </html>
  );
}

const main = {
  backgroundColor: "#f6f9fc",
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, sans-serif',
  lineHeight: "1.4",
  margin: "0",
  padding: "0",
};

const container = {
  maxWidth: "600px",
  margin: "0 auto",
  padding: "20px 0 48px",
};

const header = {
  backgroundColor: "#4f46e5",
  padding: "20px",
  textAlign: "center" as const,
  borderTopLeftRadius: "4px",
  borderTopRightRadius: "4px",
};

const headerText = {
  color: "#ffffff",
  fontSize: "24px",
  margin: "0",
};

const content = {
  backgroundColor: "#ffffff",
  padding: "32px",
  borderBottomLeftRadius: "4px",
  borderBottomRightRadius: "4px",
  boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
};

const footer = {
  textAlign: "center" as const,
  padding: "16px 0",
  color: "#6b7280",
};

const footerText = {
  fontSize: "12px",
  margin: "4px 0",
};
