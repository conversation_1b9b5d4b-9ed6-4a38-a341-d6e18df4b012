import * as React from "react";

interface ButtonProps {
  href: string;
  label: string;
}

export function Button({ href, label }: ButtonProps) {
  return (
    <div style={buttonContainer}>
      <a
        href={href}
        style={buttonStyle}
        target="_blank"
        rel="noopener noreferrer"
      >
        {label}
      </a>
    </div>
  );
}

const buttonContainer = {
  textAlign: "center" as const,
  margin: "32px 0",
};

const buttonStyle = {
  backgroundColor: "#4f46e5",
  borderRadius: "4px",
  color: "#ffffff",
  display: "inline-block",
  fontSize: "16px",
  fontWeight: "bold" as const,
  lineHeight: "1",
  padding: "14px 24px",
  textDecoration: "none",
  textAlign: "center" as const,
  cursor: "pointer",
};
