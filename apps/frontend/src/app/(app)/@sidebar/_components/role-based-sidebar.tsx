"use client";

import React, { useMemo } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@workspace/ui/components/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@workspace/ui/components/collapsible";
import { ChevronRight } from "lucide-react";
import {
  dashboardItem,
  getMenuByRole,
  isRouteActive,
  type UserRole,
  type MenuGroup,
  type MenuItem,
} from "../_constants/roleBasedSidebarConfig";
import { useSession } from "next-auth/react";

export function RoleBasedSidebar() {
  const pathName = usePathname() || "";
  const { data: session } = useSession();
  const userRole = session?.user?.role as UserRole | undefined;

  const menuGroups: MenuGroup[] = useMemo(() => {
    if (!userRole) return [];
    return getMenuByRole(userRole);
  }, [userRole]);
  const { setOpenMobile } = useSidebar();

  if (!userRole) return null;

  return (
    <>
      {/* Dashboard Item */}
      <SidebarGroup>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isRouteActive(pathName, dashboardItem.href)}
              tooltip={dashboardItem.title}
            >
              <Link
                href={dashboardItem.href}
                onClick={() => setOpenMobile(false)}
              >
                <dashboardItem.icon className="mr-2 h-5 w-5" />
                <span>{dashboardItem.title}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroup>

      {/* Menu Groups */}
      {menuGroups.map((group) => (
        <SidebarGroup key={group.title}>
          <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
          <SidebarMenu>
            {group.items.map((item) => (
              <React.Fragment key={item.title}>
                {item.items ? (
                  <SidebarMenuCollapsible item={item} pathName={pathName} />
                ) : (
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      isActive={isRouteActive(pathName, item.href)}
                      tooltip={item.title}
                    >
                      <Link
                        href={item.href}
                        onClick={() => setOpenMobile(false)}
                      >
                        <item.icon className="mr-2 h-5 w-5" />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )}
              </React.Fragment>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      ))}
    </>
  );
}

// Badge component removed as it's not being used

// Collapsible menu component for nested menu items
const SidebarMenuCollapsible = ({
  item,
  pathName,
}: {
  item: MenuItem;
  pathName: string;
}) => {
  const { setOpenMobile } = useSidebar();

  // Check if this item or any of its children are active
  const isActive =
    isRouteActive(pathName, item.href) ||
    (item.items?.some((subItem: any) =>
      isRouteActive(pathName, subItem.href),
    ) ??
      false);

  return (
    <Collapsible asChild defaultOpen={isActive} className="group/collapsible">
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton tooltip={item.title} isActive={isActive}>
            <item.icon className="mr-2 h-5 w-5" />
            <span>{item.title}</span>
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className="CollapsibleContent">
          <SidebarMenuSub>
            {item.items?.map((subItem: any) => (
              <SidebarMenuSubItem key={subItem.title}>
                <SidebarMenuSubButton
                  asChild
                  isActive={isRouteActive(pathName, subItem.href)}
                >
                  <Link
                    href={subItem.href}
                    onClick={() => setOpenMobile(false)}
                  >
                    {subItem.icon && <subItem.icon className="mr-2 h-4 w-4" />}
                    <span>{subItem.title}</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            ))}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
};
