import { Metada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export const metadata: Metadata = {
  title: "Dashboard",
  description: "Aran Care HIMS Dashboard",
};

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);

  const user = session?.user;

  if (!user) {
    redirect("/sign-in");
  }

  // Show a unified dashboard for all roles - no more role-based redirections
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold tracking-tight">Aran Care Dashboard</h1>
      <p className="text-muted-foreground">
        Welcome to your healthcare organization dashboard
      </p>
      <p>
        Your role: <strong>{user.role || "Unknown"}</strong>
      </p>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border p-4">
          <h3 className="font-semibold">Quick Actions</h3>
          <p className="text-sm text-muted-foreground">
            Access all features from the sidebar based on your role
          </p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="font-semibold">All Access</h3>
          <p className="text-sm text-muted-foreground">
            All roles can now access all screens
          </p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="font-semibold">Role-Based Sidebar</h3>
          <p className="text-sm text-muted-foreground">
            Your sidebar shows relevant options for your role
          </p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="font-semibold">Full System Access</h3>
          <p className="text-sm text-muted-foreground">
            Navigate to any section using the sidebar or direct URLs
          </p>
        </div>
      </div>
    </div>
  );
}
