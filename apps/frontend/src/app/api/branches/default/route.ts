export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { cookies } from "next/headers";

export async function POST(req: NextRequest) {
  try {
    // Legacy cookies
    const legacyToken = cookies().get("session-token")?.value;
    const legacyUserCookie = cookies().get("user-info")?.value;

    let email: string | null = null;
    let organizationId: string | null = null;

    if (legacyToken && legacyUserCookie) {
      try {
        const legacyInfo = JSON.parse(decodeURIComponent(legacyUserCookie));
        email = legacyInfo.email;
        organizationId = legacyInfo.organizationId;
      } catch {}
    }

    if (!email || !organizationId) {
      try {
        const { getToken } = await import("next-auth/jwt");
        const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
        // @ts-ignore
        email = token?.email ?? null;
        // @ts-ignore
        organizationId = token?.organizationId ?? null;
      } catch {}
    }

    if (!email || !organizationId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the branch ID from the request
    const { branchId } = await req.json();

    if (!branchId) {
      return NextResponse.json(
        { error: "Branch ID is required" },
        { status: 400 },
      );
    }

    // Get user with role information for validation
    const user = await db.user.findUnique({
      where: { email },
      include: {
        doctors: {
          include: {
            branches: {
              where: { branchId },
              include: {
                branch: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    let branch = null;

    // Role-based branch access validation
    if (user.role === "doctor") {
      // For doctors, check if they are assigned to this branch in the current organization
      const doctorProfile = await db.doctor.findFirst({
        where: {
          userId: user.id,
          organizationId,
        },
        include: {
          branches: {
            include: {
              branch: true,
            },
          },
        },
      });

      if (!doctorProfile || !doctorProfile.branches.length) {
        return NextResponse.json(
          { error: "Access denied. Doctor is not assigned to this branch." },
          { status: 403 },
        );
      }
      branch = doctorProfile.branches[0].branch;
      console.log(
        `Doctor ${user.email} setting default branch: ${branch.name}`,
      );
    } else {
      // For admins and staff, verify the branch exists and belongs to the organization
      branch = await db.branch.findFirst({
        where: {
          id: branchId,
          organizationId,
        },
      });

      if (!branch) {
        return NextResponse.json(
          { error: "Branch not found or does not belong to the organization" },
          { status: 404 },
        );
      }
    }

    // Set the branch cookies
    // Set current-branch for backward compatibility
    cookies().set(
      "current-branch",
      JSON.stringify({
        id: branch.id,
        name: branch.name,
        isHeadOffice: branch.isHeadOffice,
        branchId: branch.id, // Keep for backward compatibility
        branchName: branch.name, // Keep for backward compatibility
      }),
      {
        path: "/",
      },
    );

    // Set default-branch with the full branch object
    cookies().set("default-branch", JSON.stringify(branch), {
      path: "/",
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error setting default branch:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
