export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { cookies } from "next/headers";

export async function GET(req: NextRequest) {
  try {
    // Legacy cookies
    const legacySessionToken = cookies().get("session-token")?.value;
    const legacyUserCookie = cookies().get("user-info")?.value;

    let email: string | null = null;
    let organizationId: string | null = null;

    if (legacySessionToken && legacyUserCookie) {
      try {
        const legacyInfo = JSON.parse(decodeURIComponent(legacyUserCookie));
        email = legacyInfo.email;
        organizationId = legacyInfo.organizationId;
      } catch {}
    }

    // NextAuth session fallback
    if (!email || !organizationId) {
      try {
        const { getToken } = await import("next-auth/jwt");
        const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
        // @ts-ignore
        email = token?.email ?? null;
        // @ts-ignore
        organizationId = token?.organizationId ?? null;
      } catch (err) {
        console.error("Error retrieving NextAuth session", err);
      }
    }

    if (!email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { email },
      include: {
        organizations: {
          include: { organization: true },
        },
        doctors: {
          include: {
            branches: {
              include: {
                branch: true,
              },
            },
          },
        },
      },
    });

    if (!user || user.organizations.length === 0) {
      return NextResponse.json(
        { error: "User not found or not part of the organization" },
        { status: 404 },
      );
    }

    // Determine organizationId if still null
    if (!organizationId && user && user.organizations.length > 0) {
      const defaultOrg = user.organizations.find((o: any) => o.isDefault) || user.organizations[0];
      // @ts-ignore
      organizationId = defaultOrg.organizationId;
    }

    if (!organizationId) {
      return NextResponse.json({ error: "Organization not found" }, { status: 403 });
    }

    let branches;

    // Get user's roles for this organization

    // Remove role-based branch filtering - all users can see all branches in their organization
    // if (userRoles.includes("doctor")) {
    //   // For doctors, only return branches they are assigned to in the current organization
    //   const doctorProfile = await db.doctor.findFirst({
    //     where: {
    //       userId: user.id,
    //       organizationId: organizationId,
    //     },
    //     include: {
    //       branches: {
    //         include: {
    //           branch: true,
    //         },
    //       },
    //     },
    //   });

    //   if (!doctorProfile || !doctorProfile.branches.length) {
    //     return NextResponse.json(
    //       { error: "Doctor has no assigned branches" },
    //       { status: 403 },
    //     );
    //   }

    //   branches = doctorProfile.branches.map(
    //     (doctorBranch) => doctorBranch.branch,
    //   );
    //   console.log(
    //     `Doctor ${user.email} has access to ${branches.length} branches:`,
    //     branches.map((b) => b.name),
    //   );
    // } else if (
    //   userRoles.some((role) =>
    //     ["staff", "nurse", "receptionist", "technician", "pharmacist"].includes(
    //       role,
    //     ),
    //   )
    // ) {
    //   // For staff roles, check if they have branch assignments
    //   const staff = await db.staff.findUnique({
    //     where: { userId: user.id },
    //     include: {
    //       branches: {
    //         include: {
    //           branch: true,
    //         },
    //       },
    //     },
    //   });

    //   if (staff && staff.branches.length > 0) {
    //     // Staff has specific branch assignments
    //     branches = staff.branches.map((staffBranch) => staffBranch.branch);
    //     console.log(
    //       `Staff ${user.email} has access to ${branches.length} branches:`,
    //       branches.map((b) => b.name),
    //     );
    //   } else {
    //     // Staff with no specific assignments can see all branches
    //     branches = await db.branch.findMany({
    //       where: { organizationId },
    //       orderBy: [
    //         { isHeadOffice: "desc" }, // Head office first
    //         { name: "asc" }, // Then alphabetically by name
    //       ],
    //     });
    //   }
    // } else {
    // All roles can see all branches for the organization
    branches = await db.branch.findMany({
      where: { organizationId },
      orderBy: [
        { isHeadOffice: "desc" }, // Head office first
        { name: "asc" }, // Then alphabetically by name
      ],
    });
    // }

    // Get the current branch from the query parameter or cookie
    const url = new URL(req.url);
    let currentBranchId = url.searchParams?.get("branchId");

    // If no branch ID is provided in the query, check the cookie
    if (!currentBranchId) {
      const branchCookie = cookies().get("current-branch")?.value;
      if (branchCookie) {
        try {
          const branchInfo = JSON.parse(decodeURIComponent(branchCookie));
          currentBranchId = branchInfo.branchId;
        } catch (error) {
          console.error("Error parsing branch cookie:", error);
        }
      }
    }

    // If still no branch ID, default to the head office or the first branch
    if (!currentBranchId) {
      const headOffice = branches.find((branch) => branch.isHeadOffice);
      currentBranchId = headOffice
        ? headOffice.id
        : branches.length > 0
          ? branches[0].id
          : null;
    }

    // Find the current branch
    const currentBranch = currentBranchId
      ? branches.find((branch) => branch.id === currentBranchId) || null
      : null;

    return NextResponse.json({
      branches,
      currentBranch,
    });
  } catch (error) {
    console.error("Error fetching branches:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Legacy cookies
    const legacySessionToken2 = cookies().get("session-token")?.value;
    const legacyUserCookie2 = cookies().get("user-info")?.value;

    let email: string | null = null;
    let organizationId: string | null = null;

    if (legacySessionToken2 && legacyUserCookie2) {
      try {
        const legacyInfo = JSON.parse(decodeURIComponent(legacyUserCookie2));
        email = legacyInfo.email;
        organizationId = legacyInfo.organizationId;
      } catch {}
    }

    if (!email || !organizationId) {
      try {
        const { getToken } = await import("next-auth/jwt");
        const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
        // @ts-ignore
        email = token?.email ?? null;
        // @ts-ignore
        organizationId = token?.organizationId ?? null;
      } catch {}
    }

    if (!email || !organizationId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the branch data from the request
    const {
      name,
      facilityType,
      phone,
      email: branchEmail,
      address,
      city,
      state,
      pincode,
      latitude,
      longitude,
    } = await req.json();

    if (!name) {
      return NextResponse.json(
        { error: "Branch name is required" },
        { status: 400 },
      );
    }

    // Create the branch
    const branch = await db.branch.create({
      data: {
        name,
        facilityType: facilityType || "clinic",
        phone,
        email: branchEmail,
        address,
        city,
        state,
        pincode,
        latitude,
        longitude,

        organizationId,
      },
    });

    return NextResponse.json(branch, { status: 201 });
  } catch (error) {
    console.error("Error creating branch:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
