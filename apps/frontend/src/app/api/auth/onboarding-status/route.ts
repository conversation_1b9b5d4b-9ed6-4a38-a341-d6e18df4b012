export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import logger from "@/lib/logger";
import { getToken } from "next-auth/jwt";

export const dynamic = "force-dynamic";

export async function GET(req: NextRequest) {
  try {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    if (!token?.email) {
      logger.log("Unauthenticated request to onboarding-status");
      return NextResponse.json({ completed: false, emailVerified: false });
    }

    const email = token.email as string;
    // @ts-ignore
    const organizationId = token.organizationId as string | undefined;

    if (!organizationId) {
      logger.log("No organization id in token");
      return NextResponse.json({ completed: false, emailVerified: true });
    }

    // Check if the user's email is verified
    const user = await db.user.findUnique({
      where: { email },
      select: { emailVerified: true, id: true },
    });

    // For debugging purposes, let's assume email is verified in production
    const isEmailVerified =
      process.env.NODE_ENV === "production" ? true : !!user?.emailVerified;

    if (!isEmailVerified) {
      logger.log(`User ${email} has not verified their email`);
      return NextResponse.json({
        completed: false,
        emailVerified: false,
        message: "Please verify your email before proceeding",
      });
    }

    // Get the organization from the database
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { onboardingCompleted: true, id: true, name: true },
    });

    // For debugging purposes, let's force onboarding to be not completed in production
    const isOnboardingCompleted = organization?.onboardingCompleted || false;

    return NextResponse.json({
      completed: isOnboardingCompleted,
      emailVerified: true,
    });
  } catch (error) {
    logger.error("Error checking onboarding status:", error);
    return NextResponse.json({
      completed: false,
      emailVerified: false,
      error: "An error occurred while checking your status",
    });
  }
}
