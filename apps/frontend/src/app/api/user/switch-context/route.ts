export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import {
  getUserOrganizationRoles,
  getUserOrganizations,
} from "@/lib/organization-roles";
import { db } from "@/lib/db";
import { cookies } from "next/headers";
import { getToken } from "next-auth/jwt";
import { updateOrganizationContext } from "@/lib/organization-context-cookies";

/**
 * POST /api/user/switch-context
 * Switch user's organization, role, and branch context
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate via NextAuth JWT
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // Token contains id, email, role, organizationId (added in auth callbacks)
    const email = token?.email as string | undefined;

    if (!email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // organizationId from token if not provided
    let { organizationId, role, branchId } = await req.json();

    if (!organizationId && token?.organizationId) {
      organizationId = token.organizationId as string;
    }

    console.log("Switch context request:", { organizationId, role, branchId });

    // If no organizationId provided, try to get from org-context cookie
    if (!organizationId) {
      const orgContextCookie = cookies().get("org-context")?.value;
      if (orgContextCookie) {
        try {
          const orgContext = JSON.parse(orgContextCookie);
          organizationId = orgContext.organizationId;
        } catch (error) {
          console.error("Error parsing org-context cookie:", error);
        }
      }
    }

    if (!organizationId) {
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 },
      );
    }

    // Get user from database
    const user = await db.user.findUnique({
      where: { email },
      select: { id: true, name: true, email: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Verify user has access to this organization
    const userOrganizations = await getUserOrganizations(user.id);
    const hasAccess = userOrganizations.some(
      (userOrg) => userOrg.organizationId === organizationId,
    );

    if (!hasAccess) {
      return NextResponse.json(
        { error: "Access denied to this organization" },
        { status: 403 },
      );
    }

    // Get user's roles for this organization
    const userRoles = await getUserOrganizationRoles(user.id, organizationId);
    console.log(
      "User roles for organization:",
      userRoles,
      user,
      userOrganizations,
      organizationId,
    );

    // Allow role switching - users can have multiple roles and switch between them
    if (role && !userRoles.includes(role)) {
      console.warn("Requested role is not in user's actual roles", {
        role,
        userRoles,
      });
      return NextResponse.json(
        { error: `User does not have role "${role}" in this organization` },
        { status: 403 },
      );
    }

    // Get organization details including status
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { id: true, name: true, slug: true, status: true },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 },
      );
    }

    // Check if organization is active
    if (organization.status === "inactive") {
      return NextResponse.json(
        {
          error: "Organization is currently deactivated",
          organizationDeactivated: true,
          organizationId: organization.id,
          organizationName: organization.name,
        },
        { status: 403 },
      );
    }

    // If branch is specified, verify it belongs to the organization
    let branch = null;
    if (branchId) {
      branch = await db.branch.findFirst({
        where: {
          id: branchId,
          organizationId,
        },
        select: { id: true, name: true, isHeadOffice: true },
      });

      if (!branch) {
        return NextResponse.json(
          { error: "Branch not found or does not belong to this organization" },
          { status: 404 },
        );
      }

      // Determine the effective role for branch access check
      const effectiveRole = role || userRoles[0] || null;

      console.log("Branch access check:", {
        requestedRole: role,
        userRoles,
        effectiveRole,
        branchId,
      });

      // Remove branch access restrictions - all users can switch to any branch in their organization
      // if (
      //   effectiveRole &&
      //   [
      //     "doctor",
      //     "staff",
      //     "nurse",
      //     "receptionist",
      //     "pharmacist",
      //     "technician",
      //   ].includes(effectiveRole)
      // ) {
      //   let hasbranchAccess = false;

      //   if (effectiveRole === "doctor") {
      //     const doctorBranch = await db.doctorBranch.findFirst({
      //       where: {
      //         doctor: { userId: user.id },
      //         branchId,
      //       },
      //     });
      //     hasbranchAccess = !!doctorBranch;
      //   } else {
      //     const staffBranch = await db.staffBranch.findFirst({
      //       where: {
      //         staff: { userId: user.id },
      //         branchId,
      //       },
      //     });
      //     hasbranchAccess = !!staffBranch;
      //   }

      //   if (!hasbranchAccess) {
      //     return NextResponse.json(
      //       { error: "User does not have access to this branch" },
      //       { status: 403 },
      //     );
      //   }
      // }

      // hospitalAdmin and branchAdmin can access all branches in their organization - no additional check needed
    }

    // Get user's default branch for this organization (if not specified)
    let selectedBranch = branch;
    if (!selectedBranch) {
      // Find user's assigned branch for this organization
      let userBranch = null;

      // Check if user has a role that requires branch assignment
      if (
        role &&
        [
          "doctor",
          "staff",
          "nurse",
          "receptionist",
          "pharmacist",
          "technician",
        ].includes(role)
      ) {
        if (role === "doctor") {
          const doctorBranch = await db.doctorBranch.findFirst({
            where: {
              doctor: { userId: user.id },
              branch: { organizationId },
            },
            include: { branch: true },
          });
          userBranch = doctorBranch?.branch;
        } else {
          const staffBranch = await db.staffBranch.findFirst({
            where: {
              staff: { userId: user.id },
              branch: { organizationId },
            },
            include: { branch: true },
          });
          userBranch = staffBranch?.branch;
        }
      }

      // If no specific branch found, get the head office or first branch
      if (!userBranch) {
        userBranch = await db.branch.findFirst({
          where: { organizationId },
          orderBy: [{ isHeadOffice: "desc" }, { name: "asc" }],
        });
      }

      if (userBranch) {
        selectedBranch = {
          id: userBranch.id,
          name: userBranch.name,
          isHeadOffice: userBranch.isHeadOffice,
        };
      }
    }

    // Set current role if specified, otherwise use first available role
    const selectedRole = role || userRoles[0] || null;

    // Build current user info object for context cookie
    const currentUserInfo = {
      id: token?.id,
      email: token?.email,
      role: token?.role,
      organizationId,
      name: user.name,
    };

    // Update organization context using the new cookie management system
    const contextUpdated = updateOrganizationContext({
      organizationId: organization.id,
      organizationName: organization.name,
      organizationSlug: organization.slug,
      role: selectedRole,
      branchId: selectedBranch?.id,
      branchName: selectedBranch?.name,
      branchIsHeadOffice: selectedBranch?.isHeadOffice,
      userInfo: currentUserInfo,
    });

    if (!contextUpdated) {
      console.error("Failed to update organization context cookies");
    }

    return NextResponse.json({
      success: true,
      message: "Context switched successfully",
      context: {
        organization: {
          id: organization.id,
          name: organization.name,
          slug: organization.slug,
        },
        role: selectedRole,
        branch: selectedBranch,
        availableRoles: userRoles,
      },
    });
  } catch (error) {
    console.error("Error switching context:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

/**
 * GET /api/user/switch-context
 * Get current user context and available options
 */
export async function GET(req: NextRequest) {
  try {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    const email = token?.email as string | undefined;

    if (!email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user from database
    const user = await db.user.findUnique({
      where: { email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get user's organizations
    const userOrganizations = await getUserOrganizations(user.id);

    // Get current context from cookies (non-sensitive values)
    const cookieStore = cookies();
    const currentRoleCookie = cookieStore.get("current-role")?.value;
    const currentBranchCookie = cookieStore.get("current-branch")?.value;
    const orgContextCookie = cookieStore.get("org-context")?.value;

    let currentOrganization: any = null;
    let currentRole: string | null = null;
    let currentBranch: any = null;

    // Get current role from cookie or use first available role from UserOrganization
    if (currentRoleCookie) {
      currentRole = currentRoleCookie;
    } else if (currentOrganization) {
      const userOrgRoles = await getUserOrganizationRoles(
        user.id,
        currentOrganization.organizationId,
      );
      currentRole = userOrgRoles[0] || null;
    }

    if (currentBranchCookie) {
      try {
        currentBranch = JSON.parse(currentBranchCookie);
      } catch (error) {
        console.error("Error parsing current branch cookie:", error);
      }
    }

    // Resolve current organization
    if (!currentOrganization && orgContextCookie) {
      try {
        const orgContext = JSON.parse(orgContextCookie);
        if (orgContext.organizationId && orgContext.name) {
          currentOrganization = {
            id: orgContext.organizationId,
            organizationId: orgContext.organizationId,
            name: orgContext.name,
          };
        }
      } catch (error) {
        console.error("Error parsing org-context cookie:", error);
      }
    }

    if (!currentOrganization && token?.organizationId) {
      const matchingOrg = userOrganizations.find(
        (org) => org.organizationId === token.organizationId,
      );
      if (matchingOrg) {
        currentOrganization = {
          id: matchingOrg.organizationId,
          organizationId: matchingOrg.organizationId,
          name: matchingOrg.organization.name,
        };
      }
    }

    if (!currentOrganization && userOrganizations.length > 0) {
      currentOrganization = {
        id: userOrganizations[0].organizationId,
        organizationId: userOrganizations[0].organizationId,
        name: userOrganizations[0].organization.name,
      };
    }

    // Get available roles for current organization from UserOrganization table
    let availableRoles: string[] = [];
    if (currentOrganization) {
      const userOrg = await db.userOrganization.findFirst({
        where: {
          userId: user.id,
          organizationId: currentOrganization.organizationId,
        },
        select: { roles: true },
      });
      console.log("User organization:", userOrg);
      console.log("User organization roles:", userOrg?.roles);

      if (userOrg && userOrg.roles) {
        availableRoles = Array.isArray(userOrg.roles)
          ? (userOrg.roles as string[])
          : [];
      }
    }

    // Get available branches based on user's role assignments in current organization
    let availableBranches: any[] = [];
    if (currentOrganization && availableRoles.length > 0) {
      const currentRole = currentRoleCookie || availableRoles[0];

      if (["hospitalAdmin", "branchAdmin"].includes(currentRole)) {
        // Admins can see all branches in the organization
        availableBranches = await db.branch.findMany({
          where: { organizationId: currentOrganization.organizationId },
          select: { id: true, name: true, isHeadOffice: true },
          orderBy: [{ isHeadOffice: "desc" }, { name: "asc" }],
        });
      } else if (currentRole === "doctor") {
        // Doctors can only see branches they're assigned to
        const doctorBranches = await db.doctorBranch.findMany({
          where: {
            doctor: { userId: user.id },
            branch: { organizationId: currentOrganization.organizationId },
          },
          include: {
            branch: {
              select: { id: true, name: true, isHeadOffice: true },
            },
          },
        });
        availableBranches = doctorBranches.map((db) => db.branch);
      } else if (
        ["staff", "nurse", "receptionist", "pharmacist", "technician"].includes(
          currentRole,
        )
      ) {
        // Staff can only see branches they're assigned to
        const staffBranches = await db.staffBranch.findMany({
          where: {
            staff: { userId: user.id },
            branch: { organizationId: currentOrganization.organizationId },
          },
          include: {
            branch: {
              select: { id: true, name: true, isHeadOffice: true },
            },
          },
        });
        availableBranches = staffBranches.map((sb) => sb.branch);
      }
    }

    const responseData = {
      currentContext: {
        organization: currentOrganization,
        role: currentRole,
        branch: currentBranch,
      },
      availableOptions: {
        organizations: userOrganizations.map((org) => ({
          id: org.organizationId,
          name: org.organization.name,
          slug: org.organization.slug,
          roles: org.roles,
          isDefault: org.isDefault,
        })),
        roles: availableRoles,
        branches: availableBranches,
      },
    };

    console.log("Switch context GET response:", {
      userEmail: email,
      organizationsCount: userOrganizations.length,
      currentOrg: currentOrganization?.organizationName,
      rolesCount: availableRoles.length,
      branchesCount: availableBranches.length,
      roles: availableRoles,
      branches: availableBranches.map((b) => ({ id: b.id, name: b.name })),
    });

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error getting context:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
