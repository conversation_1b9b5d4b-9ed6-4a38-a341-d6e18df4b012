export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { cookies, headers } from "next/headers";

export async function GET() {
  try {
    // Legacy cookie-based auth (deprecated)
    const sessionToken = cookies().get("session-token")?.value;
    const userInfoCookie = cookies().get("user-info")?.value;

    let email: string | null = null;

    if (sessionToken && userInfoCookie) {
      try {
        const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
        email = userInfo.email;
      } catch {
        /* ignore */
      }
    }

    // NextAuth JWT fallback
    if (!email) {
      try {
        const { getServerSession } = await import("next-auth");
        const { authOptions } = await import("@/lib/auth");
        const session = await getServerSession(authOptions as any);
        // @ts-ignore
        email = session?.user?.email ?? null;
      } catch (e) {
        console.error("Error fetching NextAuth session", e);
      }
    }

    if (!email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { email },
      include: {
        organizations: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get the user's organizations
    const organizations = user.organizations.map((userOrg) => ({
      id: userOrg.organization.id,
      name: userOrg.organization.name,
      slug: userOrg.organization.slug,
      logo: userOrg.organization.logo,
      roles: Array.isArray(userOrg.roles) ? (userOrg.roles as string[]) : [],
      isDefault: userOrg.isDefault,
    }));

    // Get the default organization
    const defaultOrg = user.organizations.find((userOrg) => userOrg.isDefault);
    const currentOrganization = defaultOrg
      ? {
          id: defaultOrg.organization.id,
          name: defaultOrg.organization.name,
          slug: defaultOrg.organization.slug,
          logo: defaultOrg.organization.logo,
          roles: Array.isArray(defaultOrg.roles)
            ? (defaultOrg.roles as string[])
            : [],
        }
      : organizations[0];

    return NextResponse.json({
      organizations,
      currentOrganization,
    });
  } catch (error) {
    console.error("Error fetching organizations:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Legacy cookie-based auth
    const sessionToken2 = cookies().get("session-token")?.value;
    const userInfoCookie2 = cookies().get("user-info")?.value;

    let email: string | null = null;

    if (sessionToken2 && userInfoCookie2) {
      try {
        const userInfo = JSON.parse(decodeURIComponent(userInfoCookie2));
        email = userInfo.email;
      } catch {
        /* ignore */
      }
    }

    if (!email) {
      try {
        const { getServerSession } = await import("next-auth");
        const { authOptions } = await import("@/lib/auth");
        const session = await getServerSession(authOptions as any);
        // @ts-ignore
        email = session?.user?.email ?? null;
      } catch {}
    }

    if (!email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user from the database
    const user = await db.user.findUnique({
      where: { email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get the organization data from the request
    const { name, domain, logo } = await req.json();

    if (!name) {
      return NextResponse.json(
        { error: "Organization name is required" },
        { status: 400 },
      );
    }

    // Create the organization
    const organization = await db.organization.create({
      data: {
        name,
        slug: domain,
        logo,
        status: "inactive", // Set organization to inactive status by default
        users: {
          create: {
            userId: user.id,
            roles: ["owner"],
            isDefault: false,
          },
        },
      },
    });

    return NextResponse.json(organization, { status: 201 });
  } catch (error) {
    console.error("Error creating organization:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
