"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { BranchOnboardingForm } from "@/components/branch-onboarding-form";
import { useOrganization } from "@/contexts/organization-context";
import { EmailVerificationCheck } from "@/components/email-verification-check";
import logger from "@/lib/logger";

export default function OnboardingPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [organizationName, setOrganizationName] = useState<string | null>(null);
  const { data: session } = useSession();
  const userEmail = session?.user?.email ?? "";
  const [, setIsEmailVerified] = useState<boolean>(false);
  const [showEmailVerification, setShowEmailVerification] =
    useState<boolean>(false);
  const router = useRouter();
  const { isLoading: orgLoading, refreshOrganizations } = useOrganization();

  useEffect(() => {
    async function init() {
      if (!session) {
        // Wait for session to load
        return;
      }

      try {
        // Get organization via API (uses NextAuth auth)
        const res = await fetch("/api/organizations");
        if (!res.ok) throw new Error("Failed to fetch organizations");
        const data = await res.json();
        if (data.currentOrganization) {
          setOrganizationId(data.currentOrganization.id);
          setOrganizationName(data.currentOrganization.name);
        }

        const onboardingRes = await fetch("/api/auth/onboarding-status");
        const onboarding = await onboardingRes.json();

        if (onboarding.completed) {
          router.replace("/dashboard");
          return;
        }

        if (!onboarding.emailVerified) {
          setShowEmailVerification(true);
        } else {
          setIsEmailVerified(true);
        }
      } catch (error) {
        logger.error("Onboarding init error", error);
      } finally {
        setIsLoading(false);
      }
    }

    init();
  }, [session, router]);

  const handleComplete = async () => {
    // Refresh the organization context before redirecting
    await refreshOrganizations();
    router.push("/dashboard");
  };

  if (isLoading || orgLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-background to-muted/30">
        <div className="relative">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-muted border-t-primary"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="h-8 w-8 rounded-full bg-background"></div>
          </div>
        </div>
        <p className="mt-4 text-muted-foreground animate-pulse">
          Loading your application...
        </p>
      </div>
    );
  }

  if (!organizationId || !organizationName) {
    return null; // still loading org info
  }

  // Handle email verification check
  if (showEmailVerification && userEmail) {
    return (
      <EmailVerificationCheck
        email={userEmail}
        onVerified={() => {
          setIsEmailVerified(true);
          setShowEmailVerification(false);
        }}
      />
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 bg-gradient-to-br from-background to-muted/30">
      <div className="w-full max-w-4xl mx-auto mb-8 text-center animate-fade-in">
        <h1 className="text-4xl font-bold mb-3 gradient-text">
          Welcome to Aran Care
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Complete your organization setup to get started with our healthcare
          management platform
        </p>
      </div>
      <BranchOnboardingForm
        organizationId={organizationId}
        organizationName={organizationName}
        onComplete={handleComplete}
      />
    </div>
  );
}
