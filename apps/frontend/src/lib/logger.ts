/**
 * Logger utility for the application
 * This provides a consistent way to log messages across the application
 * with different log levels and categories.
 */

// Environment check to control logging behavior
const isDevelopment = process.env.NODE_ENV === "development";

// Logger interface
interface Logger {
  log: (...args: any[]) => void;
  error: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  info: (...args: any[]) => void;
  debug: (...args: any[]) => void;
  verification: (...args: any[]) => void;
}

/**
 * Create a logger function for a specific level
 * @param level The log level (e.g., 'log', 'error')
 * @returns A function that logs messages with the specified level
 */
const createLogger = (level: string) => {
  // In production, we only want to log errors by default
  if (!isDevelopment && level !== "error") {
    return () => {}; // No-op in production for non-error logs
  }

  return (...args: any[]) => {
    if (typeof console[level as keyof Console] === "function") {
      (console[level as keyof Console] as Function)(...args);
    } else {
      console.log(`[${level.toUpperCase()}]`, ...args);
    }
  };
};

/**
 * Create a category-specific logger
 * @param category The category for the logs (e.g., 'auth', 'api')
 * @returns A logger function that prefixes logs with the category
 */
const createCategoryLogger = (category: string) => {
  return (...args: any[]) => {
    if (isDevelopment) {
      console.log(`[${category.toUpperCase()}]`, ...args);
    }
  };
};

// Create the logger object with different log levels
export const logger: Logger = {
  log: createLogger("log"),
  error: createLogger("error"),
  warn: createLogger("warn"),
  info: createLogger("info"),
  debug: createLogger("debug"),
  verification: createCategoryLogger("verification"),
};

// Export default logger
export default logger;
