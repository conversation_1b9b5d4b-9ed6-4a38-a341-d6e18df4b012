/**
 * SMS Service for sending text messages using Twilio
 */

import { Twi<PERSON> } from "twilio";
import { logger } from "@/lib/logger";

// Environment variables
const TWILIO_ACCOUNT_SID = process.env.NEXT_PUBLIC_TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = process.env.NEXT_PUBLIC_TWILIO_AUTH_TOKEN;
const TWILIO_PHONE_NUMBER = process.env.NEXT_PUBLIC_TWILIO_PHONE_NUMBER;

// Types
export interface SmsData {
  to: string;
  body: string;
}

/**
 * SMS Service for sending text messages
 */
export const smsService = {
  /**
   * Send an SMS message using Twilio
   *
   * @param smsData The SMS data containing recipient and message body
   * @returns A promise that resolves to the result of the SMS sending operation
   */
  send: async (smsData: SmsData) => {
    // Validate required environment variables
    if (!TWILIO_ACCOUNT_SID) {
      return { error: "Twilio Account SID is not configured" };
    }

    if (!TWILIO_AUTH_TOKEN) {
      return { error: "T<PERSON><PERSON> Auth Token is not configured" };
    }

    if (!TWILIO_PHONE_NUMBER) {
      return { error: "Twilio Phone Number is not configured" };
    }

    try {
      // Format the phone number to E.164 format if it's not already
      const formattedPhoneNumber = formatPhoneNumber(smsData.to);

      // Initialize Twilio client
      const client = new Twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);

      // Send the SMS
      const message = await client.messages.create({
        body: smsData.body,
        from: TWILIO_PHONE_NUMBER,
        to: formattedPhoneNumber,
      });

      logger.info("SMS sent successfully", {
        messageId: message.sid,
        to: formattedPhoneNumber,
        status: message.status,
      });

      return {
        success: true,
        messageId: message.sid,
        status: message.status,
      };
    } catch (error) {
      logger.error("Failed to send SMS", {
        error: error instanceof Error ? error.message : String(error),
        to: smsData.to,
      });

      return {
        error: `Exception while sending SMS: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
};

/**
 * Format a phone number to E.164 format
 *
 * @param phoneNumber The phone number to format
 * @returns The formatted phone number
 */
function formatPhoneNumber(phoneNumber: string): string {
  // Remove any non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, "");

  // If the number doesn't start with '+', add the country code for India (+91)
  if (!phoneNumber.startsWith("+")) {
    // If the number starts with '0', remove it before adding country code
    if (digitsOnly.startsWith("0")) {
      return `+91${digitsOnly.substring(1)}`;
    }

    // If the number is 10 digits (without country code), add +91
    if (digitsOnly.length === 10) {
      return `+91${digitsOnly}`;
    }

    // Otherwise, just add + at the beginning
    return `+${digitsOnly}`;
  }

  // If it already has a + prefix, just return the digits with a + prefix
  return `+${digitsOnly}`;
}
