import { io, Socket } from "socket.io-client";

// Get the Socket.io server URL from environment variables
const SOCKET_SERVER_URL =
  process.env.NEXT_PUBLIC_SOCKET_SERVER_URL || "http://localhost:4000";

// Debug log
console.log("Socket.io server URL:", SOCKET_SERVER_URL);

// Create a singleton Socket.io client instance
let socket: Socket | null = null;

// Store room memberships for reconnection
let rooms = {
  organization: null as string | null,
  branch: null as string | null,
  doctor: null as string | null,
};

// Store last known queue state for persistence
let lastKnownQueueState: Record<string, any> = {};

export const getSocket = (): Socket => {
  if (!socket) {
    // Initialize the socket connection with improved reconnection settings
    socket = io(SOCKET_SERVER_URL, {
      autoConnect: false,
      withCredentials: true,
      reconnectionAttempts: 10, // Increased attempts
      reconnectionDelay: 1000, // Start with 1 second delay
      reconnectionDelayMax: 10000, // Max 10 seconds between attempts
      timeout: 20000,
      transports: ["websocket", "polling"], // Try websocket first, fallback to polling
    });

    // Add event listeners for connection status
    socket.on("connect", () => {
      console.log("Socket connected");

      // Rejoin rooms on reconnection
      if (rooms.organization) joinOrganization(rooms.organization);
      if (rooms.branch) joinBranch(rooms.branch);
      if (rooms.doctor) joinDoctor(rooms.doctor);
    });

    socket.on("disconnect", (reason) => {
      console.log(`Socket disconnected: ${reason}`);
    });

    socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error.message);
    });

    socket.on("reconnect", (attemptNumber) => {
      console.log(`Socket reconnected after ${attemptNumber} attempts`);
    });

    socket.on("reconnect_error", (error) => {
      console.error("Socket reconnection error:", error.message);
    });

    socket.on("reconnect_failed", () => {
      console.error("Socket reconnection failed");
    });
  }

  return socket;
};

// Helper function to connect to the socket server
export const connectSocket = (): void => {
  const socket = getSocket();
  if (!socket.connected) {
    socket.connect();
  }
};

// Helper function to disconnect from the socket server
export const disconnectSocket = (): void => {
  if (socket && socket.connected) {
    socket.disconnect();
  }
};

// Helper function to join organization room
export const joinOrganization = (organizationId: string): void => {
  const socket = getSocket();
  // Store the room for reconnection
  rooms.organization = organizationId;

  if (socket.connected && organizationId) {
    socket.emit("join-organization", organizationId);
  }
};

// Helper function to join branch room
export const joinBranch = (branchId: string): void => {
  const socket = getSocket();
  // Store the room for reconnection
  rooms.branch = branchId;

  if (socket.connected && branchId) {
    socket.emit("join-branch", branchId);
  }
};

// Helper function to join doctor room
export const joinDoctor = (doctorId: string): void => {
  const socket = getSocket();
  // Store the room for reconnection
  rooms.doctor = doctorId;

  if (socket.connected && doctorId) {
    socket.emit("join-doctor", doctorId);
  }
};

// Helper function to emit queue status updates
export const emitQueueStatusUpdate = (
  organizationId: string,
  branchId: string,
  doctorId: string,
  queueStatus: any,
): void => {
  const socket = getSocket();

  // Store the update in our local state for persistence
  if (queueStatus.queueId) {
    lastKnownQueueState[queueStatus.queueId] = {
      ...lastKnownQueueState[queueStatus.queueId],
      ...queueStatus,
      organizationId,
      branchId,
      doctorId,
      lastUpdated: new Date().toISOString(),
    };
  }

  // Emit the update if connected
  if (socket.connected && organizationId && branchId) {
    socket.emit("queue-status-update", {
      organizationId,
      branchId,
      doctorId,
      ...queueStatus,
      clientTimestamp: Date.now(), // Add client timestamp for latency tracking
    });
  } else {
    console.warn("Socket not connected, update queued for reconnection");
    // Updates will be sent when reconnected via the stored state
  }
};

// Get the last known state of a queue item
export const getLastKnownQueueState = (queueId: string): any => {
  return lastKnownQueueState[queueId] || null;
};

// Get all last known queue states
export const getAllLastKnownQueueStates = (): Record<string, any> => {
  return { ...lastKnownQueueState };
};

// For backward compatibility
export { getSocket as socket };

export default {
  getSocket,
  connectSocket,
  disconnectSocket,
  joinOrganization,
  joinBranch,
  joinDoctor,
  emitQueueStatusUpdate,
  getLastKnownQueueState,
  getAllLastKnownQueueStates,
};
