/**
 * Analytics Utilities
 *
 * This module provides utilities for tracking user interactions and events
 * in the application, particularly for ABHA verification flows.
 */

// Types of verification methods
export type VerificationMethod = "aadhaar" | "mobile" | "abha" | "abhaAddress";

// Verification flow steps
export type VerificationStep =
  | "started"
  | "otp_requested"
  | "otp_verified"
  | "completed"
  | "failed";

// Analytics event types
export type AnalyticsEventType =
  | "verification_method_selected"
  | "verification_step"
  | "verification_error"
  | "verification_success";

// Analytics event data
export interface AnalyticsEvent {
  eventType: AnalyticsEventType;
  timestamp: string;
  data: Record<string, any>;
}

// In-memory storage for analytics events (in a real app, this would be sent to a server)
const analyticsEvents: AnalyticsEvent[] = [];

/**
 * Track a verification method selection
 * @param method - The verification method selected
 * @param patientId - The patient ID
 */
export function trackVerificationMethodSelected(
  method: VerificationMethod,
  patientId: string,
): void {
  trackEvent("verification_method_selected", {
    method,
    patientId,
  });
}

/**
 * Track a verification step
 * @param method - The verification method
 * @param step - The verification step
 * @param patientId - The patient ID
 * @param additionalData - Any additional data to track
 */
export function trackVerificationStep(
  method: VerificationMethod,
  step: VerificationStep,
  patientId: string,
  additionalData: Record<string, any> = {},
): void {
  trackEvent("verification_step", {
    method,
    step,
    patientId,
    ...additionalData,
  });
}

/**
 * Track a verification error
 * @param method - The verification method
 * @param step - The verification step where the error occurred
 * @param errorCode - The error code
 * @param errorMessage - The error message
 * @param patientId - The patient ID
 */
export function trackVerificationError(
  method: VerificationMethod,
  step: VerificationStep,
  errorCode: string,
  errorMessage: string,
  patientId: string,
): void {
  trackEvent("verification_error", {
    method,
    step,
    errorCode,
    errorMessage,
    patientId,
  });
}

/**
 * Track a verification success
 * @param method - The verification method
 * @param patientId - The patient ID
 * @param abhaNumber - The ABHA number
 */
export function trackVerificationSuccess(
  method: VerificationMethod,
  patientId: string,
  abhaNumber: string,
): void {
  trackEvent("verification_success", {
    method,
    patientId,
    abhaNumber,
  });
}

/**
 * Track an analytics event
 * @param eventType - The event type
 * @param data - The event data
 */
function trackEvent(
  eventType: AnalyticsEventType,
  data: Record<string, any>,
): void {
  const event: AnalyticsEvent = {
    eventType,
    timestamp: new Date().toISOString(),
    data,
  };

  // In a real app, this would be sent to an analytics service
  analyticsEvents.push(event);

  // Log the event in development mode
  if (process.env.NODE_ENV === "development") {
    console.log("Analytics event:", event);
  }

  // In a production app, you would send this to your analytics service
  // Example: sendToAnalyticsService(event);
}

/**
 * Get all analytics events (for debugging)
 * @returns All analytics events
 */
export function getAnalyticsEvents(): AnalyticsEvent[] {
  return [...analyticsEvents];
}

/**
 * Clear all analytics events (for debugging)
 */
export function clearAnalyticsEvents(): void {
  analyticsEvents.length = 0;
}

/**
 * Get analytics summary for verification methods
 * @returns Summary of verification method usage
 */
export function getVerificationMethodsSummary(): Record<
  VerificationMethod,
  number
> {
  const summary: Record<VerificationMethod, number> = {
    aadhaar: 0,
    mobile: 0,
    abha: 0,
    abhaAddress: 0,
  };

  analyticsEvents
    .filter((event) => event.eventType === "verification_method_selected")
    .forEach((event) => {
      const method = event.data.method as VerificationMethod;
      summary[method] = (summary[method] || 0) + 1;
    });

  return summary;
}

/**
 * Get analytics summary for verification success rates
 * @returns Summary of verification success rates by method
 */
export function getVerificationSuccessRates(): Record<
  VerificationMethod,
  { attempts: number; successes: number; rate: number }
> {
  const attempts: Record<VerificationMethod, number> = {
    aadhaar: 0,
    mobile: 0,
    abha: 0,
    abhaAddress: 0,
  };

  const successes: Record<VerificationMethod, number> = {
    aadhaar: 0,
    mobile: 0,
    abha: 0,
    abhaAddress: 0,
  };

  // Count attempts (started verification flows)
  analyticsEvents
    .filter(
      (event) =>
        event.eventType === "verification_step" &&
        event.data.step === "started",
    )
    .forEach((event) => {
      const method = event.data.method as VerificationMethod;
      attempts[method] = (attempts[method] || 0) + 1;
    });

  // Count successes
  analyticsEvents
    .filter((event) => event.eventType === "verification_success")
    .forEach((event) => {
      const method = event.data.method as VerificationMethod;
      successes[method] = (successes[method] || 0) + 1;
    });

  // Calculate success rates
  const rates: Record<
    VerificationMethod,
    { attempts: number; successes: number; rate: number }
  > = {
    aadhaar: { attempts: 0, successes: 0, rate: 0 },
    mobile: { attempts: 0, successes: 0, rate: 0 },
    abha: { attempts: 0, successes: 0, rate: 0 },
    abhaAddress: { attempts: 0, successes: 0, rate: 0 },
  };

  Object.keys(attempts).forEach((method) => {
    const m = method as VerificationMethod;
    rates[m] = {
      attempts: attempts[m],
      successes: successes[m],
      rate: attempts[m] > 0 ? (successes[m] / attempts[m]) * 100 : 0,
    };
  });

  return rates;
}
