/**
 * Masks an Aadhaar number according to UIDAI guidelines
 * Masks the first 8 digits, leaving only the last 4 digits visible
 *
 * @param aadhaarNumber The full 12-digit Aadhaar number
 * @returns The masked Aadhaar number (e.g., "XXXX-XXXX-1234")
 */
export function maskAadhaarNumber(aadhaarNumber: string): string {
  if (!aadhaarNumber || aadhaarNumber.length !== 12) {
    return "";
  }

  // Extract the last 4 digits
  const lastFourDigits = aadhaarNumber.slice(-4);

  // Return the masked Aadhaar number
  return `XXXX-XXXX-${lastFourDigits}`;
}

/**
 * Checks if a string is a valid Aadhaar number
 *
 * @param aadhaarNumber The string to check
 * @returns True if the string is a valid Aadhaar number (12 digits)
 */
export function isValidAadhaarNumber(aadhaarNumber: string): boolean {
  return /^\d{12}$/.test(aadhaarNumber);
}
