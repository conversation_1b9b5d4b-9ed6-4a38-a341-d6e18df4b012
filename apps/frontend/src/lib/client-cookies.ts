"use client";

/**
 * Utility functions for handling cookies on the client side
 */

/**
 * Get the branch ID from cookies (client-side)
 * @returns The branch ID or null if not found
 */
export function getBranchIdFromCookies(): string | null {
  if (typeof document === "undefined") return null;

  // Try to get from cookies first
  const cookies = document.cookie.split(";");

  // Look for branch-id cookie
  const branchCookie = cookies.find((cookie) =>
    cookie.trim().startsWith("branch-id="),
  );
  if (branchCookie) {
    const branchId = branchCookie.split("=")[1].trim();
    if (branchId) return branchId;
  }

  // Look for user-info cookie which might contain branchId
  const userInfoCookie = cookies.find((cookie) =>
    cookie.trim().startsWith("user-info="),
  );
  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(
        decodeURIComponent(userInfoCookie.split("=")[1].trim()),
      );
      if (userInfo && userInfo.currentBranchId) {
        return userInfo.currentBranchId;
      }
    } catch (error) {
      console.error("Error parsing user-info cookie:", error);
    }
  }

  // Try localStorage as a fallback
  try {
    const storedBranchId = localStorage.getItem("branch-id");
    if (storedBranchId) return storedBranchId;
  } catch (e) {
    console.error("Error accessing localStorage:", e);
  }

  return null;
}

/**
 * Get the user info from cookies (client-side)
 * @returns The user info object or null if not found
 */
export function getUserInfoFromCookies(): any | null {
  if (typeof document === "undefined") return null;

  const cookies = document.cookie.split(";");
  const userInfoCookie = cookies.find((cookie) =>
    cookie.trim().startsWith("user-info="),
  );

  if (userInfoCookie) {
    try {
      return JSON.parse(
        decodeURIComponent(userInfoCookie.split("=")[1].trim()),
      );
    } catch (error) {
      console.error("Error parsing user-info cookie:", error);
      return null;
    }
  }

  return null;
}
