/**
 * Mask a mobile number for logging purposes
 * @param mobile - Mobile number to mask
 * @returns Masked mobile number (e.g., "98XXXX1234")
 */
export function maskMobileNumber(mobile: string): string {
  if (!mobile || mobile.length < 10) {
    return "Invalid mobile number";
  }

  // For a 10-digit mobile number, mask the middle 4 digits
  if (mobile.length === 10) {
    return `${mobile.substring(0, 2)}XXXX${mobile.substring(6)}`;
  }

  // For longer numbers (with country code), mask the middle part
  const lastFour = mobile.substring(mobile.length - 4);
  const firstTwo = mobile.substring(0, mobile.length - 8 > 2 ? 3 : 2);
  return `${firstTwo}XXXX${lastFour}`;
}
