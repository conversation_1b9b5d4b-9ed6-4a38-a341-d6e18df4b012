import { prisma } from "@/lib/prisma";
import { emitQueueStatusUpdate } from "@/lib/socket-client";

export type QueueStatus =
  | "waiting"
  | "in-consultation"
  | "paused"
  | "completed"
  | "cancelled";

export interface QueueStateUpdate {
  queueId: string;
  status: QueueStatus;
  position?: number;
  estimatedStartTime?: Date;
  actualStartTime?: Date;
  completionTime?: Date;
  pauseReason?: string;
  notes?: string;
}

/**
 * Centralized Queue State Manager
 *
 * Handles all queue state transitions and broadcasts updates to all connected clients
 * Ensures consistent state across all clients and persists state to the database
 */
export class QueueStateManager {
  /**
   * Update a queue item's status and broadcast the change to all connected clients
   */
  static async updateQueueStatus(
    queueId: string,
    update: Partial<QueueStateUpdate>,
    organizationId: string,
    branchId: string,
    doctorId: string,
  ) {
    try {
      // Validate the status transition
      if (update.status) {
        const currentQueue = await prisma.queueStatus.findUnique({
          where: { id: queueId },
          select: { status: true },
        });

        if (!currentQueue) {
          throw new Error("Queue item not found");
        }

        // Validate the status transition
        if (!this.isValidStatusTransition(currentQueue.status, update.status)) {
          throw new Error(
            `Invalid status transition from ${currentQueue.status} to ${update.status}`,
          );
        }
      }

      // Prepare update data with timestamps based on status
      const updateData: any = { ...update };

      // Set timestamps based on status
      if (update.status === "in-consultation" && !update.actualStartTime) {
        updateData.actualStartTime = new Date();
      }

      if (update.status === "completed" && !update.completionTime) {
        updateData.completionTime = new Date();
      }

      // Update the queue status in the database
      const updatedQueue = await prisma.queueStatus.update({
        where: { id: queueId },
        data: updateData,
        include: {
          appointment: {
            select: {
              id: true,
              appointmentDate: true,
              startTime: true,
              endTime: true,
              status: true,
              branch: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          doctor: {
            select: {
              id: true,
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      // If the status is "completed" or "cancelled", update the appointment status as well
      if (update.status === "completed" || update.status === "cancelled") {
        await prisma.appointment.update({
          where: { id: updatedQueue.appointmentId },
          data: {
            status: update.status === "completed" ? "completed" : "cancelled",
          },
        });
      }

      // Format the queue item for the frontend
      const formattedQueue = {
        id: updatedQueue.id,
        patientId: updatedQueue.patientId,
        patientName: `${updatedQueue.patient.firstName} ${updatedQueue.patient.lastName}`,
        doctorId: updatedQueue.doctorId,
        doctorName: updatedQueue.doctor.user.name,
        branchId: updatedQueue.appointment.branch.id,
        branchName: updatedQueue.appointment.branch.name,
        organizationId: updatedQueue.organizationId,
        appointmentId: updatedQueue.appointmentId,
        status: updatedQueue.status,
        position: updatedQueue.queueNumber,
        estimatedStartTime: updatedQueue.estimatedStartTime,
        actualStartTime: updatedQueue.actualStartTime,
        completionTime: updatedQueue.completionTime,
        pauseReason: updatedQueue.pauseReason,
        notes: updatedQueue.notes,
        createdAt: updatedQueue.createdAt.toISOString(),
        updatedAt: updatedQueue.updatedAt.toISOString(),
      };

      // Broadcast the update to all connected clients
      emitQueueStatusUpdate(organizationId, branchId, doctorId, {
        queueId,
        ...update,
        updatedAt: new Date().toISOString(),
        formattedQueue,
      });

      return formattedQueue;
    } catch (error) {
      console.error("Error updating queue status:", error);
      throw error;
    }
  }

  /**
   * Get all queue items for a specific branch, doctor, or date
   */
  static async getQueueItems(params: {
    organizationId: string;
    branchId?: string;
    doctorId?: string;
    date?: string;
    status?: string;
  }) {
    const { organizationId, branchId, doctorId, date, status } = params;

    // Build the where clause
    let where: any = {
      organizationId,
    };

    if (branchId) {
      where.appointment = {
        ...where.appointment,
        branchId,
      };
    }

    if (doctorId) {
      where.doctorId = doctorId;
    }

    if (date) {
      // Parse the date string
      const parsedDate = new Date(date);
      // Handle timezone issues by working with the date parts
      const year = parsedDate.getFullYear();
      const month = parsedDate.getMonth();
      const day = parsedDate.getDate();

      // Create start date (beginning of the day in local timezone)
      const startDate = new Date(year, month, day, 0, 0, 0, 0);

      // Create end date (end of the day in local timezone)
      const endDate = new Date(year, month, day, 23, 59, 59, 999);

      console.log(
        `QueueStateManager filtering by date: ${startDate.toISOString()} to ${endDate.toISOString()}`,
      );
      console.log(`Original date string: ${date}`);

      where.appointment = {
        ...where.appointment,
        appointmentDate: {
          gte: startDate,
          lte: endDate,
        },
      };
    }

    if (status && status !== "all") {
      where.status = status;
    }

    // Get queue status entries
    const queueStatuses = await prisma.queueStatus.findMany({
      where,
      include: {
        appointment: {
          select: {
            id: true,
            appointmentDate: true,
            startTime: true,
            endTime: true,
            duration: true,
            status: true,
            type: true,
            notes: true,
            branch: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
          },
        },
      },
      orderBy: [
        {
          status: "asc", // waiting first, then in-consultation, then paused, then completed, then cancelled
        },
        {
          queueNumber: "asc",
        },
      ],
    });

    // Format the queue items for the frontend
    return queueStatuses.map((queue) => ({
      id: queue.id,
      patientId: queue.patientId,
      patientName: `${queue.patient.firstName} ${queue.patient.lastName}`,
      doctorId: queue.doctorId,
      doctorName: queue.doctor.user.name,
      branchId: queue.appointment.branch.id,
      branchName: queue.appointment.branch.name,
      organizationId: queue.organizationId,
      appointmentId: queue.appointmentId,
      status: queue.status,
      position: queue.queueNumber,
      estimatedStartTime: queue.estimatedStartTime,
      actualStartTime: queue.actualStartTime,
      completionTime: queue.completionTime,
      pauseReason: queue.pauseReason,
      notes: queue.notes,
      appointmentDate: queue.appointment.appointmentDate,
      startTime: queue.appointment.startTime,
      endTime: queue.appointment.endTime,
      duration: queue.appointment.duration,
      createdAt: queue.createdAt.toISOString(),
      updatedAt: queue.updatedAt.toISOString(),
    }));
  }

  /**
   * Calculate estimated wait times for all patients in the queue
   */
  static async calculateEstimatedWaitTimes(doctorId: string, date: string) {
    try {
      // Parse the date string
      const parsedDate = new Date(date);
      // Handle timezone issues by working with the date parts
      const year = parsedDate.getFullYear();
      const month = parsedDate.getMonth();
      const day = parsedDate.getDate();

      // Create start date (beginning of the day in local timezone)
      const startDate = new Date(year, month, day, 0, 0, 0, 0);

      // Create end date (end of the day in local timezone)
      const endDate = new Date(year, month, day, 23, 59, 59, 999);

      console.log(
        `calculateEstimatedWaitTimes filtering by date: ${startDate.toISOString()} to ${endDate.toISOString()}`,
      );

      // Get all queue items for the doctor and date
      const queueItems = await prisma.queueStatus.findMany({
        where: {
          doctorId,
          appointment: {
            appointmentDate: {
              gte: startDate,
              lte: endDate,
            },
          },
          status: {
            in: ["waiting", "in-consultation", "paused"],
          },
        },
        include: {
          appointment: {
            select: {
              duration: true,
              startTime: true,
            },
          },
        },
        orderBy: {
          queueNumber: "asc",
        },
      });

      // Get the current in-consultation appointment
      const currentAppointment = queueItems.find(
        (item) => item.status === "in-consultation",
      );

      // If there's a current appointment, calculate how much time is left
      let currentEndTime = new Date();
      if (currentAppointment) {
        const startTime = currentAppointment.actualStartTime || new Date();
        currentEndTime = new Date(
          startTime.getTime() + currentAppointment.appointment.duration * 60000,
        );
      }

      // Calculate estimated wait times for all waiting patients
      let estimatedStartTime = currentEndTime;
      const updates = [];

      for (const item of queueItems) {
        if (item.status === "waiting") {
          // Add 5 minutes buffer between appointments
          estimatedStartTime = new Date(
            estimatedStartTime.getTime() + 5 * 60000,
          );

          updates.push({
            id: item.id,
            estimatedStartTime,
          });

          // Move to the next appointment
          estimatedStartTime = new Date(
            estimatedStartTime.getTime() + item.appointment.duration * 60000,
          );
        }
      }

      // Update all queue items with estimated wait times
      if (updates.length > 0) {
        await Promise.all(
          updates.map((update) =>
            prisma.queueStatus.update({
              where: { id: update.id },
              data: { estimatedStartTime: update.estimatedStartTime },
            }),
          ),
        );
      }

      return updates;
    } catch (error) {
      console.error("Error calculating estimated wait times:", error);
      throw error;
    }
  }

  /**
   * Validate if a status transition is allowed
   */
  private static isValidStatusTransition(
    currentStatus: string,
    newStatus: QueueStatus,
  ): boolean {
    // Define valid status transitions
    const validTransitions: Record<string, QueueStatus[]> = {
      waiting: ["in-consultation", "cancelled"],
      "in-consultation": ["paused", "completed", "cancelled"],
      paused: ["in-consultation", "cancelled"],
      completed: ["waiting"], // Allow reopening a completed appointment
      cancelled: ["waiting"], // Allow reopening a cancelled appointment
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }
}
