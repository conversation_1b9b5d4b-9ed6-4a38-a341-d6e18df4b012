import { prisma } from "@/lib/prisma";

/**
 * Check if an organization has completed onboarding
 * @param organizationId The ID of the organization to check
 * @returns A boolean indicating whether onboarding is completed
 */
export async function checkOnboardingStatus(
  organizationId: string,
): Promise<boolean> {
  if (!organizationId) {
    return false;
  }

  try {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      select: { onboardingCompleted: true },
    });

    return organization?.onboardingCompleted || false;
  } catch (error) {
    console.error("Error checking onboarding status:", error);
    return false;
  }
}
