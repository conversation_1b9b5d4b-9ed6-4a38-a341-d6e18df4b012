import crypto from "crypto";

// Get encryption key from environment variable
// In production, this should be a strong, randomly generated key stored securely
const ENCRYPTION_KEY =
  process.env.ENCRYPTION_KEY ||
  "default-key-for-development-only-change-in-production";
const IV_LENGTH = 16; // For AES, this is always 16 bytes

/**
 * Encrypts a string using AES-256-CBC encryption
 * @param text The text to encrypt
 * @returns The encrypted text as a hex string with IV prepended
 */
export function encrypt(text: string): string {
  if (!text) return "";

  // Generate a random initialization vector
  const iv = crypto.randomBytes(IV_LENGTH);

  // Create a cipher using the encryption key and IV
  const cipher = crypto.createCipheriv(
    "aes-256-cbc",
    Buffer.from(ENCRYPTION_KEY.padEnd(32).slice(0, 32)), // Ensure key is exactly 32 bytes
    iv,
  );

  // Encrypt the text
  let encrypted = cipher.update(text, "utf8", "hex");
  encrypted += cipher.final("hex");

  // Return the IV and encrypted text as a single string
  return `${iv.toString("hex")}:${encrypted}`;
}

/**
 * Decrypts a string that was encrypted using the encrypt function
 * @param text The encrypted text (IV:encryptedText format)
 * @returns The decrypted text
 */
export function decrypt(text: string): string {
  if (!text) return "";

  try {
    // Split the IV and encrypted text
    const textParts = text.split(":");
    if (textParts.length !== 2) return "";

    const iv = Buffer.from(textParts[0], "hex");
    const encryptedText = textParts[1];

    // Create a decipher using the encryption key and IV
    const decipher = crypto.createDecipheriv(
      "aes-256-cbc",
      Buffer.from(ENCRYPTION_KEY.padEnd(32).slice(0, 32)), // Ensure key is exactly 32 bytes
      iv,
    );

    // Decrypt the text
    let decrypted = decipher.update(encryptedText, "hex", "utf8");
    decrypted += decipher.final("utf8");

    return decrypted;
  } catch (error) {
    console.error("Decryption error:", error);
    return "";
  }
}
