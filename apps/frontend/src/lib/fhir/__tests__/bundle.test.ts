import { describe, it, expect } from "vitest";
import {
  createBundle,
  createDocumentBundle,
  serializeBundle,
  deserializeBundle,
} from "../bundle";
import { FhirBundle, FhirResource } from "../types";

describe("FHIR Bundle Utilities", () => {
  // Sample resources for testing
  const patientResource: FhirResource = {
    resourceType: "Patient",
    id: "patient-1",
    meta: {
      versionId: "1",
      lastUpdated: "2023-01-01T00:00:00Z",
    },
  };

  const practitionerResource: FhirResource = {
    resourceType: "Practitioner",
    id: "practitioner-1",
    meta: {
      versionId: "1",
      lastUpdated: "2023-01-01T00:00:00Z",
    },
  };

  const observationResource: FhirResource = {
    resourceType: "Observation",
    id: "observation-1",
    meta: {
      versionId: "1",
      lastUpdated: "2023-01-01T00:00:00Z",
    },
    status: "final",
    code: {
      coding: [
        {
          system: "http://loinc.org",
          code: "8480-6",
          display: "Systolic blood pressure",
        },
      ],
    },
    subject: {
      reference: "Patient/patient-1",
    },
  };

  const resources = [
    patientResource,
    practitionerResource,
    observationResource,
  ];

  describe("createBundle", () => {
    it("should create a FHIR bundle with the specified resources", () => {
      const bundle = createBundle(resources);

      expect(bundle.resourceType).toBe("Bundle");
      expect(bundle.type).toBe("collection");
      expect(bundle.entry).toHaveLength(3);
      expect(bundle.entry[0].resource).toBe(patientResource);
      expect(bundle.entry[1].resource).toBe(practitionerResource);
      expect(bundle.entry[2].resource).toBe(observationResource);
    });

    it("should create a FHIR bundle with the specified type", () => {
      const bundle = createBundle(resources, "transaction");

      expect(bundle.type).toBe("transaction");
    });
  });

  describe("createDocumentBundle", () => {
    it("should create a FHIR document bundle with a Composition resource", () => {
      const bundle = createDocumentBundle(
        resources,
        "Test Document",
        "patient-1",
        "practitioner-1",
      );

      expect(bundle.resourceType).toBe("Bundle");
      expect(bundle.type).toBe("document");
      expect(bundle.entry).toHaveLength(4); // 3 resources + 1 Composition
      expect(bundle.entry[0].resource.resourceType).toBe("Composition");
      expect(bundle.entry[0].resource.title).toBe("Test Document");
      expect(bundle.entry[0].resource.subject.reference).toBe(
        "urn:uuid/patient-1",
      );
      expect(bundle.entry[0].resource.author[0].reference).toBe(
        "urn:uuid/practitioner-1",
      );
    });

    it("should throw an error if patient resource is not found", () => {
      expect(() => {
        createDocumentBundle(
          [practitionerResource, observationResource],
          "Test Document",
          "patient-1",
          "practitioner-1",
        );
      }).toThrow("Patient resource not found in resources list");
    });

    it("should throw an error if author resource is not found", () => {
      expect(() => {
        createDocumentBundle(
          [patientResource, observationResource],
          "Test Document",
          "patient-1",
          "practitioner-1",
        );
      }).toThrow("Author resource not found in resources list");
    });
  });

  describe("serializeBundle and deserializeBundle", () => {
    it("should serialize and deserialize a FHIR bundle correctly", () => {
      const bundle = createBundle(resources);
      const serialized = serializeBundle(bundle);
      const deserialized = deserializeBundle(serialized);

      expect(deserialized).toEqual(bundle);
    });
  });
});
