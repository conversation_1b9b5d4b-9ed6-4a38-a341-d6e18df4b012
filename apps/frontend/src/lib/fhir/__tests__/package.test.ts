import { describe, it, expect, vi } from "vitest";
import { calculateChecksum, validateHealthRecordPackage } from "../package";
import { AbdmHealthRecordPackage } from "../types";

// Mock the createABDMEnvelope function
vi.mock("../abdm-crypto", () => ({
  createABDMEnvelope: vi.fn().mockResolvedValue({
    encryptedData: "encrypted-data",
    keyMaterial: {
      cryptoAlg: "ECDH",
      curve: "Curve25519",
      dhPublicKey: {
        expiry: "2023-01-01T01:00:00Z",
        parameters: "Curve25519/32byte random key",
        keyValue: "public-key",
      },
      nonce: "nonce",
    },
  }),
}));

describe("FHIR Package Utilities", () => {
  describe("calculateChecksum", () => {
    it("should calculate a SHA-256 checksum of a string", () => {
      const content = "test content";
      const checksum = calculateChecksum(content);

      // Expected SHA-256 hash of 'test content'
      const expectedChecksum =
        "6ae8a75555209fd6c44157c0aed8016e763ff435a19cf186f76863140143ff72";

      expect(checksum).toBe(expectedChecksum);
    });
  });

  describe("validateHealthRecordPackage", () => {
    it("should validate a valid health record package", () => {
      const validPackage: AbdmHealthRecordPackage = {
        pageNumber: 0,
        pageCount: 1,
        transactionId: "transaction-id",
        entries: [
          {
            content: Buffer.from('{"resourceType":"Bundle"}').toString(
              "base64",
            ),
            media: "application/fhir+json",
            checksum: calculateChecksum('{"resourceType":"Bundle"}'),
            careContextReference: "care-context-reference",
          },
        ],
        keyMaterial: {
          cryptoAlg: "ECDH",
          curve: "Curve25519",
          dhPublicKey: {
            expiry: new Date(Date.now() + 3600000).toISOString(), // 1 hour in the future
            parameters: "Curve25519/32byte random key",
            keyValue: "public-key",
          },
          nonce: "nonce",
        },
      };

      const result = validateHealthRecordPackage(validPackage);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should invalidate a package with missing transactionId", () => {
      const invalidPackage: AbdmHealthRecordPackage = {
        pageNumber: 0,
        pageCount: 1,
        transactionId: "", // Missing transactionId
        entries: [
          {
            content: Buffer.from('{"resourceType":"Bundle"}').toString(
              "base64",
            ),
            media: "application/fhir+json",
            checksum: calculateChecksum('{"resourceType":"Bundle"}'),
            careContextReference: "care-context-reference",
          },
        ],
        keyMaterial: {
          cryptoAlg: "ECDH",
          curve: "Curve25519",
          dhPublicKey: {
            expiry: new Date(Date.now() + 3600000).toISOString(),
            parameters: "Curve25519/32byte random key",
            keyValue: "public-key",
          },
          nonce: "nonce",
        },
      };

      const result = validateHealthRecordPackage(invalidPackage);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Missing transactionId");
    });

    it("should invalidate a package with missing entries", () => {
      const invalidPackage: AbdmHealthRecordPackage = {
        pageNumber: 0,
        pageCount: 1,
        transactionId: "transaction-id",
        entries: [], // Empty entries
        keyMaterial: {
          cryptoAlg: "ECDH",
          curve: "Curve25519",
          dhPublicKey: {
            expiry: new Date(Date.now() + 3600000).toISOString(),
            parameters: "Curve25519/32byte random key",
            keyValue: "public-key",
          },
          nonce: "nonce",
        },
      };

      const result = validateHealthRecordPackage(invalidPackage);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Missing entries");
    });

    it("should invalidate a package with invalid media type", () => {
      const invalidPackage: AbdmHealthRecordPackage = {
        pageNumber: 0,
        pageCount: 1,
        transactionId: "transaction-id",
        entries: [
          {
            content: Buffer.from('{"resourceType":"Bundle"}').toString(
              "base64",
            ),
            media: "application/json", // Invalid media type
            checksum: calculateChecksum('{"resourceType":"Bundle"}'),
            careContextReference: "care-context-reference",
          },
        ],
        keyMaterial: {
          cryptoAlg: "ECDH",
          curve: "Curve25519",
          dhPublicKey: {
            expiry: new Date(Date.now() + 3600000).toISOString(),
            parameters: "Curve25519/32byte random key",
            keyValue: "public-key",
          },
          nonce: "nonce",
        },
      };

      const result = validateHealthRecordPackage(invalidPackage);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain(
        "Entry 0: Invalid media type, expected 'application/fhir+json'",
      );
    });

    it("should invalidate a package with checksum mismatch", () => {
      const invalidPackage: AbdmHealthRecordPackage = {
        pageNumber: 0,
        pageCount: 1,
        transactionId: "transaction-id",
        entries: [
          {
            content: Buffer.from('{"resourceType":"Bundle"}').toString(
              "base64",
            ),
            media: "application/fhir+json",
            checksum: "invalid-checksum", // Invalid checksum
            careContextReference: "care-context-reference",
          },
        ],
        keyMaterial: {
          cryptoAlg: "ECDH",
          curve: "Curve25519",
          dhPublicKey: {
            expiry: new Date(Date.now() + 3600000).toISOString(),
            parameters: "Curve25519/32byte random key",
            keyValue: "public-key",
          },
          nonce: "nonce",
        },
      };

      const result = validateHealthRecordPackage(invalidPackage);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Entry 0: Checksum mismatch");
    });

    it("should invalidate a package with expired dhPublicKey", () => {
      const invalidPackage: AbdmHealthRecordPackage = {
        pageNumber: 0,
        pageCount: 1,
        transactionId: "transaction-id",
        entries: [
          {
            content: Buffer.from('{"resourceType":"Bundle"}').toString(
              "base64",
            ),
            media: "application/fhir+json",
            checksum: calculateChecksum('{"resourceType":"Bundle"}'),
            careContextReference: "care-context-reference",
          },
        ],
        keyMaterial: {
          cryptoAlg: "ECDH",
          curve: "Curve25519",
          dhPublicKey: {
            expiry: new Date(Date.now() - 3600000).toISOString(), // 1 hour in the past
            parameters: "Curve25519/32byte random key",
            keyValue: "public-key",
          },
          nonce: "nonce",
        },
      };

      const result = validateHealthRecordPackage(invalidPackage);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain("dhPublicKey has expired");
    });
  });
});
