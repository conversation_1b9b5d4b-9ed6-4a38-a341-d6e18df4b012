import { describe, it, expect } from "vitest";
import {
  createPatientResource,
  createVitalsObservation,
  createMedicationRequests,
  createCondition,
  createPractitionerResource,
} from "../resources";

describe("FHIR Resource Utilities", () => {
  describe("createPatientResource", () => {
    it("should create a FHIR Patient resource from a Patient model", () => {
      const patient = {
        id: "patient-1",
        firstName: "<PERSON>",
        lastName: "Do<PERSON>",
        gender: "Male",
        dateOfBirth: new Date("1980-01-01"),
        phone: "**********",
        email: "<EMAIL>",
        address: "123 Main St",
        city: "Anytown",
        state: "CA",
        pincode: "12345",
        country: "USA",
        status: "active",
        abhaProfile: {
          abhaNumber: "ABHA123456789",
        },
      };

      const resource = createPatientResource(patient);

      expect(resource.resourceType).toBe("Patient");
      expect(resource.name?.[0].family).toBe("Doe");
      expect(resource.name?.[0].given?.[0]).toBe("<PERSON>");
      expect(resource.gender).toBe("male");
      expect(resource.birthDate).toBe("1980-01-01");
      expect(resource.telecom).toHaveLength(2);
      expect(resource.telecom?.[0].system).toBe("phone");
      expect(resource.telecom?.[0].value).toBe("**********");
      expect(resource.telecom?.[1].system).toBe("email");
      expect(resource.telecom?.[1].value).toBe("<EMAIL>");
      expect(resource.address).toHaveLength(1);
      expect(resource.address?.[0].line?.[0]).toBe("123 Main St");
      expect(resource.address?.[0].city).toBe("Anytown");
      expect(resource.address?.[0].state).toBe("CA");
      expect(resource.address?.[0].postalCode).toBe("12345");
      expect(resource.address?.[0].country).toBe("USA");
      expect(resource.identifier).toHaveLength(1);
      expect(resource.identifier?.[0].system).toBe(
        "https://abdm.gov.in/id/abha",
      );
      expect(resource.identifier?.[0].value).toBe("ABHA123456789");
    });
  });

  describe("createVitalsObservation", () => {
    it("should create FHIR Observation resources from a Vitals model", () => {
      const vitals = {
        id: "vitals-1",
        bloodPressureSystolic: 120,
        bloodPressureDiastolic: 80,
        pulse: 72,
        temperature: 37.0,
        respiratoryRate: 16,
        oxygenSaturation: 98,
        height: 175,
        weight: 70,
        bmi: 22.9,
        recordedAt: new Date("2023-01-01T12:00:00Z"),
        createdAt: new Date("2023-01-01T12:00:00Z"),
      };

      const observations = createVitalsObservation(
        vitals,
        "patient-1",
        "practitioner-1",
      );

      expect(observations).toHaveLength(7); // BP, pulse, temp, resp rate, O2 sat, height, weight, BMI

      // Check blood pressure observation
      const bpObservation = observations[0];
      expect(bpObservation.resourceType).toBe("Observation");
      expect(bpObservation.status).toBe("final");
      expect(bpObservation.subject.reference).toBe("Patient/patient-1");
      expect(bpObservation.performer?.[0].reference).toBe(
        "Practitioner/practitioner-1",
      );
      expect(bpObservation.component).toHaveLength(2);
      expect(bpObservation.component?.[0].valueQuantity?.value).toBe(120);
      expect(bpObservation.component?.[1].valueQuantity?.value).toBe(80);
    });
  });

  describe("createMedicationRequests", () => {
    it("should create FHIR MedicationRequest resources from a Prescription model", () => {
      const prescription = {
        id: "prescription-1",
        prescriptionDate: new Date("2023-01-01"),
        status: "active",
      };

      const prescriptionItems = [
        {
          id: "item-1",
          medicationName: "Aspirin",
          dosage: "100mg",
          frequency: "once daily",
          duration: "7 days",
          route: "oral",
          instructions: "Take with food",
          snomedCode: "123456",
          rxcui: "789012",
        },
      ];

      const medicationRequests = createMedicationRequests(
        prescription,
        prescriptionItems,
        "patient-1",
        "practitioner-1",
      );

      expect(medicationRequests).toHaveLength(1);

      const request = medicationRequests[0];
      expect(request.resourceType).toBe("MedicationRequest");
      expect(request.status).toBe("active");
      expect(request.intent).toBe("order");
      expect(request.medicationCodeableConcept?.text).toBe("Aspirin");
      expect(request.medicationCodeableConcept?.coding).toHaveLength(2);
      expect(request.medicationCodeableConcept?.coding?.[0].code).toBe(
        "123456",
      );
      expect(request.medicationCodeableConcept?.coding?.[1].code).toBe(
        "789012",
      );
      expect(request.subject.reference).toBe("Patient/patient-1");
      expect(request.requester?.reference).toBe("Practitioner/practitioner-1");
      expect(request.dosageInstruction).toHaveLength(1);
      expect(request.dosageInstruction?.[0].text).toBe(
        "100mg, once daily, for 7 days via oral. Take with food",
      );
    });
  });

  describe("createCondition", () => {
    it("should create a FHIR Condition resource from a ClinicalNote model with SNOMED diagnoses", () => {
      const clinicalNote = {
        id: "note-1",
        content: "Patient has hypertension",
        snomedDiagnoses: JSON.stringify([
          {
            code: "38341003",
            term: "Hypertension",
          },
        ]),
        createdAt: new Date("2023-01-01"),
      };

      const condition = createCondition(
        clinicalNote,
        "patient-1",
        "practitioner-1",
      );

      expect(condition).not.toBeNull();
      expect(condition?.resourceType).toBe("Condition");
      expect(condition?.code?.coding?.[0].code).toBe("38341003");
      expect(condition?.code?.coding?.[0].display).toBe("Hypertension");
      expect(condition?.subject.reference).toBe("Patient/patient-1");
      expect(condition?.recorder?.reference).toBe(
        "Practitioner/practitioner-1",
      );
      expect(condition?.note?.[0].text).toBe("Patient has hypertension");
    });

    it("should return null if no SNOMED diagnoses are available", () => {
      const clinicalNote = {
        id: "note-1",
        content: "Patient has hypertension",
        snomedDiagnoses: null,
        createdAt: new Date("2023-01-01"),
      };

      const condition = createCondition(
        clinicalNote,
        "patient-1",
        "practitioner-1",
      );

      expect(condition).toBeNull();
    });
  });

  describe("createPractitionerResource", () => {
    it("should create a FHIR Practitioner resource from a Doctor model", () => {
      const doctor = {
        id: "doctor-1",
        contactPhone: "**********",
        contactEmail: "<EMAIL>",
        qualification: "MD",
        status: "active",
        user: {
          name: "Dr. Jane Smith",
          email: "<EMAIL>",
        },
      };

      const resource = createPractitionerResource(doctor);

      expect(resource.resourceType).toBe("Practitioner");
      expect(resource.identifier?.[0].value).toBe("doctor-1");
      expect(resource.active).toBe(true);
      expect(resource.name?.[0].text).toBe("Dr. Jane Smith");
      expect(resource.telecom).toHaveLength(2);
      expect(resource.telecom?.[0].system).toBe("phone");
      expect(resource.telecom?.[0].value).toBe("**********");
      expect(resource.telecom?.[1].system).toBe("email");
      expect(resource.telecom?.[1].value).toBe("<EMAIL>");
      expect(resource.qualification?.[0].code?.text).toBe("MD");
    });
  });
});
