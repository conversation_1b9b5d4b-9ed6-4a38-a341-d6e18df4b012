export interface DocumentInfo {
  id: string;
  title: string;
  contentType: string;
  base64Data?: string;
  url?: string;
  size?: number;
  creationDate?: string;
  language?: string;
  description?: string;
}

export function extractDocumentFromDocumentReference(
  docRef: any,
): DocumentInfo | null {
  if (!docRef || docRef.resourceType !== "DocumentReference") return null;

  const attachment = docRef.content?.[0]?.attachment;
  if (!attachment) return null;

  return {
    id: docRef.id || "unknown",
    title:
      docRef.type?.text ||
      docRef.type?.coding?.[0]?.display ||
      docRef.description ||
      "Document",
    contentType: attachment.contentType || "application/pdf",
    base64Data: attachment.data,
    url: attachment.url,
    size: attachment.size,
    creationDate: attachment.creation || docRef.date,
    language: attachment.language || docRef.language,
    description: docRef.description,
  };
}

export function extractDocumentFromBinary(binary: any): DocumentInfo | null {
  if (!binary || binary.resourceType !== "Binary") return null;

  return {
    id: binary.id || "unknown",
    title: "Binary Document",
    contentType: binary.contentType || "application/pdf",
    base64Data: binary.data,
    size: binary.data ? Math.round((binary.data.length * 3) / 4) : undefined, // Estimate size from base64
    description: "Binary attachment",
  };
}

export function isValidBase64(str: string): boolean {
  if (!str || typeof str !== "string") return false;

  try {
    // Check if it's valid base64
    const decoded = atob(str);
    const encoded = btoa(decoded);
    return encoded === str;
  } catch (e) {
    return false;
  }
}

export function createBlobFromBase64(
  base64Data: string,
  contentType: string,
): Blob | null {
  if (!isValidBase64(base64Data)) return null;

  try {
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: contentType });
  } catch (e) {
    console.error("Error creating blob from base64:", e);
    return null;
  }
}

export function createObjectURL(
  base64Data: string,
  contentType: string,
): string | null {
  const blob = createBlobFromBase64(base64Data, contentType);
  if (!blob) return null;

  return URL.createObjectURL(blob);
}

export function downloadDocument(
  base64Data: string,
  filename: string,
  contentType: string,
): void {
  const blob = createBlobFromBase64(base64Data, contentType);
  if (!blob) {
    console.error("Failed to create blob for download");
    return;
  }

  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

export function getFileExtension(contentType: string): string {
  const mimeToExt: Record<string, string> = {
    "application/pdf": "pdf",
    "image/jpeg": "jpg",
    "image/png": "png",
    "image/gif": "gif",
    "text/plain": "txt",
    "application/json": "json",
    "application/xml": "xml",
    "text/html": "html",
  };

  return mimeToExt[contentType] || "bin";
}
