/**
 * ABDM Health Record Package Utilities
 *
 * This module provides functions for packaging FHIR bundles according to ABDM specifications.
 */

import { createHash } from "crypto";
import { FhirBundle, AbdmHealthRecordPackage } from "./types";
import { serializeBundle } from "./bundle";
import { createABDMEnvelope } from "../abdm-crypto";

/**
 * Calculate SHA-256 checksum of a string
 * @param content - String content to hash
 * @returns SHA-256 checksum as a hex string
 */
export function calculateChecksum(content: string): string {
  return createHash("sha256").update(content).digest("hex");
}

/**
 * Create an ABDM health record package from a FHIR bundle
 * @param bundle - FHIR bundle to package
 * @param transactionId - ABDM transaction ID
 * @param careContextReference - Care context reference
 * @param recipientPublicKey - Recipient's public key for encryption
 * @returns ABDM health record package
 */
export async function createHealthRecordPackage(
  bundle: FhirBundle,
  transactionId: string,
  careContextReference: string,
  recipientPublicKey: string,
): Promise<AbdmHealthRecordPackage> {
  // Serialize the bundle to JSON
  const bundleJson = serializeBundle(bundle);

  // Calculate the checksum of the bundle
  const checksum = calculateChecksum(bundleJson);

  // Create ABDM envelope for the bundle
  const envelope = await createABDMEnvelope(bundleJson, recipientPublicKey);

  // Create the package
  const healthRecordPackage: AbdmHealthRecordPackage = {
    pageNumber: 0,
    pageCount: 1,
    transactionId,
    entries: [
      {
        content: envelope.encryptedData,
        media: "application/fhir+json",
        checksum,
        careContextReference,
      },
    ],
    keyMaterial: envelope.keyMaterial,
  };

  return healthRecordPackage;
}

/**
 * Validate an ABDM health record package
 * @param healthRecordPackage - ABDM health record package to validate
 * @returns Validation result
 */
export function validateHealthRecordPackage(
  healthRecordPackage: AbdmHealthRecordPackage,
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check required fields
  if (!healthRecordPackage.transactionId) {
    errors.push("Missing transactionId");
  }

  if (!healthRecordPackage.entries || !healthRecordPackage.entries.length) {
    errors.push("Missing entries");
  } else {
    // Validate each entry
    healthRecordPackage.entries.forEach((entry, index) => {
      if (!entry.content) {
        errors.push(`Entry ${index}: Missing content`);
      }

      if (!entry.media) {
        errors.push(`Entry ${index}: Missing media type`);
      } else if (entry.media !== "application/fhir+json") {
        errors.push(
          `Entry ${index}: Invalid media type, expected 'application/fhir+json'`,
        );
      }

      if (!entry.checksum) {
        errors.push(`Entry ${index}: Missing checksum`);
      } else {
        // Validate checksum
        try {
          const content = Buffer.from(entry.content, "base64").toString(
            "utf-8",
          );
          const calculatedChecksum = calculateChecksum(content);

          if (calculatedChecksum !== entry.checksum) {
            errors.push(`Entry ${index}: Checksum mismatch`);
          }

          // Validate that content is valid FHIR JSON
          try {
            const parsedContent = JSON.parse(content);
            if (parsedContent.resourceType !== "Bundle") {
              errors.push(`Entry ${index}: Content is not a FHIR Bundle`);
            }
          } catch (error) {
            errors.push(`Entry ${index}: Content is not valid JSON`);
          }
        } catch (error) {
          errors.push(`Entry ${index}: Error validating checksum`);
        }
      }

      if (!entry.careContextReference) {
        errors.push(`Entry ${index}: Missing careContextReference`);
      }
    });
  }

  // Validate key material
  if (!healthRecordPackage.keyMaterial) {
    errors.push("Missing keyMaterial");
  } else {
    if (!healthRecordPackage.keyMaterial.cryptoAlg) {
      errors.push("Missing cryptoAlg in keyMaterial");
    } else if (healthRecordPackage.keyMaterial.cryptoAlg !== "ECDH") {
      errors.push(`Invalid cryptoAlg in keyMaterial, expected 'ECDH'`);
    }

    if (!healthRecordPackage.keyMaterial.curve) {
      errors.push("Missing curve in keyMaterial");
    } else if (healthRecordPackage.keyMaterial.curve !== "Curve25519") {
      errors.push(`Invalid curve in keyMaterial, expected 'Curve25519'`);
    }

    if (!healthRecordPackage.keyMaterial.dhPublicKey) {
      errors.push("Missing dhPublicKey in keyMaterial");
    } else {
      if (!healthRecordPackage.keyMaterial.dhPublicKey.expiry) {
        errors.push("Missing expiry in dhPublicKey");
      } else {
        // Validate expiry date
        try {
          const expiryDate = new Date(
            healthRecordPackage.keyMaterial.dhPublicKey.expiry,
          );
          const now = new Date();

          if (expiryDate < now) {
            errors.push("dhPublicKey has expired");
          }
        } catch (error) {
          errors.push("Invalid expiry date format in dhPublicKey");
        }
      }

      if (!healthRecordPackage.keyMaterial.dhPublicKey.parameters) {
        errors.push("Missing parameters in dhPublicKey");
      }

      if (!healthRecordPackage.keyMaterial.dhPublicKey.keyValue) {
        errors.push("Missing keyValue in dhPublicKey");
      }
    }

    if (!healthRecordPackage.keyMaterial.nonce) {
      errors.push("Missing nonce in keyMaterial");
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
