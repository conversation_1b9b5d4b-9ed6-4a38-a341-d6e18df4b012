import {
  User,
  UserCheck,
  Pill,
  Activity,
  FileText,
  TestTube,
  AlertCircle,
  Shield,
  FileImage,
  File,
  Building,
  Calendar,
  Stethoscope,
  Receipt,
  LucideIcon,
} from "lucide-react";

export interface ResourceTypeConfig {
  icon: LucideIcon;
  color: string;
  label: string;
  priority: number; // For sorting
  category: "core" | "clinical" | "administrative" | "document";
}

export const RESOURCE_TYPE_CONFIG: Record<string, ResourceTypeConfig> = {
  // Core Resources (Priority 1-10)
  Patient: {
    icon: User,
    color: "blue",
    label: "Patient Information",
    priority: 1,
    category: "core",
  },
  Practitioner: {
    icon: UserCheck,
    color: "green",
    label: "Healthcare Provider",
    priority: 2,
    category: "core",
  },
  Organization: {
    icon: Building,
    color: "gray",
    label: "Healthcare Organization",
    priority: 3,
    category: "core",
  },
  Composition: {
    icon: FileText,
    color: "purple",
    label: "Document Composition",
    priority: 4,
    category: "core",
  },

  // Clinical Resources (Priority 11-30)
  Observation: {
    icon: Activity,
    color: "orange",
    label: "Observations & Vitals",
    priority: 11,
    category: "clinical",
  },
  MedicationRequest: {
    icon: Pill,
    color: "purple",
    label: "Medication Prescriptions",
    priority: 12,
    category: "clinical",
  },
  Medication: {
    icon: Pill,
    color: "indigo",
    label: "Medications",
    priority: 13,
    category: "clinical",
  },
  MedicationStatement: {
    icon: Pill,
    color: "violet",
    label: "Medication History",
    priority: 14,
    category: "clinical",
  },
  DiagnosticReport: {
    icon: TestTube,
    color: "red",
    label: "Diagnostic Reports",
    priority: 15,
    category: "clinical",
  },
  Condition: {
    icon: AlertCircle,
    color: "yellow",
    label: "Medical Conditions",
    priority: 16,
    category: "clinical",
  },
  Immunization: {
    icon: Shield,
    color: "teal",
    label: "Immunizations",
    priority: 17,
    category: "clinical",
  },
  Procedure: {
    icon: Stethoscope,
    color: "pink",
    label: "Medical Procedures",
    priority: 18,
    category: "clinical",
  },
  AllergyIntolerance: {
    icon: AlertCircle,
    color: "red",
    label: "Allergies & Intolerances",
    priority: 19,
    category: "clinical",
  },
  Specimen: {
    icon: TestTube,
    color: "amber",
    label: "Specimens",
    priority: 20,
    category: "clinical",
  },

  // Administrative Resources (Priority 31-50)
  Encounter: {
    icon: Calendar,
    color: "cyan",
    label: "Healthcare Encounters",
    priority: 31,
    category: "administrative",
  },
  Appointment: {
    icon: Calendar,
    color: "blue",
    label: "Appointments",
    priority: 32,
    category: "administrative",
  },
  Invoice: {
    icon: Receipt,
    color: "green",
    label: "Invoices",
    priority: 33,
    category: "administrative",
  },
  ChargeItem: {
    icon: Receipt,
    color: "emerald",
    label: "Charge Items",
    priority: 34,
    category: "administrative",
  },

  // Document Resources (Priority 51-60)
  DocumentReference: {
    icon: FileImage,
    color: "indigo",
    label: "Document References",
    priority: 51,
    category: "document",
  },
  Binary: {
    icon: File,
    color: "gray",
    label: "Binary Attachments",
    priority: 52,
    category: "document",
  },
  Media: {
    icon: FileImage,
    color: "purple",
    label: "Media Files",
    priority: 53,
    category: "document",
  },
};

export function getResourceConfig(resourceType: string): ResourceTypeConfig {
  return (
    RESOURCE_TYPE_CONFIG[resourceType] || {
      icon: File,
      color: "gray",
      label: resourceType || "Unknown Resource",
      priority: 999,
      category: "administrative",
    }
  );
}

export function sortResourcesByPriority(
  resources: Array<{ resourceType: string; data: any[] }>,
): Array<{ resourceType: string; data: any[]; config: ResourceTypeConfig }> {
  return resources
    .map((r) => ({
      ...r,
      config: getResourceConfig(r.resourceType),
    }))
    .sort((a, b) => a.config.priority - b.config.priority);
}
