/**
 * FHIR Types
 *
 * This module defines TypeScript types for FHIR resources and bundles.
 */

// FHIR Resource Types
export type FhirResourceType =
  | "Patient"
  | "Practitioner"
  | "Organization"
  | "Observation"
  | "MedicationRequest"
  | "Medication"
  | "Condition"
  | "Procedure"
  | "AllergyIntolerance"
  | "Immunization"
  | "DiagnosticReport"
  | "DocumentReference"
  | "Composition"
  | "Bundle";

// FHIR Bundle Types
export type FhirBundleType =
  | "document"
  | "message"
  | "transaction"
  | "transaction-response"
  | "batch"
  | "batch-response"
  | "history"
  | "searchset"
  | "collection";

// FHIR Resource Base Interface
export interface FhirResource {
  resourceType: FhirResourceType;
  id: string;
  meta?: {
    versionId?: string;
    lastUpdated?: string;
    source?: string;
    profile?: string[];
    security?: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    tag?: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
  };
  text?: {
    status: "generated" | "extensions" | "additional" | "empty";
    div: string;
  };
}

// FHIR Bundle Entry Interface
export interface FhirBundleEntry {
  fullUrl?: string;
  resource: FhirResource;
  request?: {
    method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
    url: string;
  };
  response?: {
    status: string;
    location?: string;
    etag?: string;
    lastModified?: string;
    outcome?: FhirResource;
  };
}

// FHIR Bundle Interface
export interface FhirBundle extends FhirResource {
  resourceType: "Bundle";
  type: FhirBundleType;
  total?: number;
  link?: Array<{
    relation: string;
    url: string;
  }>;
  entry: FhirBundleEntry[];
  signature?: {
    type: {
      system: string;
      code: string;
    };
    when: string;
    who: {
      reference: string;
    };
    data: string;
  };
}

// FHIR Patient Interface
export interface FhirPatient extends FhirResource {
  resourceType: "Patient";
  identifier?: Array<{
    system?: string;
    value?: string;
    type?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
    };
  }>;
  active?: boolean;
  name?: Array<{
    use?:
      | "usual"
      | "official"
      | "temp"
      | "nickname"
      | "anonymous"
      | "old"
      | "maiden";
    text?: string;
    family?: string;
    given?: string[];
    prefix?: string[];
    suffix?: string[];
  }>;
  telecom?: Array<{
    system?: "phone" | "fax" | "email" | "pager" | "url" | "sms" | "other";
    value?: string;
    use?: "home" | "work" | "temp" | "old" | "mobile";
  }>;
  gender?: "male" | "female" | "other" | "unknown";
  birthDate?: string;
  address?: Array<{
    use?: "home" | "work" | "temp" | "old" | "billing";
    type?: "postal" | "physical" | "both";
    text?: string;
    line?: string[];
    city?: string;
    district?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  }>;
}

// FHIR Observation Interface
export interface FhirObservation extends FhirResource {
  resourceType: "Observation";
  status:
    | "registered"
    | "preliminary"
    | "final"
    | "amended"
    | "corrected"
    | "cancelled"
    | "entered-in-error"
    | "unknown";
  category?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
  }>;
  code: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  subject: {
    reference: string;
    display?: string;
  };
  encounter?: {
    reference: string;
  };
  effectiveDateTime?: string;
  effectivePeriod?: {
    start: string;
    end?: string;
  };
  issued?: string;
  performer?: Array<{
    reference: string;
    display?: string;
  }>;
  valueQuantity?: {
    value: number;
    unit: string;
    system?: string;
    code?: string;
  };
  valueString?: string;
  valueBoolean?: boolean;
  valueInteger?: number;
  valueRange?: {
    low?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    high?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
  };
  valueRatio?: {
    numerator?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    denominator?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
  };
  interpretation?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  note?: Array<{
    text: string;
  }>;
  bodySite?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  method?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  device?: {
    reference: string;
    display?: string;
  };
  referenceRange?: Array<{
    low?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    high?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    type?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    appliesTo?: Array<{
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    }>;
    age?: {
      low?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
      high?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
    };
    text?: string;
  }>;
  component?: Array<{
    code: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    valueQuantity?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    valueString?: string;
    valueBoolean?: boolean;
    valueInteger?: number;
    valueRange?: {
      low?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
      high?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
    };
    valueRatio?: {
      numerator?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
      denominator?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
    };
    interpretation?: Array<{
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    }>;
    referenceRange?: Array<{
      low?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
      high?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
      type?: {
        coding: Array<{
          system: string;
          code: string;
          display?: string;
        }>;
        text?: string;
      };
      appliesTo?: Array<{
        coding: Array<{
          system: string;
          code: string;
          display?: string;
        }>;
        text?: string;
      }>;
      age?: {
        low?: {
          value: number;
          unit: string;
          system?: string;
          code?: string;
        };
        high?: {
          value: number;
          unit: string;
          system?: string;
          code?: string;
        };
      };
      text?: string;
    }>;
  }>;
}

// FHIR MedicationRequest Interface
export interface FhirMedicationRequest extends FhirResource {
  resourceType: "MedicationRequest";
  status:
    | "active"
    | "on-hold"
    | "cancelled"
    | "completed"
    | "entered-in-error"
    | "stopped"
    | "draft"
    | "unknown";
  intent:
    | "proposal"
    | "plan"
    | "order"
    | "original-order"
    | "reflex-order"
    | "filler-order"
    | "instance-order"
    | "option";
  medicationCodeableConcept?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  medicationReference?: {
    reference: string;
    display?: string;
  };
  subject: {
    reference: string;
    display?: string;
  };
  encounter?: {
    reference: string;
  };
  authoredOn?: string;
  requester?: {
    reference: string;
    display?: string;
  };
  performer?: {
    reference: string;
    display?: string;
  };
  reasonCode?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  reasonReference?: Array<{
    reference: string;
    display?: string;
  }>;
  note?: Array<{
    text: string;
  }>;
  dosageInstruction?: Array<{
    text?: string;
    timing?: {
      code?: {
        coding: Array<{
          system: string;
          code: string;
          display?: string;
        }>;
        text?: string;
      };
      repeat?: {
        boundsDuration?: {
          value: number;
          unit: string;
          system?: string;
          code?: string;
        };
        boundsRange?: {
          low?: {
            value: number;
            unit: string;
            system?: string;
            code?: string;
          };
          high?: {
            value: number;
            unit: string;
            system?: string;
            code?: string;
          };
        };
        boundsPeriod?: {
          start: string;
          end?: string;
        };
        count?: number;
        countMax?: number;
        duration?: number;
        durationMax?: number;
        durationUnit?: "s" | "min" | "h" | "d" | "wk" | "mo" | "a";
        frequency?: number;
        frequencyMax?: number;
        period?: number;
        periodMax?: number;
        periodUnit?: "s" | "min" | "h" | "d" | "wk" | "mo" | "a";
        dayOfWeek?: Array<
          "mon" | "tue" | "wed" | "thu" | "fri" | "sat" | "sun"
        >;
        timeOfDay?: string[];
        when?: string[];
        offset?: number;
      };
    };
    asNeededBoolean?: boolean;
    asNeededCodeableConcept?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    site?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    route?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    method?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    doseAndRate?: Array<{
      type?: {
        coding: Array<{
          system: string;
          code: string;
          display?: string;
        }>;
        text?: string;
      };
      doseRange?: {
        low?: {
          value: number;
          unit: string;
          system?: string;
          code?: string;
        };
        high?: {
          value: number;
          unit: string;
          system?: string;
          code?: string;
        };
      };
      doseQuantity?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
      rateRatio?: {
        numerator?: {
          value: number;
          unit: string;
          system?: string;
          code?: string;
        };
        denominator?: {
          value: number;
          unit: string;
          system?: string;
          code?: string;
        };
      };
      rateRange?: {
        low?: {
          value: number;
          unit: string;
          system?: string;
          code?: string;
        };
        high?: {
          value: number;
          unit: string;
          system?: string;
          code?: string;
        };
      };
      rateQuantity?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
    }>;
  }>;
  dispenseRequest?: {
    initialFill?: {
      quantity?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
      duration?: {
        value: number;
        unit: string;
        system?: string;
        code?: string;
      };
    };
    dispenseInterval?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    validityPeriod?: {
      start: string;
      end?: string;
    };
    numberOfRepeatsAllowed?: number;
    quantity?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    expectedSupplyDuration?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    performer?: {
      reference: string;
      display?: string;
    };
  };
  substitution?: {
    allowedBoolean?: boolean;
    allowedCodeableConcept?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    reason?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
  };
  priorPrescription?: {
    reference: string;
    display?: string;
  };
  detectedIssue?: Array<{
    reference: string;
    display?: string;
  }>;
  eventHistory?: Array<{
    reference: string;
    display?: string;
  }>;
}

// FHIR Condition Interface
export interface FhirCondition extends FhirResource {
  resourceType: "Condition";
  identifier?: Array<{
    system?: string;
    value?: string;
    type?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
    };
  }>;
  clinicalStatus?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
  };
  verificationStatus?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
  };
  category?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
  }>;
  severity?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
  };
  code?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  bodySite?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
  }>;
  subject: {
    reference: string;
    display?: string;
  };
  encounter?: {
    reference: string;
  };
  onsetDateTime?: string;
  onsetAge?: {
    value: number;
    unit: string;
    system?: string;
    code?: string;
  };
  onsetPeriod?: {
    start: string;
    end?: string;
  };
  onsetRange?: {
    low?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    high?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
  };
  onsetString?: string;
  abatementDateTime?: string;
  abatementAge?: {
    value: number;
    unit: string;
    system?: string;
    code?: string;
  };
  abatementPeriod?: {
    start: string;
    end?: string;
  };
  abatementRange?: {
    low?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
    high?: {
      value: number;
      unit: string;
      system?: string;
      code?: string;
    };
  };
  abatementString?: string;
  recordedDate?: string;
  recorder?: {
    reference: string;
    display?: string;
  };
  asserter?: {
    reference: string;
    display?: string;
  };
  stage?: Array<{
    summary?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    assessment?: Array<{
      reference: string;
      display?: string;
    }>;
    type?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
  }>;
  evidence?: Array<{
    code?: Array<{
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    }>;
    detail?: Array<{
      reference: string;
      display?: string;
    }>;
  }>;
  note?: Array<{
    text: string;
  }>;
}

// FHIR DiagnosticReport Interface
export interface FhirDiagnosticReport extends FhirResource {
  resourceType: "DiagnosticReport";
  status:
    | "registered"
    | "partial"
    | "preliminary"
    | "final"
    | "amended"
    | "corrected"
    | "appended"
    | "cancelled"
    | "entered-in-error"
    | "unknown";
  category?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  code: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  subject: {
    reference: string;
    display?: string;
  };
  encounter?: {
    reference: string;
  };
  effectiveDateTime?: string;
  effectivePeriod?: {
    start: string;
    end?: string;
  };
  issued?: string;
  performer?: Array<{
    reference: string;
    display?: string;
  }>;
  resultsInterpreter?: Array<{
    reference: string;
    display?: string;
  }>;
  specimen?: Array<{
    reference: string;
    display?: string;
  }>;
  result?: Array<{
    reference: string;
    display?: string;
  }>;
  imagingStudy?: Array<{
    reference: string;
    display?: string;
  }>;
  media?: Array<{
    comment?: string;
    link: {
      reference: string;
      display?: string;
    };
  }>;
  conclusion?: string;
  conclusionCode?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  presentedForm?: Array<{
    contentType: string;
    language?: string;
    data?: string;
    url?: string;
    size?: number;
    hash?: string;
    title?: string;
    creation?: string;
  }>;
}

// FHIR Procedure Interface
export interface FhirProcedure extends FhirResource {
  resourceType: "Procedure";
  status:
    | "preparation"
    | "in-progress"
    | "not-done"
    | "on-hold"
    | "stopped"
    | "completed"
    | "entered-in-error"
    | "unknown";
  category?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  code: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  subject: {
    reference: string;
    display?: string;
  };
  encounter?: {
    reference: string;
  };
  performedDateTime?: string;
  performedPeriod?: {
    start: string;
    end?: string;
  };
  performer?: Array<{
    function?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    actor: {
      reference: string;
      display?: string;
    };
  }>;
  location?: {
    reference: string;
    display?: string;
  };
  reasonCode?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  bodySite?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  outcome?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  complication?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  followUp?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  note?: Array<{
    text: string;
  }>;
}

// FHIR AllergyIntolerance Interface
export interface FhirAllergyIntolerance extends FhirResource {
  resourceType: "AllergyIntolerance";
  clinicalStatus?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
  };
  verificationStatus?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
  };
  type?: "allergy" | "intolerance";
  category?: Array<"food" | "medication" | "environment" | "biologic">;
  criticality?: "low" | "high" | "unable-to-assess";
  code: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  patient: {
    reference: string;
    display?: string;
  };
  encounter?: {
    reference: string;
  };
  onsetDateTime?: string;
  recordedDate?: string;
  recorder?: {
    reference: string;
    display?: string;
  };
  asserter?: {
    reference: string;
    display?: string;
  };
  lastOccurrence?: string;
  note?: Array<{
    text: string;
  }>;
  reaction?: Array<{
    substance?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    manifestation: Array<{
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    }>;
    description?: string;
    onset?: string;
    severity?: "mild" | "moderate" | "severe";
    exposureRoute?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    note?: Array<{
      text: string;
    }>;
  }>;
}

// FHIR Immunization Interface
export interface FhirImmunization extends FhirResource {
  resourceType: "Immunization";
  status: "completed" | "entered-in-error" | "not-done";
  statusReason?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  vaccineCode: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  patient: {
    reference: string;
    display?: string;
  };
  encounter?: {
    reference: string;
  };
  occurrenceDateTime: string;
  recorded?: string;
  primarySource?: boolean;
  reportOrigin?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  location?: {
    reference: string;
    display?: string;
  };
  manufacturer?: {
    reference: string;
    display?: string;
  };
  lotNumber?: string;
  expirationDate?: string;
  site?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  route?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  doseQuantity?: {
    value: number;
    unit: string;
    system?: string;
    code?: string;
  };
  performer?: Array<{
    function?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    actor: {
      reference: string;
      display?: string;
    };
  }>;
  note?: Array<{
    text: string;
  }>;
  reasonCode?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  isSubpotent?: boolean;
  subpotentReason?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  education?: Array<{
    documentType?: string;
    reference?: string;
    publicationDate?: string;
    presentationDate?: string;
  }>;
  programEligibility?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  fundingSource?: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  reaction?: Array<{
    date?: string;
    detail?: {
      reference: string;
      display?: string;
    };
    reported?: boolean;
  }>;
  protocolApplied?: Array<{
    series?: string;
    authority?: {
      reference: string;
      display?: string;
    };
    targetDisease?: Array<{
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    }>;
    doseNumberPositiveInt?: number;
    doseNumberString?: string;
    seriesDosesPositiveInt?: number;
    seriesDosesString?: string;
  }>;
}

// FHIR DocumentReference Interface
export interface FhirDocumentReference extends FhirResource {
  resourceType: "DocumentReference";
  status: "current" | "superseded" | "entered-in-error";
  docStatus?: "preliminary" | "final" | "amended" | "entered-in-error";
  type: {
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  };
  category?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  subject: {
    reference: string;
    display?: string;
  };
  date: string;
  author?: Array<{
    reference: string;
    display?: string;
  }>;
  authenticator?: {
    reference: string;
    display?: string;
  };
  custodian?: {
    reference: string;
    display?: string;
  };
  relatesTo?: Array<{
    code: "replaces" | "transforms" | "signs" | "appends";
    target: {
      reference: string;
      display?: string;
    };
  }>;
  description?: string;
  securityLabel?: Array<{
    coding: Array<{
      system: string;
      code: string;
      display?: string;
    }>;
    text?: string;
  }>;
  content: Array<{
    attachment: {
      contentType?: string;
      language?: string;
      data?: string;
      url?: string;
      size?: number;
      hash?: string;
      title?: string;
      creation?: string;
    };
    format?: {
      system: string;
      code: string;
      display?: string;
    };
  }>;
  context?: {
    encounter?: Array<{
      reference: string;
      display?: string;
    }>;
    event?: Array<{
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    }>;
    period?: {
      start: string;
      end?: string;
    };
    facilityType?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    practiceSetting?: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
      text?: string;
    };
    sourcePatientInfo?: {
      reference: string;
      display?: string;
    };
    related?: Array<{
      reference: string;
      display?: string;
    }>;
  };
}

// ABDM Health Record Package Types
export interface AbdmHealthRecordPackage {
  pageNumber: number;
  pageCount: number;
  transactionId: string;
  entries: Array<{
    content: string; // Base64 encoded FHIR bundle
    media: string; // "application/fhir+json"
    checksum: string; // SHA-256 checksum of the content
    careContextReference: string; // Care context reference
  }>;
  keyMaterial: {
    cryptoAlg: string; // "ECDH"
    curve: string; // "Curve25519"
    dhPublicKey: {
      expiry: string; // ISO date string
      parameters: string; // "Curve25519/32byte random key"
      keyValue: string; // Base64 encoded public key
    };
    nonce: string; // Base64 encoded nonce
  };
}
