// SNOMED code mappings for bundle types
const BUNDLE_TYPE_MAPPINGS: Record<string, string> = {
  // SNOMED codes
  "440545006": "Prescription",
  "371530004": "OPConsult",
  "41000179103": "Immunization",
  "721981007": "DiagnosticReport",
  "373942005": "DischargeSummary",
  "371529009": "Wellness", // History and physical report
  "11429006": "Wellness", // Consultation note (alternative)

  // Custom NDHM codes
  InvoiceRecord: "Invoice",
};

// Display text mappings for fallback detection
const DISPLAY_TEXT_MAPPINGS: Record<string, string> = {
  prescription: "Prescription",
  "clinical consultation report": "OPConsult",
  "immunization record": "Immunization",
  "diagnostic studies report": "DiagnosticReport",
  "discharge summary": "DischargeSummary",
  "wellness record": "Wellness",
  "history and physical report": "Wellness",
  "consultation note": "OPConsult",
  "invoice record": "Invoice",

  // Additional common patterns
  "op consult": "OPConsult",
  "consultation": "OPConsult",
  "clinical": "OPConsult",
  "diagnostic": "DiagnosticReport",
  "lab": "DiagnosticReport",
  "discharge": "DischargeSummary",
  "wellness": "Wellness",
  "vital": "Wellness",
  "immunization": "Immunization",
  "vaccination": "Immunization",
  "invoice": "Invoice",
  "bill": "Invoice",
};

export function detectBundleType(bundle: any): string {
  if (!bundle?.entry) return "Unknown";

  // Find Composition resource
  const compositionEntry = bundle.entry.find(
    (entry: any) => entry.resource?.resourceType === "Composition",
  );

  if (!compositionEntry?.resource) return "Unknown";

  const composition = compositionEntry.resource;

  // Method 1: Check SNOMED codes in type.coding
  if (composition.type?.coding?.[0]) {
    const coding = composition.type.coding[0];
    if (coding.code && BUNDLE_TYPE_MAPPINGS[coding.code]) {
      return BUNDLE_TYPE_MAPPINGS[coding.code];
    }
  }

  // Method 2: Check display text
  if (composition.type?.coding?.[0]?.display) {
    const display = composition.type.coding[0].display.toLowerCase();
    for (const [key, value] of Object.entries(DISPLAY_TEXT_MAPPINGS)) {
      if (display.includes(key)) {
        return value;
      }
    }
  }

  // Method 3: Check type.text
  if (composition.type?.text) {
    const text = composition.type.text.toLowerCase();
    for (const [key, value] of Object.entries(DISPLAY_TEXT_MAPPINGS)) {
      if (text.includes(key)) {
        return value;
      }
    }
  }

  // Method 4: Check title
  if (composition.title) {
    const title = composition.title.toLowerCase();
    for (const [key, value] of Object.entries(DISPLAY_TEXT_MAPPINGS)) {
      if (title.includes(key)) {
        return value;
      }
    }
  }

  return "Unknown";
}
