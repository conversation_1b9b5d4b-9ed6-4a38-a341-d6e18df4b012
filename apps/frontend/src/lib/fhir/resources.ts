/**
 * FHIR Resource Creation Utilities
 *
 * This module provides functions for creating FHIR resources from application data.
 */

import { v4 as uuidv4 } from "uuid";

/**
 * Map database gender values to FHIR-compliant gender values
 * @param gender - Gender value from database (f, m, o, female, male, other, etc.)
 * @returns FHIR-compliant gender value (female, male, other, unknown)
 */
function mapGenderToFhir(gender?: string | null): string {
  if (!gender) return "unknown";

  const normalizedGender = gender.toLowerCase().trim();

  switch (normalizedGender) {
    case "f":
    case "female":
      return "female";
    case "m":
    case "male":
      return "male";
    case "o":
    case "other":
      return "other";
    default:
      return "unknown";
  }
}
import {
  FhirPatient,
  FhirObservation,
  FhirMedicationRequest,
  FhirCondition,
  FhirDiagnosticReport,
  FhirProcedure,
  FhirAllergyIntolerance,
  FhirImmunization,
  FhirDocumentReference,
} from "./types";
import {
  Vitals,
  Patient,
  Prescription,
  PrescriptionItem,
  ClinicalNote,
  Doctor,
  DiagnosticReport,
  Procedure,
  AllergyIntolerance,
  Immunization,
  DocumentReference,
} from "@prisma/client";

/**
 * Create a FHIR Patient resource from a Patient model
 * @param patient - Patient model
 * @returns FHIR Patient resource
 */
export function createPatientResource(patient: Patient): FhirPatient {
  const patientResource: FhirPatient = {
    resourceType: "Patient",
    id: uuidv4(),
    meta: {
      profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"],
    },
    identifier: [],
    active: patient.status === "active",
    name: [
      {
        use: "official",
        family: patient.lastName,
        given: [patient.firstName],
        text: `${patient.firstName} ${patient.lastName}`,
      },
    ],
    telecom: [],
    gender: mapGenderToFhir(patient.gender) as
      | "male"
      | "female"
      | "other"
      | "unknown",
    birthDate: patient.dateOfBirth.toISOString().split("T")[0],
  };

  // Add ABHA identifier if available
  const patientWithAbha = patient as any;
  if (patientWithAbha.abhaProfile && patientWithAbha.abhaProfile.abhaNumber) {
    patientResource.identifier?.push({
      system: "https://abdm.gov.in/id/abha",
      value: patientWithAbha.abhaProfile.abhaNumber,
      type: {
        coding: [
          {
            system: "http://terminology.hl7.org/CodeSystem/v2-0203",
            code: "MR",
            display: "ABHA Number",
          },
        ],
      },
    });
  }

  // Add contact information
  if (patient.phone) {
    patientResource.telecom?.push({
      system: "phone",
      value: patient.phone,
      use: "home",
    });
  }

  if (patient.alternatePhone) {
    patientResource.telecom?.push({
      system: "phone",
      value: patient.alternatePhone,
      use: "mobile",
    });
  }

  if (patient.email) {
    patientResource.telecom?.push({
      system: "email",
      value: patient.email,
      use: "home",
    });
  }

  // Add address if available
  if (patient.address || patient.city || patient.state || patient.pincode) {
    const address: any = {
      use: "home",
      type: "physical",
    };

    if (patient.address) {
      address.line = [patient.address];
    }

    if (patient.city) {
      address.city = patient.city;
    }

    if (patient.state) {
      address.state = patient.state;
    }

    if (patient.pincode) {
      address.postalCode = patient.pincode;
    }

    if (patient.country) {
      address.country = patient.country;
    }

    patientResource.address = [address];
  }

  return patientResource;
}

/**
 * Create a FHIR Observation resource from a Vitals model
 * @param vitals - Vitals model
 * @param patientId - FHIR Patient resource ID
 * @param practitionerId - FHIR Practitioner resource ID
 * @returns FHIR Observation resource
 */
export function createVitalsObservation(
  vitals: Vitals,
  patientId: string,
  practitionerId: string,
): FhirObservation[] {
  const observations: FhirObservation[] = [];
  const baseObservation: Partial<FhirObservation> = {
    resourceType: "Observation",
    status: "final",
    subject: {
      reference: `Patient/${patientId}`,
    },
    performer: [
      {
        reference: `Practitioner/${practitionerId}`,
      },
    ],
    effectiveDateTime: vitals.recordedAt.toISOString(),
    issued: vitals.createdAt.toISOString(),
  };

  // Blood Pressure
  if (
    vitals.bloodPressureSystolic !== null &&
    vitals.bloodPressureDiastolic !== null
  ) {
    const bpObservation: FhirObservation = {
      ...(baseObservation as FhirObservation),
      id: uuidv4(),
      category: [
        {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/observation-category",
              code: "vital-signs",
              display: "Vital Signs",
            },
          ],
        },
      ],
      code: {
        coding: [
          {
            system: "http://loinc.org",
            code: "85354-9",
            display: "Blood pressure panel with all children optional",
          },
        ],
        text: "Blood Pressure",
      },
      component: [
        {
          code: {
            coding: [
              {
                system: "http://loinc.org",
                code: "8480-6",
                display: "Systolic blood pressure",
              },
            ],
            text: "Systolic Blood Pressure",
          },
          valueQuantity: {
            value: vitals.bloodPressureSystolic,
            unit: "mmHg",
            system: "http://unitsofmeasure.org",
            code: "mm[Hg]",
          },
        },
        {
          code: {
            coding: [
              {
                system: "http://loinc.org",
                code: "8462-4",
                display: "Diastolic blood pressure",
              },
            ],
            text: "Diastolic Blood Pressure",
          },
          valueQuantity: {
            value: vitals.bloodPressureDiastolic,
            unit: "mmHg",
            system: "http://unitsofmeasure.org",
            code: "mm[Hg]",
          },
        },
      ],
    };
    observations.push(bpObservation);
  }

  // Pulse
  if (vitals.pulse !== null) {
    const pulseObservation: FhirObservation = {
      ...(baseObservation as FhirObservation),
      id: uuidv4(),
      category: [
        {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/observation-category",
              code: "vital-signs",
              display: "Vital Signs",
            },
          ],
        },
      ],
      code: {
        coding: [
          {
            system: "http://loinc.org",
            code: "8867-4",
            display: "Heart rate",
          },
        ],
        text: "Pulse",
      },
      valueQuantity: {
        value: vitals.pulse,
        unit: "beats/minute",
        system: "http://unitsofmeasure.org",
        code: "/min",
      },
    };
    observations.push(pulseObservation);
  }

  // Temperature
  if (vitals.temperature !== null) {
    const tempObservation: FhirObservation = {
      ...(baseObservation as FhirObservation),
      id: uuidv4(),
      category: [
        {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/observation-category",
              code: "vital-signs",
              display: "Vital Signs",
            },
          ],
        },
      ],
      code: {
        coding: [
          {
            system: "http://loinc.org",
            code: "8310-5",
            display: "Body temperature",
          },
        ],
        text: "Temperature",
      },
      valueQuantity: {
        value: parseFloat(vitals.temperature.toString()),
        unit: "°C",
        system: "http://unitsofmeasure.org",
        code: "Cel",
      },
    };
    observations.push(tempObservation);
  }

  // Respiratory Rate
  if (vitals.respiratoryRate !== null) {
    const rrObservation: FhirObservation = {
      ...(baseObservation as FhirObservation),
      id: uuidv4(),
      category: [
        {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/observation-category",
              code: "vital-signs",
              display: "Vital Signs",
            },
          ],
        },
      ],
      code: {
        coding: [
          {
            system: "http://loinc.org",
            code: "9279-1",
            display: "Respiratory rate",
          },
        ],
        text: "Respiratory Rate",
      },
      valueQuantity: {
        value: vitals.respiratoryRate,
        unit: "breaths/minute",
        system: "http://unitsofmeasure.org",
        code: "/min",
      },
    };
    observations.push(rrObservation);
  }

  // Oxygen Saturation
  if (vitals.oxygenSaturation !== null) {
    const o2Observation: FhirObservation = {
      ...(baseObservation as FhirObservation),
      id: uuidv4(),
      category: [
        {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/observation-category",
              code: "vital-signs",
              display: "Vital Signs",
            },
          ],
        },
      ],
      code: {
        coding: [
          {
            system: "http://loinc.org",
            code: "2708-6",
            display: "Oxygen saturation in Arterial blood",
          },
        ],
        text: "Oxygen Saturation",
      },
      valueQuantity: {
        value: vitals.oxygenSaturation,
        unit: "%",
        system: "http://unitsofmeasure.org",
        code: "%",
      },
    };
    observations.push(o2Observation);
  }

  // Height
  if (vitals.height !== null) {
    const heightObservation: FhirObservation = {
      ...(baseObservation as FhirObservation),
      id: uuidv4(),
      category: [
        {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/observation-category",
              code: "vital-signs",
              display: "Vital Signs",
            },
          ],
        },
      ],
      code: {
        coding: [
          {
            system: "http://loinc.org",
            code: "8302-2",
            display: "Body height",
          },
        ],
        text: "Height",
      },
      valueQuantity: {
        value: parseFloat(vitals.height.toString()),
        unit: "cm",
        system: "http://unitsofmeasure.org",
        code: "cm",
      },
    };
    observations.push(heightObservation);
  }

  // Weight
  if (vitals.weight !== null) {
    const weightObservation: FhirObservation = {
      ...(baseObservation as FhirObservation),
      id: uuidv4(),
      category: [
        {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/observation-category",
              code: "vital-signs",
              display: "Vital Signs",
            },
          ],
        },
      ],
      code: {
        coding: [
          {
            system: "http://loinc.org",
            code: "29463-7",
            display: "Body weight",
          },
        ],
        text: "Weight",
      },
      valueQuantity: {
        value: parseFloat(vitals.weight.toString()),
        unit: "kg",
        system: "http://unitsofmeasure.org",
        code: "kg",
      },
    };
    observations.push(weightObservation);
  }

  // BMI
  if (vitals.bmi !== null) {
    const bmiObservation: FhirObservation = {
      ...(baseObservation as FhirObservation),
      id: uuidv4(),
      category: [
        {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/observation-category",
              code: "vital-signs",
              display: "Vital Signs",
            },
          ],
        },
      ],
      code: {
        coding: [
          {
            system: "http://loinc.org",
            code: "39156-5",
            display: "Body mass index (BMI) [Ratio]",
          },
        ],
        text: "BMI",
      },
      valueQuantity: {
        value: parseFloat(vitals.bmi.toString()),
        unit: "kg/m2",
        system: "http://unitsofmeasure.org",
        code: "kg/m2",
      },
    };
    observations.push(bmiObservation);
  }

  return observations;
}

/**
 * Create a FHIR MedicationRequest resource from a Prescription model
 * @param prescription - Prescription model
 * @param prescriptionItems - PrescriptionItem models
 * @param patientId - FHIR Patient resource ID
 * @param practitionerId - FHIR Practitioner resource ID
 * @returns FHIR MedicationRequest resources
 */
export function createMedicationRequests(
  prescription: Prescription,
  prescriptionItems: PrescriptionItem[],
  patientId: string,
  practitionerId: string,
): FhirMedicationRequest[] {
  return prescriptionItems.map((item) => {
    const medicationRequest: FhirMedicationRequest = {
      resourceType: "MedicationRequest",
      id: uuidv4(),
      status: prescription.status === "active" ? "active" : "completed",
      intent: "order",
      medicationCodeableConcept: {
        coding: [],
        text: item.medicationName,
      },
      subject: {
        reference: `Patient/${patientId}`,
      },
      authoredOn: prescription.prescriptionDate.toISOString(),
      requester: {
        reference: `Practitioner/${practitionerId}`,
      },
      dosageInstruction: [
        {
          text: `${item.dosage}, ${item.frequency}, for ${item.duration}${
            item.route ? ` via ${item.route}` : ""
          }${item.instructions ? `. ${item.instructions}` : ""}`,
        },
      ],
      note: item.instructions ? [{ text: item.instructions }] : undefined,
    };

    // Add SNOMED CT code if available
    if (item.snomedCode) {
      medicationRequest.medicationCodeableConcept?.coding.push({
        system: "http://snomed.info/sct",
        code: item.snomedCode,
        display: item.medicationName,
      });
    }

    // Add RxNorm code if available
    if (item.rxcui) {
      medicationRequest.medicationCodeableConcept?.coding.push({
        system: "http://www.nlm.nih.gov/research/umls/rxnorm",
        code: item.rxcui,
        display: item.medicationName,
      });
    }

    return medicationRequest;
  });
}

/**
 * Create a FHIR Condition resource from a ClinicalNote model
 * @param clinicalNote - ClinicalNote model
 * @param patientId - FHIR Patient resource ID
 * @param practitionerId - FHIR Practitioner resource ID
 * @returns FHIR Condition resource
 */
export function createCondition(
  clinicalNote: ClinicalNote,
  patientId: string,
  practitionerId: string,
): FhirCondition | null {
  // Only create condition if SNOMED diagnoses are available
  if (!clinicalNote.snomedDiagnoses) {
    return null;
  }

  try {
    const diagnoses = JSON.parse(clinicalNote.snomedDiagnoses);
    if (!diagnoses || !diagnoses.length) {
      return null;
    }

    const primaryDiagnosis = diagnoses[0];

    const condition: FhirCondition = {
      resourceType: "Condition",
      id: uuidv4(),
      clinicalStatus: {
        coding: [
          {
            system: "http://terminology.hl7.org/CodeSystem/condition-clinical",
            code: "active",
            display: "Active",
          },
        ],
      },
      verificationStatus: {
        coding: [
          {
            system:
              "http://terminology.hl7.org/CodeSystem/condition-ver-status",
            code: "confirmed",
            display: "Confirmed",
          },
        ],
      },
      category: [
        {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/condition-category",
              code: "encounter-diagnosis",
              display: "Encounter Diagnosis",
            },
          ],
        },
      ],
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: primaryDiagnosis.code,
            display: primaryDiagnosis.term,
          },
        ],
        text: primaryDiagnosis.term,
      },
      subject: {
        reference: `Patient/${patientId}`,
      },
      recordedDate: clinicalNote.createdAt.toISOString(),
      recorder: {
        reference: `Practitioner/${practitionerId}`,
      },
      note: [
        {
          text: clinicalNote.content,
        },
      ],
    };

    return condition;
  } catch (error) {
    console.error("Error parsing SNOMED diagnoses:", error);
    return null;
  }
}

/**
 * Create a FHIR DiagnosticReport resource from a DiagnosticReport model
 * @param report - DiagnosticReport model
 * @param patientId - FHIR Patient resource ID
 * @param practitionerId - FHIR Practitioner resource ID
 * @returns FHIR DiagnosticReport resource
 */
export function createDiagnosticReportResource(
  report: DiagnosticReport,
  patientId: string,
  practitionerId: string,
): FhirDiagnosticReport {
  return {
    resourceType: "DiagnosticReport",
    id: report.id,
    status: report.status as any,
    category: report.category
      ? [
          {
            coding: [
              {
                system: "http://terminology.hl7.org/CodeSystem/v2-0074",
                code: report.category,
                display: report.category,
              },
            ],
          },
        ]
      : undefined,
    code: {
      coding: [
        {
          system: "http://loinc.org",
          code: report.code,
          display: report.codeDisplay,
        },
      ],
    },
    subject: {
      reference: `Patient/${patientId}`,
      display: `Patient ${patientId}`,
    },
    performer: [
      {
        reference: `Practitioner/${practitionerId}`,
        display: `Dr. ${practitionerId}`,
      },
    ],
    effectiveDateTime: report.effectiveDate.toISOString(),
    issued: report.issuedDate.toISOString(),
    conclusion: report.conclusion || undefined,
    presentedForm: report.presentedForm as any,
    result: report.result as any,
  };
}

/**
 * Create a FHIR Procedure resource from a Procedure model
 * @param procedure - Procedure model
 * @param patientId - FHIR Patient resource ID
 * @param practitionerId - FHIR Practitioner resource ID
 * @returns FHIR Procedure resource
 */
export function createProcedureResource(
  procedure: Procedure,
  patientId: string,
  practitionerId: string,
): FhirProcedure {
  return {
    resourceType: "Procedure",
    id: procedure.id,
    status: procedure.status as any,
    category: procedure.category
      ? {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: procedure.category,
              display: procedure.category,
            },
          ],
        }
      : undefined,
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: procedure.code,
          display: procedure.codeDisplay,
        },
      ],
      text: procedure.codeDisplay,
    },
    subject: {
      reference: `Patient/${patientId}`,
      display: `Patient ${patientId}`,
    },
    encounter: {
      reference: `Encounter/${procedure.consultationId}`,
    },
    performedDateTime: procedure.procedureDate.toISOString(),
    performer: [
      {
        actor: {
          reference: `Practitioner/${practitionerId}`,
          display: `Dr. ${practitionerId}`,
        },
      },
    ],
    location: procedure.location
      ? {
          reference: `Location/${procedure.location}`,
          display: procedure.location,
        }
      : undefined,
    reasonCode: procedure.reasonCode
      ? [
          {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: procedure.reasonCode,
                display: procedure.reasonDisplay || procedure.reasonCode,
              },
            ],
            text: procedure.reasonDisplay || undefined,
          },
        ]
      : undefined,
    bodySite: procedure.bodySite
      ? [
          {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: procedure.bodySite,
                display: procedure.bodySite,
              },
            ],
            text: procedure.bodySite,
          },
        ]
      : undefined,
    outcome: procedure.outcome
      ? {
          coding: [
            {
              system: "http://snomed.info/sct",
              code: procedure.outcome,
              display: procedure.outcome,
            },
          ],
          text: procedure.outcome,
        }
      : undefined,
    complication: procedure.complication
      ? [
          {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: procedure.complication,
                display: procedure.complication,
              },
            ],
            text: procedure.complication,
          },
        ]
      : undefined,
    followUp: procedure.followUp
      ? [
          {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: procedure.followUp,
                display: procedure.followUp,
              },
            ],
            text: procedure.followUp,
          },
        ]
      : undefined,
    note: procedure.notes
      ? [
          {
            text: procedure.notes,
          },
        ]
      : undefined,
  };
}

/**
 * Create a FHIR AllergyIntolerance resource from an AllergyIntolerance model
 * @param allergy - AllergyIntolerance model
 * @param patientId - FHIR Patient resource ID
 * @param practitionerId - FHIR Practitioner resource ID
 * @returns FHIR AllergyIntolerance resource
 */
export function createAllergyIntoleranceResource(
  allergy: AllergyIntolerance,
  patientId: string,
  practitionerId: string,
): FhirAllergyIntolerance {
  return {
    resourceType: "AllergyIntolerance",
    id: allergy.id,
    clinicalStatus: {
      coding: [
        {
          system:
            "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical",
          code: allergy.clinicalStatus,
          display: allergy.clinicalStatus,
        },
      ],
    },
    verificationStatus: {
      coding: [
        {
          system:
            "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification",
          code: allergy.verificationStatus,
          display: allergy.verificationStatus,
        },
      ],
    },
    type: allergy.type as "allergy" | "intolerance",
    category: [
      allergy.category as "food" | "medication" | "environment" | "biologic",
    ],
    criticality: allergy.criticality as "low" | "high" | "unable-to-assess",
    code: {
      coding: [
        {
          system: "http://snomed.info/sct",
          code: allergy.code,
          display: allergy.codeDisplay,
        },
      ],
      text: allergy.codeDisplay,
    },
    patient: {
      reference: `Patient/${patientId}`,
      display: `Patient ${patientId}`,
    },
    encounter: allergy.consultationId
      ? {
          reference: `Encounter/${allergy.consultationId}`,
        }
      : undefined,
    onsetDateTime: allergy.onsetDateTime?.toISOString(),
    recordedDate: allergy.recordedDate.toISOString(),
    recorder: {
      reference: `Practitioner/${practitionerId}`,
      display: `Dr. ${practitionerId}`,
    },
    asserter: allergy.asserter
      ? {
          reference: `Practitioner/${practitionerId}`,
          display: allergy.asserter,
        }
      : undefined,
    lastOccurrence: allergy.lastOccurrence?.toISOString(),
    note: allergy.note
      ? [
          {
            text: allergy.note,
          },
        ]
      : undefined,
    reaction: allergy.reaction as any,
  };
}

/**
 * Create a FHIR Immunization resource from an Immunization model
 * @param immunization - Immunization model
 * @param patientId - FHIR Patient resource ID
 * @param practitionerId - FHIR Practitioner resource ID
 * @returns FHIR Immunization resource
 */
export function createImmunizationResource(
  immunization: Immunization,
  patientId: string,
  practitionerId: string,
): FhirImmunization {
  return {
    resourceType: "Immunization",
    id: immunization.id,
    status: immunization.status as
      | "completed"
      | "entered-in-error"
      | "not-done",
    statusReason: immunization.statusReason
      ? {
          coding: [
            {
              system: "http://terminology.hl7.org/CodeSystem/v3-ActReason",
              code: immunization.statusReason,
              display: immunization.statusReason,
            },
          ],
          text: immunization.statusReason,
        }
      : undefined,
    vaccineCode: {
      coding: [
        {
          system: "http://hl7.org/fhir/sid/cvx",
          code: immunization.vaccineCode,
          display: immunization.vaccineDisplay,
        },
      ],
      text: immunization.vaccineDisplay,
    },
    patient: {
      reference: `Patient/${patientId}`,
      display: `Patient ${patientId}`,
    },
    encounter: immunization.consultationId
      ? {
          reference: `Encounter/${immunization.consultationId}`,
        }
      : undefined,
    occurrenceDateTime: immunization.occurrenceDateTime.toISOString(),
    recorded: immunization.recorded.toISOString(),
    primarySource: immunization.primarySource,
    reportOrigin: immunization.reportOrigin
      ? {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/immunization-origin",
              code: immunization.reportOrigin,
              display: immunization.reportOrigin,
            },
          ],
          text: immunization.reportOrigin,
        }
      : undefined,
    location: immunization.location
      ? {
          reference: `Location/${immunization.location}`,
          display: immunization.location,
        }
      : undefined,
    manufacturer: immunization.manufacturer
      ? {
          reference: `Organization/${immunization.manufacturer}`,
          display: immunization.manufacturer,
        }
      : undefined,
    lotNumber: immunization.lotNumber || undefined,
    expirationDate: immunization.expirationDate?.toISOString(),
    site: immunization.site
      ? {
          coding: [
            {
              system: "http://terminology.hl7.org/CodeSystem/v3-ActSite",
              code: immunization.site,
              display: immunization.site,
            },
          ],
          text: immunization.site,
        }
      : undefined,
    route: immunization.route
      ? {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/v3-RouteOfAdministration",
              code: immunization.route,
              display: immunization.route,
            },
          ],
          text: immunization.route,
        }
      : undefined,
    doseQuantity: immunization.doseQuantity
      ? {
          value: parseFloat(immunization.doseQuantity),
          unit: "dose",
          system: "http://unitsofmeasure.org",
          code: "dose",
        }
      : undefined,
    performer: immunization.performer
      ? [
          {
            actor: {
              reference: `Practitioner/${practitionerId}`,
              display: immunization.performer,
            },
          },
        ]
      : undefined,
    note: immunization.note
      ? [
          {
            text: immunization.note,
          },
        ]
      : undefined,
    reasonCode: immunization.reasonCode
      ? [
          {
            coding: [
              {
                system: "http://snomed.info/sct",
                code: immunization.reasonCode,
                display: immunization.reasonDisplay || immunization.reasonCode,
              },
            ],
            text: immunization.reasonDisplay || undefined,
          },
        ]
      : undefined,
    isSubpotent: immunization.isSubpotent,
    subpotentReason: immunization.subpotentReason
      ? [
          {
            coding: [
              {
                system:
                  "http://terminology.hl7.org/CodeSystem/immunization-subpotent-reason",
                code: immunization.subpotentReason,
                display: immunization.subpotentReason,
              },
            ],
            text: immunization.subpotentReason,
          },
        ]
      : undefined,
    programEligibility: immunization.programEligibility
      ? [
          {
            coding: [
              {
                system:
                  "http://terminology.hl7.org/CodeSystem/immunization-program-eligibility",
                code: immunization.programEligibility,
                display: immunization.programEligibility,
              },
            ],
            text: immunization.programEligibility,
          },
        ]
      : undefined,
    fundingSource: immunization.fundingSource
      ? {
          coding: [
            {
              system:
                "http://terminology.hl7.org/CodeSystem/immunization-funding-source",
              code: immunization.fundingSource,
              display: immunization.fundingSource,
            },
          ],
          text: immunization.fundingSource,
        }
      : undefined,
    reaction: immunization.reaction as any,
    protocolApplied: immunization.protocolApplied as any,
  };
}

/**
 * Create a FHIR DocumentReference resource from a DocumentReference model
 * @param document - DocumentReference model
 * @param patientId - FHIR Patient resource ID
 * @param practitionerId - FHIR Practitioner resource ID
 * @returns FHIR DocumentReference resource
 */
export function createDocumentReferenceResource(
  document: DocumentReference,
  patientId: string,
  practitionerId: string,
): FhirDocumentReference {
  return {
    resourceType: "DocumentReference",
    id: document.id,
    status: document.status as "current" | "superseded" | "entered-in-error",
    docStatus: document.docStatus as
      | "preliminary"
      | "final"
      | "amended"
      | "entered-in-error",
    type: {
      coding: [
        {
          system: "http://loinc.org",
          code: document.type,
          display: document.typeDisplay,
        },
      ],
      text: document.typeDisplay,
    },
    category: [
      {
        coding: [
          {
            system: "http://loinc.org",
            code: document.category,
            display: document.categoryDisplay,
          },
        ],
        text: document.categoryDisplay,
      },
    ],
    subject: {
      reference: `Patient/${patientId}`,
      display: `Patient ${patientId}`,
    },
    date: document.date.toISOString(),
    author: [
      {
        reference: `Practitioner/${practitionerId}`,
        display: document.author || `Dr. ${practitionerId}`,
      },
    ],
    authenticator: document.authenticator
      ? {
          reference: `Practitioner/${practitionerId}`,
          display: document.authenticator,
        }
      : undefined,
    custodian: document.custodian
      ? {
          reference: `Organization/${document.organizationId}`,
          display: document.custodian,
        }
      : undefined,
    description: document.description || undefined,
    securityLabel: document.securityLabel
      ? [
          {
            coding: [
              {
                system:
                  "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
                code: document.securityLabel,
                display: document.securityLabel,
              },
            ],
            text: document.securityLabel,
          },
        ]
      : undefined,
    content: document.content as any,
    context: document.context as any,
  };
}

export function createPractitionerResource(
  doctor: Doctor & { user: { name: string | null; email: string | null } },
): any {
  return {
    resourceType: "Practitioner",
    id: uuidv4(),
    identifier: [
      {
        system: "https://aran.care/doctors",
        value: doctor.id,
      },
    ],
    active: doctor.status === "active",
    name: [
      {
        use: "official",
        text: doctor.user.name || "Unknown",
      },
    ],
    telecom: [
      ...(doctor.contactPhone
        ? [
            {
              system: "phone",
              value: doctor.contactPhone,
              use: "work",
            },
          ]
        : []),
      ...(doctor.contactEmail || doctor.user.email
        ? [
            {
              system: "email",
              value: doctor.contactEmail || doctor.user.email,
              use: "work",
            },
          ]
        : []),
    ],
    qualification: [
      {
        code: {
          text: doctor.qualification || "Unknown",
        },
      },
    ],
  };
}
