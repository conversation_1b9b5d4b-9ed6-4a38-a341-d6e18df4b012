import { detectBundleType } from "./bundle-type-detector";
import { getResourceConfig, ResourceTypeConfig } from "./resource-config";

export interface ParsedFhirData {
  bundleType: string;
  bundleId: string;
  timestamp: string;
  totalEntries: number;
  composition: any;
  patient: any;
  practitioner: any;
  organization: any;
  resourceGroups: {
    [resourceType: string]: {
      resources: any[];
      count: number;
      config: ResourceTypeConfig;
    };
  };
  documents: {
    documentReferences: any[];
    binaries: any[];
    totalDocuments: number;
  };
  summary: {
    coreResources: number;
    clinicalResources: number;
    administrativeResources: number;
    documentResources: number;
  };
}

export function parseAllBundleEntries(bundle: any): ParsedFhirData {
  if (!bundle || !bundle.entry) {
    throw new Error("Invalid bundle structure");
  }

  const bundleType = detectBundleType(bundle);
  const resourceGroups: { [key: string]: any[] } = {};
  const documents = {
    documentReferences: [] as any[],
    binaries: [] as any[],
    totalDocuments: 0,
  };

  let composition: any = null;
  let patient: any = null;
  let practitioner: any = null;
  let organization: any = null;

  // Parse all entries
  bundle.entry.forEach((entry: any) => {
    const resource = entry.resource;
    if (!resource || !resource.resourceType) return;

    const resourceType = resource.resourceType;

    // Store special resources separately
    switch (resourceType) {
      case "Composition":
        composition = resource;
        break;
      case "Patient":
        patient = resource;
        break;
      case "Practitioner":
        practitioner = practitioner || resource; // Take first practitioner
        break;
      case "Organization":
        organization = organization || resource; // Take first organization
        break;
      case "DocumentReference":
        documents.documentReferences.push(resource);
        break;
      case "Binary":
        documents.binaries.push(resource);
        break;
    }

    // Group all resources by type
    if (!resourceGroups[resourceType]) {
      resourceGroups[resourceType] = [];
    }
    resourceGroups[resourceType].push(resource);
  });

  // Calculate document totals
  documents.totalDocuments =
    documents.documentReferences.length + documents.binaries.length;

  // Transform resource groups with config
  const transformedResourceGroups: { [key: string]: any } = {};
  Object.entries(resourceGroups).forEach(([resourceType, resources]) => {
    transformedResourceGroups[resourceType] = {
      resources,
      count: resources.length,
      config: getResourceConfig(resourceType),
    };
  });

  // Calculate summary
  const summary = {
    coreResources: 0,
    clinicalResources: 0,
    administrativeResources: 0,
    documentResources: 0,
  };

  Object.values(transformedResourceGroups).forEach((group: any) => {
    switch (group.config.category) {
      case "core":
        summary.coreResources += group.count;
        break;
      case "clinical":
        summary.clinicalResources += group.count;
        break;
      case "administrative":
        summary.administrativeResources += group.count;
        break;
      case "document":
        summary.documentResources += group.count;
        break;
    }
  });

  return {
    bundleType,
    bundleId: bundle.id || "Unknown",
    timestamp: bundle.timestamp || bundle.meta?.lastUpdated || "Unknown",
    totalEntries: bundle.entry.length,
    composition,
    patient,
    practitioner,
    organization,
    resourceGroups: transformedResourceGroups,
    documents,
    summary,
  };
}
