/**
 * FHIR Bundle Creation Utilities
 *
 * This module provides functions for creating FHIR bundles from FHIR resources.
 */

import { v4 as uuidv4 } from "uuid";
import { FhirBundle, FhirResource, FhirBundleType } from "./types";

/**
 * Create a FHIR Bundle from a list of FHIR resources
 * @param resources - List of FHIR resources to include in the bundle
 * @param bundleType - Type of bundle to create
 * @returns FHIR Bundle
 */
export function createBundle(
  resources: FhirResource[],
  bundleType: FhirBundleType = "collection",
): FhirBundle {
  const bundleId = uuidv4();
  const timestamp = new Date().toISOString();

  const bundle: FhirBundle = {
    resourceType: "Bundle",
    id: bundleId,
    meta: {
      lastUpdated: timestamp,
      profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Bundle"],
    },
    type: bundleType,
    entry: resources.map((resource) => ({
      fullUrl: `urn:uuid:${resource.id}`,
      resource: resource,
    })),
  };

  return bundle;
}

/**
 * Create a FHIR Document Bundle from a list of FHIR resources
 * @param resources - List of FHIR resources to include in the bundle
 * @param title - Title of the document
 * @param patientId - ID of the patient resource
 * @param authorId - ID of the author resource
 * @returns FHIR Bundle of type 'document'
 */
export function createDocumentBundle(
  resources: FhirResource[],
  title: string,
  patientId: string,
  authorId: string,
): FhirBundle {
  // Find the patient and author resources
  const patientResource = resources.find((r) => r.id === patientId);
  const authorResource = resources.find((r) => r.id === authorId);

  if (!patientResource) {
    throw new Error("Patient resource not found in resources list");
  }

  if (!authorResource) {
    throw new Error("Author resource not found in resources list");
  }

  // Create a Composition resource
  const compositionId = uuidv4();
  const timestamp = new Date().toISOString();

  const composition: FhirResource = {
    resourceType: "Composition",
    id: compositionId,
    status: "final",
    type: {
      coding: [
        {
          system: "http://loinc.org",
          code: "34133-9",
          display: "Summary of episode note",
        },
      ],
      text: title, // Use the passed title instead of hardcoded "Clinical Summary"
    },
    subject: {
      reference: `urn:uuid:${patientId}`,
    },
    date: timestamp,
    author: [
      {
        reference: `urn:uuid:${authorId}`,
      },
    ],
    title: title,
    section: [
      {
        title: "Health Records",
        code: {
          coding: [
            {
              system: "http://loinc.org",
              code: "8716-3",
              display: "Vital signs",
            },
          ],
        },
        entry: resources
          .filter(
            (r) =>
              r.id !== patientId && r.id !== authorId && r.id !== compositionId,
          )
          .map((r) => ({ reference: `urn:uuid:${r.id}` })),
      },
    ],
  } as any;

  // Create the bundle with the Composition as the first resource
  const allResources = [composition, ...resources];

  return createBundle(allResources, "document");
}

/**
 * Serialize a FHIR Bundle to JSON
 * @param bundle - FHIR Bundle to serialize
 * @returns JSON string representation of the bundle
 */
export function serializeBundle(bundle: FhirBundle): string {
  return JSON.stringify(bundle);
}

/**
 * Deserialize a JSON string to a FHIR Bundle
 * @param json - JSON string representation of a FHIR Bundle
 * @returns FHIR Bundle
 */
export function deserializeBundle(json: string): FhirBundle {
  return JSON.parse(json) as FhirBundle;
}
