/**
 * Utility functions for displaying FHIR bundle types with proper naming
 */

// Function to map internal bundle types to proper display names
export const getBundleTypeDisplayName = (bundleType: string): string => {
  const bundleTypeMap: Record<string, string> = {
    // Current internal names to proper display names
    wellness: "WellnessRecord",
    prescription: "Prescription",
    opconsult: "OPConsultation",
    invoice: "Invoice",
    immunization: "ImmunizationRecord",
    document: "HealthDocumentRecord",
    dischargesummary: "DischargeSummary",
    diagnosticreport: "DiagnosticReport",

    // Handle variations and case differences
    Wellness: "WellnessRecord",
    Prescription: "Prescription",
    OPConsult: "OPConsultation",
    Invoice: "Invoice",
    Immunization: "ImmunizationRecord",
    Document: "HealthDocumentRecord",
    DischargeSummary: "DischargeSummary",
    DiagnosticReport: "DiagnosticReport",

    // Handle other possible variations
    HealthDocumentRecord: "HealthDocumentRecord",
    WellnessRecord: "WellnessRecord",
    ImmunizationRecord: "ImmunizationRecord",
    OPConsultation: "OPConsultation",

    // Handle additional variations that might come from the system
    "OP Consult": "OPConsultation",
    "Wellness Record": "WellnessRecord",
    "Health Document": "HealthDocumentRecord",
    "Immunization Record": "ImmunizationRecord",
    "Discharge Summary": "DischargeSummary",
    "Diagnostic Report": "DiagnosticReport",

    // Handle unknown/fallback cases
    Unknown: "HealthDocumentRecord",
  };

  return bundleTypeMap[bundleType] || bundleType;
};

// Bundle type to icon mapping for consistent display
export const BUNDLE_TYPE_ICONS = {
  Prescription: "Pill",
  OPConsultation: "Stethoscope",
  ImmunizationRecord: "Shield",
  DiagnosticReport: "TestTube",
  DischargeSummary: "FileText",
  WellnessRecord: "Heart",
  Invoice: "Receipt",
  HealthDocumentRecord: "FileImage",
  Unknown: "FileImage",
} as const;

// Bundle type to color mapping for consistent styling
export const BUNDLE_TYPE_COLORS = {
  Prescription: "bg-purple-100 text-purple-800 border-purple-200",
  OPConsultation: "bg-blue-100 text-blue-800 border-blue-200",
  ImmunizationRecord: "bg-teal-100 text-teal-800 border-teal-200",
  DiagnosticReport: "bg-red-100 text-red-800 border-red-200",
  DischargeSummary: "bg-gray-100 text-gray-800 border-gray-200",
  WellnessRecord: "bg-green-100 text-green-800 border-green-200",
  Invoice: "bg-yellow-100 text-yellow-800 border-yellow-200",
  HealthDocumentRecord: "bg-indigo-100 text-indigo-800 border-indigo-200",
  Unknown: "bg-gray-100 text-gray-800 border-gray-200",
} as const;
