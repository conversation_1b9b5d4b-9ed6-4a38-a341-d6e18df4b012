import { v4 as uuidv4 } from "uuid";

export interface FhirBinary {
  resourceType: "Binary";
  id: string;
  meta: {
    profile: string[];
  };
  contentType: string;
  data: string;
}

export interface DocumentAttachment {
  contentType: string;
  language: string;
  data: string;
  title: string;
  creation: string;
}

export interface FhirDocumentReference {
  resourceType: "DocumentReference";
  id: string;
  meta: {
    profile: string[];
  };
  status: "current";
  docStatus: "final";
  type: {
    text: string;
  };
  subject: {
    reference: string;
    display: string;
  };
  content: Array<{
    attachment: DocumentAttachment;
  }>;
}

/**
 * Convert PDF buffer to base64 string for FHIR Binary resource
 */
export function convertPdfToBase64(pdfBuffer: Buffer): string {
  return pdfBuffer.toString("base64");
}

/**
 * Create FHIR Binary resource following ABDM/NDHM standards
 */
export function createFhirBinary(
  pdfBuffer: Buffer,
  contentType: string = "application/pdf",
): FhirBinary {
  return {
    resourceType: "Binary",
    id: uuidv4(),
    meta: {
      profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Binary"],
    },
    contentType,
    data: convertPdfToBase64(pdfBuffer),
  };
}

/**
 * Create FHIR DocumentReference with embedded PDF attachment
 */
export function createFhirDocumentReference(
  pdfBuffer: Buffer,
  title: string,
  patientId: string,
  patientName: string,
  contentType: string = "application/pdf",
): FhirDocumentReference {
  return {
    resourceType: "DocumentReference",
    id: uuidv4(),
    meta: {
      profile: [
        "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference",
      ],
    },
    status: "current",
    docStatus: "final",
    type: {
      text: title,
    },
    subject: {
      reference: `urn:uuid:${patientId}`,
      display: patientName,
    },
    content: [
      {
        attachment: {
          contentType,
          language: "en-IN",
          data: convertPdfToBase64(pdfBuffer),
          title,
          creation: new Date().toISOString(),
        },
      },
    ],
  };
}

/**
 * Add Binary resource to FHIR bundle
 */
export function addBinaryToBundle(
  bundle: any,
  binaryResource: FhirBinary,
): void {
  // Add to bundle entries
  bundle.entry.push({
    fullUrl: `urn:uuid:${binaryResource.id}`,
    resource: binaryResource,
  });

  // Reference in Composition section if it exists
  if (bundle.entry[0]?.resource?.section) {
    bundle.entry[0].resource.section[0].entry.push({
      reference: `urn:uuid:${binaryResource.id}`,
      type: "Binary",
    });
  }
}

/**
 * Add DocumentReference to FHIR bundle
 */
export function addDocumentReferenceToBundle(
  bundle: any,
  documentReference: FhirDocumentReference,
): void {
  // Add to bundle entries
  bundle.entry.push({
    fullUrl: `urn:uuid:${documentReference.id}`,
    resource: documentReference,
  });

  // Reference in Composition section if it exists
  if (bundle.entry[0]?.resource?.section) {
    bundle.entry[0].resource.section[0].entry.push({
      reference: `urn:uuid:${documentReference.id}`,
      type: "DocumentReference",
    });
  }
}

/**
 * Validate PDF buffer
 */
export function validatePdfBuffer(buffer: Buffer): boolean {
  if (!buffer || buffer.length === 0) {
    return false;
  }

  // Check PDF magic number
  const pdfHeader = buffer.subarray(0, 4).toString();
  return pdfHeader === "%PDF";
}

/**
 * Get file size in human readable format
 */
export function getFileSizeString(buffer: Buffer): string {
  const bytes = buffer.length;
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}
