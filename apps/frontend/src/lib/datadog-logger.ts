/**
 * Datadog Logger for Production Application
 * Provides structured logging for monitoring and debugging
 */

interface DatadogLogEntry {
  level: "info" | "warn" | "error" | "debug";
  message: string;
  service: string;
  timestamp: string;
  environment: string;
  tags?: Record<string, string>;
  attributes?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

class DatadogLogger {
  private service: string;
  private environment: string;

  constructor() {
    this.service = process.env.NEXT_PUBLIC_DATADOG_SERVICE || "aran-care";
    this.environment = process.env.NEXT_PUBLIC_ENVIRONMENT || "development";
  }

  private createLogEntry(
    level: DatadogLogEntry["level"],
    message: string,
  ): DatadogLogEntry {
    return {
      level,
      message,
      service: this.service,
      environment: this.environment,
      timestamp: new Date().toISOString(),
    };
  }

  info(
    message: string,
    attributes?: Record<string, any>,
    tags?: Record<string, string>,
  ): void {
    const entry = this.createLogEntry("info", message);
    if (attributes) entry.attributes = attributes;
    if (tags) entry.tags = tags;
    this.log(entry);
  }

  warn(
    message: string,
    attributes?: Record<string, any>,
    tags?: Record<string, string>,
  ): void {
    const entry = this.createLogEntry("warn", message);
    if (attributes) entry.attributes = attributes;
    if (tags) entry.tags = tags;
    this.log(entry);
  }

  error(
    message: string,
    error?: Error,
    attributes?: Record<string, any>,
    tags?: Record<string, string>,
  ): void {
    const entry = this.createLogEntry("error", message);
    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
      };
    }
    if (attributes) entry.attributes = attributes;
    if (tags) entry.tags = tags;
    this.log(entry);
  }

  debug(
    message: string,
    attributes?: Record<string, any>,
    tags?: Record<string, string>,
  ): void {
    const entry = this.createLogEntry("debug", message);
    if (attributes) entry.attributes = attributes;
    if (tags) entry.tags = tags;
    this.log(entry);
  }

  private log(entry: DatadogLogEntry): void {
    // Log to console in development
    if (this.environment === "development") {
      const consoleMethod =
        entry.level === "error"
          ? "error"
          : entry.level === "warn"
            ? "warn"
            : entry.level === "debug"
              ? "debug"
              : "info";

      console[consoleMethod](`[DATADOG] ${entry.message}`, entry);
    }

    // In production, logs are captured by Datadog RUM automatically
    // The RUM script will collect console logs and send them to Datadog
  }
}

// Export singleton instance
export const datadogLogger = new DatadogLogger();

// Export types
export type { DatadogLogEntry };
