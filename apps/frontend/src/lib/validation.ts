/**
 * Comprehensive validation utilities for the application
 *
 * This file contains all validation patterns and functions as per application requirements:
 * - Mobile Number Validation: (\+91|0)?[1-9][0-9]{9}
 * - Date of Birth Validation: \d{4}-(0[0-9]|1[012])-(0[0-9]|[12][0-9]|3[01])$
 * - ABHA Address Validation: (^[a-zA-Z0-9]+[.]?[a-zA-Z0-9][]?[a-zA-Z0-9]+$)|(^[a-zA-Z0-9]+[]?[a-zA-Z0-9][.]?[a-zA-Z0-9]+$)
 * - ABHA Number Validation: \d{2}-\d{4}-\d{4}-\d{4}
 * - OTP Validation: [0-9]{6}
 * - Password Validation: ^(?=.[A-Z])(?=.\d)(?=.[!@#$^-])[A-Za-z\d!@#$%^&*-]{8,}$
 * - Email Validation: ^[a-zA-Z0-9_-]+(?:.[a-zA-Z0-9_-]+)@(?:[a-zA-Z0-9-]+.)+[a-zA-Z]{2,7}$
 */

// Validation regex patterns
export const VALIDATION_PATTERNS = {
  // Mobile Number: Optional country code or leading zero, first digit non-zero, 10 digits total
  MOBILE: /(\+91|0)?[1-9][0-9]{9}/,

  // Date of Birth: YYYY-MM-DD format with proper month and day validation
  DATE_OF_BIRTH: /\d{4}-(0[0-9]|1[012])-(0[0-9]|[12][0-9]|3[01])$/,

  // ABHA Address: Format like username@domain (e.g., john.doe@sbx, user123@abdm)
  ABHA_ADDRESS:
    /^[a-zA-Z0-9]+([._]?[a-zA-Z0-9]+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z0-9]+)*$/,

  // ABHA Number: XX-XXXX-XXXX-XXXX format
  ABHA_NUMBER: /\d{2}-\d{4}-\d{4}-\d{4}/,

  // OTP: Exactly 6 digits
  OTP: /[0-9]{6}/,

  // Password: At least 8 chars, 1 uppercase, 1 digit, 1 special char from specified set
  PASSWORD: /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*\-])[A-Za-z\d!@#$%^&*\-]{8,}$/,

  // Email: Standard email format with proper domain validation
  EMAIL:
    /^[a-zA-Z0-9_-]+(?:\.[a-zA-Z0-9_-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,7}$/,

  // Aadhaar: Exactly 12 digits
  AADHAAR: /^\d{12}$/,
} as const;

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

/**
 * Validate mobile number format
 * Supports: 9876543210, +************, 09876543210
 * @param mobile - Mobile number to validate
 * @returns ValidationResult with isValid boolean and optional message
 */
export function validateMobile(mobile: string): ValidationResult {
  if (!mobile || typeof mobile !== "string") {
    return { isValid: false, message: "Mobile number is required" };
  }

  const isValid = VALIDATION_PATTERNS.MOBILE.test(mobile);
  return {
    isValid,
    message: isValid
      ? undefined
      : "Please enter a valid 10-digit mobile number (e.g., 9876543210, +************)",
  };
}

/**
 * Validate date of birth format (YYYY-MM-DD)
 * @param dateOfBirth - Date of birth to validate
 * @returns ValidationResult with isValid boolean and optional message
 */
export function validateDateOfBirth(dateOfBirth: string): ValidationResult {
  if (!dateOfBirth || typeof dateOfBirth !== "string") {
    return { isValid: false, message: "Date of birth is required" };
  }

  const isValid = VALIDATION_PATTERNS.DATE_OF_BIRTH.test(dateOfBirth);
  return {
    isValid,
    message: isValid
      ? undefined
      : "Please enter a valid date in YYYY-MM-DD format (e.g., 2023-04-15)",
  };
}

/**
 * Validate ABHA Address format
 * @param abhaAddress - ABHA Address to validate
 * @returns ValidationResult with isValid boolean and optional message
 */
export function validateAbhaAddress(abhaAddress: string): ValidationResult {
  if (!abhaAddress || typeof abhaAddress !== "string") {
    return { isValid: false, message: "ABHA Address is required" };
  }

  const isValid = VALIDATION_PATTERNS.ABHA_ADDRESS.test(abhaAddress);
  return {
    isValid,
    message: isValid
      ? undefined
      : "Please enter a valid ABHA Address (e.g., john.doe@sbx, user123@abdm)",
  };
}

/**
 * Validate ABHA number format (XX-XXXX-XXXX-XXXX)
 * @param abhaNumber - ABHA number to validate
 * @returns ValidationResult with isValid boolean and optional message
 */
export function validateAbhaNumber(abhaNumber: string): ValidationResult {
  if (!abhaNumber || typeof abhaNumber !== "string") {
    return { isValid: false, message: "ABHA Number is required" };
  }

  const isValid = VALIDATION_PATTERNS.ABHA_NUMBER.test(abhaNumber);
  return {
    isValid,
    message: isValid
      ? undefined
      : "Please enter a valid ABHA Number in XX-XXXX-XXXX-XXXX format (e.g., 12-3456-7890-1234)",
  };
}

/**
 * Validate OTP format (6 digits)
 * @param otp - OTP to validate
 * @returns ValidationResult with isValid boolean and optional message
 */
export function validateOTP(otp: string): ValidationResult {
  if (!otp || typeof otp !== "string") {
    return { isValid: false, message: "OTP is required" };
  }

  const isValid = VALIDATION_PATTERNS.OTP.test(otp);
  return {
    isValid,
    message: isValid
      ? undefined
      : "Please enter a valid 6-digit OTP (e.g., 123456)",
  };
}

/**
 * Validate password format
 * Must contain: at least 8 characters, 1 uppercase letter, 1 digit, 1 special character
 * @param password - Password to validate
 * @returns ValidationResult with isValid boolean and optional message
 */
export function validatePassword(password: string): ValidationResult {
  if (!password || typeof password !== "string") {
    return { isValid: false, message: "Password is required" };
  }

  const isValid = VALIDATION_PATTERNS.PASSWORD.test(password);

  if (!isValid) {
    const hasMinLength = password.length >= 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasDigit = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*-]/.test(password);

    let message = "Password must contain: ";
    const missing = [];

    if (!hasMinLength) missing.push("at least 8 characters");
    if (!hasUppercase) missing.push("1 uppercase letter");
    if (!hasDigit) missing.push("1 digit");
    if (!hasSpecialChar) missing.push("1 special character (!@#$%^&*-)");

    message += missing.join(", ");

    return { isValid: false, message };
  }

  return { isValid: true };
}

/**
 * Validate email format
 * @param email - Email to validate
 * @returns ValidationResult with isValid boolean and optional message
 */
export function validateEmail(email: string): ValidationResult {
  if (!email || typeof email !== "string") {
    return { isValid: false, message: "Email is required" };
  }

  const isValid = VALIDATION_PATTERNS.EMAIL.test(email);
  return {
    isValid,
    message: isValid
      ? undefined
      : "Please enter a valid email address (e.g., <EMAIL>)",
  };
}

/**
 * Validate Aadhaar number format (12 digits)
 * @param aadhaar - Aadhaar number to validate
 * @returns ValidationResult with isValid boolean and optional message
 */
export function validateAadhaar(aadhaar: string): ValidationResult {
  if (!aadhaar || typeof aadhaar !== "string") {
    return { isValid: false, message: "Aadhaar number is required" };
  }

  const isValid = VALIDATION_PATTERNS.AADHAAR.test(aadhaar);
  return {
    isValid,
    message: isValid
      ? undefined
      : "Please enter a valid 12-digit Aadhaar number",
  };
}

// Simple validation functions that return boolean (for backward compatibility)
export const simpleValidation = {
  mobile: (mobile: string): boolean => validateMobile(mobile).isValid,
  dateOfBirth: (dob: string): boolean => validateDateOfBirth(dob).isValid,
  abhaAddress: (address: string): boolean =>
    validateAbhaAddress(address).isValid,
  abhaNumber: (number: string): boolean => validateAbhaNumber(number).isValid,
  otp: (otp: string): boolean => validateOTP(otp).isValid,
  password: (password: string): boolean => validatePassword(password).isValid,
  email: (email: string): boolean => validateEmail(email).isValid,
  aadhaar: (aadhaar: string): boolean => validateAadhaar(aadhaar).isValid,
};

// Export patterns for direct use if needed
export { VALIDATION_PATTERNS as ValidationPatterns };
