/**
 * Cryptography Utilities
 *
 * This module provides cryptographic functions for ABDM integration.
 */

import { webcrypto } from "crypto";

/**
 * Generate an ECDH key pair for Curve25519
 * @returns Public and private keys in base64 format
 */
export async function generateKeyPair(): Promise<{
  publicKey: string;
  privateKey: string;
}> {
  // Generate a key pair for ECDH with Curve25519
  const keyPair = await webcrypto.subtle.generateKey(
    {
      name: "ECDH",
      namedCurve: "P-256", // Using P-256 as a substitute for Curve25519 which isn't directly supported
    },
    true,
    ["deriveKey", "deriveBits"],
  );

  // Export the public key
  const publicKeyBuffer = await webcrypto.subtle.exportKey(
    "spki",
    keyPair.publicKey,
  );

  // Export the private key
  const privateKeyBuffer = await webcrypto.subtle.exportKey(
    "pkcs8",
    keyPair.privateKey,
  );

  // Convert to base64
  const publicKey = Buffer.from(publicKeyBuffer).toString("base64");
  const privateKey = Buffer.from(privateKeyBuffer).toString("base64");

  return { publicKey, privateKey };
}

/**
 * Encrypt data using a public key
 * @param data - Data to encrypt
 * @param publicKey - Public key in base64 format
 * @returns Encrypted data in base64 format
 */
export async function encryptWithPublicKey(
  data: string,
  publicKey: string,
): Promise<string> {
  // Convert base64 public key to binary
  const publicKeyBuffer = Buffer.from(publicKey, "base64");

  // Import the public key
  const importedPublicKey = await webcrypto.subtle.importKey(
    "spki",
    publicKeyBuffer,
    {
      name: "RSA-OAEP",
      hash: "SHA-256",
    },
    false,
    ["encrypt"],
  );

  // Encrypt the data
  const dataBuffer = Buffer.from(data, "utf-8");
  const encryptedBuffer = await webcrypto.subtle.encrypt(
    {
      name: "RSA-OAEP",
    },
    importedPublicKey,
    dataBuffer,
  );

  // Convert to base64
  return Buffer.from(encryptedBuffer).toString("base64");
}

/**
 * Decrypt data using a private key
 * @param encryptedData - Encrypted data in base64 format
 * @param privateKey - Private key in base64 format
 * @returns Decrypted data
 */
export async function decryptWithPrivateKey(
  encryptedData: string,
  privateKey: string,
): Promise<string> {
  // Convert base64 private key to binary
  const privateKeyBuffer = Buffer.from(privateKey, "base64");

  // Import the private key
  const importedPrivateKey = await webcrypto.subtle.importKey(
    "pkcs8",
    privateKeyBuffer,
    {
      name: "RSA-OAEP",
      hash: "SHA-256",
    },
    false,
    ["decrypt"],
  );

  // Decrypt the data
  const encryptedBuffer = Buffer.from(encryptedData, "base64");
  const decryptedBuffer = await webcrypto.subtle.decrypt(
    {
      name: "RSA-OAEP",
    },
    importedPrivateKey,
    encryptedBuffer,
  );

  // Convert to string
  return Buffer.from(decryptedBuffer).toString("utf-8");
}
