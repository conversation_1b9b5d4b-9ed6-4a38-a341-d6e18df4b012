import { PrismaClient } from "@prisma/client";
import { generateUUID } from "@/lib/utils";

// Create a new PrismaClient instance
const prismaClientSingleton = () => {
  // Create the base Prisma client
  const prisma = new PrismaClient({
    log: process.env.NODE_ENV === "development" ? ["error", "warn"] : ["error"],
  });

  // Add middleware for user creation
  prisma.$use(async (params, next) => {
    // Only apply this middleware to user creation operations
    if (params.model === "User" && params.action === "create") {
      const { data } = params.args;

      // Check if a user with this email already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email },
        include: {
          organizations: {
            where: {
              organizationId: data.organizations?.create?.organizationId,
            },
          },
        },
      });

      // If user exists, handle organization connection
      if (existingUser) {
        // Check if user is already connected to this organization
        if (
          data.organizations?.create &&
          existingUser.organizations.length === 0
        ) {
          // Connect user to this organization
          await prisma.userOrganization.create({
            data: {
              userId: existingUser.id,
              organizationId: data.organizations.create.organizationId,
              roles: Array.isArray(data.organizations.create.role)
                ? data.organizations.create.role
                : [data.organizations.create.role],
              isDefault:
                (await prisma.userOrganization.count({
                  where: { userId: existingUser.id },
                })) === 0,
            },
          });
        }

        // Return the existing user instead of creating a new one
        return existingUser;
      }
    }

    // For all other operations, proceed normally
    return next(params);
  });

  // Add middleware for patient creation to check for existing patients
  prisma.$use(async (params, next) => {
    // Only apply this middleware to patient creation operations
    if (params.model === "Patient" && params.action === "create") {
      const { data } = params.args;

      // Only check for duplicates if we have the required fields
      // Skip validation if essential fields are missing or empty
      const hasRequiredFields =
        data.firstName &&
        data.lastName &&
        data.dateOfBirth &&
        data.phone &&
        data.organizationId;

      if (hasRequiredFields) {
        // Validate dateOfBirth format before querying
        let validDateOfBirth = data.dateOfBirth;

        // If dateOfBirth is an empty string, skip the duplicate check
        if (
          typeof data.dateOfBirth === "string" &&
          data.dateOfBirth.trim() === ""
        ) {
          console.warn("Empty dateOfBirth provided, skipping duplicate check");
          return next(params);
        }

        // Check if a patient with this exact combination already exists in this organization
        // We allow multiple patients with same email/phone but different names/DOB
        const existingPatient = await prisma.patient.findFirst({
          where: {
            firstName: data.firstName,
            lastName: data.lastName,
            dateOfBirth: validDateOfBirth,
            phone: data.phone,
            organizationId: data.organizationId,
          },
        });

        // If exact patient exists, throw an error with the existing patient data
        if (existingPatient) {
          const error = new Error(
            `Patient with this exact details already exists in this organization`,
          );
          // @ts-ignore
          error.patient = existingPatient;
          throw error;
        }
      } else {
        console.warn(
          "Incomplete patient data provided, skipping duplicate check:",
          {
            hasFirstName: !!data.firstName,
            hasLastName: !!data.lastName,
            hasDateOfBirth: !!data.dateOfBirth,
            hasPhone: !!data.phone,
            hasOrganizationId: !!data.organizationId,
          },
        );
      }
    }

    // For all other operations, proceed normally
    return next(params);
  });

  // Add middleware for ConsentNotify and HiRequest to send acknowledgments
  prisma.$use(async (params, next) => {
    // Process the operation
    const result = await next(params);

    // Post-processing after create operation for ConsentNotify
    if (params.model === "ConsentNotify" && params.action === "create") {
      try {
        console.log("ConsentNotify middleware triggered for create operation");

        // Extract consentId and requestId from the created record
        const { consentId, requestId, id } = result;

        if (consentId && requestId) {
          console.log(
            `Sending acknowledgment for consent notification: ${consentId}`,
          );

          // Get access token
          const { getAccessToken } = await import("@/services/abdm/utils/auth");
          const { abdmFetch } = await import("@/lib/abdm-fetch");
          const accessToken = await getAccessToken();

          console.log({ accessToken });

          // ABDM API endpoints
          const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
          const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

          if (!ABDM_BASE_URL) {
            throw new Error("ABDM_BASE_URL is not configured");
          }

          // Create a timestamp for the request
          const timestamp = new Date().toISOString();

          // Format the base URL correctly
          const baseUrl = ABDM_BASE_URL.endsWith("/api")
            ? ABDM_BASE_URL
            : ABDM_BASE_URL.includes("/api/")
              ? ABDM_BASE_URL
              : `${ABDM_BASE_URL}/api`;

          // Prepare the payload
          const payload = {
            acknowledgement: {
              status: "ok",
              consentId,
            },
            response: {
              requestId,
            },
          };

          // Log the full URL for debugging
          const apiUrl = `${baseUrl}/hiecm/consent/v3/request/hip/on-notify`;
          console.log(`Sending acknowledgment to: ${apiUrl}`);

          let acknowledgementSuccess = false;

          try {
            // Make the API request
            await abdmFetch(apiUrl, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "REQUEST-ID": requestId,
                TIMESTAMP: timestamp,
                "X-CM-ID": ABDM_CM_ID,
                Authorization: `Bearer ${accessToken}`,
              },
              body: JSON.stringify(payload),
              signal: AbortSignal.timeout(120000), // 120 seconds timeout
            });

            console.log(
              `Successfully sent acknowledgment for consent notification: ${consentId}`,
            );
            acknowledgementSuccess = true;
          } catch (apiError) {
            console.error(
              `Failed to send acknowledgment for consent notification: ${consentId}`,
              apiError,
            );
            acknowledgementSuccess = false;
          }

          // Update the grantAcknowledgement field based on API success
          try {
            await prisma.consentNotify.update({
              where: { id },
              data: {
                grantAcknowledgement: acknowledgementSuccess,
                updatedAt: new Date(),
              },
            });

            console.log(
              `Updated grantAcknowledgement to ${acknowledgementSuccess} for consent notification: ${consentId}`,
            );
          } catch (updateError) {
            console.error(
              `Failed to update grantAcknowledgement for consent notification: ${consentId}`,
              updateError,
            );
          }
        }
      } catch (error) {
        console.error(
          "Error in ConsentNotify middleware post-processing:",
          error,
        );
        // Don't throw the error to avoid affecting the main operation
      }
    }

    // Post-processing after create operation for HiRequest
    if (params.model === "HiRequest" && params.action === "create") {
      try {
        console.log("HiRequest middleware triggered for create operation");

        // Extract transactionId, requestId and id from the created record
        const { transactionId, requestId, id } = result;

        if (transactionId && requestId) {
          console.log(
            `Sending acknowledgment for health information request: ${transactionId}`,
          );

          // Get access token
          const { getAccessToken } = await import("@/services/abdm/utils/auth");
          const { abdmFetch } = await import("@/lib/abdm-fetch");
          const accessToken = await getAccessToken();

          console.log({ accessToken });

          // ABDM API endpoints
          const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
          const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

          if (!ABDM_BASE_URL) {
            throw new Error("ABDM_BASE_URL is not configured");
          }

          // Create a timestamp for the request
          const timestamp = new Date().toISOString();

          // Format the base URL correctly
          const baseUrl = ABDM_BASE_URL.endsWith("/api")
            ? ABDM_BASE_URL
            : ABDM_BASE_URL.includes("/api/")
              ? ABDM_BASE_URL
              : `${ABDM_BASE_URL}/api`;

          // Prepare the payload
          const payload = {
            hiRequest: {
              transactionId: transactionId,
              sessionStatus: "ACKNOWLEDGED",
            },
            response: {
              requestId: requestId,
            },
          };

          // Log the full URL for debugging
          const apiUrl = `${baseUrl}/hiecm/data-flow/v3/health-information/hip/on-request`;
          console.log(`Sending acknowledgment to: ${apiUrl}`);

          let acknowledgementSuccess = false;

          console.log({ payload });
          try {
            // Make the API request
            const response = await abdmFetch(apiUrl, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "REQUEST-ID": generateUUID(),
                TIMESTAMP: timestamp,
                "X-CM-ID": ABDM_CM_ID,
                Authorization: `Bearer ${accessToken}`,
              },
              body: JSON.stringify(payload),
              signal: AbortSignal.timeout(120000), // 120 seconds timeout
            });

            // Log response status
            console.log({ responseStatus: response.status });

            console.log(
              `Successfully sent acknowledgment for health information request: ${transactionId}`,
            );
            acknowledgementSuccess = true;
          } catch (apiError) {
            console.error(
              `Failed to send acknowledgment for health information request: ${transactionId}`,
              apiError,
            );
            acknowledgementSuccess = false;
          }

          // Update the acknowledgementSent field based on API success
          try {
            await prisma.hiRequest.update({
              where: { id },
              data: {
                acknowledgementSent: acknowledgementSuccess,
                updatedAt: new Date(),
              },
            });

            console.log(
              `Updated acknowledgementSent to ${acknowledgementSuccess} for health information request: ${transactionId}`,
            );
          } catch (updateError) {
            console.error(
              `Failed to update acknowledgementSent for health information request: ${transactionId}`,
              updateError,
            );
          }
        }
      } catch (error) {
        console.error("Error in HiRequest middleware post-processing:", error);
        // Don't throw the error to avoid affecting the main operation
      }
    }

    return result;
  });

  return prisma;
};

// Define the type for the global variable
type PrismaClientSingleton = ReturnType<typeof prismaClientSingleton>;

// Define the global variable
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClientSingleton | undefined;
};

// Export the PrismaClient instance
export const db = globalForPrisma.prisma ?? prismaClientSingleton();

// In development, attach the PrismaClient to the global object
if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = db;
}

// Helper function to check if the database is connected
export async function isDatabaseConnected() {
  try {
    await db.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error("Database connection check failed:", error);
    return false;
  }
}
