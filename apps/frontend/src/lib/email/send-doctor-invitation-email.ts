// We'll use a simple HTML template instead of React components for server compatibility

import { emailService } from "./emailService";
import { logger } from "@/lib/logger";

interface SendDoctorInvitationEmailParams {
  to: string;
  doctorName: string;
  hospitalName: string;
  token: string;
}

export async function sendDoctorInvitationEmail({
  to,
  doctorName,
  hospitalName,
  token,
}: SendDoctorInvitationEmailParams) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    const invitationUrl = `${baseUrl}/doctor-invitation?token=${token}`;

    // Create a simple HTML email template
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4f46e5;">Doctor Invitation</h1>

        <p>Hello ${doctorName},</p>

        <p>
          You have been invited to join ${hospitalName} as a doctor on Aran Care HIMS. To accept this invitation and set up your account, please click the button below:
        </p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${invitationUrl}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            Accept Invitation
          </a>
        </div>

        <p>
          If the button doesn't work, you can also copy and paste this link into your browser:
          <a href="${invitationUrl}" style="color: #4f46e5; text-decoration: underline;">${invitationUrl}</a>
        </p>

        <p>
          This invitation will expire in 24 hours for security reasons. If you did not expect this invitation, please ignore this email.
        </p>

        <p>
          If you have any questions or need assistance, please contact the hospital administration.
        </p>
      </div>
    `;

    const result = await emailService.send({
      to,
      subject: `Invitation to join ${hospitalName} as a doctor`,
      html: emailHtml,
    });

    if (result.error) {
      logger.error("Failed to send doctor invitation email", {
        error: result.error,
        details: result.details,
        recipient: to,
      });
      return { success: false, error: result.error };
    }

    logger.info("Doctor invitation email sent successfully", {
      recipient: to,
      hospitalName,
    });

    return { success: true };
  } catch (error) {
    logger.error("Exception while sending doctor invitation email", {
      error: error instanceof Error ? error.message : String(error),
      recipient: to,
    });
    return { success: false, error: "Failed to send invitation email" };
  }
}
