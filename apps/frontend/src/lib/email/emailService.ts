interface EmailData {
  from?: string;
  to: string;
  subject: string;
  html: string;
  tenantId?: string;
  text?: string;
  cc?: string;
}

// Environment variables
const EMAIL_API_URL = process.env.NEXT_PUBLIC_MAILED_APP_URL;
const EMAIL_API_KEY = process.env.NEXT_PUBLIC_MAIL_API_KEY || "";
const DEFAULT_SENDER = process.env.NEXT_PUBLIC_SEND_EMAIL_FROM || "";

// API endpoint for the mail service
const API_URL = `${EMAIL_API_URL}/api/client/email/send`;

export const emailService = {
  send: async (emailData: EmailData) => {
    // Validate required environment variables with detailed error messages
    if (!EMAIL_API_URL) {
      console.error(
        "❌ Email service configuration error: NEXT_PUBLIC_MAILED_APP_URL is not set",
      );
      return {
        success: false,
        error:
          "Email service URL is not configured. Please set NEXT_PUBLIC_MAILED_APP_URL environment variable.",
      };
    }

    if (!EMAIL_API_KEY) {
      console.error(
        "❌ Email service configuration error: NEXT_PUBLIC_MAIL_API_KEY is not set",
      );
      return {
        success: false,
        error:
          "Email service API key is not configured. Please set NEXT_PUBLIC_MAIL_API_KEY environment variable.",
      };
    }

    if (!DEFAULT_SENDER) {
      console.error(
        "❌ Email service configuration error: NEXT_PUBLIC_SEND_EMAIL_FROM is not set",
      );
      return {
        success: false,
        error:
          "Sender email address is not configured. Please set NEXT_PUBLIC_SEND_EMAIL_FROM environment variable.",
      };
    }

    // Validate email data
    if (!emailData.to || !emailData.subject || !emailData.html) {
      console.error("❌ Email validation error: Missing required fields", {
        to: !!emailData.to,
        subject: !!emailData.subject,
        html: !!emailData.html,
      });
      return {
        success: false,
        error: "Missing required email fields (to, subject, html)",
      };
    }

    try {
      // Simplify the payload structure to avoid validation errors
      const payload = {
        from: DEFAULT_SENDER,
        to: emailData.to,
        subject: emailData.subject,
        htmlContent: emailData.html,
        platform: "Aran Care HIMS",
      };

      console.log("📧 Sending email via API:", {
        url: API_URL,
        to: emailData.to,
        subject: emailData.subject,
        from: DEFAULT_SENDER,
      });

      // Send the email using the mail API
      const response = await fetch(API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "api-key": EMAIL_API_KEY,
        },
        body: JSON.stringify(payload),
      });

      // Process the API response

      // Read the response body only once
      let responseData;
      try {
        responseData = await response.json();
      } catch (e) {
        // If JSON parsing fails, try to get the text
        try {
          responseData = { text: await response.text() };
        } catch (textError) {
          // If both fail, just use an empty object
          responseData = {};
        }
      }

      if (!response.ok) {
        console.error("❌ Email API error:", {
          status: response.status,
          statusText: response.statusText,
          responseData,
          url: API_URL,
          to: emailData.to,
        });
        return {
          success: false,
          error: `Failed to send email: ${response.status} ${response.statusText}`,
          details: responseData,
        };
      }

      // Use the already parsed response data
      const result = responseData || { message: "Email sent successfully" };
      console.log("✅ Email sent successfully:", {
        to: emailData.to,
        subject: emailData.subject,
        response: result,
      });
      return { success: true, ...result };
    } catch (error) {
      console.error("❌ Email service exception:", {
        error: error instanceof Error ? error.message : String(error),
        to: emailData.to,
        subject: emailData.subject,
        url: API_URL,
      });
      return {
        success: false,
        error: `Exception while sending email: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
};
