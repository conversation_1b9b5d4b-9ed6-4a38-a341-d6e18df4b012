import { emailService } from "./emailService";
import { logger } from "@/lib/logger";

interface SendBranchAdminInvitationEmailParams {
  to: string;
  adminName: string;
  hospitalName: string;
  branchName: string;
  token: string;
}

export async function sendBranchAdminInvitationEmail({
  to,
  adminName,
  hospitalName,
  branchName,
  token,
}: SendBranchAdminInvitationEmailParams) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    const invitationUrl = `${baseUrl}/branch-admin-invitation?token=${token}`;

    // Create a simple HTML email template
    const emailHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Branch Admin Invitation</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            max-width: 150px;
            margin-bottom: 20px;
          }
          h1 {
            color: #4f46e5;
            margin-bottom: 20px;
          }
          .content {
            background-color: #f9fafb;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
          }
          .button {
            display: inline-block;
            background-color: #4f46e5;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: bold;
            margin: 20px 0;
          }
          .footer {
            font-size: 12px;
            color: #6b7280;
            text-align: center;
            margin-top: 30px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Aran Care HIMS</h1>
        </div>
        <div class="content">
          <p>Hello ${adminName || "there"},</p>
          <p>You have been invited to join <strong>${hospitalName}</strong> as a Branch Administrator for <strong>${branchName}</strong>.</p>
          <p>As a Branch Administrator, you will be responsible for managing staff, departments, and operations for your assigned branch.</p>
          <p>Please click the button below to accept the invitation and set up your account:</p>
          <p style="text-align: center;">
            <a href="${invitationUrl}" class="button">Accept Invitation</a>
          </p>
          <p>This invitation link will expire in 24 hours.</p>
          <p>If you did not expect this invitation, please ignore this email.</p>
        </div>
        <div class="footer">
          <p>This is an automated message from Aran Care HIMS. Please do not reply to this email.</p>
          <p>&copy; ${new Date().getFullYear()} Aran Care. All rights reserved.</p>
        </div>
      </body>
      </html>
    `;

    // Send the email
    const result = await emailService.send({
      to,
      subject: `Branch Administrator Invitation for ${branchName} - ${hospitalName}`,
      html: emailHtml,
    });

    logger.log("Branch admin invitation email sent:", result);
    return result;
  } catch (error) {
    logger.error("Error sending branch admin invitation email:", error);
    throw error;
  }
}
