import { emailService } from "./emailService";
import VerificationEmail from "@/emails/verification-email";
import * as React from "react";
// Use a dynamic import for renderToStaticMarkup to avoid SSR issues
const renderToStaticMarkup = (component: React.ReactElement): string => {
  // In a browser environment, we need to handle this differently
  if (typeof window !== "undefined") {
    return "<div>Email content (browser environment)</div>";
  }

  // In a server environment, we can use renderToStaticMarkup
  try {
    // Using require instead of import to avoid SSR issues
    const ReactDOMServer = require("react-dom/server");
    return ReactDOMServer.renderToStaticMarkup(component);
  } catch (error) {
    // Return a fallback template in case of rendering error
    return "<div>Error rendering email template</div>";
  }
};

interface SendVerificationEmailParams {
  email: string;
  hospitalName: string;
  adminName: string;
  token: string;
}

export async function sendVerificationEmail({
  email,
  hospitalName,
  adminName,
  token,
}: SendVerificationEmailParams) {
  try {
    // Create verification URL
    const baseUrl = process.env.NEXT_PUBLIC_APP_DOMAIN
      ? `http://${process.env.NEXT_PUBLIC_APP_DOMAIN}`
      : "http://localhost:3000";
    // Ensure the URL is properly formatted and encoded
    const verificationUrl = `${baseUrl}/verify?token=${encodeURIComponent(token)}&email=${encodeURIComponent(email)}`;

    // Render the email template to HTML
    const emailHtml = renderToStaticMarkup(
      VerificationEmail({
        hospitalName,
        adminName,
        verificationUrl,
      }),
    );

    // Send the email
    const result = await emailService.send({
      to: email,
      subject: `Verify Your Email for ${hospitalName} - Aran Care HIMS`,
      html: emailHtml,
    });

    // Ensure we return a consistent format
    if (!result.success || result.error) {
      console.error("❌ Failed to send verification email:", {
        error: result.error,
        recipient: email,
        hospitalName,
      });
      return {
        success: false,
        error: result.error || "Failed to send verification email",
      };
    }

    console.log("✅ Verification email sent successfully to:", email);
    return { success: true };
  } catch (error) {
    console.error("❌ Exception while sending verification email:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to send verification email",
    };
  }
}
