/**
 * Send UIL OTP Email
 *
 * This module handles sending OTP emails for User Initiated Linking (UIL) flow
 */

import { emailService } from "./emailService";
import { logger } from "@/lib/logger";

interface SendUILOtpEmailParams {
  to: string;
  patientName: string;
  hospitalName: string;
  otp: string;
  expiresAt: Date;
}

/**
 * Send UIL OTP email to patient
 * @param params - Email parameters
 * @returns Result of email sending operation
 */
export async function sendUILOtpEmail({
  to,
  patientName,
  hospitalName,
  otp,
  expiresAt,
}: SendUILOtpEmailParams) {
  try {
    // Format expiry time
    const expiryTime = new Date(expiresAt).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });

    // Create a simple HTML email template
    const emailHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your OTP for ABHA Care Context Linking</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          h1 {
            color: #4f46e5;
            margin-bottom: 20px;
          }
          .content {
            background-color: #f9fafb;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
          }
          .otp-container {
            text-align: center;
            margin: 20px 0;
          }
          .otp {
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 5px;
            color: #4f46e5;
            padding: 10px 20px;
            background-color: #eef2ff;
            border-radius: 8px;
            display: inline-block;
          }
          .footer {
            font-size: 12px;
            color: #6b7280;
            text-align: center;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Aran Care HIMS</h1>
        </div>
        <div class="content">
          <p>Hello ${patientName || "Patient"},</p>
          <p>Your One-Time Password (OTP) for linking your care context with your ABHA address at <strong>${hospitalName}</strong> is (This is the OTP for linking):</p>
          
          <div class="otp-container">
            <div class="otp">${otp}</div>
          </div>
          
          <p>This OTP will expire at ${expiryTime}. Please do not share this OTP with anyone.</p>
          <p>If you did not request this OTP, please ignore this email.</p>
        </div>
        <div class="footer">
          <p>This is an automated message from Aran Care HIMS. Please do not reply to this email.</p>
          <p>&copy; ${new Date().getFullYear()} Aran Care. All rights reserved.</p>
        </div>
      </body>
      </html>
    `;

    // Send the email
    const result = await emailService.send({
      to,
      subject: `Your OTP for ABHA Care Context Linking (123456) - ${hospitalName}`,
      html: emailHtml,
    });

    if (result.error) {
      logger.error("Failed to send UIL OTP email", {
        error: result.error,
        details: result.details,
        recipient: to,
      });
      return { success: false, error: result.error };
    }

    logger.info("UIL OTP email sent successfully", {
      recipient: to,
      hospitalName,
    });

    return { success: true };
  } catch (error) {
    logger.error("Exception while sending UIL OTP email", {
      error: error instanceof Error ? error.message : String(error),
      recipient: to,
    });
    return { success: false, error: "Failed to send UIL OTP email" };
  }
}
