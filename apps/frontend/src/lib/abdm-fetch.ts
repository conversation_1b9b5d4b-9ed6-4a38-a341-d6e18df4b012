/**
 * ABDM Fetch Utility
 *
 * This module provides a robust fetch utility for ABDM API calls.
 * It includes timeout handling, error handling, and response validation.
 */

/**
 * Interface for the parsed response from ABDM API
 */
export interface AbdmResponse {
  [key: string]: any;
  authResult?: "Success" | "Failed";
  message?: string;
  txnId?: string;
}

/**
 * Enhanced fetch with timeout, error handling, and response validation
 * @param url - URL to fetch
 * @param options - Fetch options
 * @param responseType - Type of response expected ('json' or 'text')
 * @returns Promise with the parsed response
 */
export async function abdmFetch<T = AbdmResponse>(
  url: string,
  options: RequestInit = {},
  responseType: "json" | "text" = "json",
): Promise<T> {
  try {
    // Execute the fetch
    const response = await fetch(url, options);

    // Get the response text
    const responseText = await response.text();

    // If not OK, try to parse as JSON if possible and throw an error
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      try {
        // Log the full response text for debugging
        console.error(`ABDM API error response for ${url}:`, responseText);

        const errorData = JSON.parse(responseText);
        console.error(
          `ABDM API parsed error for ${url}:`,
          JSON.stringify(errorData, null, 2),
        );

        if (errorData.error) {
          errorMessage =
            errorData.error.message || errorData.error.code || errorData.error;
        } else if (errorData.details && errorData.details.length > 0) {
          // Handle structured error details
          errorMessage = errorData.details
            .map(
              (detail: any) =>
                detail.message || detail.code || JSON.stringify(detail),
            )
            .join("; ");
        } else if (errorData.message) {
          errorMessage = errorData.message;
        } else if (errorData.error) {
          errorMessage = errorData.error;
        }
      } catch (e) {
        // If parsing fails, use the response text or status as error message
        if (responseText && responseText.trim()) {
          errorMessage = responseText;
        } else {
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
      }
      throw new Error(errorMessage);
    }

    // Handle the response based on the expected type
    if (responseType === "text") {
      // For text responses (like HTML content), return as is
      return responseText as unknown as T;
    } else {
      // For JSON responses, parse and validate
      try {
        // Handle empty responses
        if (!responseText || responseText.trim() === "") {
          console.log("Empty response received from ABDM API");

          // Check if this is a token generation request
          if (
            url.includes("/token/generate-token") ||
            url.includes("/token/retrieve-token")
          ) {
            console.log(
              "Empty response for token request - this might indicate a successful operation",
            );

            // For token requests, we'll try to get the token from the response headers
            const tokenHeader =
              response.headers.get("X-Token") ||
              response.headers.get("x-token");
            if (tokenHeader) {
              console.log("Found token in response headers:", tokenHeader);
              return { token: tokenHeader } as T;
            }

            // If we're here, we need to check if this is a duplicate request
            // In which case we should return a specific error
            if (response.status === 409 || response.status === 400) {
              throw new Error("ABDM-1092: Duplicate Link token request");
            }
          }

          // For other requests, return an empty object
          return {} as T;
        }

        const data = JSON.parse(responseText);

        // Log the parsed data structure for debugging
        console.log(
          "ABDM API response structure:",
          JSON.stringify(data, null, 2),
        );

        // Check if the response has authResult and it's 'Failed'
        if (
          typeof data === "object" &&
          data !== null &&
          "authResult" in data &&
          data.authResult === "Failed"
        ) {
          throw new Error(
            (data as AbdmResponse).message ||
              "Operation failed. Please try again.",
          );
        }

        // Check for error field in the response
        if (
          typeof data === "object" &&
          data !== null &&
          "error" in data &&
          data.error
        ) {
          const errorMessage =
            typeof data.error === "object" && data.error !== null
              ? data.error.message ||
                data.error.code ||
                JSON.stringify(data.error)
              : String(data.error);
          throw new Error(errorMessage);
        }

        return data as T;
      } catch (e) {
        if (e instanceof Error) {
          console.error("Error parsing JSON response:", e.message);
          console.error("Response text was:", responseText);
          throw e; // Re-throw if it's already an Error object
        }
        throw new Error(
          `Invalid response format from ABDM API: ${responseText}`,
        );
      }
    }
  } catch (error) {
    // Enhance error logging
    if (error instanceof Error) {
      const cause = (error as any).cause;
      if (cause && cause.code === "UND_ERR_CONNECT_TIMEOUT") {
        console.error(
          `Connection timeout error when connecting to ABDM server: ${cause.message}. ` +
            `This could be due to network issues or the ABDM server being unavailable.`,
        );
        throw new Error(
          `Connection timeout error when connecting to ABDM server. Please try again later.`,
        );
      } else if (error.message.includes("Runtime Error")) {
        console.error(`ABDM API runtime error for ${url}:`, error.message);
        // Check if the URL is using the correct endpoint
        if (url.includes("/v3/verification/")) {
          console.error(
            `Possible incorrect API endpoint. The correct endpoint might be /v3/profile/login/ instead of /v3/verification/`,
          );
          throw new Error(`ABDM API endpoint error. Please contact support.`);
        } else {
          throw new Error(`ABDM API runtime error. Please try again later.`);
        }
      } else {
        console.error(`ABDM API error for ${url}:`, error.message);
      }
    } else {
      console.error(`ABDM API error for ${url}:`, error);
    }
    throw error;
  }
}
