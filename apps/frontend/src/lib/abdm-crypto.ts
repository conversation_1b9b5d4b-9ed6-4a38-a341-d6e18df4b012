/**
 * ABDM Cryptography Utilities
 *
 * This module provides cryptographic functions for ABDM envelope encryption.
 */

import { webcrypto } from "crypto";

/**
 * Generate a random nonce
 * @param length - Length of the nonce in bytes
 * @returns Base64-encoded nonce
 */
export function generateNonce(length = 32): string {
  const nonceBuffer = Buffer.alloc(length);
  webcrypto.getRandomValues(nonceBuffer);
  return nonceBuffer.toString("base64");
}

/**
 * Generate an ECDH key pair for Curve25519
 * @returns Public and private keys in base64 format
 */
export async function generateECDHKeyPair(): Promise<{
  publicKey: string;
  privateKey: string;
}> {
  try {
    // Generate a key pair for ECDH with P-256 (closest to Curve25519 in Web Crypto API)
    const keyPair = await webcrypto.subtle.generateKey(
      {
        name: "ECDH",
        namedCurve: "P-256",
      },
      true,
      ["deriveKey", "deriveBits"],
    );

    // Export the public key
    const publicKeyBuffer = await webcrypto.subtle.exportKey(
      "spki",
      keyPair.publicKey,
    );

    // Export the private key
    const privateKeyBuffer = await webcrypto.subtle.exportKey(
      "pkcs8",
      keyPair.privateKey,
    );

    // Convert to base64
    const publicKey = Buffer.from(publicKeyBuffer).toString("base64");
    const privateKey = Buffer.from(privateKeyBuffer).toString("base64");

    return { publicKey, privateKey };
  } catch (error) {
    console.error("Error generating ECDH key pair:", error);
    throw new Error("Failed to generate ECDH key pair");
  }
}

/**
 * Derive a shared secret from a private key and a public key
 * @param privateKey - Private key in base64 format
 * @param publicKey - Public key in base64 format
 * @returns Shared secret in base64 format
 */
export async function deriveSharedSecret(
  privateKey: string,
  publicKey: string,
): Promise<string> {
  try {
    // Convert base64 keys to buffers
    const privateKeyBuffer = Buffer.from(privateKey, "base64");
    const publicKeyBuffer = Buffer.from(publicKey, "base64");

    // Import the private key
    const importedPrivateKey = await webcrypto.subtle.importKey(
      "pkcs8",
      privateKeyBuffer,
      {
        name: "ECDH",
        namedCurve: "P-256",
      },
      false,
      ["deriveKey", "deriveBits"],
    );

    // Import the public key
    const importedPublicKey = await webcrypto.subtle.importKey(
      "spki",
      publicKeyBuffer,
      {
        name: "ECDH",
        namedCurve: "P-256",
      },
      false,
      [],
    );

    // Derive bits
    const sharedBits = await webcrypto.subtle.deriveBits(
      {
        name: "ECDH",
        public: importedPublicKey,
      },
      importedPrivateKey,
      256,
    );

    // Convert to base64
    return Buffer.from(sharedBits).toString("base64");
  } catch (error) {
    console.error("Error deriving shared secret:", error);
    throw new Error("Failed to derive shared secret");
  }
}

/**
 * Encrypt data using AES-GCM with a key and nonce
 * @param data - Data to encrypt
 * @param key - Encryption key in base64 format
 * @param nonce - Nonce in base64 format
 * @returns Encrypted data in base64 format
 */
export async function encryptAESGCM(
  data: string,
  key: string,
  nonce: string,
): Promise<string> {
  try {
    // Convert base64 key and nonce to buffers
    const keyBuffer = Buffer.from(key, "base64");
    const nonceBuffer = Buffer.from(nonce, "base64");
    const dataBuffer = Buffer.from(data, "utf-8");

    // Import the key
    const importedKey = await webcrypto.subtle.importKey(
      "raw",
      keyBuffer,
      {
        name: "AES-GCM",
      },
      false,
      ["encrypt"],
    );

    // Encrypt the data
    const encryptedBuffer = await webcrypto.subtle.encrypt(
      {
        name: "AES-GCM",
        iv: nonceBuffer,
        tagLength: 128,
      },
      importedKey,
      dataBuffer,
    );

    // Convert to base64
    return Buffer.from(encryptedBuffer).toString("base64");
  } catch (error) {
    console.error("Error encrypting data:", error);
    throw new Error("Failed to encrypt data");
  }
}

/**
 * Decrypt data using AES-GCM with a key and nonce
 * @param encryptedData - Encrypted data in base64 format
 * @param key - Decryption key in base64 format
 * @param nonce - Nonce in base64 format
 * @returns Decrypted data
 */
export async function decryptAESGCM(
  encryptedData: string,
  key: string,
  nonce: string,
): Promise<string> {
  try {
    // Convert base64 key, nonce, and encrypted data to buffers
    const keyBuffer = Buffer.from(key, "base64");
    const nonceBuffer = Buffer.from(nonce, "base64");
    const encryptedBuffer = Buffer.from(encryptedData, "base64");

    // Import the key
    const importedKey = await webcrypto.subtle.importKey(
      "raw",
      keyBuffer,
      {
        name: "AES-GCM",
      },
      false,
      ["decrypt"],
    );

    // Decrypt the data
    const decryptedBuffer = await webcrypto.subtle.decrypt(
      {
        name: "AES-GCM",
        iv: nonceBuffer,
        tagLength: 128,
      },
      importedKey,
      encryptedBuffer,
    );

    // Convert to string
    return Buffer.from(decryptedBuffer).toString("utf-8");
  } catch (error) {
    console.error("Error decrypting data:", error);
    throw new Error("Failed to decrypt data");
  }
}

/**
 * Create an ABDM envelope for health record data
 * @param data - Data to encrypt
 * @param recipientPublicKey - Recipient's public key in base64 format
 * @returns Encrypted data and encryption metadata
 */
export async function createABDMEnvelope(
  data: string,
  recipientPublicKey: string,
): Promise<{
  encryptedData: string;
  keyMaterial: {
    cryptoAlg: string;
    curve: string;
    dhPublicKey: {
      expiry: string;
      parameters: string;
      keyValue: string;
    };
    nonce: string;
  };
}> {
  try {
    // Generate a key pair
    const { publicKey, privateKey } = await generateECDHKeyPair();

    // Generate a nonce
    const nonce = generateNonce();

    // Derive a shared secret
    const sharedSecret = await deriveSharedSecret(
      privateKey,
      recipientPublicKey,
    );

    // Encrypt the data
    const encryptedData = await encryptAESGCM(data, sharedSecret, nonce);

    // Set expiry date to 30 minutes from now
    const expiryDate = new Date();
    expiryDate.setMinutes(expiryDate.getMinutes() + 30);

    // Return the encrypted data and encryption metadata
    return {
      encryptedData,
      keyMaterial: {
        cryptoAlg: "ECDH",
        curve: "Curve25519",
        dhPublicKey: {
          expiry: expiryDate.toISOString(),
          parameters: "Curve25519/32byte random key",
          keyValue: publicKey,
        },
        nonce,
      },
    };
  } catch (error) {
    console.error("Error creating ABDM envelope:", error);
    throw new Error("Failed to create ABDM envelope");
  }
}
