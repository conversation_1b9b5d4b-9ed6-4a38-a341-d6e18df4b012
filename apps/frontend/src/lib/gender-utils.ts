/**
 * Gender utility functions for transforming gender codes to display values
 */

/**
 * Transform gender code to display value
 * @param gender - Gender code (M, F, O, male, female, other, etc.)
 * @returns Display value (Male, Female, Other)
 */
export function formatGenderDisplay(gender?: string | null): string {
  if (!gender) return "Not specified";

  const normalizedGender = gender.trim().toUpperCase();

  switch (normalizedGender) {
    case "M":
    case "MALE":
      return "Male";
    case "F":
    case "FEMALE":
      return "Female";
    case "O":
    case "OTHER":
      return "Other";
    default:
      // Fallback: capitalize first letter
      return gender.charAt(0).toUpperCase() + gender.slice(1).toLowerCase();
  }
}

/**
 * Transform gender display value back to code for database storage
 * @param displayValue - Display value (Male, Female, Other)
 * @returns Gender code (male, female, other)
 */
export function formatGenderCode(displayValue?: string | null): string {
  if (!displayValue) return "other";

  const normalizedValue = displayValue.trim().toLowerCase();

  switch (normalizedValue) {
    case "male":
      return "male";
    case "female":
      return "female";
    case "other":
      return "other";
    default:
      return "other";
  }
}
