/**
 * OTP Service for generating, storing, and verifying OTPs
 */

import { db } from "@/lib/db";
import { smsService } from "@/lib/sms/smsService";
import { logger } from "@/lib/logger";

// Constants
const OTP_EXPIRY_MINUTES = 10;
const OTP_LENGTH = 6;

/**
 * Generate a random numeric OTP of specified length
 *
 * @param length Length of the OTP (default: 6)
 * @returns A random numeric OTP
 */
export function generateOTP(length: number = OTP_LENGTH): string {
  // Generate a random number with the specified number of digits
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return Math.floor(min + Math.random() * (max - min + 1)).toString();
}

/**
 * Calculate the expiry time for an OTP
 *
 * @param minutes Minutes until expiry (default: 10)
 * @returns Date object representing the expiry time
 */
export function calculateExpiryTime(
  minutes: number = OTP_EXPIRY_MINUTES,
): Date {
  const expiryTime = new Date();
  expiryTime.setMinutes(expiryTime.getMinutes() + minutes);
  return expiryTime;
}

/**
 * Format a mobile number for display (mask all but last 4 digits)
 *
 * @param mobile The mobile number to format
 * @returns Formatted mobile number with last 4 digits visible
 */
export function formatMobileForDisplay(mobile: string): string {
  // Remove any non-digit characters
  const digitsOnly = mobile.replace(/\D/g, "");

  // Get the last 4 digits
  const lastFourDigits = digitsOnly.slice(-4);

  // Replace all but the last 4 digits with X
  const maskedPart = "X".repeat(digitsOnly.length - 4);

  return `${maskedPart}${lastFourDigits}`;
}

/**
 * Send an OTP to a mobile number
 *
 * @param params Parameters for sending OTP
 * @returns Result of the OTP sending operation
 */
export async function sendOTP({
  mobile,
  purpose,
  patientId,
  userId,
  expiryMinutes = OTP_EXPIRY_MINUTES,
}: {
  mobile: string;
  purpose: string;
  patientId?: string;
  userId?: string;
  expiryMinutes?: number;
}) {
  try {
    // Generate a new OTP
    const otp = generateOTP();
    const expiresAt = calculateExpiryTime(expiryMinutes);

    // Store the OTP in the database
    const otpRecord = await db.mobileOtp.create({
      data: {
        mobile,
        otp,
        purpose,
        expiresAt,
        patientId,
        userId,
      },
    });

    // Prepare the SMS message
    const message = `Your verification code is: ${otp}. It will expire in ${expiryMinutes} minutes. Do not share this code with anyone.`;

    // Send the SMS
    const smsResult = await smsService.send({
      to: mobile,
      body: message,
    });

    if (smsResult.error) {
      logger.error("Failed to send OTP SMS", {
        error: smsResult.error,
        mobile: formatMobileForDisplay(mobile),
        purpose,
      });

      return {
        success: false,
        error: smsResult.error,
        otpId: otpRecord.id,
      };
    }

    logger.info("OTP SMS sent successfully", {
      mobile: formatMobileForDisplay(mobile),
      purpose,
      messageId: smsResult.messageId,
    });

    return {
      success: true,
      message: "OTP sent successfully",
      otpId: otpRecord.id,
      expiresAt,
    };
  } catch (error) {
    logger.error("Error sending OTP", {
      error: error instanceof Error ? error.message : String(error),
      mobile: formatMobileForDisplay(mobile),
      purpose,
    });

    return {
      success: false,
      error: `Failed to send OTP: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Verify an OTP
 *
 * @param params Parameters for verifying OTP
 * @returns Result of the OTP verification
 */
export async function verifyOTP({
  mobile,
  otp,
  purpose,
}: {
  mobile: string;
  otp: string;
  purpose: string;
}) {
  try {
    // Find the most recent unexpired OTP for this mobile and purpose
    const otpRecord = await db.mobileOtp.findFirst({
      where: {
        mobile,
        purpose,
        expiresAt: {
          gt: new Date(),
        },
        verified: false,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // If no OTP record found
    if (!otpRecord) {
      return {
        success: false,
        error: "OTP expired or not found",
      };
    }

    // Check if max attempts exceeded
    if (otpRecord.attempts >= otpRecord.maxAttempts) {
      return {
        success: false,
        error: "Maximum verification attempts exceeded",
      };
    }

    // Increment the attempts counter
    await db.mobileOtp.update({
      where: { id: otpRecord.id },
      data: { attempts: { increment: 1 } },
    });

    // Check if OTP matches
    if (otpRecord.otp !== otp) {
      return {
        success: false,
        error: "Invalid OTP",
        remainingAttempts: otpRecord.maxAttempts - (otpRecord.attempts + 1),
      };
    }

    // Mark OTP as verified
    await db.mobileOtp.update({
      where: { id: otpRecord.id },
      data: { verified: true },
    });

    return {
      success: true,
      message: "OTP verified successfully",
    };
  } catch (error) {
    logger.error("Error verifying OTP", {
      error: error instanceof Error ? error.message : String(error),
      mobile: formatMobileForDisplay(mobile),
      purpose,
    });

    return {
      success: false,
      error: `Failed to verify OTP: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}
