import { cookies } from "next/headers";

export interface OrganizationContext {
  organizationId: string;
  organizationName: string;
  organizationSlug?: string;
  currentRole?: string;
  currentBranchId?: string;
  currentBranchName?: string;
  availableRoles?: string[];
  availableBranches?: Array<{
    id: string;
    name: string;
    isHeadOffice: boolean;
  }>;
}

export interface UserInfo {
  id: string;
  name: string;
  email: string;
  role: string;
  organizationId: string;
  organizationName: string;
  organizationSlug?: string;
  currentBranchId?: string | null;
}

/**
 * Get current organization context from cookies
 */
export function getCurrentOrganizationContext(): OrganizationContext | null {
  try {
    const cookieStore = cookies();

    // Get user-info cookie (primary source)
    const userInfoCookie = cookieStore.get("user-info")?.value;
    const currentRoleCookie = cookieStore.get("current-role")?.value;
    const currentBranchCookie = cookieStore.get("current-branch")?.value;

    if (!userInfoCookie) {
      return null;
    }

    const userInfo: UserInfo = JSON.parse(decodeURIComponent(userInfoCookie));

    let currentBranch = null;
    if (currentBranchCookie) {
      try {
        currentBranch = JSON.parse(currentBranchCookie);
      } catch (error) {
        console.error("Error parsing current branch cookie:", error);
      }
    }

    return {
      organizationId: userInfo.organizationId,
      organizationName: userInfo.organizationName,
      organizationSlug: userInfo.organizationSlug,
      currentRole: currentRoleCookie || userInfo.role,
      currentBranchId: currentBranch?.id || userInfo.currentBranchId,
      currentBranchName: currentBranch?.name,
    };
  } catch (error) {
    console.error("Error getting organization context:", error);
    return null;
  }
}

/**
 * Update organization context cookies
 */
export function updateOrganizationContext(context: {
  organizationId: string;
  organizationName: string;
  organizationSlug?: string;
  role?: string;
  branchId?: string;
  branchName?: string;
  branchIsHeadOffice?: boolean;
  userInfo?: UserInfo;
}) {
  try {
    const cookieStore = cookies();

    // Get current user info
    let currentUserInfo: UserInfo | null = null;
    const userInfoCookie = cookieStore.get("user-info")?.value;

    if (userInfoCookie) {
      try {
        currentUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      } catch (error) {
        console.error("Error parsing current user info:", error);
      }
    }

    // Update user-info cookie with new organization context
    const updatedUserInfo: UserInfo = {
      id: currentUserInfo?.id || context.userInfo?.id || "",
      name: currentUserInfo?.name || context.userInfo?.name || "",
      email: currentUserInfo?.email || context.userInfo?.email || "",
      role: context.role || currentUserInfo?.role || "",
      organizationId: context.organizationId,
      organizationName: context.organizationName,
      organizationSlug: context.organizationSlug,
      currentBranchId: context.branchId || null,
    };

    const cookieOptions = {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax" as const,
      maxAge: 30 * 24 * 60 * 60, // 30 days
    };

    // Set user-info cookie
    cookieStore.set(
      "user-info",
      JSON.stringify(updatedUserInfo),
      cookieOptions,
    );

    // Set current role cookie
    if (context.role) {
      cookieStore.set("current-role", context.role, cookieOptions);
    }

    // Set current branch cookie
    if (context.branchId && context.branchName) {
      const branchInfo = {
        id: context.branchId,
        name: context.branchName,
        isHeadOffice: context.branchIsHeadOffice || false,
      };
      cookieStore.set(
        "current-branch",
        JSON.stringify(branchInfo),
        cookieOptions,
      );
    }

    // Set organization-specific context cookie for easy access
    const orgContext = {
      organizationId: context.organizationId,
      organizationName: context.organizationName,
      organizationSlug: context.organizationSlug,
      currentRole: context.role,
      currentBranchId: context.branchId,
      currentBranchName: context.branchName,
    };

    cookieStore.set("org-context", JSON.stringify(orgContext), cookieOptions);

    console.log("Organization context updated:", {
      organizationId: context.organizationId,
      organizationName: context.organizationName,
      role: context.role,
      branchId: context.branchId,
      branchName: context.branchName,
    });

    return true;
  } catch (error) {
    console.error("Error updating organization context:", error);
    return false;
  }
}

/**
 * Clear organization context cookies
 */
export function clearOrganizationContext() {
  try {
    const cookieStore = cookies();

    // Clear organization-specific cookies
    cookieStore.delete("current-role");
    cookieStore.delete("current-branch");
    cookieStore.delete("org-context");

    console.log("Organization context cleared");
    return true;
  } catch (error) {
    console.error("Error clearing organization context:", error);
    return false;
  }
}

/**
 * Get user info from cookies
 */
export function getUserInfoFromCookies(): UserInfo | null {
  try {
    const cookieStore = cookies();
    const userInfoCookie = cookieStore.get("user-info")?.value;

    if (!userInfoCookie) {
      return null;
    }

    return JSON.parse(decodeURIComponent(userInfoCookie));
  } catch (error) {
    console.error("Error getting user info from cookies:", error);
    return null;
  }
}

/**
 * Check if user has valid session
 */
export function hasValidSession(): boolean {
  try {
    const cookieStore = cookies();
    const sessionToken = cookieStore.get("session-token")?.value;
    const userInfo = getUserInfoFromCookies();

    return !!(sessionToken && userInfo && userInfo.email);
  } catch (error) {
    console.error("Error checking session validity:", error);
    return false;
  }
}
