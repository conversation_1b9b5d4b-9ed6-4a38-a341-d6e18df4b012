import crypto from "crypto";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const NEXT_PUBLIC_ABDM_CLIENT_ID = process.env.NEXT_PUBLIC_ABDM_CLIENT_ID;
const ABDM_CLIENT_SECRET = process.env.NEXT_PUBLIC_ABDM_CLIENT_SECRET;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

// Cache for access token
let cachedToken: { token: string; expiresAt: number } | null = null;

/**
 * Get access token for ABDM API
 * @returns Access token and expiry
 */
export async function getAccessToken(): Promise<{
  accessToken: string;
  expiresAt: number;
}> {
  // Check if we have a valid cached token
  const now = Date.now();
  if (cachedToken && cachedToken.expiresAt > now) {
    return { accessToken: cachedToken.token, expiresAt: cachedToken.expiresAt };
  }

  // Check if required environment variables are set
  if (!NEXT_PUBLIC_ABDM_CLIENT_ID || !ABDM_CLIENT_SECRET) {
    throw new Error("ABDM client credentials not configured");
  }

  try {
    const requestId = crypto.randomUUID();
    const timestamp = new Date().toISOString();

    const payload = {
      clientId: NEXT_PUBLIC_ABDM_CLIENT_ID,
      clientSecret: ABDM_CLIENT_SECRET,
      grantType: "client_credentials",
    };

    // Create an AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 120000); // 120 seconds timeout

    try {
      const response = await fetch(
        `${ABDM_BASE_URL}/hiecm/gateway/v3/sessions`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "REQUEST-ID": requestId,
            TIMESTAMP: timestamp,
            "X-CM-ID": ABDM_CM_ID,
          },
          body: JSON.stringify(payload),
          signal: controller.signal,
        },
      );

      // Clear the timeout since the request completed
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = "Failed to get access token";
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          if (errorText) {
            errorMessage = errorText;
          }
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();

      if (!data.accessToken) {
        throw new Error("Access token not found in response");
      }

      // Calculate expiry time (default to 30 minutes if not provided)
      const expiresIn = data.expiresIn || 1800;
      const expiresAt = now + expiresIn * 1000;

      // Cache the token
      cachedToken = {
        token: data.accessToken,
        expiresAt: expiresAt,
      };

      return { accessToken: data.accessToken, expiresAt };
    } catch (error) {
      // Clear the timeout if there was an error
      clearTimeout(timeoutId);

      // Check if this is an AbortError (timeout)
      if (error instanceof DOMException && error.name === "AbortError") {
        console.error("ABDM authentication request timed out");
        throw new Error(
          "ABDM authentication request timed out after 120 seconds",
        );
      }

      // Rethrow other errors
      throw error;
    }
  } catch (error) {
    console.error("Error getting ABDM access token:", error);
    throw error;
  }
}
