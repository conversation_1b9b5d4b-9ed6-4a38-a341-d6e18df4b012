/**
 * Organization-level role management utilities
 * Handles role operations for the new organization-based role system
 */

import { prisma } from "@/lib/prisma";

// Define available roles
export const AVAILABLE_ROLES = [
  "superAdmin",
  "hospitalAdmin",
  "branchAdmin",
  "doctor",
  "nurse",
  "staff",
  "receptionist",
  "pharmacist",
  "technician",
] as const;

export type UserRole = (typeof AVAILABLE_ROLES)[number];

// Role hierarchy for permission checking
export const ROLE_HIERARCHY: Record<UserRole, number> = {
  superAdmin: 1000,
  hospitalAdmin: 100,
  branchAdmin: 80,
  doctor: 60,
  nurse: 40,
  staff: 30,
  receptionist: 20,
  pharmacist: 20,
  technician: 10,
};

/**
 * Get user's roles for a specific organization
 */
export async function getUserOrganizationRoles(
  userId: string,
  organizationId: string,
): Promise<UserRole[]> {
  try {
    const userOrg = await prisma.userOrganization.findUnique({
      where: {
        userId_organizationId: {
          userId,
          organizationId,
        },
      },
    });
    console.log("User organization roles:", userOrg);

    if (!userOrg) {
      return [];
    }

    // Handle both new JSON format and legacy single role
    if (Array.isArray(userOrg.roles)) {
      return userOrg.roles as UserRole[];
    }

    // Legacy support - if roles is a string, convert to array
    if (typeof userOrg.roles === "string") {
      return [userOrg.roles as UserRole];
    }

    return [];
  } catch (error) {
    console.error("Error getting user organization roles:", error);
    return [];
  }
}

/**
 * Check if user has a specific role in an organization
 */
export async function userHasRole(
  userId: string,
  organizationId: string,
  role: UserRole,
): Promise<boolean> {
  const roles = await getUserOrganizationRoles(userId, organizationId);
  return roles.includes(role);
}

/**
 * Check if user has any of the specified roles in an organization
 */
export async function userHasAnyRole(
  userId: string,
  organizationId: string,
  roles: UserRole[],
): Promise<boolean> {
  const userRoles = await getUserOrganizationRoles(userId, organizationId);
  return roles.some((role) => userRoles.includes(role));
}

/**
 * Check if user has sufficient permission level
 */
export async function userHasPermissionLevel(
  userId: string,
  organizationId: string,
  requiredLevel: number,
): Promise<boolean> {
  const userRoles = await getUserOrganizationRoles(userId, organizationId);
  const userMaxLevel = Math.max(
    ...userRoles.map((role) => ROLE_HIERARCHY[role] || 0),
  );
  return userMaxLevel >= requiredLevel;
}

/**
 * Add roles to user in an organization
 */
export async function addUserOrganizationRoles(
  userId: string,
  organizationId: string,
  newRoles: UserRole[],
): Promise<void> {
  try {
    const existingUserOrg = await prisma.userOrganization.findUnique({
      where: {
        userId_organizationId: {
          userId,
          organizationId,
        },
      },
    });

    if (existingUserOrg) {
      // Merge with existing roles
      const currentRoles = Array.isArray(existingUserOrg.roles)
        ? (existingUserOrg.roles as UserRole[])
        : [];

      const updatedRoles = [...new Set([...currentRoles, ...newRoles])];

      await prisma.userOrganization.update({
        where: {
          userId_organizationId: {
            userId,
            organizationId,
          },
        },
        data: {
          roles: updatedRoles,
        },
      });
    } else {
      // Create new UserOrganization entry
      await prisma.userOrganization.create({
        data: {
          userId,
          organizationId,
          roles: newRoles,
          isDefault: true, // First organization is default
        },
      });
    }
  } catch (error) {
    console.error("Error adding user organization roles:", error);
    throw error;
  }
}

/**
 * Remove roles from user in an organization
 */
export async function removeUserOrganizationRoles(
  userId: string,
  organizationId: string,
  rolesToRemove: UserRole[],
): Promise<void> {
  try {
    const existingUserOrg = await prisma.userOrganization.findUnique({
      where: {
        userId_organizationId: {
          userId,
          organizationId,
        },
      },
    });

    if (!existingUserOrg) {
      return; // Nothing to remove
    }

    const currentRoles = Array.isArray(existingUserOrg.roles)
      ? (existingUserOrg.roles as UserRole[])
      : [];

    const updatedRoles = currentRoles.filter(
      (role) => !rolesToRemove.includes(role),
    );

    if (updatedRoles.length === 0) {
      // Remove the entire UserOrganization entry if no roles left
      await prisma.userOrganization.delete({
        where: {
          userId_organizationId: {
            userId,
            organizationId,
          },
        },
      });
    } else {
      await prisma.userOrganization.update({
        where: {
          userId_organizationId: {
            userId,
            organizationId,
          },
        },
        data: {
          roles: updatedRoles,
        },
      });
    }
  } catch (error) {
    console.error("Error removing user organization roles:", error);
    throw error;
  }
}

/**
 * Get all organizations where user has any role
 */
export async function getUserOrganizations(userId: string) {
  try {
    return await prisma.userOrganization.findMany({
      where: { userId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
            logo: true,
            status: true,
          },
        },
      },
      orderBy: [{ isDefault: "desc" }, { createdAt: "asc" }],
    });
  } catch (error) {
    console.error("Error getting user organizations:", error);
    return [];
  }
}

/**
 * Set default organization for user
 */
export async function setDefaultOrganization(
  userId: string,
  organizationId: string,
): Promise<void> {
  try {
    // Remove default from all organizations
    await prisma.userOrganization.updateMany({
      where: { userId },
      data: { isDefault: false },
    });

    // Set new default
    await prisma.userOrganization.update({
      where: {
        userId_organizationId: {
          userId,
          organizationId,
        },
      },
      data: { isDefault: true },
    });
  } catch (error) {
    console.error("Error setting default organization:", error);
    throw error;
  }
}

/**
 * Get user's default organization
 */
export async function getUserDefaultOrganization(userId: string) {
  try {
    return await prisma.userOrganization.findFirst({
      where: {
        userId,
        isDefault: true,
      },
      include: {
        organization: true,
      },
    });
  } catch (error) {
    console.error("Error getting user default organization:", error);
    return null;
  }
}

/**
 * Check if user can manage roles (admin permissions)
 */
export async function canManageRoles(
  userId: string,
  organizationId: string,
): Promise<boolean> {
  return await userHasAnyRole(userId, organizationId, [
    "hospitalAdmin",
    "branchAdmin",
  ]);
}

/**
 * Get role display name
 */
export function getRoleDisplayName(role: UserRole): string {
  const roleNames: Record<UserRole, string> = {
    superAdmin: "Super Admin",
    hospitalAdmin: "Hospital Admin",
    branchAdmin: "Branch Admin",
    doctor: "Doctor",
    nurse: "Nurse",
    staff: "Staff",
    receptionist: "Receptionist",
    pharmacist: "Pharmacist",
    technician: "Technician",
  };

  return roleNames[role] || role;
}

/**
 * Get role color for UI
 */
export function getRoleColor(role: UserRole): string {
  const roleColors: Record<UserRole, string> = {
    superAdmin: "text-red-600 bg-red-50 border-red-200",
    hospitalAdmin: "text-blue-600 bg-blue-50 border-blue-200",
    branchAdmin: "text-green-600 bg-green-50 border-green-200",
    doctor: "text-purple-600 bg-purple-50 border-purple-200",
    nurse: "text-pink-600 bg-pink-50 border-pink-200",
    staff: "text-orange-600 bg-orange-50 border-orange-200",
    receptionist: "text-yellow-600 bg-yellow-50 border-yellow-200",
    pharmacist: "text-indigo-600 bg-indigo-50 border-indigo-200",
    technician: "text-gray-600 bg-gray-50 border-gray-200",
  };

  return roleColors[role] || "text-gray-600 bg-gray-50 border-gray-200";
}
