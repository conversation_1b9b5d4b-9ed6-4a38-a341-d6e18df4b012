/**
 * <PERSON><PERSON> Logger
 *
 * This module provides logging utilities for ABDM operations.
 * It includes structured logging for API requests, responses, and errors.
 * Integrated with Datadog for production monitoring.
 */

import { datadogLogger } from "./datadog-logger";

// Log levels
export enum LogLevel {
  DEBUG = "DEBUG",
  INFO = "INFO",
  WARN = "WARN",
  ERROR = "ERROR",
}

// Log categories
export enum LogCategory {
  API_REQUEST = "API_REQUEST",
  API_RESPONSE = "API_RESPONSE",
  API_ERROR = "API_ERROR",
  AUTHENTICATION = "AUTHENTICATION",
  OTP = "OTP",
  AADHAAR = "AADHAAR",
  MOBILE = "MOBILE",
  ABHA = "ABHA",
  CONSENT = "CONSENT",
  HEALTH_RECORD = "HEALTH_RECORD",
  FHIR = "FHIR",
  DATABASE = "DATABASE",
  PERFORMANCE = "PERFORMANCE",
  GENERAL = "GENERAL",
}

// Log entry interface
interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: any;
  error?: Error | unknown;
  requestId?: string;
  patientId?: string;
  endpoint?: string;
}

// Mask sensitive data in logs
function maskSensitiveData(data: any): any {
  if (!data) return data;

  // Create a deep copy to avoid modifying the original object
  const maskedData = JSON.parse(JSON.stringify(data));

  // List of sensitive fields to mask
  const sensitiveFields = [
    "aadhaar",
    "aadhar",
    "aadhaarNumber",
    "mobile",
    "mobileNumber",
    "phoneNumber",
    "otp",
    "otpValue",
    "password",
    "token",
    "accessToken",
    "refreshToken",
    "xToken",
    "Authorization",
    "loginId",
    "healthId",
    "healthIdNumber",
  ];

  // Function to recursively mask sensitive fields
  function maskRecursively(obj: any) {
    if (typeof obj !== "object" || obj === null) return;

    Object.keys(obj).forEach((key) => {
      // Check if the key is sensitive
      if (
        sensitiveFields.some((field) =>
          key.toLowerCase().includes(field.toLowerCase()),
        )
      ) {
        // Mask the value based on its type
        if (typeof obj[key] === "string") {
          const value = obj[key];
          if (value.length <= 4) {
            obj[key] = "****";
          } else {
            // Keep first 2 and last 2 characters, mask the rest
            obj[key] =
              value.substring(0, 2) +
              "*".repeat(value.length - 4) +
              value.substring(value.length - 2);
          }
        } else if (typeof obj[key] === "number") {
          obj[key] = "****";
        }
      } else if (typeof obj[key] === "object" && obj[key] !== null) {
        // Recursively process nested objects
        maskRecursively(obj[key]);
      }
    });
  }

  maskRecursively(maskedData);
  return maskedData;
}

// Main logger function
export function abdmLog(entry: Omit<LogEntry, "timestamp">): void {
  const timestamp = new Date().toISOString();
  const logEntry: LogEntry = {
    timestamp,
    ...entry,
  };

  // Mask sensitive data if present
  if (logEntry.data) {
    logEntry.data = maskSensitiveData(logEntry.data);
  }

  // Format the log message
  const formattedMessage = `[ABDM][${logEntry.level}][${logEntry.category}] ${logEntry.message}`;

  // Log based on level
  switch (logEntry.level) {
    case LogLevel.DEBUG:
      console.debug(formattedMessage, {
        ...logEntry,
        message: undefined, // Avoid duplicate message
      });
      break;
    case LogLevel.INFO:
      console.info(formattedMessage, {
        ...logEntry,
        message: undefined,
      });
      break;
    case LogLevel.WARN:
      console.warn(formattedMessage, {
        ...logEntry,
        message: undefined,
      });
      break;
    case LogLevel.ERROR:
      console.error(formattedMessage, {
        ...logEntry,
        message: undefined,
      });
      break;
  }

  // Send to Datadog for production monitoring
  try {
    const datadogLevel = logEntry.level.toLowerCase() as
      | "debug"
      | "info"
      | "warn"
      | "error";
    const tags = {
      "abdm.category": logEntry.category,
      ...(logEntry.endpoint && { "abdm.endpoint": logEntry.endpoint }),
      ...(logEntry.patientId && { "abdm.patient_id": logEntry.patientId }),
    };

    const attributes = {
      category: logEntry.category,
      ...(logEntry.data && { data: logEntry.data }),
      ...(logEntry.requestId && { requestId: logEntry.requestId }),
      ...(logEntry.patientId && { patientId: logEntry.patientId }),
      ...(logEntry.endpoint && { endpoint: logEntry.endpoint }),
    };

    if (datadogLevel === "error" && logEntry.error) {
      datadogLogger.error(
        formattedMessage,
        logEntry.error as Error,
        attributes,
        tags,
      );
    } else {
      datadogLogger[datadogLevel](formattedMessage, attributes, tags);
    }
  } catch (error) {
    // Silently handle Datadog logging errors to prevent application disruption
    if (process.env.NODE_ENV === "development") {
      console.error("Failed to send log to Datadog:", error);
    }
  }
}

// Convenience methods for different log levels
export const abdmLogger = {
  debug: (
    category: LogCategory,
    message: string,
    data?: any,
    requestId?: string,
    patientId?: string,
    endpoint?: string,
  ) => {
    abdmLog({
      level: LogLevel.DEBUG,
      category,
      message,
      data,
      requestId,
      patientId,
      endpoint,
    });
  },

  info: (
    category: LogCategory,
    message: string,
    data?: any,
    requestId?: string,
    patientId?: string,
    endpoint?: string,
  ) => {
    abdmLog({
      level: LogLevel.INFO,
      category,
      message,
      data,
      requestId,
      patientId,
      endpoint,
    });
  },

  warn: (
    category: LogCategory,
    message: string,
    data?: any,
    error?: unknown,
    requestId?: string,
    patientId?: string,
    endpoint?: string,
  ) => {
    abdmLog({
      level: LogLevel.WARN,
      category,
      message,
      data,
      error,
      requestId,
      patientId,
      endpoint,
    });
  },

  error: (
    category: LogCategory,
    message: string,
    error?: unknown,
    data?: any,
    requestId?: string,
    patientId?: string,
    endpoint?: string,
  ) => {
    abdmLog({
      level: LogLevel.ERROR,
      category,
      message,
      data,
      error,
      requestId,
      patientId,
      endpoint,
    });
  },

  // Log API request
  request: (
    endpoint: string,
    method: string,
    payload?: any,
    requestId?: string,
    patientId?: string,
  ) => {
    abdmLog({
      level: LogLevel.INFO,
      category: LogCategory.API_REQUEST,
      message: `${method} ${endpoint}`,
      data: payload,
      requestId,
      patientId,
      endpoint,
    });
  },

  // Log API response
  response: (
    endpoint: string,
    method: string,
    statusCode: number,
    responseData?: any,
    requestId?: string,
    patientId?: string,
  ) => {
    abdmLog({
      level: statusCode >= 400 ? LogLevel.ERROR : LogLevel.INFO,
      category: LogCategory.API_RESPONSE,
      message: `${method} ${endpoint} - Status: ${statusCode}`,
      data: responseData,
      requestId,
      patientId,
      endpoint,
    });
  },

  // Log API error
  apiError: (
    endpoint: string,
    method: string,
    error: unknown,
    requestId?: string,
    patientId?: string,
  ) => {
    abdmLog({
      level: LogLevel.ERROR,
      category: LogCategory.API_ERROR,
      message: `${method} ${endpoint} - Error`,
      error,
      requestId,
      patientId,
      endpoint,
    });
  },
};
