import { db } from "@/lib/db";

/**
 * Store generated FHIR bundles in the database for later reuse
 * @param bundlesResult - The result from generateAllBundles
 * @param consultationId - Consultation ID
 * @param patientId - Patient ID
 * @param organizationId - Organization ID
 * @param consentId - Optional consent ID
 * @returns Array of stored bundle records
 */
export async function storeFhirBundles(
  bundlesResult: any,
  consultationId: string,
  patientId: string,
  organizationId: string,
  consentId?: string,
) {
  const storedBundles = [];
  const timestamp = Date.now();

  try {
    // Store WellnessRecord bundle
    if (bundlesResult.wellnessRecord) {
      const wellnessBundle = await db.fhirBundle.create({
        data: {
          bundleId: `wellness-${consultationId}-${timestamp}`,
          consultationId: consultationId,
          patientId: patientId,
          consentId: consentId || null,
          bundleType: "WellnessRecord",
          bundleJson: bundlesResult.wellnessRecord,
          status: "generated",
          organizationId: organizationId,
        },
      });
      storedBundles.push(wellnessBundle);
    }

    // Store OPConsultation bundle
    if (bundlesResult.opConsultNote) {
      const opConsultBundle = await db.fhirBundle.create({
        data: {
          bundleId: `opconsult-${consultationId}-${timestamp}`,
          consultationId: consultationId,
          patientId: patientId,
          consentId: consentId || null,
          bundleType: "OPConsultation",
          bundleJson: bundlesResult.opConsultNote,
          status: "generated",
          organizationId: organizationId,
        },
      });
      storedBundles.push(opConsultBundle);
    }

    // Store Prescription bundle
    if (bundlesResult.prescription) {
      const prescriptionBundle = await db.fhirBundle.create({
        data: {
          bundleId: `prescription-${consultationId}-${timestamp}`,
          consultationId: consultationId,
          patientId: patientId,
          consentId: consentId || null,
          bundleType: "Prescription",
          bundleJson: bundlesResult.prescription,
          status: "generated",
          organizationId: organizationId,
        },
      });
      storedBundles.push(prescriptionBundle);
    }

    // Store Invoice bundle
    if (bundlesResult.invoice) {
      const invoiceBundle = await db.fhirBundle.create({
        data: {
          bundleId: `invoice-${consultationId}-${timestamp}`,
          consultationId: consultationId,
          patientId: patientId,
          consentId: consentId || null,
          bundleType: "Invoice",
          bundleJson: bundlesResult.invoice,
          status: "generated",
          organizationId: organizationId,
        },
      });
      storedBundles.push(invoiceBundle);
    }

    // Store DiagnosticReport bundle
    if (bundlesResult.diagnosticReport) {
      const diagnosticBundle = await db.fhirBundle.create({
        data: {
          bundleId: `diagnostic-${consultationId}-${timestamp}`,
          consultationId: consultationId,
          patientId: patientId,
          consentId: consentId || null,
          bundleType: "DiagnosticReport",
          bundleJson: bundlesResult.diagnosticReport,
          status: "generated",
          organizationId: organizationId,
        },
      });
      storedBundles.push(diagnosticBundle);
    }

    // Store ImmunizationRecord bundle
    if (bundlesResult.immunizationRecord) {
      const immunizationBundle = await db.fhirBundle.create({
        data: {
          bundleId: `immunization-${consultationId}-${timestamp}`,
          consultationId: consultationId,
          patientId: patientId,
          consentId: consentId || null,
          bundleType: "ImmunizationRecord",
          bundleJson: bundlesResult.immunizationRecord,
          status: "generated",
          organizationId: organizationId,
        },
      });
      storedBundles.push(immunizationBundle);
    }

    // Store DischargeSummary bundle
    if (bundlesResult.dischargeSummary) {
      const dischargeBundle = await db.fhirBundle.create({
        data: {
          bundleId: `discharge-${consultationId}-${timestamp}`,
          consultationId: consultationId,
          patientId: patientId,
          consentId: consentId || null,
          bundleType: "DischargeSummary",
          bundleJson: bundlesResult.dischargeSummary,
          status: "generated",
          organizationId: organizationId,
        },
      });
      storedBundles.push(dischargeBundle);
    }

    console.log(`✅ Stored ${storedBundles.length} FHIR bundles in database`, {
      consultationId,
      patientId,
      organizationId,
      consentId,
      bundleTypes: storedBundles.map((b) => b.bundleType),
    });

    return storedBundles;
  } catch (error) {
    console.error("❌ Error storing FHIR bundles:", error);
    throw error;
  }
}

/**
 * Retrieve stored FHIR bundles based on hiTypes
 * @param consultationId - Consultation ID
 * @param hiTypes - Array of health information types to retrieve
 * @param consentId - Optional consent ID for filtering (not used for filtering, just for logging)
 * @returns Array of stored bundle records
 */
export async function getStoredFhirBundles(
  consultationId: string,
  hiTypes: string[],
  consentId?: string,
) {
  try {
    // Filter only by consultationId and hiTypes, NOT by consentId
    // because the stored bundles may have different consentIds
    const whereClause: any = {
      consultationId: consultationId,
      bundleType: {
        in: hiTypes,
      },
      status: {
        in: ["generated", "uploaded"],
      },
    };

    const storedBundles = await db.fhirBundle.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
    });

    console.log(`🔍 Retrieved ${storedBundles.length} stored FHIR bundles`, {
      consultationId,
      requestedConsentId: consentId,
      requestedHiTypes: hiTypes,
      foundBundles: storedBundles.map((b) => ({
        bundleType: b.bundleType,
        bundleId: b.bundleId,
        storedConsentId: b.consentId,
        status: b.status,
      })),
    });

    return storedBundles;
  } catch (error) {
    console.error("❌ Error retrieving stored FHIR bundles:", error);
    throw error;
  }
}
