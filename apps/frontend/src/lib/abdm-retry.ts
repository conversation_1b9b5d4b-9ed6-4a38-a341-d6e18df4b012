/**
 * ABDM Retry Utility
 *
 * This module provides robust retry mechanisms for ABDM API calls.
 * It includes exponential backoff, jitter, and circuit breaking.
 */

import { shouldRetryAbdmError, calculateRetryDelay } from "./abdm-error-utils";

// Circuit breaker state
interface CircuitBreakerState {
  failures: number;
  lastFailure: number;
  status: "CLOSED" | "OPEN" | "HALF_OPEN";
}

// Circuit breaker configuration
interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
}

// Circuit breaker map for different endpoints
const circuitBreakers: Map<string, CircuitBreakerState> = new Map();

// Default circuit breaker configuration
const defaultCircuitBreakerConfig: CircuitBreakerConfig = {
  failureThreshold: 5, // Number of failures before opening the circuit
  resetTimeout: 60000, // 1 minute timeout before trying again
};

/**
 * Check if the circuit is open for a given endpoint
 * @param endpoint - API endpoint
 * @param config - Circuit breaker configuration
 * @returns Boolean indicating if the circuit is open
 */
function isCircuitOpen(
  endpoint: string,
  config: CircuitBreakerConfig = defaultCircuitBreakerConfig,
): boolean {
  const circuitBreaker = circuitBreakers.get(endpoint);

  if (!circuitBreaker) {
    // Initialize circuit breaker if it doesn't exist
    circuitBreakers.set(endpoint, {
      failures: 0,
      lastFailure: 0,
      status: "CLOSED",
    });
    return false;
  }

  // If circuit is OPEN, check if reset timeout has passed
  if (circuitBreaker.status === "OPEN") {
    const now = Date.now();
    if (now - circuitBreaker.lastFailure > config.resetTimeout) {
      // Move to HALF_OPEN state to test if the service is back
      circuitBreaker.status = "HALF_OPEN";
      return false;
    }
    return true;
  }

  return false;
}

/**
 * Record a successful API call
 * @param endpoint - API endpoint
 */
function recordSuccess(endpoint: string): void {
  const circuitBreaker = circuitBreakers.get(endpoint);

  if (circuitBreaker) {
    if (circuitBreaker.status === "HALF_OPEN") {
      // Reset the circuit breaker on successful call in HALF_OPEN state
      circuitBreaker.failures = 0;
      circuitBreaker.status = "CLOSED";
    } else if (circuitBreaker.status === "CLOSED") {
      // Reset failure count on successful call
      circuitBreaker.failures = 0;
    }
  }
}

/**
 * Record a failed API call
 * @param endpoint - API endpoint
 * @param config - Circuit breaker configuration
 */
function recordFailure(
  endpoint: string,
  config: CircuitBreakerConfig = defaultCircuitBreakerConfig,
): void {
  let circuitBreaker = circuitBreakers.get(endpoint);

  if (!circuitBreaker) {
    circuitBreaker = {
      failures: 0,
      lastFailure: 0,
      status: "CLOSED",
    };
    circuitBreakers.set(endpoint, circuitBreaker);
  }

  circuitBreaker.failures += 1;
  circuitBreaker.lastFailure = Date.now();

  // Check if we need to open the circuit
  if (
    circuitBreaker.status !== "OPEN" &&
    circuitBreaker.failures >= config.failureThreshold
  ) {
    circuitBreaker.status = "OPEN";
  }
}

/**
 * Retry a function with exponential backoff and circuit breaking
 * @param fn - Function to retry
 * @param endpoint - API endpoint for circuit breaking
 * @param maxRetries - Maximum number of retries
 * @param baseDelay - Base delay in milliseconds
 * @param maxDelay - Maximum delay in milliseconds
 * @returns Promise with the function result
 */
export async function retryWithExponentialBackoff<T>(
  fn: () => Promise<T>,
  endpoint: string,
  maxRetries: number = 1,
  baseDelay: number = 1000,
  maxDelay: number = 30000,
): Promise<T> {
  // Check if circuit is open
  if (isCircuitOpen(endpoint)) {
    throw new Error(
      `Service ${endpoint} is temporarily unavailable. Please try again later.`,
    );
  }

  let lastError: unknown;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Execute the function
      const result = await fn();

      // Record success for circuit breaker
      recordSuccess(endpoint);

      return result;
    } catch (error) {
      lastError = error;

      // Check if we should retry
      if (attempt < maxRetries && shouldRetryAbdmError(error)) {
        // Calculate delay with exponential backoff
        const delay = calculateRetryDelay(attempt, baseDelay, maxDelay);

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, delay));
      } else {
        // Record failure for circuit breaker
        recordFailure(endpoint);

        // Throw the last error
        throw error;
      }
    }
  }

  // This should never happen, but TypeScript requires a return statement
  throw lastError;
}

/**
 * Enhanced fetch with retry, timeout, and circuit breaking
 * @param url - URL to fetch
 * @param options - Fetch options
 * @param maxRetries - Maximum number of retries
 * @param baseDelay - Base delay in milliseconds
 * @param retryStatusCodes - HTTP status codes to retry
 * @returns Promise with the fetch response
 */
export async function enhancedFetch(
  url: string,
  options: RequestInit = {},
  maxRetries: number = 1,
  baseDelay: number = 1000,
  retryStatusCodes: number[] = [408, 429, 500, 502, 503, 504, 0],
): Promise<Response> {
  const endpoint = new URL(url).pathname;

  return retryWithExponentialBackoff(
    async () => {
      try {
        // Execute the fetch
        const response = await fetch(url, options);
        // Check if we should retry based on status code
        if (!response.ok && retryStatusCodes.includes(response.status)) {
          throw new Error(
            `HTTP error ${response.status}: ${response.statusText}`,
          );
        }

        return response;
      } catch (error) {
        throw error;
      }
    },
    endpoint,
    maxRetries,
    baseDelay,
  );
}
