import { prisma } from "@/lib/prisma";

/**
 * Get the current organization for a user
 * @param userId The ID of the user
 * @returns The current organization or null if not found
 */
export async function getCurrentOrganization(userId: string) {
  try {
    // First, try to find the default organization for the user
    const userOrganization = await prisma.userOrganization.findFirst({
      where: {
        userId,
        isDefault: true,
      },
      include: {
        organization: true,
      },
    });

    // If a default organization is found, return it
    if (userOrganization) {
      return userOrganization.organization;
    }

    // If no default organization is found, get the first organization the user belongs to
    const firstUserOrganization = await prisma.userOrganization.findFirst({
      where: {
        userId,
      },
      include: {
        organization: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Return the first organization or null if none found
    return firstUserOrganization ? firstUserOrganization.organization : null;
  } catch (error) {
    console.error("Error getting current organization:", error);
    return null;
  }
}

/**
 * Get all organizations for a user
 * @param userId The ID of the user
 * @returns Array of organizations the user belongs to
 */
export async function getUserOrganizations(userId: string) {
  try {
    const userOrganizations = await prisma.userOrganization.findMany({
      where: {
        userId,
      },
      include: {
        organization: true,
      },
      orderBy: {
        isDefault: "desc", // Default organization first
      },
    });

    return userOrganizations.map((uo) => uo.organization);
  } catch (error) {
    console.error("Error getting user organizations:", error);
    return [];
  }
}

/**
 * Set the default organization for a user
 * @param userId The ID of the user
 * @param organizationId The ID of the organization to set as default
 * @returns True if successful, false otherwise
 */
export async function setDefaultOrganization(
  userId: string,
  organizationId: string,
) {
  try {
    // First, unset the current default organization
    await prisma.userOrganization.updateMany({
      where: {
        userId,
        isDefault: true,
      },
      data: {
        isDefault: false,
      },
    });

    // Then, set the new default organization
    await prisma.userOrganization.update({
      where: {
        userId_organizationId: {
          userId,
          organizationId,
        },
      },
      data: {
        isDefault: true,
      },
    });

    return true;
  } catch (error) {
    console.error("Error setting default organization:", error);
    return false;
  }
}
