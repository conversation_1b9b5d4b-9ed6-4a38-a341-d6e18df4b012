import { cookies } from "next/headers";
import { db } from "@/lib/db";

// Helper function to check if we're in static generation
function isStaticGeneration() {
  try {
    // This will throw during static generation
    cookies();
    return false;
  } catch (e: any) {
    if (e.message?.includes("Dynamic server usage")) {
      return true;
    }
    throw e;
  }
}

export async function getCurrentUser() {
  // During static generation, return null
  if (isStaticGeneration()) {
    return null;
  }

  try {
    // Get the session token from cookies
    const sessionToken = cookies().get("session-token")?.value;
    if (!sessionToken) {
      return null;
    }

    // Get user info from cookies
    const userInfoCookie = cookies().get("user-info")?.value;
    if (!userInfoCookie) {
      return null;
    }

    try {
      const userInfo = JSON.parse(userInfoCookie);

      // Validate required fields
      if (!userInfo.organizationId || !userInfo.email) {
        console.error(
          "Invalid user info - missing organizationId or email:",
          userInfo,
        );
        return null;
      }

      return {
        id: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        role: userInfo.role,
        organizationId: userInfo.organizationId,
      };
    } catch (error) {
      console.error("Error parsing user info:", error);
      return null;
    }
  } catch (error) {
    console.error("Error getting current user:", error);
    return null;
  }
}

/**
 * Check if the current user's organization is active
 */
export async function isCurrentOrganizationActive(): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    if (!user || !user.organizationId) {
      return false;
    }

    const organization = await db.organization.findUnique({
      where: { id: user.organizationId },
      select: { status: true },
    });

    return organization?.status === "active";
  } catch (error) {
    console.error("Error checking organization status:", error);
    // Default to true to avoid breaking functionality
    return true;
  }
}

/**
 * Get current user with organization status
 */
export async function getCurrentUserWithOrgStatus() {
  try {
    const user = await getCurrentUser();
    if (!user || !user.organizationId) {
      return null;
    }

    const organization = await db.organization.findUnique({
      where: { id: user.organizationId },
      select: {
        id: true,
        name: true,
        status: true,
        logo: true,
        slug: true,
      },
    });

    if (!organization) {
      return null;
    }

    return {
      ...user,
      organization: {
        ...organization,
        isActive: organization.status === "active",
      },
    };
  } catch (error) {
    console.error("Error getting user with organization status:", error);
    return null;
  }
}
