import { Server as NetServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import { NextApiRequest } from "next";
import { NextApiResponse } from "next";

export type NextApiResponseWithSocket = NextApiResponse & {
  socket: {
    server: NetServer & {
      io?: SocketIOServer;
    };
  };
};

export const initSocketServer = (
  _req: NextApiRequest,
  res: NextApiResponseWithSocket,
) => {
  if (!res.socket.server.io) {
    console.log("Initializing Socket.io server...");

    // Create a new Socket.io server
    const io = new SocketIOServer(res.socket.server);

    // Store the Socket.io server instance
    res.socket.server.io = io;

    // Set up event handlers
    io.on("connection", (socket) => {
      console.log(`Client connected: ${socket.id}`);

      // Join organization and branch rooms for scoped updates
      socket.on("join-organization", (organizationId: string) => {
        socket.join(`org:${organizationId}`);
        console.log(
          `Socket ${socket.id} joined organization ${organizationId}`,
        );
      });

      socket.on("join-branch", (branchId: string) => {
        socket.join(`branch:${branchId}`);
        console.log(`Socket ${socket.id} joined branch ${branchId}`);
      });

      socket.on("join-doctor", (doctorId: string) => {
        socket.join(`doctor:${doctorId}`);
        console.log(`Socket ${socket.id} joined doctor ${doctorId}`);
      });

      // Handle queue status updates
      socket.on("queue-status-update", (data) => {
        // Broadcast to all clients in the organization and branch
        io.to(`org:${data.organizationId}`)
          .to(`branch:${data.branchId}`)
          .emit("queue-status-updated", data);
        console.log(`Queue status updated: ${JSON.stringify(data)}`);
      });

      socket.on("disconnect", () => {
        console.log(`Client disconnected: ${socket.id}`);
      });
    });
  }

  return res.socket.server.io;
};

// Helper function to emit queue status updates
export const emitQueueStatusUpdate = (
  io: SocketIOServer,
  organizationId: string,
  branchId: string,
  doctorId: string,
  queueStatus: any,
) => {
  // Emit to organization, branch, and doctor rooms
  io.to(`org:${organizationId}`)
    .to(`branch:${branchId}`)
    .to(`doctor:${doctorId}`)
    .emit("queue-status-updated", queueStatus);
};
