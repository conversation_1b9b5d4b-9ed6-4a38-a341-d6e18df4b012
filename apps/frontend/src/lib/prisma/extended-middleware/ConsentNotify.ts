import { PrismaClient } from "@prisma/client";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "@/services/abdm/utils/auth";

type PrismaQueryArgs = {
  operation: string;
  model: string;
  args: any;
  query: (args: any) => Promise<any>;
};

const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL || "";
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

export const extendConsentNotifyMiddleware = (prisma: PrismaClient) => {
  console.log("ConsentNotify middleware being registered");

  return prisma.$extends({
    query: {
      consentNotify: {
        async create({ args, query }: PrismaQueryArgs) {
          console.log(
            "ConsentNotify middleware create method called with args:",
            JSON.stringify(args, null, 2),
          );

          const result = await query(args);
          console.log(
            "ConsentNotify middleware: Record created successfully with ID:",
            result?.id,
          );

          // Check if ABDM_BASE_URL already contains /api
          const baseUrl = ABDM_BASE_URL.endsWith("/api")
            ? ABDM_BASE_URL
            : ABDM_BASE_URL.includes("/api/")
              ? ABDM_BASE_URL
              : `${ABDM_BASE_URL}/api`;

          const timestamp = new Date().toISOString();

          const accessToken = await getAccessToken();

          // Log the full URL and headers for debugging
          const apiUrl = `${baseUrl}/hiecm/consent/v3/request/hip/on-notify`;
          console.log(`Sending request to: ${apiUrl}`);
          console.log("Headers:", {
            "Content-Type": "application/json",
            "REQUEST-ID": result?.requestId,
            TIMESTAMP: timestamp,
            "X-CM-ID": ABDM_CM_ID,
            Authorization: `Bearer ${accessToken}`,
          });

          // Make the API request with the properly formatted URL
          try {
            const response = await abdmFetch(apiUrl, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "REQUEST-ID": result?.requestId,
                TIMESTAMP: timestamp,
                "X-CM-ID": ABDM_CM_ID,
                Authorization: `Bearer ${accessToken}`,
              },
              body: JSON.stringify({
                acknowledgement: {
                  status: "ok",
                  consentId: result?.consentId,
                },
                response: {
                  requestId: result?.requestId,
                },
              }),
              signal: AbortSignal.timeout(120000), // 120 seconds timeout
            });

            console.log({
              response,
            });

            console.log("Acknowledgment sent successfully:", response);
          } catch (error) {
            console.error("Error sending acknowledgment:", error);
          }

          return result;
        },
      },
    },
  });
};
