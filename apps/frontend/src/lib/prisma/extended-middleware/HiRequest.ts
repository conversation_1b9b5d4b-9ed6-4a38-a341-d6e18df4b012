import { PrismaClient } from "@prisma/client";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "@/services/abdm/utils/auth";

type PrismaQueryArgs = {
  operation: string;
  model: string;
  args: any;
  query: (args: any) => Promise<any>;
};

const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL || "";
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Send acknowledgment to ABDM for health information request
 * @param transactionId - Transaction ID
 * @param requestId - Request ID
 */
async function sendHealthInfoAcknowledgment(
  transactionId: string,
  requestId: string,
) {
  try {
    // Get access token
    const accessToken = await getAccessToken();

    if (!ABDM_BASE_URL) {
      throw new Error("ABDM_BASE_URL is not configured");
    }

    // Create a timestamp for the request
    const timestamp = new Date().toISOString();

    // Format the base URL correctly
    const baseUrl = ABDM_BASE_URL.endsWith("/api")
      ? ABDM_BASE_URL
      : ABDM_BASE_URL.includes("/api/")
        ? ABDM_BASE_URL
        : `${ABDM_BASE_URL}/api`;

    // Prepare the payload
    const payload = {
      hiRequest: {
        transactionId: transactionId,
        sessionStatus: "ACKNOWLEDGED",
      },
      response: {
        requestId: requestId,
      },
    };

    // Log the full URL and headers for debugging
    const apiUrl = `${baseUrl}/hiecm/data-flow/v3/health-information/hip/on-request`;

    // Make the API request with the properly formatted URL
    const response = await abdmFetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "REQUEST-ID": requestId,
        TIMESTAMP: timestamp,
        "X-CM-ID": ABDM_CM_ID,
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(payload),
      signal: AbortSignal.timeout(120000), // 120 seconds timeout
    });

    return response;
  } catch (error) {
    // Enhanced error logging
    console.error(
      "Error sending health information request acknowledgment:",
      error,
    );

    throw error;
  }
}

export const extendHiRequestMiddleware = (prisma: PrismaClient) => {
  console.log("HiRequest middleware being registered");

  return prisma.$extends({
    query: {
      hiRequest: {
        async create({ args, query }: PrismaQueryArgs) {
          console.log(
            "HiRequest middleware create method called with args:",
            JSON.stringify(args, null, 2),
          );

          const result = await query(args);
          console.log(
            "HiRequest middleware: Record created successfully with ID:",
            result?.id,
          );

          try {
            // Send acknowledgment to ABDM
            await sendHealthInfoAcknowledgment(
              result?.transactionId,
              result?.requestId,
            );

            console.log(
              `Successfully sent acknowledgment to ABDM for health information request: ${result?.transactionId}`,
            );
          } catch (error) {
            console.error(
              `Failed to send acknowledgment to ABDM for health information request: ${result?.transactionId}`,
              error,
            );
          }

          return result;
        },
      },
    },
  });
};
