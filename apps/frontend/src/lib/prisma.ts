import { PrismaClient } from "@prisma/client";

// Create a simple PrismaClient instance without middleware for page components
const createPrismaClient = () => {
  return new PrismaClient({
    log: process.env.NODE_ENV === "development" ? ["error", "warn"] : ["error"],
  });
};

// Define the type for the global variable
type PrismaClientSingleton = ReturnType<typeof createPrismaClient>;

// Define the global variable
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClientSingleton | undefined;
};

// Export the basic PrismaClient instance (without middleware)
export const prisma = globalForPrisma.prisma ?? createPrismaClient();

// In development, attach the PrismaClient to the global object
if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;
}

// Helper function to check if the database is connected
export async function isDatabaseConnected() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error("Database connection check failed:", error);
    return false;
  }
}
