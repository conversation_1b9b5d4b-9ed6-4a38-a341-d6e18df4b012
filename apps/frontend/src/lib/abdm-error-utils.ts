/**
 * ABDM Error Utilities
 *
 * This module provides utilities for handling ABDM API errors,
 * including error code mapping, user-friendly messages, and
 * retry strategies.
 */

// Official ABDM error codes and their user-friendly messages
export const ABDM_ERROR_CODES: Record<string, string> = {
  // Database and connection errors
  "ABDM-1000": "Unable to connect to the database. Please try again later.",
  "ABDM-1001": "No data found. Please check your details and try again.",
  "ABDM-1002":
    "Data integrity violation. Please try again with valid information.",
  "ABDM-1003": "Email service is unavailable. Please try again later.",
  "ABDM-1004": "SMS service is unavailable. Please try again later.",
  "ABDM-1005": "Invalid receiver information. Please check your details.",
  "ABDM-1006":
    "Invalid request format. Please check your information and try again.",
  "ABDM-1007": "Connection timed out. Please try again later.",
  "ABDM-1008": "SMS service is currently disabled. Please try again later.",
  "ABDM-1009": "Email service is currently disabled. Please try again later.",
  "ABDM-1010":
    "Validation failed. Please check your information and try again.",
  "ABDM-1011": "Gateway database is unavailable. Please try again later.",
  "ABDM-1012":
    "No records found for this ABHA Address. Please verify your ABHA Address.",
  "ABDM-1013": "Invalid ABHA Number. Please check and try again.",
  "ABDM-1014": "Invalid Mobile or Email. Please check and try again.",
  "ABDM-1015": "Invalid response received. Please try again later.",
  "ABDM-1016": "Invalid timestamp. Please try again.",
  "ABDM-1017": "Invalid transaction ID. Please try again.",
  "ABDM-1018": "Share Profile database is unavailable. Please try again later.",
  "ABDM-1019": "Dependent service is unavailable. Please try again later.",
  "ABDM-1020": "Unknown database error. Please try again later.",
  "ABDM-1021":
    "Insufficient privileges for this operation. Please contact support.",
  "ABDM-1022": "Too many requests. Please try again after some time.",
  "ABDM-1023": "Invalid user. Please check your credentials.",
  "ABDM-1024": "Dependent service is unavailable. Please try again later.",
  "ABDM-1025": "Invalid service ID. Please try again.",
  "ABDM-1026": "Invalid link token. Please try again.",
  "ABDM-1027": "Your account is blocked. Please try again after 24 hours.",
  "ABDM-1028":
    "Healthcare provider (HIP) is unavailable. Please try again later.",
  "ABDM-1029": "Redis server is unavailable. Please try again later.",
  "ABDM-1030": "Invalid request ID. Please try again.",
  "ABDM-1031": "Invalid request. Please check your information and try again.",
  "ABDM-1032": "Invalid header in request. Please try again.",
  "ABDM-1033":
    "Health Information User (HIU) is unavailable. Please try again later.",
  "ABDM-1034": "Notification service is unavailable. Please try again later.",
  "ABDM-1035":
    "Invalid healthcare provider (HIP) ID or OTP does not match. Please try again.",
  "ABDM-1036":
    "Data does not match. Please check your information and try again.",
  "ABDM-1037": "Counter and care context count mismatch. Please try again.",
  "ABDM-1038": "ABHA address and link token mismatch. Please try again.",
  "ABDM-1039": "Invalid consent request ID. Please try again.",
  "ABDM-1040": "Invalid Health Information User (HIU) ID. Please try again.",
  "ABDM-1041": "Invalid acknowledgement. Please try again.",
  "ABDM-1042":
    "Provider information is mandatory. Please provide required details.",
  "ABDM-1043":
    "ABHA Address does not match with KYC details. Please verify your information.",
  "ABDM-1044": "Broadcast failed. Please try again later.",
  "ABDM-1045": "Database access is restricted. Please contact support.",
  "ABDM-1046": "Invalid purpose specified. Please select a valid purpose.",
  "ABDM-1047": "Purpose does not exist. Please select a valid purpose.",
  "ABDM-1048": "Request timed out. Please try again later.",
  "ABDM-1049": "Invalid profile share intent keys. Please try again.",
  "ABDM-1050": "Invalid profile share metadata keys. Please try again.",
  "ABDM-1051":
    "Invalid ABHA Number or ABHA Address. Please check and try again.",
  "ABDM-1052":
    "Invalid transaction ID or response request ID. Please try again.",
  "ABDM-1053": "Data already exists. Please try with different information.",
  "ABDM-1054": "Invalid subscription request ID. Please try again.",
  "ABDM-1061": "Consent artifact has expired. Please request a new consent.",
  "ABDM-1062": "Consent has not been granted. Please request consent first.",
  "ABDM-1063":
    "Invalid date range specified. Please provide a valid date range.",
  "ABDM-1064":
    "Request with this ID already exists. Please use a different request ID.",
  "ABDM-1094": "Your session has expired. Please login again.",
  "ABDM-1100":
    "You have requested multiple OTPs or exceeded maximum attempts. Please try again in 30 minutes.",
  "ABDM-1101":
    "This ABHA Address already exists. Please create with a unique ABHA Address.",
  "ABDM-1108":
    "Notification database service is unavailable. Please try again later.",
  "ABDM-1109": "ABHA database service is unavailable. Please try again later.",
  "ABDM-1115":
    "ABHA Number not found. We did not find any ABHA number linked to this mobile number. Please use ABHA linked mobile number.",
  "ABDM-1200": "LGD Gateway is unavailable. Please try again later.",
  "ABDM-1201": "IDP Gateway is unavailable. Please try again later.",
  "ABDM-1202": "Document Gateway is unavailable. Please try again later.",
  "ABDM-1205":
    "Document database Gateway is unavailable. Please try again later.",
  "ABDM-1401":
    "Healthcare provider (HIP) is not available. Please try again later.",
  "ABDM-1402":
    "Acknowledgement not received from healthcare provider (HIP). Please try again later.",

  // Generic error
  "ABDM-9999": "An unknown error occurred. Please try again later.",

  // Additional custom error codes for better UX
  "ABDM-0000": "An unknown error occurred. Please try again later.",
  "ABDM-0001": "Runtime error. Please try again later.",
  "ABDM-0002": "Invalid request format. Please try again.",
  "ABDM-0003": "Service temporarily unavailable. Please try again later.",

  // Network errors (custom codes)
  "ABDM-9001": "Network error. Please check your connection and try again.",
  "ABDM-9002": "ABDM server is currently unavailable. Please try again later.",
  "ABDM-9003": "Request timed out. Please try again.",
  "ABDM-9004": "Connection to ABDM server failed. Please try again later.",
  "ABDM-9005":
    "ABDM service is experiencing high traffic. Please try again later.",
};

/**
 * Get a user-friendly error message for an ABDM error code
 * @param errorCode - ABDM error code
 * @param fallbackMessage - Fallback message if error code is not found
 * @returns User-friendly error message
 */
export function getAbdmErrorMessage(
  errorCode: string,
  fallbackMessage: string = "An error occurred. Please try again.",
): string {
  return ABDM_ERROR_CODES[errorCode] || fallbackMessage;
}

/**
 * Parse ABDM error response and extract error code and message
 * @param error - Error object or response from ABDM API
 * @returns Parsed error with code and message
 */
export function parseAbdmError(error: any): { code: string; message: string } {
  try {
    // Handle Error objects
    if (error instanceof Error) {
      const errorMessage = error.message;

      // Check if the error message contains an ABDM error code
      const codeMatch = errorMessage.match(/ABDM-\d{4}/);
      if (codeMatch) {
        const code = codeMatch[0];
        return {
          code,
          message: getAbdmErrorMessage(code, errorMessage),
        };
      }

      // Check for network errors
      if (
        errorMessage.includes("network") ||
        errorMessage.includes("connection")
      ) {
        return {
          code: "ABDM-9001",
          message: getAbdmErrorMessage("ABDM-9001"),
        };
      }

      if (errorMessage.includes("timeout")) {
        return {
          code: "ABDM-9003",
          message: getAbdmErrorMessage("ABDM-9003"),
        };
      }

      return {
        code: "ABDM-0000",
        message: errorMessage,
      };
    }

    // Handle API response objects
    if (typeof error === "object" && error !== null) {
      // Check for specific invalid Aadhaar error patterns first
      if (error.loginId && typeof error.loginId === "string") {
        const loginIdMessage = error.loginId.toLowerCase();
        if (loginIdMessage.includes("invalid")) {
          return {
            code: "ABDM-1013",
            message: "Aadhaar Number is not valid",
          };
        }
      }

      // Check for ABDM error code in various formats
      const code = error.code || error.errorCode || error.error?.code || "";

      if (code && code.startsWith("ABDM-")) {
        return {
          code,
          message: getAbdmErrorMessage(
            code,
            error.message || error.error?.message || "An error occurred",
          ),
        };
      }

      // Check for error message
      const message =
        error.message ||
        error.error?.message ||
        error.error ||
        "An error occurred";

      // Check for specific invalid Aadhaar error patterns
      if (typeof message === "string") {
        const lowerMessage = message.toLowerCase();
        if (
          lowerMessage.includes("invalid loginid") ||
          lowerMessage.includes("invalid login id") ||
          message.includes("HTTP 400: Bad Request") ||
          (error.loginId &&
            typeof error.loginId === "string" &&
            error.loginId.toLowerCase().includes("invalid"))
        ) {
          return {
            code: "ABDM-1013",
            message: "Aadhaar Number is not valid",
          };
        }
      }

      // Check if the message contains an ABDM error code
      const codeMatch =
        typeof message === "string" ? message.match(/ABDM-\d{4}/) : null;
      if (codeMatch) {
        const extractedCode = codeMatch[0];
        return {
          code: extractedCode,
          message: getAbdmErrorMessage(extractedCode, message),
        };
      }

      return {
        code: "ABDM-0000",
        message: typeof message === "string" ? message : "An error occurred",
      };
    }

    // Handle string errors
    if (typeof error === "string") {
      const codeMatch = error.match(/ABDM-\d{4}/);
      if (codeMatch) {
        const code = codeMatch[0];
        return {
          code,
          message: getAbdmErrorMessage(code, error),
        };
      }

      return {
        code: "ABDM-0000",
        message: error,
      };
    }
  } catch (e) {
    console.error("Error parsing ABDM error:", e);
  }

  // Default fallback
  return {
    code: "ABDM-0000",
    message: "An unknown error occurred. Please try again later.",
  };
}

/**
 * Determine if an error should be retried
 * @param error - Error object or response from ABDM API
 * @returns Boolean indicating if the error should be retried
 */
export function shouldRetryAbdmError(error: any): boolean {
  const { code } = parseAbdmError(error);

  // List of error codes that should be retried
  const retryableCodes = [
    // Network and connection errors
    "ABDM-1000", // Unable to connect to the database
    "ABDM-1003", // Email Gateway is unavailable
    "ABDM-1004", // SMS Gateway is unavailable
    "ABDM-1007", // Connection failed due to timeout
    "ABDM-1008", // SMS service currently disabled
    "ABDM-1009", // Email service currently disabled
    "ABDM-1011", // Gateway database unavailable
    "ABDM-1018", // Share Profile database unavailable
    "ABDM-1019", // Dependent Service Unavailable
    "ABDM-1022", // Too many requests
    "ABDM-1024", // Dependent service unavailable
    "ABDM-1028", // HIP is unavailable
    "ABDM-1029", // Redis server is unavailable
    "ABDM-1033", // HIU is unavailable
    "ABDM-1034", // Notification service unavailable
    "ABDM-1044", // Broadcast Failed
    "ABDM-1048", // Timeout
    "ABDM-1108", // Notification DB service unavailable
    "ABDM-1109", // ABHA DB service unavailable
    "ABDM-1200", // LGD Gateway is unavailable
    "ABDM-1201", // IDP Gateway is unavailable
    "ABDM-1202", // Document Gateway is unavailable
    "ABDM-1205", // Document DB Gateway is unavailable
    "ABDM-1401", // HIP is not available
    "ABDM-1402", // Acknowledgement is not received from HIP

    // Custom network error codes
    "ABDM-9001", // Network error
    "ABDM-9002", // Server unavailable
    "ABDM-9003", // Request timeout
    "ABDM-9004", // Connection failed
    "ABDM-9005", // High traffic
    "ABDM-0003", // Service temporarily unavailable
  ];

  // Check for specific error messages that indicate retryable errors
  if (error && typeof error === "object") {
    const errorMessage = error.message || error.error?.message || "";
    if (typeof errorMessage === "string") {
      const lowerCaseMessage = errorMessage.toLowerCase();
      if (
        lowerCaseMessage.includes("timeout") ||
        lowerCaseMessage.includes("connection") ||
        lowerCaseMessage.includes("network") ||
        lowerCaseMessage.includes("unavailable") ||
        lowerCaseMessage.includes("server error") ||
        lowerCaseMessage.includes("try again")
      ) {
        return true;
      }
    }

    // Check for HTTP status codes that indicate retryable errors
    const statusCode = error.status || error.statusCode || 0;
    if ([408, 429, 500, 502, 503, 504].includes(statusCode)) {
      return true;
    }
  }

  return retryableCodes.includes(code);
}

/**
 * Calculate retry delay with exponential backoff
 * @param attempt - Current retry attempt (0-based)
 * @param baseDelay - Base delay in milliseconds
 * @param maxDelay - Maximum delay in milliseconds
 * @returns Delay in milliseconds for the next retry
 */
export function calculateRetryDelay(
  attempt: number,
  baseDelay: number = 1000,
  maxDelay: number = 30000,
): number {
  // Exponential backoff with jitter
  const exponentialDelay = Math.min(maxDelay, baseDelay * Math.pow(2, attempt));
  // Add jitter (±20%) to prevent thundering herd problem
  const jitter = exponentialDelay * 0.2 * (Math.random() - 0.5);

  return Math.max(baseDelay, Math.floor(exponentialDelay + jitter));
}
