/**
 * Utility functions for handling branch-related cookies
 */

import { cookies } from "next/headers";

/**
 * Get the current branch ID from cookies (server-side)
 * @returns The branch ID or null if not found
 */
export function getCurrentBranchFromCookies(): string | null {
  const cookieStore = cookies();

  // First try default-branch cookie (contains full branch object)
  const defaultBranchCookie = cookieStore.get("default-branch")?.value;
  if (defaultBranchCookie) {
    try {
      const branch = JSON.parse(decodeURIComponent(defaultBranchCookie));
      return branch.id;
    } catch (error) {
      console.error("Error parsing default-branch cookie:", error);
    }
  }

  // Then check if we have current-branch cookie
  const currentBranchCookie = cookieStore.get("current-branch")?.value;
  if (currentBranchCookie) {
    try {
      const branchInfo = JSON.parse(decodeURIComponent(currentBranchCookie));
      return branchInfo.id;
    } catch (error) {
      console.error("Error parsing current-branch cookie:", error);
    }
  }

  // If no branch cookies found, return null
  return null;
}

/**
 * Get the current branch object from cookies (server-side)
 * @returns The branch object or null if not found
 */
export function getCurrentBranchObjectFromCookies(): any | null {
  const cookieStore = cookies();

  // Then check if we have current-branch cookie
  const currentBranchCookie = cookieStore.get("current-branch")?.value;
  if (currentBranchCookie) {
    try {
      const branchInfo = JSON.parse(decodeURIComponent(currentBranchCookie));
      console.log("Branch info from cookie 1:", branchInfo);
      return {
        id: branchInfo.id,
        name: branchInfo.branchName,
      };
    } catch (error) {
      console.error("Error parsing current-branch cookie:", error);
    }
  }

  // If no branch cookies found, return null
  return null;
}

/**
 * Set the current branch in cookies (server-side)
 * @param branch - The branch object to set
 */
export function setCurrentBranchInCookies(branch: {
  id: string;
  name: string;
  [key: string]: any;
}) {
  const cookieStore = cookies();

  // Set current-branch for backward compatibility
  cookieStore.set(
    "current-branch",
    JSON.stringify({
      branchId: branch.id,
      branchName: branch.name,
    }),
    {
      path: "/",
    },
  );

  // Set default-branch with full branch object
  cookieStore.set("default-branch", JSON.stringify(branch), {
    path: "/",
  });
}
