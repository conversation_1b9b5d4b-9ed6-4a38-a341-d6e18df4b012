import { compare, hash as bcryptHash } from "bcryptjs";
import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { prisma } from "./prisma";

export async function hashPassword(password: string) {
  return await bcryptHash(password, 10);
}

export async function comparePassword(
  password: string,
  hashedPassword: string,
) {
  try {
    const result = await compare(password, hashedPassword);
    return result;
  } catch (error) {
    console.error("Error comparing passwords:", error);
    return false;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          include: {
            organizations: {
              select: {
                organizationId: true,
                isDefault: true,
                roles: true,
              },
            },
          },
        });

        if (!user || !user.password) {
          return null;
        }

        const isPasswordValid = await comparePassword(
          credentials.password,
          user.password,
        );

        if (!isPasswordValid) {
          return null;
        }

        // Find default organization or use the first one
        const defaultOrg =
          user.organizations.find((org) => org.isDefault) ||
          user.organizations[0];

        // Get the role from the default organization
        const userRoles = Array.isArray(defaultOrg?.roles)
          ? (defaultOrg.roles as string[])
          : [];
        const primaryRole = userRoles[0] || "user"; // Use first role as primary role

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          currentRole:   user.role,
          currentBranch: defaultOrg?.organizationId || "",        // decide here
          organisation:  defaultOrg || null,   // { id, name, … }
        };
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.currentRole   = user.currentRole;
        token.currentBranch = user.currentBranch;
        token.organisation  = user.organisation;
      }
      return token;
    },
    async session({ session, token }) {
      session.currentRole   = token.currentRole;
      session.currentBranch = token.currentBranch;
      session.organisation  = token.organisation;
      return session;
    },
  },
  pages: {
    signIn: "/sign-in",
    signOut: "/sign-in",
    error: "/sign-in",
  },
};
