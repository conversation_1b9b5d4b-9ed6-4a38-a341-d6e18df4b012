/**
 * Converts an array of objects to a CSV string
 * @param data - Array of objects to convert
 * @param headers - Optional custom headers (keys and display names)
 * @returns CSV string
 */
export function convertToCSV<T extends Record<string, any>>(
  data: T[],
  headers?: { key: keyof T; label: string }[],
): string {
  if (!data || data.length === 0) {
    return "";
  }

  // If headers are not provided, use the keys of the first object
  const keys = headers
    ? headers.map((h) => h.key)
    : (Object.keys(data[0]) as (keyof T)[]);
  const headerRow = headers
    ? headers.map((h) => `"${h.label}"`).join(",")
    : keys.map((key) => `"${String(key)}"`).join(",");

  // Convert each object to a CSV row
  const rows = data.map((obj) => {
    return keys
      .map((key) => {
        const value = obj[key];
        // Handle different types of values
        if (value === null || value === undefined) {
          return '""';
        } else if (typeof value === "object") {
          // For objects (including Date), convert to string
          return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
        } else {
          // For primitive types, convert to string and escape quotes
          return `"${String(value).replace(/"/g, '""')}"`;
        }
      })
      .join(",");
  });

  // Combine header row and data rows
  return [headerRow, ...rows].join("\n");
}

/**
 * Downloads data as a CSV file
 * @param data - Array of objects to download
 * @param filename - Name of the file to download
 * @param headers - Optional custom headers (keys and display names)
 */
export function downloadCSV<T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[],
): void {
  if (!data || data.length === 0) {
    console.warn("No data to download");
    return;
  }

  // Convert data to CSV
  const csv = convertToCSV(data, headers);

  // Create a Blob with the CSV data
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

  // Create a download link
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);

  // Set link properties
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    filename.endsWith(".csv") ? filename : `${filename}.csv`,
  );
  link.style.visibility = "hidden";

  // Add link to document, click it, and remove it
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Formats a date for export
 * @param date - Date to format
 * @param format - Format to use (default: 'yyyy-MM-dd')
 * @returns Formatted date string
 */
export function formatDateForExport(
  date: Date | string | number,
  format: string = "yyyy-MM-dd",
): string {
  const d = new Date(date);

  // Check if date is valid
  if (isNaN(d.getTime())) {
    return "";
  }

  // Format the date based on the specified format
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  const hours = String(d.getHours()).padStart(2, "0");
  const minutes = String(d.getMinutes()).padStart(2, "0");
  const seconds = String(d.getSeconds()).padStart(2, "0");

  // Replace format tokens with actual values
  return format
    .replace("yyyy", String(year))
    .replace("MM", month)
    .replace("dd", day)
    .replace("HH", hours)
    .replace("mm", minutes)
    .replace("ss", seconds);
}
