import { toast } from "sonner";

/**
 * Utility function for making API requests with error handling and timeout
 * @param url - The API endpoint URL
 * @param options - Fetch options
 * @param timeoutMs - Timeout in milliseconds (default: 30000)
 * @returns The response data or throws an error
 */
export async function fetchWithErrorHandling<T>(
  url: string,
  options?: RequestInit,
  timeoutMs: number = 30000,
): Promise<T> {
  // Create an AbortController for timeout handling
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });

    // Clear the timeout
    clearTimeout(timeoutId);

    // Check if the response is OK
    if (!response.ok) {
      // Try to parse the error message from the response
      let errorMessage = "An error occurred";
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorData.message || errorMessage;
      } catch {
        // If we can't parse the error message, use the status text
        errorMessage = response.statusText || errorMessage;
      }

      throw new Error(`${errorMessage} (${response.status})`);
    }

    // Parse the response as JSON
    const data = await response.json();
    return data as T;
  } catch (error: any) {
    // Clear the timeout
    clearTimeout(timeoutId);

    // Handle AbortError (timeout)
    if (error.name === "AbortError") {
      throw new Error(`Request timed out after ${timeoutMs}ms`);
    }

    // Re-throw the error
    throw error;
  }
}

/**
 * Utility function for making API requests with error handling, toast notifications, and fallback data
 * @param url - The API endpoint URL
 * @param options - Fetch options
 * @param fallbackData - Fallback data to return if the request fails
 * @param timeoutMs - Timeout in milliseconds (default: 30000)
 * @returns The response data or fallback data
 */
export async function fetchWithFallback<T>(
  url: string,
  options?: RequestInit,
  fallbackData?: T,
  timeoutMs: number = 30000,
): Promise<T> {
  try {
    return await fetchWithErrorHandling<T>(url, options, timeoutMs);
  } catch (error: any) {
    // Show error toast
    toast.error(error.message || "Failed to fetch data");
    console.error("API Error:", error);

    // Return fallback data if provided
    if (fallbackData !== undefined) {
      return fallbackData;
    }

    // Re-throw the error if no fallback data is provided
    throw error;
  }
}

/**
 * Utility function for caching API responses in localStorage
 * @param key - The cache key
 * @param data - The data to cache
 * @param expirationMs - Cache expiration time in milliseconds (default: 5 minutes)
 */
export function cacheData<T>(
  key: string,
  data: T,
  expirationMs: number = 5 * 60 * 1000,
): void {
  try {
    const cacheItem = {
      data,
      expiration: Date.now() + expirationMs,
    };
    localStorage.setItem(`cache_${key}`, JSON.stringify(cacheItem));
  } catch (error) {
    console.error("Error caching data:", error);
  }
}

/**
 * Utility function for retrieving cached API responses from localStorage
 * @param key - The cache key
 * @returns The cached data or null if not found or expired
 */
export function getCachedData<T>(key: string): T | null {
  try {
    const cachedItem = localStorage.getItem(`cache_${key}`);
    if (!cachedItem) return null;

    const { data, expiration } = JSON.parse(cachedItem);
    if (Date.now() > expiration) {
      // Cache expired, remove it
      localStorage.removeItem(`cache_${key}`);
      return null;
    }

    return data as T;
  } catch (error) {
    console.error("Error retrieving cached data:", error);
    return null;
  }
}

/**
 * Utility function for fetching data with caching
 * @param url - The API endpoint URL
 * @param options - Fetch options
 * @param cacheKey - The cache key
 * @param expirationMs - Cache expiration time in milliseconds (default: 5 minutes)
 * @param fallbackData - Fallback data to return if the request fails
 * @returns The response data (from cache or fresh)
 */
export async function fetchWithCache<T>(
  url: string,
  options?: RequestInit,
  cacheKey?: string,
  expirationMs: number = 5 * 60 * 1000,
  fallbackData?: T,
): Promise<T> {
  // If no cache key is provided, use the URL as the key
  const key = cacheKey || url;

  // Try to get data from cache
  if (cacheKey) {
    const cachedData = getCachedData<T>(key);
    if (cachedData) {
      return cachedData;
    }
  }

  // Fetch fresh data
  try {
    const data = await fetchWithErrorHandling<T>(url, options);

    // Cache the data
    if (cacheKey) {
      cacheData(key, data, expirationMs);
    }

    return data;
  } catch (error) {
    // Show error toast
    if (error instanceof Error) {
      toast.error(error.message || "Failed to fetch data");
    } else {
      toast.error("Failed to fetch data");
    }
    console.error("API Error:", error);

    // Return fallback data if provided
    if (fallbackData !== undefined) {
      return fallbackData;
    }

    // Re-throw the error if no fallback data is provided
    throw error;
  }
}
