"use client";

/**
 * Client-side session utility
 * This is a client-side version of the session utility that doesn't use next/headers
 */

export function getCurrentUserClient() {
  try {
    // Get user info from cookies
    const userInfoCookie = document.cookie
      .split("; ")
      .find((row) => row.startsWith("user-info="));

    if (!userInfoCookie) {
      return null;
    }

    const userInfoValue = userInfoCookie.split("=")[1];
    if (!userInfoValue) {
      return null;
    }

    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoValue));
      return {
        id: userInfo.id || "user-id", // Fallback for demo
        email: userInfo.email,
        name: userInfo.name,
        role: userInfo.role,
        organizationId: userInfo.organizationId || "org-1", // Fallback for demo
      };
    } catch (error) {
      console.error("Error parsing user info:", error);
      return null;
    }
  } catch (error) {
    console.error("Error getting current user:", error);
    return null;
  }
}
