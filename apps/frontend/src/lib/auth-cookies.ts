/**
 * Utility functions for handling authentication via cookies
 */

import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

/**
 * Get the user from cookies
 * @returns The user object or null if not authenticated
 */
export function getUserFromCookies() {
  const cookieStore = cookies();
  const userCookie = cookieStore.get("user");

  if (!userCookie) {
    return null;
  }

  try {
    return JSON.parse(userCookie.value);
  } catch (error) {
    console.error("Error parsing user cookie:", error);
    return null;
  }
}

/**
 * Get the user from request cookies (for API routes)
 * @param req - The Next.js request object
 * @returns The user object or null if not authenticated
 */

export function getUserFromRequestCookies(req: NextRequest) {
  // First try user-info cookie
  const userInfoCookie = req.cookies.get("user-info");

  if (userInfoCookie) {
    try {
      // URL decode the cookie value first
      const decodedValue = decodeURIComponent(userInfoCookie.value);
      const userInfo = JSON.parse(decodedValue);
      return {
        id: userInfo.id || "user-id", // Fallback for demo
        email: userInfo.email,
        name: userInfo.name,
        role: userInfo.role,
        organizationId: userInfo.organizationId || "org-1", // Fallback for demo
      };
    } catch (error) {
      console.error("Error parsing user-info cookie:", error);
      console.error("Cookie value:", userInfoCookie.value);
    }
  }

  // Fallback to user cookie (older format)
  const userCookie = req.cookies.get("user");

  if (userCookie) {
    try {
      // URL decode the cookie value first
      const decodedValue = decodeURIComponent(userCookie.value);
      return JSON.parse(decodedValue);
    } catch (error) {
      console.error("Error parsing user cookie:", error);
      console.error("Cookie value:", userCookie.value);
    }
  }

  return null;
}

/**
 * Set user info cookie in response
 * @param response - The Next.js response object
 * @param userInfo - The user info object to set in cookie
 */
export function setUserInfoCookie(response: NextResponse, userInfo: any) {
  const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

  response.cookies.set("user-info", JSON.stringify(userInfo), {
    expires,
    path: "/",
    httpOnly: false,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
  });
}
