import { cookies } from "next/headers";
import { prisma } from "@/lib/prisma";

/**
 * Server-side function to check if a user has completed onboarding
 * This function is meant to be used in server components or API routes
 */
export async function checkOnboardingStatus(): Promise<boolean> {
  try {
    // Get the user info from cookies
    const userInfoCookie = cookies().get("user-info");

    if (!userInfoCookie) {
      return false;
    }

    // Parse the user info
    const userInfo = JSON.parse(decodeURIComponent(userInfoCookie.value));
    const organizationId = userInfo.organizationId;

    if (!organizationId) {
      return false;
    }

    // Get the organization from the database
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      select: { onboardingCompleted: true },
    });

    return organization?.onboardingCompleted || false;
  } catch (error) {
    console.error("Error checking onboarding status:", error);
    return false;
  }
}
