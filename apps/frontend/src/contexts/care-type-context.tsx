"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useOrganization } from "@/contexts/organization-context";
import { Department } from "@/contexts/department-context";

export interface CareType {
  id: string;
  name: string;
  description?: string | null;
  price: number;
  departmentId: string;
  organizationId: string;
  department?: Department;
}

interface CareTypeContextType {
  careTypes: CareType[];
  isLoading: boolean;
  refreshCareTypes: (departmentId?: string) => Promise<void>;
}

const CareTypeContext = createContext<CareTypeContextType | undefined>(
  undefined,
);

export function CareTypeProvider({ children }: { children: React.ReactNode }) {
  const [careTypes, setCareTypes] = useState<CareType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentOrganization, isLoading: isOrgLoading } = useOrganization();

  const refreshCareTypes = async (departmentId?: string) => {
    if (!currentOrganization) {
      setCareTypes([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      let url = "/api/care-types";
      if (departmentId) {
        url += `?departmentId=${departmentId}`;
      }

      // Add a small delay to ensure the API has time to process the request
      await new Promise((resolve) => setTimeout(resolve, 300));

      const response = await fetch(url, {
        // Add cache: 'no-store' to prevent caching
        cache: "no-store",
        // Add a timestamp to the URL to prevent caching
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCareTypes(data.careTypes || []);
      } else {
        console.error("Failed to fetch care types");
        setCareTypes([]);
      }
    } catch (error) {
      console.error("Error fetching care types:", error);
      setCareTypes([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh care types when the organization changes
  useEffect(() => {
    if (!isOrgLoading && currentOrganization) {
      refreshCareTypes();
    }
  }, [currentOrganization, isOrgLoading]);

  return (
    <CareTypeContext.Provider
      value={{
        careTypes,
        isLoading,
        refreshCareTypes,
      }}
    >
      {children}
    </CareTypeContext.Provider>
  );
}

export function useCareType() {
  const context = useContext(CareTypeContext);
  if (context === undefined) {
    throw new Error("useCareType must be used within a CareTypeProvider");
  }
  return context;
}
