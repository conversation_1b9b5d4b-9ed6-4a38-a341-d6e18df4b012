"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useOrganization } from "@/contexts/organization-context";

export interface Department {
  id: string;
  name: string;
  description?: string | null;
  departmentCode: string;
  status: string;
  organizationId: string;
}

interface DepartmentContextType {
  departments: Department[];
  isLoading: boolean;
  refreshDepartments: () => Promise<void>;
}

const DepartmentContext = createContext<DepartmentContextType | undefined>(
  undefined,
);

export function DepartmentProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentOrganization, isLoading: isOrgLoading } = useOrganization();

  const refreshDepartments = async () => {
    if (!currentOrganization) {
      setDepartments([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/departments");
      if (response.ok) {
        const data = await response.json();
        setDepartments(data.departments || []);
      } else {
        console.error("Failed to fetch departments");
        setDepartments([]);
      }
    } catch (error) {
      console.error("Error fetching departments:", error);
      setDepartments([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh departments when the organization changes
  useEffect(() => {
    if (!isOrgLoading && currentOrganization) {
      refreshDepartments();
    }
  }, [currentOrganization, isOrgLoading]);

  return (
    <DepartmentContext.Provider
      value={{
        departments,
        isLoading,
        refreshDepartments,
      }}
    >
      {children}
    </DepartmentContext.Provider>
  );
}

export function useDepartment() {
  const context = useContext(DepartmentContext);
  if (context === undefined) {
    throw new Error("useDepartment must be used within a DepartmentProvider");
  }
  return context;
}
