"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
  useMemo,
} from "react";
import { useOrganization } from "./organization-context";
import { useBranch } from "./branch-context";

interface Staff {
  id: string;
  name: string;
  role: string;
  contactNumber?: string;
  departmentId?: string;
  department?: {
    id: string;
    name: string;
  };
  branchId: string;
  branch: {
    id: string;
    name: string;
  };
  joiningDate?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface StaffContextType {
  staff: Staff[];
  isLoading: boolean;
  refreshStaff: (
    branchId?: string,
    departmentId?: string,
    role?: string,
    status?: string,
  ) => Promise<void>;
}

const StaffContext = createContext<StaffContextType | undefined>(undefined);

export function StaffProvider({ children }: { children: ReactNode }) {
  const [staff, setStaff] = useState<Staff[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentOrganization } = useOrganization();
  const { currentBranch } = useBranch();

  const refreshStaff = useCallback(
    async (
      branchId?: string,
      departmentId?: string,
      role?: string,
      status?: string,
    ) => {
      if (!currentOrganization) {
        setStaff([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        let url = "/api/staff";
        const params = new URLSearchParams();

        if (branchId) {
          params.append("branchId", branchId);
        }

        if (departmentId) {
          params.append("departmentId", departmentId);
        }

        if (role) {
          params.append("role", role);
        }

        if (status) {
          params.append("status", status);
        }

        if (params.toString()) {
          url += `?${params.toString()}`;
        }

        const response = await fetch(url, {
          cache: "no-store",
          headers: {
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          },
        });

        if (response.ok) {
          const data = await response.json();
          setStaff(data.staff || []);
        } else {
          console.error("Failed to fetch staff");
          setStaff([]);
        }
      } catch (error) {
        console.error("Error fetching staff:", error);
        setStaff([]);
      } finally {
        setIsLoading(false);
      }
    },
    [currentOrganization],
  );

  useEffect(() => {
    // Only refresh when organization changes
    if (currentOrganization) {
      // Always filter by current branch if it exists
      if (currentBranch) {
        refreshStaff(currentBranch.id);
      } else {
        refreshStaff();
      }
    }
  }, [currentOrganization]); // Only depend on organization changes

  // Separate effect for branch changes to ensure staff is always filtered by branch
  useEffect(() => {
    // Always refresh staff when branch changes
    if (currentBranch && currentOrganization) {
      refreshStaff(currentBranch.id);
    }
  }, [currentBranch?.id]); // Only depend on branch ID changes

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      staff,
      isLoading,
      refreshStaff,
    }),
    [staff, isLoading, refreshStaff],
  );

  return (
    <StaffContext.Provider value={contextValue}>
      {children}
    </StaffContext.Provider>
  );
}

export function useStaff() {
  const context = useContext(StaffContext);
  if (context === undefined) {
    throw new Error("useStaff must be used within a StaffProvider");
  }
  return context;
}
