"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useRouter } from "next/navigation";

interface User {
  id: string;
  name: string;
  email: string;
  companyName?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (
    email: string,
    password: string,
    rememberMe?: boolean,
  ) => Promise<void>;
  signup: (
    name: string,
    email: string,
    password: string,
    companyName: string,
  ) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const router = useRouter();

  // Check if user is already logged in
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem("user");
      if (storedUser) {
        setUser(JSON.parse(storedUser));
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error("Failed to load user from localStorage:", error);
    }
  }, []);

  const login = async (
    email: string,
    _password: string,
    rememberMe?: boolean,
  ) => {
    // This is a mock implementation
    // In a real app, you would call your API here
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Mock user data
      const userData: User = {
        id: "1",
        name: "John Doe",
        email: email,
        companyName: "Acme Inc.",
      };

      setUser(userData);
      setIsAuthenticated(true);

      // Store user data in localStorage if rememberMe is true
      if (rememberMe) {
        localStorage.setItem("user", JSON.stringify(userData));
      }

      // Set a cookie for the middleware
      document.cookie = "auth=true; path=/; max-age=86400";

      router.push("/dashboard");
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  };

  const signup = async (
    name: string,
    email: string,
    _password: string,
    companyName: string,
  ) => {
    // This is a mock implementation
    // In a real app, you would call your API here
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Mock user data
      const userData: User = {
        id: "1",
        name: name,
        email: email,
        companyName: companyName,
      };

      setUser(userData);
      setIsAuthenticated(true);

      // Store user data in localStorage
      localStorage.setItem("user", JSON.stringify(userData));

      // Set a cookie for the middleware
      document.cookie = "auth=true; path=/; max-age=86400";

      router.push("/dashboard");
    } catch (error) {
      console.error("Signup failed:", error);
      throw error;
    }
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem("user");

    // Remove the auth cookie
    document.cookie = "auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

    router.push("/sign-in");
  };

  return (
    <AuthContext.Provider
      value={{ user, isAuthenticated, login, signup, logout }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
