"use client";

import { createLazyContextHook } from "@/components/lazy-provider";
import { usePatient as useOriginalPatient } from "./patient-context";
import { useDoctor as useOriginalDoctor } from "./doctor-context";
import { useDepartment as useOriginalDepartment } from "./department-context";
import { useCareType as useOriginalCareType } from "./care-type-context";
import { useStaff as useOriginalStaff } from "./staff-context";
import { useDoctorInvitation as useOriginalDoctorInvitation } from "./doctor-invitation-context";

// Create lazy versions of the context hooks
export const useLazyPatient = createLazyContextHook(
  useOriginalPatient,
  "patient",
);
export const useLazyDoctor = createLazyContextHook(useOriginalDoctor, "doctor");
export const useLazyDepartment = createLazyContextHook(
  useOriginalDepartment,
  "department",
);
export const useLazyCareType = createLazyContextHook(
  useOriginalCareType,
  "careType",
);
export const useLazyStaff = createLazyContextHook(useOriginalStaff, "staff");
export const useLazyDoctorInvitation = createLazyContextHook(
  useOriginalDoctorInvitation,
  "doctorInvitation",
);
