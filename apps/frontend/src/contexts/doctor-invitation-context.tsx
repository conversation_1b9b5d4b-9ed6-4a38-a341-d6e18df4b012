"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useOrganization } from "./organization-context";

interface DoctorInvitation {
  id: string;
  email: string;
  token: string;
  status: string;
  expiresAt: string;
  organizationId: string;
  userId?: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface DoctorInvitationContextType {
  invitations: DoctorInvitation[];
  isLoading: boolean;
  refreshInvitations: (status?: string) => Promise<void>;
}

const DoctorInvitationContext = createContext<
  DoctorInvitationContextType | undefined
>(undefined);

export function DoctorInvitationProvider({
  children,
}: {
  children: ReactNode;
}) {
  const [invitations, setInvitations] = useState<DoctorInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentOrganization } = useOrganization();

  const refreshInvitations = async (status?: string) => {
    if (!currentOrganization) {
      setInvitations([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      let url = "/api/doctor-invitations";
      const params = new URLSearchParams();

      if (status) {
        params.append("status", status);
      }

      // Add a timestamp to prevent caching
      params.append("_t", Date.now().toString());

      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      const response = await fetch(url, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setInvitations(data.invitations || []);
      } else {
        console.error("Failed to fetch doctor invitations");
        setInvitations([]);
      }
    } catch (error) {
      console.error("Error fetching doctor invitations:", error);
      setInvitations([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshInvitations();
  }, [currentOrganization]);

  return (
    <DoctorInvitationContext.Provider
      value={{ invitations, isLoading, refreshInvitations }}
    >
      {children}
    </DoctorInvitationContext.Provider>
  );
}

export function useDoctorInvitation() {
  const context = useContext(DoctorInvitationContext);
  if (context === undefined) {
    throw new Error(
      "useDoctorInvitation must be used within a DoctorInvitationProvider",
    );
  }
  return context;
}
