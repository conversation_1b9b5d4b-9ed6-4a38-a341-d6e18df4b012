"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import { useOrganization } from "@/contexts/organization-context";

export interface Branch {
  id: string;
  name: string;
  facilityType: string;
  phone?: string | null;
  email?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  pincode?: string | null;
  latitude?: string | null;
  longitude?: string | null;
  isHeadOffice: boolean;
  organizationId: string;
}

interface BranchContextType {
  currentBranch: Branch | null;
  branches: Branch[];
  setCurrentBranch: (branch: Branch) => Promise<void>;
  isLoading: boolean;
  refreshBranches: () => Promise<void>;
}

const BranchContext = createContext<BranchContextType | undefined>(undefined);

export function BranchProvider({ children }: { children: React.ReactNode }) {
  const [currentBranch, setCurrentBranchState] = useState<Branch | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentOrganization, isLoading: isOrgLoading } = useOrganization();

  const setCurrentBranch = useCallback(async (branch: Branch) => {
    try {
      const response = await fetch("/api/branches/default", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ branchId: branch.id }),
      });

      if (response.ok) {
        setCurrentBranchState(branch);
      } else {
        console.error("Failed to set default branch");
      }
    } catch (error) {
      console.error("Error setting default branch:", error);
    }
  }, []);

  const refreshBranches = useCallback(async () => {
    if (!currentOrganization) {
      setBranches([]);
      setCurrentBranchState(null);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      // First, try to get current branch from cookie
      let currentBranchFromCookie: Branch | null = null;
      try {
        const cookies = document.cookie.split(";");
        const branchCookie = cookies.find((cookie) =>
          cookie.trim().startsWith("current-branch="),
        );

        if (branchCookie) {
          const branchValue = branchCookie.split("=")[1];
          const decodedValue = decodeURIComponent(branchValue);
          const branchData = JSON.parse(decodedValue);

          // Convert cookie format to Branch format
          currentBranchFromCookie = {
            id: branchData.id,
            name: branchData.name,
            isHeadOffice: branchData.isHeadOffice || false,
            facilityType: "clinic", // Default value
            organizationId: currentOrganization.id,
          };
        }
      } catch (error) {
        console.error("Error parsing current-branch cookie:", error);
      }

      const response = await fetch("/api/branches");
      if (response.ok) {
        const data = await response.json();
        setBranches(data.branches || []);

        // Use branch from cookie if available, otherwise use API response
        if (currentBranchFromCookie) {
          setCurrentBranchState(currentBranchFromCookie);
        } else if (data.currentBranch) {
          setCurrentBranchState(data.currentBranch);
        } else if (data.branches && data.branches.length > 0) {
          // Default to head office or first branch
          const headOffice = data.branches.find((b: Branch) => b.isHeadOffice);
          const defaultBranch = headOffice || data.branches[0];
          setCurrentBranchState(defaultBranch);

          // Set the default branch via API
          setCurrentBranch(defaultBranch);
        } else {
          setCurrentBranchState(null);
        }
      } else {
        console.error("Failed to fetch branches");
        setBranches([]);
        setCurrentBranchState(null);
      }
    } catch (error) {
      console.error("Error fetching branches:", error);
      setBranches([]);
      setCurrentBranchState(null);
    } finally {
      setIsLoading(false);
    }
  }, [currentOrganization, setCurrentBranch]);

  // Refresh branches when the organization changes
  useEffect(() => {
    if (!isOrgLoading && currentOrganization) {
      refreshBranches();
    }
  }, [currentOrganization?.id, isOrgLoading, refreshBranches]); // Only depend on organization ID changes

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      currentBranch,
      branches,
      setCurrentBranch,
      isLoading,
      refreshBranches,
    }),
    [currentBranch, branches, setCurrentBranch, isLoading, refreshBranches],
  );

  return (
    <BranchContext.Provider value={contextValue}>
      {children}
    </BranchContext.Provider>
  );
}

export function useBranch() {
  const context = useContext(BranchContext);
  if (context === undefined) {
    throw new Error("useBranch must be used within a BranchProvider");
  }
  return context;
}
