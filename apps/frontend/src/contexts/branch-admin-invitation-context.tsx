"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useOrganization } from "./organization-context";
import { useBranch } from "./branch-context";

interface BranchAdminInvitation {
  id: string;
  email: string;
  token: string;
  status: string;
  expiresAt: string;
  organizationId: string;
  branchId: string;
  branch: {
    id: string;
    name: string;
  };
  userId?: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  invitedBy: string;
  inviter: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface BranchAdminInvitationContextType {
  invitations: BranchAdminInvitation[];
  isLoading: boolean;
  refreshInvitations: (status?: string, branchId?: string) => Promise<void>;
}

const BranchAdminInvitationContext = createContext<
  BranchAdminInvitationContextType | undefined
>(undefined);

export function BranchAdminInvitationProvider({
  children,
}: {
  children: ReactNode;
}) {
  const [invitations, setInvitations] = useState<BranchAdminInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentOrganization } = useOrganization();
  const { currentBranch } = useBranch();

  const refreshInvitations = async (status?: string, branchId?: string) => {
    if (!currentOrganization) {
      setInvitations([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      let url = "/api/branch-admin-invitations";
      const params = new URLSearchParams();

      if (status) {
        params.append("status", status);
      }

      if (branchId) {
        params.append("branchId", branchId);
      } else if (currentBranch) {
        // If no specific branch ID is provided, use the current branch
        params.append("branchId", currentBranch.id);
      }

      // Add a timestamp to prevent caching
      params.append("_t", Date.now().toString());

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setInvitations(data.invitations || []);
      } else {
        console.error("Failed to fetch branch admin invitations");
        setInvitations([]);
      }
    } catch (error) {
      console.error("Error fetching branch admin invitations:", error);
      setInvitations([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (currentOrganization) {
      refreshInvitations();
    }
  }, [currentOrganization?.id]);

  // Refresh invitations when branch changes
  useEffect(() => {
    if (currentBranch && currentOrganization) {
      refreshInvitations(undefined, currentBranch.id);
    }
  }, [currentBranch?.id]);

  return (
    <BranchAdminInvitationContext.Provider
      value={{ invitations, isLoading, refreshInvitations }}
    >
      {children}
    </BranchAdminInvitationContext.Provider>
  );
}

export function useBranchAdminInvitation() {
  const context = useContext(BranchAdminInvitationContext);
  if (context === undefined) {
    throw new Error(
      "useBranchAdminInvitation must be used within a BranchAdminInvitationProvider",
    );
  }
  return context;
}
