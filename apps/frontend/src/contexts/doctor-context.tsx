"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useOrganization } from "./organization-context";

interface Doctor {
  id: string;
  userId: string;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  profileDescription?: string;
  specializations?: string;
  qualifications?: string;
  contactEmail?: string;
  contactPhone?: string;
  joiningDate?: string;
  yearsOfExperience: number;
  status: string;
  departmentId?: string;
  department?: {
    id: string;
    name: string;
  };
  branches: {
    id: string;
    branchId: string;
    branch: {
      id: string;
      name: string;
    };
  }[];
  createdAt: string;
  updatedAt: string;
}

interface DoctorContextType {
  doctors: Doctor[];
  isLoading: boolean;
  refreshDoctors: (
    branchId?: string,
    departmentId?: string,
    status?: string,
  ) => Promise<void>;
}

const DoctorContext = createContext<DoctorContextType | undefined>(undefined);

export function DoctorProvider({ children }: { children: ReactNode }) {
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentOrganization } = useOrganization();

  const refreshDoctors = async (
    branchId?: string,
    departmentId?: string,
    status?: string,
  ) => {
    if (!currentOrganization) {
      setDoctors([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      let url = "/api/doctors";
      const params = new URLSearchParams();

      if (branchId) {
        params.append("branchId", branchId);
      }

      if (departmentId) {
        params.append("departmentId", departmentId);
      }

      if (status) {
        params.append("status", status);
      }

      // Add a timestamp to prevent caching
      params.append("_t", Date.now().toString());

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setDoctors(data.doctors || []);
      } else {
        console.error("Failed to fetch doctors");
        setDoctors([]);
      }
    } catch (error) {
      console.error("Error fetching doctors:", error);
      setDoctors([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshDoctors();
  }, [currentOrganization]);

  return (
    <DoctorContext.Provider value={{ doctors, isLoading, refreshDoctors }}>
      {children}
    </DoctorContext.Provider>
  );
}

export function useDoctor() {
  const context = useContext(DoctorContext);
  if (context === undefined) {
    throw new Error("useDoctor must be used within a DoctorProvider");
  }
  return context;
}
