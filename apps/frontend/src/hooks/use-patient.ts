"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  bloodGroup?: string;
  maritalStatus?: string;
  occupation?: string;
  email?: string;
  phone: string;
  alternatePhone?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country: string;
  allergies?: string;
  chronicDiseases?: string;
  currentMedications?: string;
  familyMedicalHistory?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelation?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  insuranceExpiryDate?: string;
  registrationDate: string;
  status: string;
  primaryBranchId: string;
  primaryBranch?: {
    id: string;
    name: string;
  };
  organizationId: string;
  userId?: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  // ABHA Information
  abhaNumber?: string;
  abhaAddress?: string;
  healthIdNumber?: string;
  abhaCardUrl?: string;
  documents?: any[];
  createdAt: string;
  updatedAt: string;
}

interface UsePatientResult {
  patient: Patient | null;
  isLoading: boolean;
  error: string | null;
  refreshPatient: () => Promise<void>;
}

export function usePatient(patientId: string): UsePatientResult {
  const [patient, setPatient] = useState<Patient | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshPatient = async () => {
    if (!patientId) {
      setPatient(null);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/patients/${patientId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch patient");
      }

      const data = await response.json();
      setPatient(data.patient);
    } catch (error) {
      console.error("Error fetching patient:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load patient",
      );
      toast.error("Failed to load patient details");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshPatient();
  }, [patientId]);

  return { patient, isLoading, error, refreshPatient };
}
