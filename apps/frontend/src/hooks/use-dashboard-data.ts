"use client";

import { useState, useEffect } from "react";
import { fetchWithCache } from "@/lib/api-utils";
import { toast } from "sonner";

interface UseDashboardDataOptions<T> {
  url: string;
  fetchOptions?: RequestInit;
  cacheKey?: string;
  cacheTime?: number;
  fallbackData?: T;
  dependencies?: any[];
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  enabled?: boolean;
}

/**
 * Custom hook for fetching dashboard data with caching and error handling
 */
export function useDashboardData<T>(options: UseDashboardDataOptions<T>) {
  const {
    url,
    fetchOptions,
    cacheKey,
    cacheTime = 5 * 60 * 1000, // 5 minutes default
    fallbackData,
    dependencies = [],
    onSuccess,
    onError,
    enabled = true,
  } = options;

  const [data, setData] = useState<T | undefined>(fallbackData);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Skip fetching if disabled
    if (!enabled) {
      setIsLoading(false);
      return;
    }

    let isMounted = true;
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await fetchWithCache<T>(
          url,
          fetchOptions,
          cacheKey,
          cacheTime,
          fallbackData,
        );

        if (isMounted) {
          setData(result);
          setIsLoading(false);
          onSuccess?.(result);
        }
      } catch (err) {
        if (isMounted) {
          const error = err instanceof Error ? err : new Error(String(err));
          setError(error);
          setIsLoading(false);
          onError?.(error);

          // Show error toast if no onError handler is provided
          if (!onError) {
            toast.error(`Failed to fetch data: ${error.message}`);
          }
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [url, ...dependencies]);

  // Function to manually refetch data
  const refetch = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Skip cache when manually refetching
      const result = await fetchWithCache<T>(
        url,
        fetchOptions,
        undefined, // Skip cache
        cacheTime,
        fallbackData,
      );

      setData(result);
      setIsLoading(false);
      onSuccess?.(result);

      // Update cache with new data
      if (cacheKey) {
        localStorage.setItem(
          `cache_${cacheKey}`,
          JSON.stringify({
            data: result,
            expiration: Date.now() + cacheTime,
          }),
        );
      }

      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      setIsLoading(false);
      onError?.(error);

      // Show error toast if no onError handler is provided
      if (!onError) {
        toast.error(`Failed to fetch data: ${error.message}`);
      }

      throw error;
    }
  };

  return { data, isLoading, error, refetch };
}
