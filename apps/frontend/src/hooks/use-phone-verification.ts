import { useState, useCallback } from "react";
import { toast } from "sonner";

interface UsePhoneVerificationProps {
  onVerificationSuccess?: () => void;
  onVerificationError?: (error: string) => void;
}

interface PhoneVerificationState {
  isLoading: boolean;
  isVerifying: boolean;
  isVerified: boolean;
  otpSent: boolean;
  error: string | null;
  remainingAttempts?: number;
  expiresAt?: string;
  cooldownTime: number;
}

export function usePhoneVerification({
  onVerificationSuccess,
  onVerificationError,
}: UsePhoneVerificationProps = {}) {
  const [state, setState] = useState<PhoneVerificationState>({
    isLoading: false,
    isVerifying: false,
    isVerified: false,
    otpSent: false,
    error: null,
    cooldownTime: 0,
  });

  // Send OTP to phone number
  const sendOTP = useCallback(
    async (
      phone: string,
      purpose = "phone-verification",
      patientId?: string,
    ) => {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const response = await fetch("/api/phone/send-otp", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ phone, purpose, patientId }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to send OTP");
        }

        setState((prev) => ({
          ...prev,
          isLoading: false,
          otpSent: true,
          expiresAt: data.expiresAt,
          cooldownTime: 60, // 60 seconds cooldown
        }));

        // Start cooldown timer
        const timer = setInterval(() => {
          setState((prev) => {
            if (prev.cooldownTime <= 1) {
              clearInterval(timer);
              return { ...prev, cooldownTime: 0 };
            }
            return { ...prev, cooldownTime: prev.cooldownTime - 1 };
          });
        }, 1000);

        toast.success("OTP sent successfully to your phone number");
        return { success: true, data };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to send OTP";
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        toast.error(errorMessage);
        onVerificationError?.(errorMessage);
        return { success: false, error: errorMessage };
      }
    },
    [onVerificationError],
  );

  // Verify OTP
  const verifyOTP = useCallback(
    async (phone: string, otp: string, purpose = "phone-verification") => {
      setState((prev) => ({ ...prev, isVerifying: true, error: null }));

      try {
        const response = await fetch("/api/phone/verify-otp", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ phone, otp, purpose }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to verify OTP");
        }

        setState((prev) => ({
          ...prev,
          isVerifying: false,
          isVerified: true,
          error: null,
        }));

        toast.success("Phone number verified successfully");
        onVerificationSuccess?.();
        return { success: true, data };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to verify OTP";
        setState((prev) => ({
          ...prev,
          isVerifying: false,
          error: errorMessage,
          remainingAttempts: (error as any).remainingAttempts,
        }));
        toast.error(errorMessage);
        onVerificationError?.(errorMessage);
        return { success: false, error: errorMessage };
      }
    },
    [onVerificationSuccess, onVerificationError],
  );

  // Reset verification state
  const resetVerification = useCallback(() => {
    setState({
      isLoading: false,
      isVerifying: false,
      isVerified: false,
      otpSent: false,
      error: null,
      cooldownTime: 0,
    });
  }, []);

  // Resend OTP
  const resendOTP = useCallback(
    async (
      phone: string,
      purpose = "phone-verification",
      patientId?: string,
    ) => {
      if (state.cooldownTime > 0) {
        toast.error(
          `Please wait ${state.cooldownTime} seconds before resending`,
        );
        return { success: false, error: "Cooldown active" };
      }
      return sendOTP(phone, purpose, patientId);
    },
    [state.cooldownTime, sendOTP],
  );

  return {
    ...state,
    sendOTP,
    verifyOTP,
    resendOTP,
    resetVerification,
  };
}
