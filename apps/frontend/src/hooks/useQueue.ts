import { useEffect, useState, useCallback } from "react";
import { useSocket } from "@/components/providers/socket-provider";
import { emitQueueStatusUpdate } from "@/lib/socket-client";
import { useOrganization } from "@/contexts/organization-context";
import { useBranch } from "@/contexts/branch-context";

export interface QueueItem {
  id: string;
  patientId: string;
  patientName: string;
  status: "waiting" | "in-consultation" | "paused" | "completed" | "cancelled";
  position: number;
  doctorId: string;
  doctorName?: string;
  branchId: string;
  branchName?: string;
  organizationId: string;
  appointmentId: string;
  estimatedStartTime?: string | Date;
  actualStartTime?: string | Date;
  completionTime?: string | Date;
  pauseReason?: string;
  notes?: string;
  appointmentDate?: string | Date;
  startTime?: string;
  endTime?: string;
  duration?: number;
  createdAt: string;
  updatedAt: string;
}

export const useQueue = (doctorId?: string, date?: Date) => {
  const [queueItems, setQueueItems] = useState<QueueItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { socket, isConnected } = useSocket();
  const { currentOrganization: organization } = useOrganization();
  const { currentBranch: branch } = useBranch();

  // Fetch queue items from the API
  const fetchQueueItems = useCallback(async () => {
    if (!branch?.id) return;

    try {
      setIsLoading(true);
      setError(null);

      // Format date parameter if provided
      let dateParam = "";
      if (date) {
        // Format date as YYYY-MM-DD to avoid timezone issues
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
        const day = String(date.getDate()).padStart(2, "0");
        const formattedDate = `${year}-${month}-${day}`;
        dateParam = `&date=${formattedDate}`;
        console.log(`useQueue hook using date: ${formattedDate}`);
      }

      const url = doctorId
        ? `/api/queue?branchId=${branch.id}&doctorId=${doctorId}${dateParam}`
        : `/api/queue?branchId=${branch.id}${dateParam}`;

      console.log("Fetching queue items with URL:", url);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to fetch queue items");
      }

      const data = await response.json();
      if (data.queueStatuses) {
        setQueueItems(data.queueStatuses);
      } else {
        setQueueItems([]);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred",
      );
      console.error("Error fetching queue items:", err);
    } finally {
      setIsLoading(false);
    }
  }, [branch?.id, doctorId, date]);

  // Update a queue item
  const updateQueueItem = useCallback(
    async (queueId: string, status: QueueItem["status"], position?: number) => {
      if (!organization?.id || !branch?.id) {
        setError("Organization or branch not available");
        return;
      }

      try {
        const response = await fetch(`/api/queue/${queueId}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ status, position }),
        });

        if (!response.ok) {
          throw new Error("Failed to update queue item");
        }

        const updatedItem = await response.json();

        // Update local state
        setQueueItems((prev) =>
          prev.map((item) => (item.id === queueId ? updatedItem : item)),
        );

        // Emit socket event
        emitQueueStatusUpdate(
          organization.id,
          branch.id,
          updatedItem.doctorId,
          {
            queueId,
            status,
            position,
            updatedAt: new Date().toISOString(),
          },
        );

        return updatedItem;
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred",
        );
        console.error("Error updating queue item:", err);
        return null;
      }
    },
    [organization?.id, branch?.id],
  );

  // Join socket rooms when connected
  const { joinRooms } = useSocket();

  useEffect(() => {
    if (!isConnected) return;

    // Use the joinRooms function from the socket provider
    if (organization?.id) {
      joinRooms(organization.id, branch?.id, doctorId);
    }
  }, [isConnected, joinRooms, organization?.id, branch?.id, doctorId]);

  // Listen for queue updates
  useEffect(() => {
    if (!isConnected || !socket) return;

    const handleQueueUpdate = (data: any) => {
      console.log("Queue status updated:", data);

      // If we have the formatted queue item, update it directly in state
      if (data.formattedQueue) {
        setQueueItems((prevItems) => {
          // Check if the item already exists in our state
          const existingItemIndex = prevItems.findIndex(
            (item) => item.id === data.formattedQueue.id,
          );

          // If the item exists, update it
          if (existingItemIndex !== -1) {
            const updatedItems = [...prevItems];
            updatedItems[existingItemIndex] = data.formattedQueue;
            return updatedItems;
          }

          // If the item doesn't exist, add it (if it belongs to our view)
          if (
            (!doctorId || data.formattedQueue.doctorId === doctorId) &&
            (!branch?.id || data.formattedQueue.branchId === branch.id)
          ) {
            return [...prevItems, data.formattedQueue];
          }

          return prevItems;
        });
      } else {
        // If we don't have the formatted queue item, refresh the entire queue
        fetchQueueItems();
      }
    };

    socket.on("queue-status-updated", handleQueueUpdate);

    return () => {
      socket.off("queue-status-updated", handleQueueUpdate);
    };
  }, [isConnected, socket, fetchQueueItems, doctorId, branch?.id]);

  // Initial fetch
  useEffect(() => {
    if (branch?.id) {
      fetchQueueItems();
    }
  }, [branch?.id, fetchQueueItems, date]);

  return {
    queueItems,
    isLoading,
    error,
    fetchQueueItems,
    updateQueueItem,
  };
};
