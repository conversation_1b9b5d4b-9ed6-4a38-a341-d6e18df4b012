// This file is deprecated. Use the SocketProvider and useSocket hook from @/components/providers/socket-provider.tsx instead.
// Keeping this file for backward compatibility.

import { useEffect, useState } from "react";
import { useOrganization } from "@/contexts/organization-context";
import {
  connectSocket,
  disconnectSocket,
  getSocket,
  joinBranch,
  joinDoctor,
  joinOrganization,
} from "@/lib/socket-client";

// Define a hook to use Socket.io in components
export const useSocket = (branchId?: string, doctorId?: string) => {
  const [isConnected, setIsConnected] = useState(false);
  const { currentOrganization: organization } = useOrganization();
  const socketInstance = getSocket();

  useEffect(() => {
    // Connect to the socket server
    connectSocket();

    // Set up event handlers
    const onConnect = () => {
      console.log("Socket connected with ID:", socketInstance.id);
      setIsConnected(true);

      // Join organization room if available
      if (organization?.id) {
        joinOrganization(organization.id);
      }

      // Join branch room if available
      if (branchId) {
        joinBranch(branchId);
      }

      // Join doctor room if available
      if (doctorId) {
        joinDoctor(doctorId);
      }
    };

    const onDisconnect = () => {
      console.log("Socket disconnected");
      setIsConnected(false);
    };

    socketInstance.on("connect", onConnect);
    socketInstance.on("disconnect", onDisconnect);

    // Clean up on unmount
    return () => {
      socketInstance.off("connect", onConnect);
      socketInstance.off("disconnect", onDisconnect);
      disconnectSocket();
    };
  }, [organization?.id, branchId, doctorId, socketInstance]);

  return { socket: socketInstance, isConnected };
};

// Client-side only version of the hook
export const useSocketClient = (branchId?: string, doctorId?: string) => {
  const [isConnected, setIsConnected] = useState(false);
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const socketInstance = getSocket();

  useEffect(() => {
    // Get organization ID from cookies on the client side
    const getUserInfoFromCookie = () => {
      try {
        const userInfoCookie = document.cookie
          .split("; ")
          .find((row) => row.startsWith("user-info="));

        if (userInfoCookie) {
          const userInfo = JSON.parse(
            decodeURIComponent(userInfoCookie.split("=")[1]),
          );
          return userInfo.organizationId || null;
        }
        return null;
      } catch (error) {
        console.error("Error parsing user info from cookie:", error);
        return null;
      }
    };

    const orgId = getUserInfoFromCookie();
    setOrganizationId(orgId);

    if (!orgId) return;

    // Connect to the socket server
    connectSocket();

    // Set up event handlers
    const onConnect = () => {
      console.log("Socket connected with ID:", socketInstance.id);
      setIsConnected(true);

      // Join organization room
      joinOrganization(orgId);

      // Join branch room if available
      if (branchId) {
        joinBranch(branchId);
      }

      // Join doctor room if available
      if (doctorId) {
        joinDoctor(doctorId);
      }
    };

    const onDisconnect = () => {
      console.log("Socket disconnected");
      setIsConnected(false);
    };

    socketInstance.on("connect", onConnect);
    socketInstance.on("disconnect", onDisconnect);

    // Clean up on unmount
    return () => {
      socketInstance.off("connect", onConnect);
      socketInstance.off("disconnect", onDisconnect);
      disconnectSocket();
    };
  }, [branchId, doctorId, socketInstance]);

  return { socket: socketInstance, isConnected, organizationId };
};
