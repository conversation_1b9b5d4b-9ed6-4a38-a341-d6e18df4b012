"use client";

import { useEffect, useRef, useState } from "react";
import { useSocket } from "@/components/providers/socket-provider";
import { joinBranch, joinDoctor, joinOrganization } from "@/lib/socket-client";
import { toast } from "sonner";

interface UseSocketEventsOptions {
  organizationId?: string;
  branchId?: string;
  doctorId?: string;
  events: {
    name: string;
    handler: (data: any) => void;
  }[];
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Error) => void;
  showToasts?: boolean;
}

/**
 * Custom hook for handling socket events with automatic room joining
 */
export function useSocketEvents(options: UseSocketEventsOptions) {
  const {
    organizationId,
    branchId,
    doctorId,
    events,
    onConnect,
    onDisconnect,
    onError,
    showToasts = false,
  } = options;

  const { socket, isConnected } = useSocket();
  const [isJoined, setIsJoined] = useState(false);
  const eventsRef = useRef(events);

  // Update events ref when events change
  useEffect(() => {
    eventsRef.current = events;
  }, [events]);

  // Handle socket connection and room joining
  useEffect(() => {
    if (!socket || !isConnected) {
      setIsJoined(false);
      return;
    }

    let mounted = true;
    const joinRooms = async () => {
      try {
        // Join organization room
        if (organizationId) {
          await joinOrganization(organizationId);
          if (showToasts) {
            toast.success("Connected to organization updates");
          }
        }

        // Join branch room
        if (branchId) {
          await joinBranch(branchId);
          if (showToasts) {
            toast.success("Connected to branch updates");
          }
        }

        // Join doctor room
        if (doctorId) {
          await joinDoctor(doctorId);
          if (showToasts) {
            toast.success("Connected to doctor updates");
          }
        }

        if (mounted) {
          setIsJoined(true);
          onConnect?.();
        }
      } catch (error) {
        console.error("Error joining rooms:", error);
        if (mounted) {
          onError?.(error instanceof Error ? error : new Error(String(error)));
          if (showToasts) {
            toast.error("Failed to connect to real-time updates");
          }
        }
      }
    };

    joinRooms();

    return () => {
      mounted = false;
    };
  }, [
    socket,
    isConnected,
    organizationId,
    branchId,
    doctorId,
    onConnect,
    onError,
    showToasts,
  ]);

  // Handle socket events
  useEffect(() => {
    if (!socket || !isConnected || !isJoined) {
      return;
    }

    // Register event handlers
    eventsRef.current.forEach((event) => {
      socket.on(event.name, event.handler);
    });

    // Handle disconnect
    const handleDisconnect = () => {
      setIsJoined(false);
      onDisconnect?.();
      if (showToasts) {
        toast.error("Disconnected from real-time updates");
      }
    };

    socket.on("disconnect", handleDisconnect);

    // Cleanup event handlers
    return () => {
      eventsRef.current.forEach((event) => {
        socket.off(event.name, event.handler);
      });
      socket.off("disconnect", handleDisconnect);
    };
  }, [socket, isConnected, isJoined, onDisconnect, showToasts]);

  return {
    isConnected,
    isJoined,
    socket,
  };
}
