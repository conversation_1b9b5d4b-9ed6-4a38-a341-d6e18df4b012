import { useState, useEffect } from "react";

// Helper function to get cookie value by name
function getCookie(name: string): string | null {
  if (typeof document === "undefined") return null; // SSR check

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(";").shift() || null;
  }
  return null;
}

export function useCurrentBranch() {
  const [currentBranch, setCurrentBranch] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to update the current branch via API
  const updateCurrentBranch = async (branchId: string) => {
    try {
      await fetch("/api/branches/default", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ branchId }),
      });
    } catch (error) {
      console.error("Error updating current branch:", error);
    }
  };

  useEffect(() => {
    const getBranchFromCookies = () => {
      setIsLoading(true);

      try {
        // Then check if we have current-branch cookie
        const currentBranchCookie = getCookie("current-branch");
        if (currentBranchCookie) {
          try {
            const branchInfo = JSON.parse(
              decodeURIComponent(currentBranchCookie),
            );

            // Get the branch ID from cookie (handle both formats)
            const branchId = branchInfo.id || branchInfo.branchId;

            if (branchId) {
              // Fetch complete branch data including hipId from API
              fetchBranchById(branchId);
              return;
            }
          } catch (error) {}
        }

        // If no branch cookies found, fetch from API
        fetchBranchFromAPI();
      } catch (error) {
        fetchBranchFromAPI();
      }
    };

    const fetchBranchById = async (branchId: string) => {
      try {
        // Fetch specific branch data including hipId
        const response = await fetch(`/api/branches/${branchId}`);
        const data = await response.json();

        if (response.ok && data) {
          setCurrentBranch(data);
          setIsLoading(false);
        } else {
          // If specific branch fetch fails, fall back to general API
          fetchBranchFromAPI();
        }
      } catch (error) {
        console.error("Error fetching specific branch:", error);
        // If specific branch fetch fails, fall back to general API
        fetchBranchFromAPI();
      }
    };

    const fetchBranchFromAPI = async () => {
      try {
        // First try to get the current branch
        const currentBranchResponse = await fetch("/api/branches");
        const data = await currentBranchResponse.json();

        if (currentBranchResponse.ok) {
          if (data.currentBranch) {
            // If we have a current branch, use it
            setCurrentBranch(data.currentBranch);
          } else if (data.branches?.length > 0) {
            // Otherwise, use the first branch or head office
            const headOffice = data.branches.find((b: any) => b.isHeadOffice);
            const branch = headOffice || data.branches[0];

            setCurrentBranch(branch);

            // Set this as the current branch
            await updateCurrentBranch(branch.id);
          } else {
            setError("No branches available");
          }
        } else {
          setError("Failed to fetch branches");
        }
      } catch (error) {
        console.error("Error fetching branch data:", error);
        setError("Failed to fetch branches");
      } finally {
        setIsLoading(false);
      }
    };

    getBranchFromCookies();
  }, []);

  // Wrapper for setCurrentBranch that also updates the API
  const setCurrent = async (branch: any) => {
    setCurrentBranch(branch);
    await updateCurrentBranch(branch.id);
  };

  return { currentBranch, isLoading, error, setCurrentBranch: setCurrent };
}
