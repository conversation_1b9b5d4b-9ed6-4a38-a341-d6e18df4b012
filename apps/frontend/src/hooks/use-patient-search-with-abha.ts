"use client";

import { useState, useCallback } from "react";
import {
  searchPhoneAndCheckABHA,
  isPhoneNumber
} from "@/services/patient-service";
import { toast } from "sonner";

// Types
export interface PatientSearchResult {
  patient: any | null;
  abhaData: any[] | null;
  abhaError: string | null;
  hasPatient: boolean;
  hasAbha: boolean;
  patientHasAbhaProfile: boolean;
}

export interface SearchBannerState {
  show: boolean;
  type:
    | "verify-abha"
    | "create-abha"
    | "verify-register"
    | "create-register"
    | null;
  message: string;
  patient?: any;
  abhaData?: any[];
  phone?: string;
  name?: string;
}

export interface UsePatientSearchWithABHAResult {
  searchResult: PatientSearchResult | null;
  bannerStates: SearchBannerState[];
  isSearching: boolean;
  searchError: string | null;
  searchPhoneWithABHA: (phone: string) => Promise<void>;
  clearSearch: () => void;
  hideBanner: (index: number) => void;
}

export function usePatientSearchWithABHA(): UsePatientSearchWithABHAResult {
  const [searchResult, setSearchResult] = useState<PatientSearchResult | null>(
    null,
  );
  const [bannerStates, setBannerStates] = useState<SearchBannerState[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);



  const searchPhoneWithABHA = useCallback(async (phone: string) => {
    if (!isPhoneNumber(phone)) return;

    setIsSearching(true);
    setSearchError(null);
    setBannerStates([]);

    try {
      const result = await searchPhoneAndCheckABHA(phone);
      console.log("Search Result:", result);
      console.log("ABHA Raw Data:", JSON.stringify(result.abhaData, null, 2));
      setSearchResult(result);

      const newBanners: SearchBannerState[] = [];

      if (result.hasPatient) {
        if (result.patientHasAbhaProfile) {
          // Patient already has ABHA, no banners needed
        } else if (result.hasAbha && Array.isArray(result.abhaData)) {
          // Patient without ABHA profile, but matching ABHA entries exist
          result.abhaData.forEach((entry) => {
            if (Array.isArray(entry?.ABHA)) {
              entry.ABHA.forEach((abhaEntry: any) => {
                newBanners.push({
                  show: true,
                  type: "verify-abha",
                  message: "ABHA Found – Verify ABHA for this Patient",
                  patient: result.patient,
                  abhaData: [abhaEntry],
                  phone,
                });
              });
            }
          });
        } else {
          // Patient without ABHA, and no ABHA entries found
          newBanners.push({
            show: true,
            type: "create-abha",
            message: "No ABHA Found – Create ABHA for this Patient",
            patient: result.patient,
            phone,
          });
        }
      } else {
        if (result.hasAbha && Array.isArray(result.abhaData)) {
          // No patient, but ABHA entries found
          result.abhaData.forEach((entry) => {
            if (Array.isArray(entry?.ABHA)) {
              entry.ABHA.forEach((abhaEntry: any) => {
                newBanners.push({
                  show: true,
                  type: "verify-register",
                  message: "ABHA Found – Verify and Register Patient with ABHA",
                  abhaData: [abhaEntry],
                  phone,
                });
              });
            }
          });
        } else {
          // No patient, no ABHA – create ABHA and register
          newBanners.push({
            show: true,
            type: "create-register",
            message: "No ABHA Found – Create ABHA and Register New Patient",
            phone,
          });
        }
      }

      console.log("Prepared banners:", JSON.stringify(newBanners, null, 2));
      setBannerStates(newBanners);
    } catch (error) {
      console.error("Error searching phone with ABHA:", error);
      setSearchError(error instanceof Error ? error.message : "Search failed");
      toast.error("Failed to search patient and ABHA information");
    } finally {
      setIsSearching(false);
    }
  }, []);

  const clearSearch = useCallback(() => {
    setSearchResult(null);
    setBannerStates([]);
    setSearchError(null);
  }, []);

  const hideBanner = useCallback((index: number) => {
    setBannerStates((prev) => {
      const newBanners = [...prev];
      newBanners.splice(index, 1);
      return newBanners;
    });
  }, []);

  return {
    searchResult,
    bannerStates,
    isSearching,
    searchError,
    searchPhoneWithABHA,
    clearSearch,
    hideBanner,
  };
}
