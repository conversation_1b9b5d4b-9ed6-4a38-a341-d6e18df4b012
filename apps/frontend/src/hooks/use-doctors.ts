"use client";

import { useState, useEffect } from "react";

export function useDoctors() {
  const [doctors, setDoctors] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDoctors = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch("/api/doctors");
        const data = await response.json();

        if (response.ok) {
          setDoctors(data.doctors || []);
        } else {
          setError(data.error || "Failed to fetch doctors");
          console.error("Error fetching doctors:", data.error);
        }
      } catch (err) {
        setError("An error occurred while fetching doctors");
        console.error("Error fetching doctors:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDoctors();
  }, []);

  return { doctors, isLoading, error };
}
