import { useEffect, useCallback, useState } from "react";
import { isConsentExpired } from "@/utils/consent-utils";

interface UseConsentExpiryMonitorProps {
  consent: any;
  onExpired?: () => void;
  checkInterval?: number; // in milliseconds, default 60000 (1 minute)
}

/**
 * Hook to monitor consent expiration in real-time
 * Checks if a consent has expired based on dataEraseAt or dateRange.to
 * and calls onExpired callback when expiration is detected
 */
export function useConsentExpiryMonitor({
  consent,
  onExpired,
  checkInterval = 60000, // Check every minute by default
}: UseConsentExpiryMonitorProps) {
  const checkExpiration = useCallback(() => {
    if (!consent) return;

    const expired = isConsentExpired(consent);

    if (expired && onExpired) {
      onExpired();
    }

    return expired;
  }, [consent, onExpired]);

  useEffect(() => {
    if (!consent) return;

    // Check immediately
    checkExpiration();

    // Set up interval to check periodically
    const interval = setInterval(checkExpiration, checkInterval);

    return () => clearInterval(interval);
  }, [consent, checkExpiration, checkInterval]);

  return {
    checkExpiration,
  };
}

/**
 * Utility function to get time remaining until consent expires (non-hook version)
 */
export function getConsentTimeRemaining(consent: any) {
  if (!consent) return null;

  const permission =
    typeof consent.permission === "string"
      ? JSON.parse(consent.permission)
      : consent.permission;

  if (!permission) return null;

  const now = new Date();
  let expiryDate: Date | null = null;

  // Only use dataEraseAt for consent expiration
  // dateRange.to represents data access period, not consent validity
  if (permission.dataEraseAt) {
    expiryDate = new Date(permission.dataEraseAt);
  }

  if (!expiryDate) return null;

  const timeRemaining = expiryDate.getTime() - now.getTime();

  if (timeRemaining <= 0) return null; // Already expired

  // Convert to human-readable format
  const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
  );
  const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));

  return {
    totalMs: timeRemaining,
    days,
    hours,
    minutes,
    expiryDate,
    formatted:
      days > 0
        ? `${days} day${days !== 1 ? "s" : ""}, ${hours} hour${hours !== 1 ? "s" : ""}`
        : hours > 0
          ? `${hours} hour${hours !== 1 ? "s" : ""}, ${minutes} minute${minutes !== 1 ? "s" : ""}`
          : `${minutes} minute${minutes !== 1 ? "s" : ""}`,
  };
}

/**
 * Hook to get time remaining until consent expires with automatic updates
 */
export function useConsentTimeRemaining(
  consent: any,
  updateInterval: number = 60000,
) {
  const [timeRemaining, setTimeRemaining] = useState(() =>
    getConsentTimeRemaining(consent),
  );

  useEffect(() => {
    // Update immediately
    setTimeRemaining(getConsentTimeRemaining(consent));

    // Set up interval to update periodically
    const interval = setInterval(() => {
      setTimeRemaining(getConsentTimeRemaining(consent));
    }, updateInterval);

    return () => clearInterval(interval);
  }, [consent, updateInterval]);

  return timeRemaining;
}
