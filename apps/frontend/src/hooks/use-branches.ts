"use client";

import { useState, useEffect } from "react";

export function useBranches() {
  const [branches, setBranches] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBranches = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch("/api/branches");
        const data = await response.json();

        if (response.ok) {
          setBranches(data.branches || []);
        } else {
          setError(data.error || "Failed to fetch branches");
          console.error("Error fetching branches:", data.error);
        }
      } catch (err) {
        setError("An error occurred while fetching branches");
        console.error("Error fetching branches:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBranches();
  }, []);

  return { branches, isLoading, error };
}
