# ABHA Management

ABHA (<PERSON>yu<PERSON>man Bharat Health Account) is a key component of the ABDM ecosystem. This section covers how to manage ABHA profiles in Aran Care HIMS.

## What is ABHA?

ABHA provides patients with:

- A unique health identifier for all healthcare interactions
- Access to their health records across different healthcare providers
- Control over consent for sharing their health information
- Participation in the national digital health ecosystem

## ABHA Management in Aran Care

import { Cards } from 'nextra/components';

<Cards num={3}>
  <Cards.Card arrow title="ABHA Creation" href="/docs/documentation/abha/creation">
    Create new ABHA numbers for patients using Aadhaar verification
  </Cards.Card>
  <Cards.Card arrow title="ABHA Verification" href="/docs/documentation/abha/verification">
    Verify patients using ABHA Address, ABHA Number, Aadhaar, or Mobile
  </Cards.Card>
  <Cards.Card arrow title="Download ABHA Card" href="/docs/documentation/abha/download">
    View and download patient ABHA cards
  </Cards.Card>
</Cards>

## Integration with Patient Management

ABHA management is closely integrated with patient management in Aran Care HIMS:

- ABHA verification during patient registration
- ABHA creation for patients who don't have one
- Linking patient records to their ABHA profile
- Accessing patient health records through ABHA

## Best Practices

- Verify patient identity before ABHA verification or creation
- Ensure mobile numbers are correctly entered for OTP verification
- Keep ABHA profile information up-to-date
- Educate patients about the benefits of ABHA
- Securely store ABHA credentials