# Initiating Care Contexts

This guide explains how to initiate and link care contexts to a patient's ABHA in Aran Care HIMS.

## Prerequisites

Before creating a care context, ensure:

1. **Patient ABHA Verification**
   - Valid ABHA number
   - Verified ABHA address
   - Active ABHA status

2. **Active Consultation**
   - Ongoing or completed consultation
   - Doctor assigned
   - Valid consultation records

3. **Facility Requirements**
   - ABDM-registered as HIP (Health Information Provider)
   - Valid HIP ID
   - Active ABDM integration

4. **Patient-Facility Linking**
   - ABHA linked to your facility
   - Valid linking token
   - Active linking status


## Linking Process

1. **Access Consultation Details**
   - Go to the consultation details page
   - Ensure the consultation is started
   - Locate the "Create Care Context" button in the top banner
   - Click on Create Care Context

![Navigate to consultation details](/docsImages/care-context-initiate-0.png)

2. **Select Health Information Types**
   Available types:
   - Prescription
   - DiagnosticReport
   - OPConsultation
   - DischargeSummary
   - ImmunizationRecord
   - HealthDocumentRecord
   - WellnessRecord
