# Introduction to Care Contexts

A Care Context is a logical bundle of health records. Each HMIS/LMIS system must decide how to organize data for a user into one or more care contexts. A care context is the element that is linked by the HMIS/LMIS with the ABHA address of the user.

![Care Context Dashboard showing the list of linked health records](/docsImages/care-context-list.png)

## Understanding Care Contexts

The HIE-CM (Health Information Exchange - Consent Manager) is "data blind" by design, meaning the HIE-CM should not have any visibility on the content of health records. In Aran Care, a care context contains the following essential information:

1. **A reference ID**: A unique identifier in Aran Care that serves as the primary reference for a clinical encounter. This reference ID:
   - Maps directly to the Consultation ID in our system
   - Enables secure retrieval of associated health records
   - Maintains ABDM compliance through our HRP (Health Repository Provider)
   - Ensures data privacy while allowing authorized access
   - Links all related health information from a single consultation


2. **A display name**: A human-readable description that includes:
   - Doctor's name
   - Consultation date and time

Example display name: "Consultation with Dr. <PERSON> on 15th June 2023 at 2:30 PM"

3. Types of health information **HI Types** included:
   - Prescription
   - Diagnostic Report
   - OPConsultation
   - DischargeSummary
   - ImmunizationRecord
   - HealthDocumentRecord
   - WellnessRecord
   - Invoice


