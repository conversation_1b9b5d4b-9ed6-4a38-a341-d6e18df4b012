# ABDM Service

This directory contains the ABDM (Ayushman Bharat Digital Mission) service implementation for the Aran Care HIMS SaaS application.

## Directory Structure

```
src/services/abdm/
├── abha-create/                # ABHA creation services
│   └── aadhaar/                # ABHA creation using <PERSON><PERSON><PERSON><PERSON>
├── abha-verify/                # ABHA verification services
│   ├── aadhaar/                # ABHA verification using <PERSON><PERSON><PERSON><PERSON>
│   ├── abha-address/           # ABHA verification using ABHA Address
│   ├── abha-id/                # ABHA verification using ABHA ID
│   └── mobile/                 # ABHA verification using Mobile
├── utils/                      # Utility functions
│   ├── auth.ts                 # Authentication utilities
│   ├── request.ts              # Request utilities
│   └── validation.ts           # Validation utilities
└── index.ts                    # Main entry point
```

## Usage

### ABHA Verification

```typescript
import { abhaVerify } from "@/services/abdm";

// Verify ABHA using ABHA Address
const requestOtp = async (abhaAddress: string) => {
  const result =
    await abhaVerify.abhaAddress.requestAbhaAddressOtp(abhaAddress);
  return result;
};

const verifyOtp = async (otp: string, txnId: string) => {
  const result = await abhaVerify.abhaAddress.verifyAbhaAddressOtp(otp, txnId);
  return result;
};
```

### Authentication

```typescript
import { utils } from "@/services/abdm";

// Get access token
const accessToken = await utils.getAccessToken();

// Clear cached token
utils.clearCachedToken();
```

## Adding New Services

To add a new service:

1. Create a new directory under the appropriate category (e.g., `abha-verify/new-method/`)
2. Create the necessary files (e.g., `request-otp.ts`, `verify-otp.ts`)
3. Create an `index.ts` file to export the functions
4. Update the parent `index.ts` file to export the new service

## Best Practices

- Each function should be in a separate file
- Use descriptive names for functions and files
- Add JSDoc comments to document the function's purpose, parameters, and return values
- Use the utility functions for common operations
- Handle errors properly and provide meaningful error messages
- Use the validation functions to validate input parameters
- Use the request utilities for making API calls
