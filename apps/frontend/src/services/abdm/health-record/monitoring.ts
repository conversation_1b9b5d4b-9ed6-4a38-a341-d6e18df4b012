/**
 * Health Record Monitoring Service
 *
 * This service provides monitoring and analytics for health record operations.
 */

import { db } from "@/lib/db";
import { abdmLogger, LogCategory } from "@/lib/abdm-logger";

/**
 * Record a health record operation
 * @param operation - Operation type
 * @param status - Operation status
 * @param details - Operation details
 */
export async function recordHealthRecordOperation(
  operation: "PACKAGE" | "UPLOAD" | "VALIDATE",
  status: "SUCCESS" | "FAILURE",
  details: {
    patientId: string;
    organizationId: string;
    recordType?: string;
    recordId?: string;
    bundleId?: string;
    consentId?: string;
    transactionId?: string;
    errorMessage?: string;
    userId?: string;
  },
) {
  try {
    // Create operation record in database
    // First, find a health record fetch to associate with this operation
    const healthRecordFetch = await db.healthRecordFetch.findFirst({
      where: {
        patientId: details.patientId,
        organizationId: details.organizationId,
        // Use transactionId if available, otherwise find the most recent one
        ...(details.transactionId
          ? { transactionId: details.transactionId }
          : {}),
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!healthRecordFetch) {
      throw new Error("No health record fetch found for this operation");
    }

    await db.healthRecordOperation.create({
      data: {
        operationType: operation, // Field is named operationType in the schema, not operation
        status,
        patientId: details.patientId,
        organizationId: details.organizationId,
        recordType: details.recordType,
        recordId: details.recordId,
        bundleId: details.bundleId,
        consentId: details.consentId,
        transactionId: details.transactionId,
        errorMessage: details.errorMessage,
        userId: details.userId,
        timestamp: new Date(),
        // Required fields
        fetchId: healthRecordFetch.id,
        updatedAt: new Date(),
      },
    });

    // Log the operation
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Health record ${operation} ${status}`,
      {
        operation,
        status,
        ...details,
      },
      details.transactionId,
      details.patientId,
    );
  } catch (error) {
    // Log error but don't fail the main operation
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      "Failed to record health record operation",
      error,
      {
        operation,
        status,
        ...details,
      },
      details.transactionId,
      details.patientId,
    );
  }
}

/**
 * Get health record operations for a patient
 * @param patientId - Patient ID
 * @param organizationId - Organization ID
 * @returns Health record operations
 */
export async function getPatientHealthRecordOperations(
  patientId: string,
  organizationId: string,
) {
  return db.healthRecordOperation.findMany({
    where: {
      patientId,
      organizationId,
    },
    orderBy: {
      timestamp: "desc",
    },
  });
}

/**
 * Get health record operations for an organization
 * @param organizationId - Organization ID
 * @param limit - Maximum number of operations to return
 * @returns Health record operations
 */
export async function getOrganizationHealthRecordOperations(
  organizationId: string,
  limit = 100,
) {
  return db.healthRecordOperation.findMany({
    where: {
      organizationId,
    },
    orderBy: {
      timestamp: "desc",
    },
    take: limit,
  });
}

/**
 * Get health record operation statistics for an organization
 * @param organizationId - Organization ID
 * @param days - Number of days to include in statistics
 * @returns Health record operation statistics
 */
export async function getHealthRecordStatistics(
  organizationId: string,
  days = 30,
) {
  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // Get operations within date range
  const operations = await db.healthRecordOperation.findMany({
    where: {
      organizationId,
      timestamp: {
        gte: startDate,
        lte: endDate,
      },
    },
  });

  // Calculate statistics
  const totalOperations = operations.length;
  const successfulOperations = operations.filter(
    (op) => op.status === "SUCCESS",
  ).length;
  const failedOperations = operations.filter(
    (op) => op.status === "FAILURE",
  ).length;

  const packageOperations = operations.filter(
    (op) => op.operationType === "PACKAGE",
  ).length;
  const uploadOperations = operations.filter(
    (op) => op.operationType === "UPLOAD",
  ).length;
  const validateOperations = operations.filter(
    (op) => op.operationType === "VALIDATE",
  ).length;

  const successRate =
    totalOperations > 0 ? (successfulOperations / totalOperations) * 100 : 0;

  // Group by record type
  const recordTypes = operations.reduce(
    (acc, op) => {
      if (op.recordType) {
        acc[op.recordType] = (acc[op.recordType] || 0) + 1;
      }
      return acc;
    },
    {} as Record<string, number>,
  );

  // Group by day
  const operationsByDay = operations.reduce(
    (acc, op) => {
      // Skip operations without a timestamp
      if (!op.timestamp) return acc;

      const day = op.timestamp.toISOString().split("T")[0];
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  return {
    totalOperations,
    successfulOperations,
    failedOperations,
    packageOperations,
    uploadOperations,
    validateOperations,
    successRate,
    recordTypes,
    operationsByDay,
  };
}
