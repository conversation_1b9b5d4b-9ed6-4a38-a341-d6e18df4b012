/**
 * Handle health information request and send notification
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";
import { abdmLogger, LogCategory } from "@/lib/abdm-logger";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";
const ABDM_HIP_ID = process.env.NEXT_PUBLIC_ABDM_HIP_ID;

/**
 * Process health information request and send notification to ABDM
 * @param transactionId - Transaction ID from the request
 * @param consentId - Consent ID from the request
 * @param dateRange - Date range for the health information
 * @param keyMaterial - Key material for encryption
 * @param dataPushUrl - URL to push the health information to
 * @returns Response from ABDM
 */
export async function processHealthInformationRequest(
  transactionId: string,
  consentId: string,
  dateRange: { from: string; to: string },
  keyMaterial: any,
  dataPushUrl: string,
) {
  try {
    if (!transactionId) {
      throw new Error("Transaction ID is required");
    }

    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    // Log the request
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Processing health information request for consent ${consentId}`,
      {
        transactionId,
        consentId,
        dateRange,
      },
    );

    // Find the consent in the database
    const consent = await db.consent.findFirst({
      where: {
        consentId,
      },
      include: {
        patient: true,
      },
    });

    if (!consent) {
      throw new Error(`Consent not found for ID: ${consentId}`);
    }

    // Create a record of the health information request
    await db.healthRecordFetch.create({
      data: {
        consentId: consent.id,
        transactionId,
        patientId: consent.patientId,
        organizationId: consent.organizationId,
        status: "REQUESTED",
        requestTimestamp: new Date(),
        responseData: {
          dateRange,
          keyMaterial,
          dataPushUrl,
        },
      },
    });

    // Create an audit log
    await db.consentAuditLog.create({
      data: {
        consentId: consent.id,
        action: "HEALTH_INFO_REQUEST",
        actorId: "system",
        actorRole: "system",
        details: {
          transactionId,
          timestamp: new Date().toISOString(),
          dateRange,
        },
        ipAddress: "system",
        userAgent: "system",
      },
    });

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the payload for the notification
    const payload = {
      requestId,
      timestamp,
      notification: {
        consentId,
        transactionId,
        doneAt: new Date().toISOString(),
        notifier: {
          type: "HIP",
          id: ABDM_HIP_ID || "",
        },
        statusNotification: {
          sessionStatus: "ACKNOWLEDGED",
          hipId: ABDM_HIP_ID || "",
          statusResponses: [
            {
              careContextReference: "default-care-context",
              hiStatus: "OK",
              description: "Health information request acknowledged",
            },
          ],
        },
      },
    };

    // Log the notification payload
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Sending health information notification for consent ${consentId}`,
      {
        transactionId,
        consentId,
        requestId,
      },
    );

    // Make the API request to send the notification
    const response = await abdmFetch(
      `${ABDM_BASE_URL}/api/hiecm/data-flow/v3/health-information/notify`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Log the response
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Health information notification sent for consent ${consentId}`,
      {
        transactionId,
        consentId,
        response,
      },
    );

    return response;
  } catch (error) {
    // Log the error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to process health information request for consent`,
      error,
    );

    throw error;
  }
}
