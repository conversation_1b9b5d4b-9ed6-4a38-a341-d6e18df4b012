/**
 * Health Record Packaging Service
 *
 * This service handles the creation of FHIR bundles and ABDM health record packages.
 */

import { db } from "@/lib/db";
import { v4 as uuidv4 } from "uuid";
import { abdmLogger, LogCategory } from "@/lib/abdm-logger";
import {
  createPatientResource,
  createPractitionerResource,
  createVitalsObservation,
  createMedicationRequests,
  createCondition,
  createDocumentBundle,
  createHealthRecordPackage,
  validateHealthRecordPackage,
  createDiagnosticReportResource,
  createProcedureResource,
  createAllergyIntoleranceResource,
  createImmunizationResource,
  createDocumentReferenceResource,
  FhirBundle,
  FhirResource,
} from "@/lib/fhir";

/**
 * Create a FHIR bundle for a patient's vitals
 * @param patientId - Patient ID
 * @param vitalsId - Vitals ID
 * @param organizationId - Organization ID
 * @returns FHIR bundle
 */
export async function createVitalsBundle(
  patientId: string,
  vitalsId: string,
  organizationId: string,
  consultationId?: string,
): Promise<FhirBundle> {
  // Log the operation start
  abdmLogger.info(
    LogCategory.HEALTH_RECORD,
    `Creating vitals bundle for patient ${patientId}`,
    { vitalsId, organizationId, consultationId },
    undefined,
    patientId,
  );
  try {
    // Fetch the patient, vitals, and doctor data
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        abhaProfile: true,
      },
    });

    if (!patient) {
      const error = new Error("Patient not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create vitals bundle: Patient not found`,
        error,
        { patientId, vitalsId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const vitals = await db.vitals.findFirst({
      where: {
        id: vitalsId,
        patientId,
        organizationId,
      },
    });

    if (!vitals) {
      const error = new Error("Vitals record not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create vitals bundle: Vitals record not found`,
        error,
        { patientId, vitalsId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const doctor = await db.doctor.findFirst({
      where: {
        id: vitals.doctorId,
        organizationId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      const error = new Error("Doctor not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create vitals bundle: Doctor not found`,
        error,
        {
          patientId,
          vitalsId,
          doctorId: vitals.doctorId,
          organizationId,
          consultationId,
        },
        patientId,
      );
      throw error;
    }

    // Create FHIR resources
    const patientResource = createPatientResource(patient);
    const practitionerResource = createPractitionerResource(doctor);
    const observationResources = createVitalsObservation(
      vitals,
      patientResource.id,
      practitionerResource.id,
    );

    // Combine all resources
    const resources: FhirResource[] = [
      patientResource,
      practitionerResource,
      ...observationResources,
    ];

    // Create a document bundle
    const bundle = createDocumentBundle(
      resources,
      `Vitals Record - ${new Date(vitals.recordedAt).toLocaleDateString()}`,
      patientResource.id,
      practitionerResource.id,
    );

    // Store the bundle in the database
    const bundleId = uuidv4();
    await db.$transaction([
      // Create the FHIR bundle record
      db.fhirBundle.create({
        data: {
          bundleId,
          bundleType: "document",
          bundleJson: bundle as any,
          patientId,
          organizationId,
          status: "created",
        },
      }),
      // Create FHIR resource records for each resource
      ...resources.map((resource) =>
        db.fhirResource.create({
          data: {
            resourceType: resource.resourceType,
            resourceId: resource.id,
            fhirJson: resource as any,
            patientId,
            organizationId,
            sourceType:
              resource.resourceType === "Observation"
                ? "vitals"
                : resource.resourceType === "Patient"
                  ? "patient"
                  : resource.resourceType === "Practitioner"
                    ? "doctor"
                    : "other",
            sourceId:
              resource.resourceType === "Observation"
                ? vitalsId
                : resource.resourceType === "Patient"
                  ? patientId
                  : resource.resourceType === "Practitioner"
                    ? doctor.id
                    : "",
            bundleId,
          },
        }),
      ),
    ]);

    // Log success
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Successfully created vitals bundle for patient ${patientId}`,
      {
        bundleId,
        vitalsId,
        resourceCount: resources.length,
        observationCount: observationResources.length,
        consultationId,
      },
      undefined,
      patientId,
    );

    return bundle;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to create vitals bundle`,
      error,
      { patientId, vitalsId, organizationId, consultationId },
      patientId,
    );
    throw error;
  }
}

/**
 * Create a FHIR bundle for a patient's prescription
 * @param patientId - Patient ID
 * @param prescriptionId - Prescription ID
 * @param organizationId - Organization ID
 * @returns FHIR bundle
 */
export async function createPrescriptionBundle(
  patientId: string,
  prescriptionId: string,
  organizationId: string,
  consultationId?: string,
): Promise<FhirBundle> {
  // Log the operation start
  abdmLogger.info(
    LogCategory.HEALTH_RECORD,
    `Creating prescription bundle for patient ${patientId}`,
    { prescriptionId, organizationId, consultationId },
    undefined,
    patientId,
  );
  try {
    // Fetch the patient, prescription, and doctor data
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        abhaProfile: true,
      },
    });

    if (!patient) {
      const error = new Error("Patient not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create prescription bundle: Patient not found`,
        error,
        { patientId, prescriptionId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const prescription = await db.prescription.findFirst({
      where: {
        id: prescriptionId,
        patientId,
        organizationId,
      },
      include: {
        items: true,
      },
    });

    if (!prescription) {
      const error = new Error("Prescription not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create prescription bundle: Prescription not found`,
        error,
        { patientId, prescriptionId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const doctor = await db.doctor.findFirst({
      where: {
        id: prescription.doctorId,
        organizationId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      const error = new Error("Doctor not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create prescription bundle: Doctor not found`,
        error,
        {
          patientId,
          prescriptionId,
          doctorId: prescription.doctorId,
          organizationId,
          consultationId,
        },
        patientId,
      );
      throw error;
    }

    // Create FHIR resources
    const patientResource = createPatientResource(patient);
    const practitionerResource = createPractitionerResource(doctor);
    const medicationRequestResources = createMedicationRequests(
      prescription,
      prescription.items,
      patientResource.id,
      practitionerResource.id,
    );

    // Combine all resources
    const resources: FhirResource[] = [
      patientResource,
      practitionerResource,
      ...medicationRequestResources,
    ];

    // Create a document bundle
    const bundle = createDocumentBundle(
      resources,
      `Prescription - ${new Date(
        prescription.prescriptionDate,
      ).toLocaleDateString()}`,
      patientResource.id,
      practitionerResource.id,
    );

    // Store the bundle in the database
    const bundleId = uuidv4();
    await db.$transaction([
      // Create the FHIR bundle record
      db.fhirBundle.create({
        data: {
          bundleId,
          bundleType: "document",
          bundleJson: bundle as any,
          patientId,
          organizationId,
          status: "created",
        },
      }),
      // Create FHIR resource records for each resource
      ...resources.map((resource) =>
        db.fhirResource.create({
          data: {
            resourceType: resource.resourceType,
            resourceId: resource.id,
            fhirJson: resource as any,
            patientId,
            organizationId,
            sourceType:
              resource.resourceType === "MedicationRequest"
                ? "prescription"
                : resource.resourceType === "Patient"
                  ? "patient"
                  : resource.resourceType === "Practitioner"
                    ? "doctor"
                    : "other",
            sourceId:
              resource.resourceType === "MedicationRequest"
                ? prescriptionId
                : resource.resourceType === "Patient"
                  ? patientId
                  : resource.resourceType === "Practitioner"
                    ? doctor.id
                    : "",
            bundleId,
          },
        }),
      ),
    ]);

    // Log success
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Successfully created prescription bundle for patient ${patientId}`,
      {
        bundleId,
        prescriptionId,
        resourceCount: resources.length,
        medicationRequestCount: medicationRequestResources.length,
        consultationId,
      },
      undefined,
      patientId,
    );

    return bundle;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to create prescription bundle`,
      error,
      { patientId, prescriptionId, organizationId, consultationId },
      patientId,
    );
    throw error;
  }
}

/**
 * Create a FHIR bundle for a patient's clinical note
 * @param patientId - Patient ID
 * @param clinicalNoteId - Clinical Note ID
 * @param organizationId - Organization ID
 * @returns FHIR bundle
 */
export async function createClinicalNoteBundle(
  patientId: string,
  clinicalNoteId: string,
  organizationId: string,
  consultationId?: string,
): Promise<FhirBundle> {
  // Log the operation start
  abdmLogger.info(
    LogCategory.HEALTH_RECORD,
    `Creating clinical note bundle for patient ${patientId}`,
    { clinicalNoteId, organizationId, consultationId },
    undefined,
    patientId,
  );
  try {
    // Fetch the patient, clinical note, and doctor data
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        abhaProfile: true,
      },
    });

    if (!patient) {
      const error = new Error("Patient not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create clinical note bundle: Patient not found`,
        error,
        { patientId, clinicalNoteId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const clinicalNote = await db.clinicalNote.findFirst({
      where: {
        id: clinicalNoteId,
        patientId,
        organizationId,
      },
    });

    if (!clinicalNote) {
      const error = new Error("Clinical note not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create clinical note bundle: Clinical note not found`,
        error,
        { patientId, clinicalNoteId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const doctor = await db.doctor.findFirst({
      where: {
        id: clinicalNote.doctorId,
        organizationId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      const error = new Error("Doctor not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create clinical note bundle: Doctor not found`,
        error,
        {
          patientId,
          clinicalNoteId,
          doctorId: clinicalNote.doctorId,
          organizationId,
          consultationId,
        },
        patientId,
      );
      throw error;
    }

    // Create FHIR resources
    const patientResource = createPatientResource(patient);
    const practitionerResource = createPractitionerResource(doctor);

    // Create condition resource if SNOMED diagnoses are available
    const conditionResource = createCondition(
      clinicalNote,
      patientResource.id,
      practitionerResource.id,
    );

    // Combine all resources
    const resources: FhirResource[] = [patientResource, practitionerResource];

    if (conditionResource) {
      resources.push(conditionResource);
    }

    // Create a document bundle
    const bundle = createDocumentBundle(
      resources,
      `Clinical Note - ${new Date(
        clinicalNote.createdAt,
      ).toLocaleDateString()}`,
      patientResource.id,
      practitionerResource.id,
    );

    // Store the bundle in the database
    const bundleId = uuidv4();
    await db.$transaction([
      // Create the FHIR bundle record
      db.fhirBundle.create({
        data: {
          bundleId,
          bundleType: "document",
          bundleJson: bundle as any,
          patientId,
          organizationId,
          status: "created",
        },
      }),
      // Create FHIR resource records for each resource
      ...resources.map((resource) =>
        db.fhirResource.create({
          data: {
            resourceType: resource.resourceType,
            resourceId: resource.id,
            fhirJson: resource as any,
            patientId,
            organizationId,
            sourceType:
              resource.resourceType === "Condition"
                ? "clinicalNote"
                : resource.resourceType === "Patient"
                  ? "patient"
                  : resource.resourceType === "Practitioner"
                    ? "doctor"
                    : "other",
            sourceId:
              resource.resourceType === "Condition"
                ? clinicalNoteId
                : resource.resourceType === "Patient"
                  ? patientId
                  : resource.resourceType === "Practitioner"
                    ? doctor.id
                    : "",
            bundleId,
          },
        }),
      ),
    ]);

    // Log success
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Successfully created clinical note bundle for patient ${patientId}`,
      {
        bundleId,
        clinicalNoteId,
        resourceCount: resources.length,
        documentReferenceCount: 1, // Only one document reference per clinical note
        consultationId,
      },
      undefined,
      patientId,
    );

    return bundle;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to create clinical note bundle`,
      error,
      { patientId, clinicalNoteId, organizationId, consultationId },
      patientId,
    );
    throw error;
  }
}

/**
 * Create a FHIR bundle for a patient's diagnostic report
 * @param patientId - Patient ID
 * @param diagnosticReportId - Diagnostic Report ID
 * @param organizationId - Organization ID
 * @returns FHIR bundle
 */
export async function createDiagnosticReportBundle(
  patientId: string,
  diagnosticReportId: string,
  organizationId: string,
  consultationId?: string,
): Promise<FhirBundle> {
  // Log the operation start
  abdmLogger.info(
    LogCategory.HEALTH_RECORD,
    `Creating diagnostic report bundle for patient ${patientId}`,
    { diagnosticReportId, organizationId, consultationId },
    undefined,
    patientId,
  );
  try {
    // Fetch the patient, diagnostic report, and doctor data
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        abhaProfile: true,
      },
    });

    if (!patient) {
      const error = new Error("Patient not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create diagnostic report bundle: Patient not found`,
        error,
        { patientId, diagnosticReportId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const diagnosticReport = await db.diagnosticReport.findFirst({
      where: {
        id: diagnosticReportId,
        patientId,
        organizationId,
      },
    });

    if (!diagnosticReport) {
      const error = new Error("Diagnostic report not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create diagnostic report bundle: Diagnostic report not found`,
        error,
        { patientId, diagnosticReportId, organizationId },
        patientId,
      );
      throw error;
    }

    const doctor = await db.doctor.findFirst({
      where: {
        id: diagnosticReport.doctorId,
        organizationId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      const error = new Error("Doctor not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create diagnostic report bundle: Doctor not found`,
        error,
        {
          patientId,
          diagnosticReportId,
          doctorId: diagnosticReport.doctorId,
          organizationId,
        },
        patientId,
      );
      throw error;
    }

    // Create FHIR resources
    const patientResource = createPatientResource(patient);
    const practitionerResource = createPractitionerResource(doctor);
    const diagnosticReportResource = createDiagnosticReportResource(
      diagnosticReport,
      patientResource.id,
      practitionerResource.id,
    );

    // Combine all resources
    const resources: FhirResource[] = [
      patientResource,
      practitionerResource,
      diagnosticReportResource,
    ];

    // Create a document bundle
    const bundle = createDocumentBundle(
      resources,
      `Diagnostic Report - ${new Date(
        diagnosticReport.reportDate,
      ).toLocaleDateString()}`,
      patientResource.id,
      practitionerResource.id,
    );

    // Store the bundle in the database
    const bundleId = uuidv4();
    await db.$transaction([
      // Create the FHIR bundle record
      db.fhirBundle.create({
        data: {
          bundleId,
          bundleType: "document",
          bundleJson: bundle as any,
          patientId,
          organizationId,
          status: "created",
        },
      }),
      // Create FHIR resource records for each resource
      ...resources.map((resource) =>
        db.fhirResource.create({
          data: {
            resourceType: resource.resourceType,
            resourceId: resource.id,
            fhirJson: resource as any,
            patientId,
            organizationId,
            sourceType:
              resource.resourceType === "DiagnosticReport"
                ? "diagnosticReport"
                : resource.resourceType === "Patient"
                  ? "patient"
                  : resource.resourceType === "Practitioner"
                    ? "doctor"
                    : "other",
            sourceId:
              resource.resourceType === "DiagnosticReport"
                ? diagnosticReportId
                : resource.resourceType === "Patient"
                  ? patientId
                  : resource.resourceType === "Practitioner"
                    ? doctor.id
                    : "",
            bundleId,
          },
        }),
      ),
    ]);

    // Log success
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Successfully created diagnostic report bundle for patient ${patientId}`,
      {
        bundleId,
        diagnosticReportId,
        resourceCount: resources.length,
      },
      undefined,
      patientId,
    );

    return bundle;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to create diagnostic report bundle`,
      error,
      { patientId, diagnosticReportId, organizationId },
      patientId,
    );
    throw error;
  }
}

/**
 * Create a FHIR bundle for a patient's procedure
 * @param patientId - Patient ID
 * @param procedureId - Procedure ID
 * @param organizationId - Organization ID
 * @returns FHIR bundle
 */
export async function createProcedureBundle(
  patientId: string,
  procedureId: string,
  organizationId: string,
  consultationId?: string,
): Promise<FhirBundle> {
  // Log the operation start
  abdmLogger.info(
    LogCategory.HEALTH_RECORD,
    `Creating procedure bundle for patient ${patientId}`,
    { procedureId, organizationId, consultationId },
    undefined,
    patientId,
  );
  try {
    // Fetch the patient, procedure, and doctor data
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        abhaProfile: true,
      },
    });

    if (!patient) {
      const error = new Error("Patient not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create procedure bundle: Patient not found`,
        error,
        { patientId, procedureId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const procedure = await db.procedure.findFirst({
      where: {
        id: procedureId,
        patientId,
        organizationId,
      },
    });

    if (!procedure) {
      const error = new Error("Procedure not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create procedure bundle: Procedure not found`,
        error,
        { patientId, procedureId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const doctor = await db.doctor.findFirst({
      where: {
        id: procedure.doctorId,
        organizationId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      const error = new Error("Doctor not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create procedure bundle: Doctor not found`,
        error,
        {
          patientId,
          procedureId,
          doctorId: procedure.doctorId,
          organizationId,
          consultationId,
        },
        patientId,
      );
      throw error;
    }

    // Create FHIR resources
    const patientResource = createPatientResource(patient);
    const practitionerResource = createPractitionerResource(doctor);
    const procedureResource = createProcedureResource(
      procedure,
      patientResource.id,
      practitionerResource.id,
    );

    // Combine all resources
    const resources: FhirResource[] = [
      patientResource,
      practitionerResource,
      procedureResource,
    ];

    // Create a document bundle
    const bundle = createDocumentBundle(
      resources,
      `Procedure - ${new Date(procedure.procedureDate).toLocaleDateString()}`,
      patientResource.id,
      practitionerResource.id,
    );

    // Store the bundle in the database
    const bundleId = uuidv4();
    await db.$transaction([
      // Create the FHIR bundle record
      db.fhirBundle.create({
        data: {
          bundleId,
          bundleType: "document",
          bundleJson: bundle as any,
          patientId,
          organizationId,
          status: "created",
        },
      }),
      // Create FHIR resource records for each resource
      ...resources.map((resource) =>
        db.fhirResource.create({
          data: {
            resourceType: resource.resourceType,
            resourceId: resource.id,
            fhirJson: resource as any,
            patientId,
            organizationId,
            sourceType:
              resource.resourceType === "Procedure"
                ? "procedure"
                : resource.resourceType === "Patient"
                  ? "patient"
                  : resource.resourceType === "Practitioner"
                    ? "doctor"
                    : "other",
            sourceId:
              resource.resourceType === "Procedure"
                ? procedureId
                : resource.resourceType === "Patient"
                  ? patientId
                  : resource.resourceType === "Practitioner"
                    ? doctor.id
                    : "",
            bundleId,
          },
        }),
      ),
    ]);

    // Log success
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Successfully created procedure bundle for patient ${patientId}`,
      {
        bundleId,
        procedureId,
        resourceCount: resources.length,
        consultationId,
      },
      undefined,
      patientId,
    );

    return bundle;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to create procedure bundle`,
      error,
      { patientId, procedureId, organizationId, consultationId },
      patientId,
    );
    throw error;
  }
}

/**
 * Create a FHIR bundle for a patient's allergy intolerance
 * @param patientId - Patient ID
 * @param allergyId - Allergy Intolerance ID
 * @param organizationId - Organization ID
 * @returns FHIR bundle
 */
export async function createAllergyIntoleranceBundle(
  patientId: string,
  allergyId: string,
  organizationId: string,
  consultationId?: string,
): Promise<FhirBundle> {
  // Log the operation start
  abdmLogger.info(
    LogCategory.HEALTH_RECORD,
    `Creating allergy intolerance bundle for patient ${patientId}`,
    { allergyId, organizationId, consultationId },
    undefined,
    patientId,
  );
  try {
    // Fetch the patient, allergy, and doctor data
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        abhaProfile: true,
      },
    });

    if (!patient) {
      const error = new Error("Patient not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create allergy intolerance bundle: Patient not found`,
        error,
        { patientId, allergyId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const allergy = await db.allergyIntolerance.findFirst({
      where: {
        id: allergyId,
        patientId,
        organizationId,
      },
    });

    if (!allergy) {
      const error = new Error("Allergy intolerance not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create allergy intolerance bundle: Allergy intolerance not found`,
        error,
        { patientId, allergyId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const doctor = await db.doctor.findFirst({
      where: {
        id: allergy.doctorId,
        organizationId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      const error = new Error("Doctor not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create allergy intolerance bundle: Doctor not found`,
        error,
        {
          patientId,
          allergyId,
          doctorId: allergy.doctorId,
          organizationId,
          consultationId,
        },
        patientId,
      );
      throw error;
    }

    // Create FHIR resources
    const patientResource = createPatientResource(patient);
    const practitionerResource = createPractitionerResource(doctor);
    const allergyResource = createAllergyIntoleranceResource(
      allergy,
      patientResource.id,
      practitionerResource.id,
    );

    // Combine all resources
    const resources: FhirResource[] = [
      patientResource,
      practitionerResource,
      allergyResource,
    ];

    // Create a document bundle
    const bundle = createDocumentBundle(
      resources,
      `Allergy Intolerance - ${allergy.codeDisplay}`,
      patientResource.id,
      practitionerResource.id,
    );

    // Store the bundle in the database
    const bundleId = uuidv4();
    await db.$transaction([
      // Create the FHIR bundle record
      db.fhirBundle.create({
        data: {
          bundleId,
          bundleType: "document",
          bundleJson: bundle as any,
          patientId,
          organizationId,
          status: "created",
        },
      }),
      // Create FHIR resource records for each resource
      ...resources.map((resource) =>
        db.fhirResource.create({
          data: {
            resourceType: resource.resourceType,
            resourceId: resource.id,
            fhirJson: resource as any,
            patientId,
            organizationId,
            sourceType:
              resource.resourceType === "AllergyIntolerance"
                ? "allergyIntolerance"
                : resource.resourceType === "Patient"
                  ? "patient"
                  : resource.resourceType === "Practitioner"
                    ? "doctor"
                    : "other",
            sourceId:
              resource.resourceType === "AllergyIntolerance"
                ? allergyId
                : resource.resourceType === "Patient"
                  ? patientId
                  : resource.resourceType === "Practitioner"
                    ? doctor.id
                    : "",
            bundleId,
          },
        }),
      ),
    ]);

    // Log success
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Successfully created allergy intolerance bundle for patient ${patientId}`,
      {
        bundleId,
        allergyId,
        resourceCount: resources.length,
        consultationId,
      },
      undefined,
      patientId,
    );

    return bundle;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to create allergy intolerance bundle`,
      error,
      { patientId, allergyId, organizationId, consultationId },
      patientId,
    );
    throw error;
  }
}

/**
 * Create a FHIR bundle for a patient's immunization
 * @param patientId - Patient ID
 * @param immunizationId - Immunization ID
 * @param organizationId - Organization ID
 * @returns FHIR bundle
 */
export async function createImmunizationBundle(
  patientId: string,
  immunizationId: string,
  organizationId: string,
  consultationId?: string,
): Promise<FhirBundle> {
  // Log the operation start
  abdmLogger.info(
    LogCategory.HEALTH_RECORD,
    `Creating immunization bundle for patient ${patientId}`,
    { immunizationId, organizationId, consultationId },
    undefined,
    patientId,
  );
  try {
    // Fetch the patient, immunization, and doctor data
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        abhaProfile: true,
      },
    });

    if (!patient) {
      const error = new Error("Patient not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create immunization bundle: Patient not found`,
        error,
        { patientId, immunizationId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const immunization = await db.immunization.findFirst({
      where: {
        id: immunizationId,
        patientId,
        organizationId,
      },
    });

    if (!immunization) {
      const error = new Error("Immunization not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create immunization bundle: Immunization not found`,
        error,
        { patientId, immunizationId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const doctor = await db.doctor.findFirst({
      where: {
        id: immunization.doctorId,
        organizationId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      const error = new Error("Doctor not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create immunization bundle: Doctor not found`,
        error,
        {
          patientId,
          immunizationId,
          doctorId: immunization.doctorId,
          organizationId,
          consultationId,
        },
        patientId,
      );
      throw error;
    }

    // Create FHIR resources
    const patientResource = createPatientResource(patient);
    const practitionerResource = createPractitionerResource(doctor);
    const immunizationResource = createImmunizationResource(
      immunization,
      patientResource.id,
      practitionerResource.id,
    );

    // Combine all resources
    const resources: FhirResource[] = [
      patientResource,
      practitionerResource,
      immunizationResource,
    ];

    // Create a document bundle
    const bundle = createDocumentBundle(
      resources,
      `Immunization - ${immunization.vaccineDisplay}`,
      patientResource.id,
      practitionerResource.id,
    );

    // Store the bundle in the database
    const bundleId = uuidv4();
    await db.$transaction([
      // Create the FHIR bundle record
      db.fhirBundle.create({
        data: {
          bundleId,
          bundleType: "document",
          bundleJson: bundle as any,
          patientId,
          organizationId,
          status: "created",
        },
      }),
      // Create FHIR resource records for each resource
      ...resources.map((resource) =>
        db.fhirResource.create({
          data: {
            resourceType: resource.resourceType,
            resourceId: resource.id,
            fhirJson: resource as any,
            patientId,
            organizationId,
            sourceType:
              resource.resourceType === "Immunization"
                ? "immunization"
                : resource.resourceType === "Patient"
                  ? "patient"
                  : resource.resourceType === "Practitioner"
                    ? "doctor"
                    : "other",
            sourceId:
              resource.resourceType === "Immunization"
                ? immunizationId
                : resource.resourceType === "Patient"
                  ? patientId
                  : resource.resourceType === "Practitioner"
                    ? doctor.id
                    : "",
            bundleId,
          },
        }),
      ),
    ]);

    // Log success
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Successfully created immunization bundle for patient ${patientId}`,
      {
        bundleId,
        immunizationId,
        resourceCount: resources.length,
        consultationId,
      },
      undefined,
      patientId,
    );

    return bundle;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to create immunization bundle`,
      error,
      { patientId, immunizationId, organizationId, consultationId },
      patientId,
    );
    throw error;
  }
}

/**
 * Create a FHIR bundle for a patient's document reference
 * @param patientId - Patient ID
 * @param documentReferenceId - Document Reference ID
 * @param organizationId - Organization ID
 * @returns FHIR bundle
 */
export async function createDocumentReferenceBundle(
  patientId: string,
  documentReferenceId: string,
  organizationId: string,
  consultationId?: string,
): Promise<FhirBundle> {
  // Log the operation start
  abdmLogger.info(
    LogCategory.HEALTH_RECORD,
    `Creating document reference bundle for patient ${patientId}`,
    { documentReferenceId, organizationId, consultationId },
    undefined,
    patientId,
  );
  try {
    // Fetch the patient, document reference, and doctor data
    const patient = await db.patient.findFirst({
      where: {
        id: patientId,
        organizationId,
      },
      include: {
        abhaProfile: true,
      },
    });

    if (!patient) {
      const error = new Error("Patient not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create document reference bundle: Patient not found`,
        error,
        { patientId, documentReferenceId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const documentReference = await db.documentReference.findFirst({
      where: {
        id: documentReferenceId,
        patientId,
        organizationId,
      },
    });

    if (!documentReference) {
      const error = new Error("Document reference not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create document reference bundle: Document reference not found`,
        error,
        { patientId, documentReferenceId, organizationId, consultationId },
        patientId,
      );
      throw error;
    }

    const doctor = await db.doctor.findFirst({
      where: {
        id: documentReference.doctorId,
        organizationId,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      const error = new Error("Doctor not found");
      abdmLogger.error(
        LogCategory.HEALTH_RECORD,
        `Failed to create document reference bundle: Doctor not found`,
        error,
        {
          patientId,
          documentReferenceId,
          doctorId: documentReference.doctorId,
          organizationId,
          consultationId,
        },
        patientId,
      );
      throw error;
    }

    // Create FHIR resources
    const patientResource = createPatientResource(patient);
    const practitionerResource = createPractitionerResource(doctor);
    const documentReferenceResource = createDocumentReferenceResource(
      documentReference,
      patientResource.id,
      practitionerResource.id,
    );

    // Combine all resources
    const resources: FhirResource[] = [
      patientResource,
      practitionerResource,
      documentReferenceResource,
    ];

    // Create a document bundle
    const bundle = createDocumentBundle(
      resources,
      `Document Reference - ${documentReference.typeDisplay}`,
      patientResource.id,
      practitionerResource.id,
    );

    // Store the bundle in the database
    const bundleId = uuidv4();
    await db.$transaction([
      // Create the FHIR bundle record
      db.fhirBundle.create({
        data: {
          bundleId,
          bundleType: "document",
          bundleJson: bundle as any,
          patientId,
          organizationId,
          status: "created",
        },
      }),
      // Create FHIR resource records for each resource
      ...resources.map((resource) =>
        db.fhirResource.create({
          data: {
            resourceType: resource.resourceType,
            resourceId: resource.id,
            fhirJson: resource as any,
            patientId,
            organizationId,
            sourceType:
              resource.resourceType === "DocumentReference"
                ? "documentReference"
                : resource.resourceType === "Patient"
                  ? "patient"
                  : resource.resourceType === "Practitioner"
                    ? "doctor"
                    : "other",
            sourceId:
              resource.resourceType === "DocumentReference"
                ? documentReferenceId
                : resource.resourceType === "Patient"
                  ? patientId
                  : resource.resourceType === "Practitioner"
                    ? doctor.id
                    : "",
            bundleId,
          },
        }),
      ),
    ]);

    // Log success
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Successfully created document reference bundle for patient ${patientId}`,
      {
        bundleId,
        documentReferenceId,
        resourceCount: resources.length,
        consultationId,
      },
      undefined,
      patientId,
    );

    return bundle;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to create document reference bundle`,
      error,
      { patientId, documentReferenceId, organizationId, consultationId },
      patientId,
    );
    throw error;
  }
}

/**
 * Create an ABDM health record package for a FHIR bundle
 * @param bundleId - FHIR bundle ID
 * @param transactionId - ABDM transaction ID
 * @param careContextReference - Care context reference
 * @param recipientPublicKey - Recipient's public key for encryption
 * @returns ABDM health record package
 */
export async function createHealthRecordPackageFromBundle(
  bundleId: string,
  transactionId: string,
  careContextReference: string,
  recipientPublicKey: string,
): Promise<any> {
  // Fetch the FHIR bundle
  const fhirBundle = await db.fhirBundle.findFirst({
    where: {
      bundleId,
    },
  });

  if (!fhirBundle) {
    throw new Error("FHIR bundle not found");
  }

  // Create the health record package
  const healthRecordPackage = await createHealthRecordPackage(
    fhirBundle.bundleJson as any,
    transactionId,
    careContextReference,
    recipientPublicKey,
  );

  // Validate the package
  const validation = validateHealthRecordPackage(healthRecordPackage);
  if (!validation.valid) {
    throw new Error(
      `Invalid health record package: ${validation.errors.join(", ")}`,
    );
  }

  // Update the bundle status
  await db.fhirBundle.update({
    where: {
      bundleId,
    },
    data: {
      status: "packaged",
      packageChecksum: healthRecordPackage.entries[0].checksum,
      transactionId,
    },
  });

  return healthRecordPackage;
}
