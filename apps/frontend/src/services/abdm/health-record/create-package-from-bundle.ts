/**
 * Create a health record package from a FHIR bundle
 */

import { db } from "@/lib/db";
import { abdmLogger, LogCategory } from "@/lib/abdm-logger";
import { createABDMEnvelope } from "@/lib/abdm-crypto";

/**
 * Create a health record package from a FHIR bundle
 * @param bundleId - FHIR bundle ID
 * @param transactionId - Transaction ID
 * @param careContextReference - Care context reference
 * @param recipientPublicKey - Recipient's public key
 * @returns Health record package
 */
export async function createHealthRecordPackageFromBundle(
  bundleId: string,
  transactionId: string,
  careContextReference: string,
  recipientPublicKey: string,
): Promise<any> {
  try {
    // Get the FHIR bundle
    const fhirBundle = await db.fhirBundle.findFirst({
      where: {
        bundleId,
      },
      include: {
        patient: true,
      },
    });

    if (!fhirBundle) {
      throw new Error("FHIR bundle not found");
    }

    // Get the patient
    const patient = fhirBundle.patient;
    if (!patient) {
      throw new Error("Patient not found");
    }

    // Get the ABHA profile
    const abhaProfile = await db.abhaProfile.findFirst({
      where: {
        patientId: patient.id,
      },
    });

    if (!abhaProfile) {
      throw new Error("ABHA profile not found");
    }

    // Create the FHIR bundle content
    const fhirBundleJson = fhirBundle.bundleJson;
    if (!fhirBundleJson) {
      throw new Error("FHIR bundle content not found");
    }

    // Convert the bundle JSON to a string
    const fhirBundleContent = JSON.stringify(fhirBundleJson);

    // Create the ABDM envelope
    const envelope = await createABDMEnvelope(
      fhirBundleContent,
      recipientPublicKey,
    );

    // Create the health record package
    const healthRecordPackage = {
      requestId: transactionId,
      timestamp: new Date().toISOString(),
      hiRequest: {
        consent: {
          id: transactionId,
        },
        dataPushRequest: {
          careContexts: [
            {
              patientReference: patient.id,
              careContextReference,
            },
          ],
          keyMaterial: {
            cryptoAlg: "ECDH",
            curve: "Curve25519",
            dhPublicKey: {
              expiry: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
              parameters: "Curve25519/32byte random key",
              keyValue: envelope.keyMaterial.dhPublicKey.keyValue,
            },
            nonce: envelope.keyMaterial.nonce,
          },
          records: [
            {
              content: envelope.encryptedData,
            },
          ],
        },
      },
    };

    // Log the package creation
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Health record package created from bundle ${bundleId}`,
      {
        bundleId,
        transactionId,
        patientId: patient.id,
        careContextReference,
      },
    );

    return healthRecordPackage;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to create health record package from bundle`,
      error,
      {
        bundleId,
        transactionId,
        careContextReference,
      },
    );

    throw error;
  }
}
