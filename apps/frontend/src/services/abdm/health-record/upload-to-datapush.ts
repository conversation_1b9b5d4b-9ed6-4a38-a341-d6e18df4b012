/**
 * Health Record Upload to DataPushUrl Service
 *
 * This service handles the upload of ABDM health record packages to a dataPushUrl.
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { db } from "@/lib/db";
import { createHealthRecordPackageFromBundle } from "./package";
import { abdmLogger, LogCategory } from "@/lib/abdm-logger";

/**
 * Upload a health record package to a dataPushUrl
 * @param bundleId - FHIR bundle ID
 * @param transactionId - Transaction ID
 * @param dataPushUrl - URL to push the data to
 * @param keyMaterial - Key material for encryption
 * @returns Upload response
 */
export async function uploadHealthRecordToDataPushUrl(
  bundleId: string,
  transactionId: string,
  dataPushUrl: string,
  keyMaterial: any,
): Promise<any> {
  try {
    // Validate that we have the required data
    if (!bundleId) {
      throw new Error("Bundle ID is required");
    }

    if (!transactionId) {
      throw new Error("Transaction ID is required");
    }

    if (!dataPushUrl) {
      throw new Error("DataPushUrl is required");
    }

    // Parse keyMaterial if it's a string
    const parsedKeyMaterial =
      typeof keyMaterial === "string" ? JSON.parse(keyMaterial) : keyMaterial;

    if (
      !parsedKeyMaterial ||
      !parsedKeyMaterial.dhPublicKey ||
      !parsedKeyMaterial.dhPublicKey.keyValue
    ) {
      throw new Error("Key material is required");
    }

    // Get the FHIR bundle
    const fhirBundle = await db.fhirBundle.findFirst({
      where: {
        bundleId,
      },
      include: {
        patient: true,
      },
    });

    if (!fhirBundle) {
      throw new Error("FHIR bundle not found");
    }

    // Get the care context for this patient
    const careContext = await db.careContext.findFirst({
      where: {
        patientId: fhirBundle.patientId || "",
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!careContext) {
      throw new Error("Care context not found for this patient");
    }

    // Create the health record package
    const healthRecordPackage = await createHealthRecordPackageFromBundle(
      bundleId,
      transactionId,
      careContext.id,
      parsedKeyMaterial.dhPublicKey.keyValue,
    );

    // Log the upload attempt
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Uploading health record to dataPushUrl for transaction ${transactionId}`,
      {
        bundleId,
        transactionId,
        patientId: fhirBundle.patientId,
        careContextReference: careContext.id,
      },
    );

    // Make the API request
    const response = await abdmFetch(dataPushUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(healthRecordPackage),
      signal: AbortSignal.timeout(120000), // 120 seconds timeout
    });

    // Update the bundle status
    await db.fhirBundle.update({
      where: {
        bundleId,
      },
      data: {
        status: "uploaded",
        transactionId,
      },
    });

    return response;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to upload health record to dataPushUrl`,
      error,
      {
        bundleId,
        transactionId,
        dataPushUrl,
      },
    );

    // Update the bundle status to error
    if (bundleId) {
      await db.fhirBundle.update({
        where: {
          bundleId,
        },
        data: {
          status: "error",
          statusDetails:
            error instanceof Error ? error.message : "Unknown error",
        },
      });
    }

    throw error;
  }
}
