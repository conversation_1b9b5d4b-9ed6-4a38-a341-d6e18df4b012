/**
 * Health Record Upload Service
 *
 * This service handles the upload of ABDM health record packages.
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";
import { createHealthRecordPackageFromBundle } from "./package";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";
const ABDM_HIP_ID = process.env.NEXT_PUBLIC_ABDM_HIP_ID;

/**
 * Upload a health record package to ABDM
 * @param bundleId - FHIR bundle ID
 * @param consentId - Consent ID
 * @param careContextReference - Care context reference
 * @param recipientPublicKey - Recipient's public key for encryption
 * @returns Upload response
 */
export async function uploadHealthRecord(
  bundleId: string,
  consentId: string,
  careContextReference: string,
  recipientPublicKey: string,
): Promise<any> {
  try {
    // Validate that we have the required data
    if (!bundleId) {
      throw new Error("Bundle ID is required");
    }

    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    if (!careContextReference) {
      throw new Error("Care context reference is required");
    }

    if (!recipientPublicKey) {
      throw new Error("Recipient public key is required");
    }

    // Fetch the consent to ensure it's valid
    const consent = await db.consent.findFirst({
      where: {
        consentId,
      },
    });

    if (!consent) {
      throw new Error("Consent not found");
    }

    if (consent.status !== "GRANTED") {
      throw new Error(
        `Consent is not granted. Current status: ${consent.status}`,
      );
    }

    // Generate a transaction ID
    const transactionId = generateUUID();

    // Create the health record package
    const healthRecordPackage = await createHealthRecordPackageFromBundle(
      bundleId,
      transactionId,
      careContextReference,
      recipientPublicKey,
    );

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/api/v3/health/record/package`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          "X-HIP-ID": ABDM_HIP_ID || "",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(healthRecordPackage),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Update the bundle status
    await db.fhirBundle.update({
      where: {
        bundleId,
      },
      data: {
        status: "uploaded",
        transactionId,
      },
    });

    // Log the consent access
    await db.consentAuditLog.create({
      data: {
        consentId: consent.id,
        action: "DATA_UPLOAD",
        actorId: "system", // System-initiated upload
        actorRole: "system",
        details: {
          transactionId,
          bundleId,
          careContextReference,
          timestamp: new Date().toISOString(),
        },
        ipAddress: "system",
        userAgent: "system",
      },
    });

    return response;
  } catch (error) {
    console.error("Error uploading health record:", error);

    // Update the bundle status to error
    if (bundleId) {
      await db.fhirBundle.update({
        where: {
          bundleId,
        },
        data: {
          status: "error",
          statusDetails:
            error instanceof Error ? error.message : "Unknown error",
        },
      });
    }

    throw error;
  }
}

/**
 * Check if a health record can be shared based on consent
 * @param patientId - Patient ID
 * @param recordType - Record type (vitals, prescription, clinicalNote)
 * @param recordId - Record ID
 * @returns Whether the record can be shared
 */
export async function canShareHealthRecord(
  patientId: string,
  recordType: string,
): Promise<boolean> {
  // Check if the patient has any active consents
  const activeConsents = await db.consent.findMany({
    where: {
      patientId,
      status: "GRANTED",
      expiryDate: {
        gt: new Date(),
      },
    },
  });

  if (!activeConsents.length) {
    return false;
  }

  // Check if the record type is included in any of the consents
  const validHiTypes = new Map([
    ["vitals", "Vital Signs"],
    ["prescription", "Prescription"],
    ["clinicalNote", "Clinical Notes"],
  ]);

  const hiType = validHiTypes.get(recordType);
  if (!hiType) {
    return false;
  }

  return activeConsents.some((consent) => consent.hiTypes.includes(hiType));
}
