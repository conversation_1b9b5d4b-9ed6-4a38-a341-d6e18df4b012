/**
 * Health Record Fetch Service
 *
 * This service handles fetching health records from ABDM.
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";
import { abdmLogger, LogCategory } from "@/lib/abdm-logger";
import { getCurrentBranchObjectFromCookies } from "@/lib/branch-cookies";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Get the facility ID (HIU ID) from the current branch
 * @returns The facility ID or throws an error if not found
 */
async function getFacilityIdFromCurrentBranch(): Promise<string> {
  // Get current branch from cookies
  const currentBranch = getCurrentBranchObjectFromCookies();

  if (!currentBranch?.id) {
    throw new Error("Current branch not found in cookies");
  }

  // Fetch the branch details from database to get the facility ID
  const branch = await db.branch.findUnique({
    where: { id: currentBranch.id },
    select: {
      id: true,
      name: true,
      hipId: true,
      hipStatus: true,
    },
  });

  if (!branch) {
    throw new Error("Branch not found in database");
  }

  if (!branch.hipId) {
    throw new Error(
      `Branch "${branch.name}" is not registered as a HIP. Please register the facility first.`,
    );
  }

  if (branch.hipStatus !== "registered") {
    throw new Error(
      `Branch "${branch.name}" HIP registration is not active. Status: ${branch.hipStatus}`,
    );
  }

  return branch.hipId;
}

/**
 * Fetch health records from ABDM
 * @param consentId - Consent ID
 * @param transactionId - Transaction ID (optional, will be generated if not provided)
 * @param facilityId - Facility ID (HIU ID) to use for the request
 * @returns Fetch response
 */
export async function fetchHealthRecords(
  consentId: string,
  transactionId?: string,
  facilityId?: string,
) {
  try {
    // Validate that we have the required data
    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    // Fetch the consent to ensure it's valid and granted
    const consent = await db.consent.findFirst({
      where: {
        consentId,
      },
      include: {
        patient: true,
      },
    });

    if (!consent) {
      throw new Error("Consent not found");
    }

    // Generate a transaction ID if not provided
    const txnId = transactionId || generateUUID();

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Get the consent details to extract date range
    // Handle both object and JSON string formats for permission
    let consentPermission = consent.permission as any;
    if (typeof consentPermission === 'string') {
      try {
        consentPermission = JSON.parse(consentPermission);
      } catch (error) {
        console.error('Failed to parse consent permission JSON:', error);
        consentPermission = null;
      }
    }

    let dateRange;
    if (consentPermission?.dateRange) {
      // Handle Date objects or ISO strings
      const fromDate = consentPermission.dateRange.from instanceof Date
        ? consentPermission.dateRange.from
        : new Date(consentPermission.dateRange.from);
      const toDate = consentPermission.dateRange.to instanceof Date
        ? consentPermission.dateRange.to
        : new Date(consentPermission.dateRange.to);

      // Convert to ISO strings for ABDM API
      dateRange = {
        from: fromDate.toISOString(),
        to: toDate.toISOString(),
      };
    } else {
      // Fallback to default date range
      dateRange = {
        from: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year ago
        to: new Date().toISOString(), // now
      };
    }

    // Get configurable webhook URL from environment variables
    const dataPushUrl = process.env.NEXT_PUBLIC_ABDM_WEBHOOK_BASE_URL
      ? `${process.env.NEXT_PUBLIC_ABDM_WEBHOOK_BASE_URL}`
      : `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/webhook/api/v3/hiu/data/push`;

    // Prepare the payload according to ABDM HIU health information request format
    const payload = {
      hiRequest: {
        consent: {
          id: consentId,
        },
        dateRange: {
          from: dateRange.from,
          to: dateRange.to,
        },
        dataPushUrl,
        keyMaterial: {
          cryptoAlg: "ECDH",
          curve: "curve25519",
          dhPublicKey: {
            expiry: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutes from now
            parameters: "Curve25519/32byte random key",
            keyValue: process.env.NEXT_PUBLIC_SENDER_PUBLIC_KEY,
          },
          nonce: process.env.NEXT_PUBLIC_SENDER_NONCE,
        },
      },
    };

    // Log consent details for debugging
    console.log("Consent Details:", {
      consentId: consent.consentId || consent.consentRequestId,
      status: consent.status,
      purpose: consent.purpose,
      hiTypes: consent.hiTypes,
      permission: consent.permission,
      patientId: consent.patientId,
      organizationId: consent.organizationId,
      createdAt: consent.createdAt,
      updatedAt: consent.updatedAt,
    });

    // Log the fetch request with full payload for debugging
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Fetching health records for consent ${consentId}`,
      {
        consentId,
        transactionId: txnId,
        patientId: consent.patientId,
        payload: JSON.stringify(payload, null, 2),
      },
    );
    console.log({ payload });

    // Get facility ID - use provided one or try to get from current branch
    let resolvedFacilityId = facilityId;
    if (!resolvedFacilityId) {
      try {
        resolvedFacilityId = await getFacilityIdFromCurrentBranch();
      } catch (error) {
        console.warn("Could not get facility ID from current branch:", error);
        // Fallback to environment variable
        resolvedFacilityId =
          process.env.NEXT_PUBLIC_ABDM_HIP_ID || process.env.ABDM_HIP_ID;
        if (!resolvedFacilityId) {
          throw new Error(
            "Facility ID is required but not provided and could not be determined from current branch",
          );
        }
      }
    }

    // Prepare headers
    const headers = {
      "Content-Type": "application/json",
      "REQUEST-ID": requestId,
      TIMESTAMP: timestamp,
      "X-CM-ID": ABDM_CM_ID || "sbx",
      "X-HIU-ID": resolvedFacilityId,
      Authorization: `Bearer ${accessToken}`,
    };

    console.log(`🔄 HEALTH INFORMATION REQUEST: Creating mapping for requestId: ${requestId}`);

    // Store the request mapping BEFORE making ABDM API call
    // This ensures we can track the request even if the API call fails
    await (db as any).healthInformationRequest.create({
      data: {
        requestId: requestId,
        consentId: consent.id,
        patientId: consent.patientId,
        organizationId: consent.organizationId,
        status: "INITIATED",
        requestPayload: {
          hiRequest: payload.hiRequest,
          headers: {
            requestId,
            timestamp,
            dataPushUrl,
            facilityId: resolvedFacilityId,
          },
          consentDetails: {
            consentId: consent.consentId || consent.consentRequestId,
            patientId: consent.patientId,
            organizationId: consent.organizationId,
          },
        },
      },
    });

    console.log(`✅ MAPPING STORED: requestId ${requestId} → consentId ${consent.id}`);

    // Log headers for debugging (without sensitive token)
    console.log("ABDM Health Information Request Headers:", {
      ...headers,
      Authorization: `Bearer ${accessToken.substring(0, 20)}...`,
    });

    // Log environment variables for debugging
    console.log("Environment Variables:", {
      ABDM_BASE_URL,
      ABDM_CM_ID,
      facilityId: resolvedFacilityId,
      hasAccessToken: !!accessToken,
    });

    // Make the API request to ABDM health information request endpoint
    const response = await abdmFetch(
      `${ABDM_BASE_URL}/hiecm/data-flow/v3/health-information/request`,
      {
        method: "POST",
        headers,
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Create a record of the fetch operation
    await db.healthRecordFetch.create({
      data: {
        consentId: consent.id,
        transactionId: txnId,
        patientId: consent.patientId,
        organizationId: consent.organizationId,
        status: "REQUESTED",
        requestTimestamp: new Date(),
        responseData: {
          requestId,
          timestamp,
          dataPushUrl,
          dateRange,
          keyMaterial: payload.hiRequest.keyMaterial,
        },
      },
    });

    // Create an audit log
    await db.consentAuditLog.create({
      data: {
        consentId: consent.id,
        action: "FETCH_RECORDS",
        actorId: "system", // This should be replaced with actual user ID
        actorRole: "system", // This should be replaced with actual user role
        details: {
          transactionId: txnId,
          timestamp: new Date().toISOString(),
        },
        ipAddress: "system",
        userAgent: "system",
      },
    });

    return {
      transactionId: txnId,
      status: "REQUESTED",
      ...response,
    };
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to fetch health records`,
      error,
    );

    throw error;
  }
}

/**
 * Process health records received from ABDM
 * @param data - Health record data
 * @param transactionId - Transaction ID
 * @returns Processing result
 */
export async function processHealthRecords(data: any, transactionId: string) {
  try {
    // Validate that we have the required data
    if (!data) {
      throw new Error("Health record data is required");
    }

    if (!transactionId) {
      throw new Error("Transaction ID is required");
    }

    // Find the fetch record
    const fetchRecord = await db.healthRecordFetch.findFirst({
      where: {
        transactionId,
      },
      include: {
        patient: true,
      },
    });

    if (!fetchRecord) {
      throw new Error("Fetch record not found for transaction ID");
    }

    // Log the processing
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `Processing health records for transaction ${transactionId}`,
      {
        transactionId,
        patientId: fetchRecord.patientId,
      },
    );

    // Process and store the health records
    // This will depend on the structure of the data received
    // For now, we'll just store the raw data
    await db.healthRecordFetch.update({
      where: {
        id: fetchRecord.id,
      },
      data: {
        status: "RECEIVED",
        responseTimestamp: new Date(),
        responseData: data,
      },
    });

    // Parse and store FHIR resources if available
    // Note: FhirBundle and FhirResource models have been removed from the schema
    // FHIR data processing is now handled via webhook storage
    if (data.entries && Array.isArray(data.entries)) {
      abdmLogger.info(
        LogCategory.HEALTH_RECORD,
        `Received ${data.entries.length} FHIR entries for transaction ${transactionId}`,
        {
          transactionId,
          patientId: fetchRecord.patientId,
          entryCount: data.entries.length,
        },
      );

      // Store the raw FHIR data in the response data for now
      // This can be processed by other parts of the application as needed
      const existingResponseData = (fetchRecord.responseData as any) || {};
      await db.healthRecordFetch.update({
        where: { id: fetchRecord.id },
        data: {
          responseData: {
            ...existingResponseData,
            fhirEntries: data.entries,
            processedAt: new Date().toISOString(),
          },
        },
      });
    }

    return {
      status: "PROCESSED",
      transactionId,
    };
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to process health records`,
      error,
    );

    throw error;
  }
}

/**
 * Get fetched health records for a patient
 * @param patientId - Patient ID
 * @param organizationId - Organization ID
 * @param options - Options for filtering and pagination
 * @returns Fetched health records
 */
export async function getPatientHealthRecords(
  patientId: string,
  organizationId: string,
  options: {
    status?: string;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  } = {},
) {
  try {
    // Validate that we have the required data
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    if (!organizationId) {
      throw new Error("Organization ID is required");
    }

    // Set default options
    const {
      status,
      limit = 100,
      offset = 0,
      sortBy = "responseTimestamp",
      sortOrder = "desc",
    } = options;

    // Build the where clause
    const where: any = {
      patientId,
      organizationId,
    };

    if (status) {
      where.status = status;
    }

    // Get the fetch records
    const fetchRecords = await db.healthRecordFetch.findMany({
      where,
      orderBy: {
        [sortBy]: sortOrder,
      },
      skip: offset,
      take: limit,
    });

    // Note: FhirResource model has been removed from the schema
    // FHIR resources are now stored in the responseData field of healthRecordFetch
    // Extract FHIR entries from responseData if available
    const result = fetchRecords.map((record) => {
      const responseData = record.responseData as any;
      const fhirEntries = responseData?.fhirEntries || [];

      return {
        ...record,
        resources: fhirEntries, // Use FHIR entries from responseData
        fhirEntryCount: fhirEntries.length,
      };
    });

    return result;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to get patient health records`,
      error,
    );

    throw error;
  }
}

/**
 * Get FHIR resources by type for a patient
 * @param patientId - Patient ID
 * @param organizationId - Organization ID
 * @param resourceType - FHIR resource type
 * @returns FHIR resources of the specified type
 */
export async function getFhirResourcesByType(
  patientId: string,
  organizationId: string,
  resourceType: string,
) {
  try {
    // Validate that we have the required data
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    if (!organizationId) {
      throw new Error("Organization ID is required");
    }

    if (!resourceType) {
      throw new Error("Resource type is required");
    }

    // Note: FhirResource model has been removed from the schema
    // Get FHIR resources from healthRecordFetch responseData
    const fetchRecords = await db.healthRecordFetch.findMany({
      where: {
        patientId,
        organizationId,
        status: "RECEIVED",
      },
      orderBy: {
        responseTimestamp: "desc",
      },
    });

    // Extract resources of the specified type from all fetch records
    const resources: any[] = [];
    for (const record of fetchRecords) {
      const responseData = record.responseData as any;
      const fhirEntries = responseData?.fhirEntries || [];

      for (const entry of fhirEntries) {
        try {
          // Decrypt and parse the content if needed
          let content = entry.content;
          if (typeof content === "string") {
            content = Buffer.from(content, "base64").toString("utf-8");
            content = JSON.parse(content);
          }

          // Check if this is a bundle with entries
          if (content.entry && Array.isArray(content.entry)) {
            for (const bundleEntry of content.entry) {
              const resource = bundleEntry.resource;
              if (resource && resource.resourceType === resourceType) {
                resources.push({
                  ...resource,
                  _fetchRecord: {
                    id: record.id,
                    transactionId: record.transactionId,
                    createdAt: record.responseTimestamp,
                  },
                });
              }
            }
          }
          // Check if this is a direct resource
          else if (content.resourceType === resourceType) {
            resources.push({
              ...content,
              _fetchRecord: {
                id: record.id,
                transactionId: record.transactionId,
                createdAt: record.responseTimestamp,
              },
            });
          }
        } catch (error) {
          abdmLogger.error(
            LogCategory.HEALTH_RECORD,
            `Failed to parse FHIR entry for resource type ${resourceType}`,
            error,
          );
        }
      }
    }

    return resources;
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to get FHIR resources by type`,
      error,
    );

    throw error;
  }
}
