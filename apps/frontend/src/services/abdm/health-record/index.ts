/**
 * ABDM Health Record Service
 *
 * This service handles health record packaging, upload, and fetching from ABDM.
 */

import * as packageModule from "./package";
import * as uploadModule from "./upload";
import * as fetchModule from "./fetch";
import { processHealthInformationRequest } from "./on-request";
import { uploadHealthRecordToDataPushUrl } from "./upload-to-datapush";
import { createHealthRecordPackageFromBundle } from "./create-package-from-bundle";

export const packageService = {
  ...packageModule,
  createHealthRecordPackageFromBundle,
};
export const upload = {
  ...uploadModule,
  uploadHealthRecordToDataPushUrl,
};
export const fetch = fetchModule;
export const onRequest = { processHealthInformationRequest };
