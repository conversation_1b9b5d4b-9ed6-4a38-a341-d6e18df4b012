/**
 * Download ABHA card
 */

// Direct fetch is used instead of abdmFetch to handle binary data
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Download ABHA card using X-token
 * @param xToken - X-token obtained from ABHA verification
 * @returns ABHA card content (HTML or base64 encoded image)
 */
export async function downloadAbhaCard(xToken: string) {
  try {
    if (!xToken) {
      throw new Error("X-token is required");
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Make a direct fetch request to handle binary data properly
    const response = await fetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/account/abha-card`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
          "X-Token": xToken.startsWith("Bearer ") ? xToken : `Bearer ${xToken}`,
        },
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    if (!response.ok) {
      // Handle error response
      let errorMessage = `Failed to fetch ABHA card. Status: ${response.status}`;

      // Try to parse error as JSON
      try {
        const errorText = await response.text();
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorData.error || errorMessage;

          // Check for token expiration
          if (
            errorData.code === "ABDM-1094" ||
            errorMessage.includes("token expired")
          ) {
            throw new Error(
              "Your ABHA session has expired. Please login again.",
            );
          }
        } catch (e) {
          // If not JSON, use the text as is
          if (errorText) {
            errorMessage = errorText;
          }
        }
      } catch (e) {
        // If we can't read the response, use the default error message
      }

      throw new Error(errorMessage);
    }

    // Check the content type to determine how to handle the response
    const contentType = response.headers.get("Content-Type") || "";

    if (contentType.includes("image/")) {
      // For image responses, convert to base64 data URL
      const arrayBuffer = await response.arrayBuffer();

      // Use Buffer in Node.js environment
      if (typeof Buffer !== "undefined") {
        const buffer = Buffer.from(arrayBuffer);
        const base64 = buffer.toString("base64");
        return `<html><body><img src="data:${contentType};base64,${base64}" style="width:100%;height:auto;" /></body></html>`;
      } else {
        // Fallback for browser environment
        const bytes = new Uint8Array(arrayBuffer);
        let binary = "";
        for (let i = 0; i < bytes.byteLength; i++) {
          binary += String.fromCharCode(bytes[i]);
        }
        const base64 = btoa(binary);
        return `<html><body><img src="data:${contentType};base64,${base64}" style="width:100%;height:auto;" /></body></html>`;
      }
    } else if (contentType.includes("application/pdf")) {
      // For PDF responses, convert to base64 data URL
      const arrayBuffer = await response.arrayBuffer();

      // Use Buffer in Node.js environment
      if (typeof Buffer !== "undefined") {
        const buffer = Buffer.from(arrayBuffer);
        const base64 = buffer.toString("base64");
        return `<html><body><embed src="data:application/pdf;base64,${base64}" type="application/pdf" width="100%" height="100%" /></body></html>`;
      } else {
        // Fallback for browser environment
        const bytes = new Uint8Array(arrayBuffer);
        let binary = "";
        for (let i = 0; i < bytes.byteLength; i++) {
          binary += String.fromCharCode(bytes[i]);
        }
        const base64 = btoa(binary);
        return `<html><body><embed src="data:application/pdf;base64,${base64}" type="application/pdf" width="100%" height="100%" /></body></html>`;
      }
    } else {
      // For HTML or text responses
      const content = await response.text();

      // Check for common error patterns in the content
      if (
        content.includes("TOKEN_EXPIRED") ||
        content.includes("ABDM-1094") ||
        content.toLowerCase().includes("token expired")
      ) {
        throw new Error("Your ABHA session has expired. Please login again.");
      }

      return content;
    }
  } catch (error) {
    console.error("Error downloading ABHA card:", error);
    throw error;
  }
}
