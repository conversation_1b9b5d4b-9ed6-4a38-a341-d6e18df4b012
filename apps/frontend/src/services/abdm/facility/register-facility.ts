/**
 * Register a facility as a Health Information Provider (HIP) with ABDM
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Register a facility as a Health Information Provider (HIP) with ABDM
 * @param branchId - Branch ID to register
 * @returns Registration response
 */
export async function registerFacility(branchId: string) {
  try {
    // Get branch details
    const branch = await db.branch.findUnique({
      where: { id: branchId },
      include: {
        organization: true,
      },
    });

    if (!branch) {
      throw new Error("Branch not found");
    }

    // Check if branch is already registered
    if (branch.hipId) {
      return {
        success: true,
        message: "Branch is already registered as a HIP",
        hipId: branch.hipId,
        status: branch.hipStatus,
      };
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the facility registration payload based on the new API format
    const payload = {
      infoDtlsRequestDTO: {
        facName: branch.name,
        country: "India",
        state: branch.state || "33", // Default to Tamil Nadu (33) if not provided
        district: branch.city || "589", // Default to Chennai (589) if not provided
        pincode: branch.pincode || "600001",
        geolocation: "13.0827,80.2707", // Default Chennai coordinates
        subDistrict: "5698", // Default value
        vilCityTown: branch.city || "Chennai",
        address1: branch.address || "",
        address2: "",
        landlineNo: "",
        mobileNo: branch.phone || "9999999999",
        facEmail: branch.email || "",
        facWebsite: "",
        facOwnership: "P", // Private
        facOwnerGovt: "",
        facOwnerPrivate: "",
        ownerSubType: "",
        facCentral: "",
        privateProfitType: "",
        privateNotProfitType: "",
        mobileNoVerified: "Yes",
        facEmailVerified: "No",
        systemOfMedicine: "H", // Homeopathy
        typeOfService: "OPD",
        facilityType: 63, // Default to Clinic
        facilitySubtype: 28, // Default to General Clinic
        facOperStatus: "F", // Fully Operational
        bookAppUrl: "",
        ifAnyOther: "",
        crtUsr: branch.organization.id || requestId,
        emrSystem: "Y",
        emrSoftware: "Aran Care HIMS",
        entityType: "",
        healthId: branch.organization.id || requestId,
        facRegion: "R",
      },
      attachmentsDtlsRequestDTO: [],
      medicalInfraRequestDTO: [],
      operationRequestDTO: [],
      optionalDetailRequestDTO: [],
      fieldQueryRequestDTO: [],
      tfacilityLinkedUseRequestDTO: [],
      equipmentDtlsRequestDTO: [],
    };

    // Also keep the old payload format for backward compatibility with other endpoints
    const oldFormatPayload = {
      requestId,
      timestamp,
      facility: {
        name: branch.name,
        type: mapFacilityType(branch.facilityType),
        active: true,
        address: {
          line: branch.address || "",
          district: branch.city || "",
          state: branch.state || "",
          pincode: branch.pincode || "",
        },
        telecom: [
          {
            system: "phone",
            value: branch.phone || "",
          },
          {
            system: "email",
            value: branch.email || "",
          },
        ],
        services: branch.services
          ? branch.services.split(",").map((s) => s.trim())
          : [],
        ownerOrganization: {
          name: branch.organization.name,
          id: branch.organization.id,
        },
      },
    };

    // Make the API call to register the facility
    // Try different API endpoints until one works
    let response;
    const endpoints = [
      // New endpoint from the API documentation
      `https://apihspsbx.abdm.gov.in/v4/hfr/facility/master`,
      // Fallback endpoints
      `${ABDM_BASE_URL}/gateway/v1/facility/register`,
      `${ABDM_BASE_URL}/v1/facility/register`,
      `${ABDM_BASE_URL}/gateway/v0.5/facility/register`,
      `${ABDM_BASE_URL}/v0.5/facility/register`,
    ];

    let lastError;
    for (const endpoint of endpoints) {
      try {
        // Use the appropriate payload based on the endpoint
        const useNewFormat = endpoint.includes(
          "apihspsbx.abdm.gov.in/v4/hfr/facility/master",
        );
        const requestPayload = useNewFormat ? payload : oldFormatPayload;

        // Set appropriate headers based on the endpoint
        const headers: Record<string, string> = {
          "Content-Type": "application/json",
        };

        // Add additional headers for old format endpoints
        if (!useNewFormat) {
          headers["X-CM-ID"] = ABDM_CM_ID;
          headers["Authorization"] = `Bearer ${accessToken}`;
          headers["REQUEST-ID"] = requestId;
          headers["TIMESTAMP"] = timestamp;
        }

        response = await abdmFetch(endpoint, {
          method: "POST",
          headers,
          body: JSON.stringify(requestPayload),
        });

        // Add the endpoint to the response for later reference
        response.endpoint = endpoint;
        break; // If successful, break out of the loop
      } catch (error) {
        console.error(
          `Failed to register facility with endpoint ${endpoint}:`,
          error,
        );
        lastError = error;
      }
    }

    // If all endpoints failed, throw the last error
    if (!response) {
      throw (
        lastError || new Error("All facility registration endpoints failed")
      );
    }

    // Process the response based on the endpoint used
    const isNewApiFormat = endpoints.indexOf(response.endpoint) === 0; // Check if we used the new API format

    if (isNewApiFormat) {
      // For the new API format, the response structure is different

      // Extract facility ID from the response
      const facilityId = response.facilityId || response.id || generateUUID();

      // Update the branch with the HIP ID
      await db.branch.update({
        where: { id: branchId },
        data: {
          hipId: facilityId,
          hipStatus: "registered",
          hipRegistrationDate: new Date(),
          hipRegistrationDetails: response,
        },
      });

      return {
        success: true,
        message: "Facility registered successfully with new API",
        hipId: facilityId,
        status: "registered",
        details: response,
      };
    } else if (response.success) {
      // For the old API format with success
      const data = response.data;

      // Update the branch with the HIP ID
      await db.branch.update({
        where: { id: branchId },
        data: {
          hipId: data.facilityId,
          hipStatus: "registered",
          hipRegistrationDate: new Date(),
          hipRegistrationDetails: data,
        },
      });

      return {
        success: true,
        message: "Facility registered successfully",
        hipId: data.facilityId,
        status: "registered",
        details: data,
      };
    } else {
      // Update the branch with the failed status
      await db.branch.update({
        where: { id: branchId },
        data: {
          hipStatus: "failed",
          hipRegistrationDetails: response.error || response,
        },
      });

      throw new Error(response.error?.message || "Failed to register facility");
    }
  } catch (error: any) {
    console.error("Error registering facility:", error);
    throw new Error(error.message || "Failed to register facility");
  }
}

/**
 * Map internal facility type to ABDM facility type
 * @param facilityType - Internal facility type
 * @returns ABDM facility type
 */
function mapFacilityType(facilityType: string): string {
  const typeMap: Record<string, string> = {
    clinic: "Healthcare Clinic/Center",
    hospital: "Hospital",
    laboratory: "Laboratory",
    pharmacy: "Pharmacy",
    other: "Other",
  };

  return typeMap[facilityType] || "Healthcare Clinic/Center";
}
