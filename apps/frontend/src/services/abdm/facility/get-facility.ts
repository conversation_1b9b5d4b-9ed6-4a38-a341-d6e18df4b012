/**
 * Get facility details from ABDM
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Get facility details from ABDM
 * @param hipId - HIP ID to get details for
 * @returns Facility details
 */
export async function getFacilityDetails(hipId: string) {
  try {
    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Make the API call to get facility details
    // Try different API endpoints until one works
    let response;
    const endpoints = [
      `${ABDM_BASE_URL}/gateway/v1/facility/${hipId}`,
      `${ABDM_BASE_URL}/v1/facility/${hipId}`,
      `${ABDM_BASE_URL}/gateway/v0.5/facility/${hipId}`,
      `${ABDM_BASE_URL}/v0.5/facility/${hipId}`,
    ];

    let lastError;
    for (const endpoint of endpoints) {
      try {
        response = await abdmFetch(endpoint, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "X-CM-ID": ABDM_CM_ID,
            "REQUEST-ID": requestId,
            TIMESTAMP: timestamp,
            Authorization: `Bearer ${accessToken}`,
          },
        });
        break; // If successful, break out of the loop
      } catch (error) {
        console.error(
          `Failed to get facility details with endpoint ${endpoint}:`,
          error,
        );
        lastError = error;
      }
    }

    // If all endpoints failed, throw the last error
    if (!response) {
      throw lastError || new Error("All facility details endpoints failed");
    }

    // Process the response
    if (response.success) {
      return {
        success: true,
        facility: response.data,
      };
    } else {
      throw new Error(
        response.error?.message || "Failed to get facility details",
      );
    }
  } catch (error: any) {
    console.error("Error getting facility details:", error);
    throw new Error(error.message || "Failed to get facility details");
  }
}

/**
 * Get facility details by branch ID
 * @param branchId - Branch ID to get details for
 * @returns Facility details
 */
export async function getFacilityDetailsByBranchId(branchId: string) {
  try {
    // Get branch details
    const branch = await db.branch.findUnique({
      where: { id: branchId },
    });

    if (!branch) {
      throw new Error("Branch not found");
    }

    if (!branch.hipId) {
      throw new Error("Branch is not registered as a HIP");
    }

    // Get facility details
    return getFacilityDetails(branch.hipId);
  } catch (error: any) {
    console.error("Error getting facility details by branch ID:", error);
    throw new Error(error.message || "Failed to get facility details");
  }
}
