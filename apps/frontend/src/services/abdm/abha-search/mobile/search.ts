/**
 * Search ABHA using Mobile number
 */

import { encryptAadhaar } from "../../utils/encryption";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";
import { validateMobile } from "../../utils/validation";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Search ABHA using mobile number
 * @param mobile - Mobile number to search with
 * @returns Search results with transaction ID
 */
export async function searchAbhaByMobile(mobile: string) {
  try {
    // Validate mobile number
    validateMobile(mobile);

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the mobile number
    const encryptedMobile = await encryptAadhaar(mobile);

    // Prepare the payload according to ABDM V3 API specifications
    const payload = {
      scope: ["search-abha"],
      mobile: encryptedMobile,
    };

    console.log({ ABDM_SANDBOX_BASE_URL });

    // Make the API request with the enhanced abdmFetch
    const data = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/account/abha/search`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
          BENEFIT_NAME: "abc",
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    console.log({ data });

    // Log the response for debugging
    console.log("ABHA Search API response:", data);

    // Return the data
    return data;
  } catch (error) {
    console.error("Error searching ABHA by mobile:", error);
    throw error;
  }
}
