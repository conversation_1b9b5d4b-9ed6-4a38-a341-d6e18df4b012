# ABDM Service Migration Plan

This document outlines the plan to migrate the ABDM service from a monolithic file to a modular structure with each function in a separate file.

## Current Structure

Currently, all ABDM service functions are defined in a single file:

```typescript
src / services / abdm - service.ts;
```

## Target Structure

The target structure is to have each function in a separate file, organized by category:

```bash
src/services/abdm/
├── abha-create/                # ABHA creation services
│   └── aadhaar/                # ABHA creation using Aadhaar
│       ├── request-otp.ts      # Request OTP for ABHA creation
│       ├── verify-otp.ts       # Verify OTP and create ABHA
│       └── index.ts            # Export functions
├── abha-verify/                # ABHA verification services
│   ├── aadhaar/                # ABHA verification using <PERSON><PERSON>haar
│   │   ├── request-otp.ts      # Request OTP for ABHA verification
│   │   ├── verify-otp.ts       # Verify OTP and retrieve ABHA
│   │   └── index.ts            # Export functions
│   ├── abha-address/           # ABHA verification using ABHA Address
│   │   ├── request-otp.ts      # Request OTP for ABHA Address verification
│   │   ├── verify-otp.ts       # Verify OTP and retrieve ABHA
│   │   └── index.ts            # Export functions
│   ├── abha-id/                # ABHA verification using ABHA ID
│   │   ├── request-otp.ts      # Request OTP for ABHA ID verification
│   │   ├── verify-otp.ts       # Verify OTP and retrieve ABHA
│   │   └── index.ts            # Export functions
│   ├── mobile/                 # ABHA verification using Mobile
│   │   ├── request-otp.ts      # Request OTP for Mobile verification
│   │   ├── verify-otp.ts       # Verify OTP and retrieve ABHA
│   │   └── index.ts            # Export functions
│   └── index.ts                # Export all verification methods
├── abha-card/                  # ABHA card services
│   ├── download.ts             # Download ABHA card
│   └── index.ts                # Export functions
├── consent/                    # Consent management services
│   ├── request.ts              # Request consent
│   ├── store.ts                # Store consent
│   ├── revoke.ts               # Revoke consent
│   ├── fetch.ts                # Fetch consent details
│   └── index.ts                # Export functions
├── utils/                      # Utility functions
│   ├── auth.ts                 # Authentication utilities
│   ├── request.ts              # Request utilities
│   └── validation.ts           # Validation utilities
└── index.ts                    # Main entry point
```

## Migration Steps

1. ✅ Create the directory structure
2. ✅ Create utility functions
3. ✅ Migrate ABHA Address verification functions
4. Migrate ABHA ID verification functions
5. Migrate Aadhaar verification functions
6. Migrate Mobile verification functions
7. Migrate ABHA creation functions
8. Migrate ABHA card functions
9. Migrate Consent management functions
10. Update API endpoints to use the new service structure
11. Create a compatibility layer for backward compatibility
12. Remove the old service file

## Progress

- ✅ ABHA Address verification
- ✅ ABHA ID verification
- ✅ Aadhaar verification
- ✅ Mobile verification
- ✅ ABHA creation
- ✅ ABHA card
- ✅ Consent management
- ✅ Compatibility layer

## Compatibility Layer

To ensure backward compatibility, we'll create a compatibility layer that exports the same functions as the old service file:

```typescript
// src/services/abdm-service.ts
import * as abhaVerify from "./abdm/abha-verify";
import * as abhaCreate from "./abdm/abha-create";
import * as abhaCard from "./abdm/abha-card";
import * as consent from "./abdm/consent";

export const abdmService = {
  // ABHA verification
  requestAbhaAddressOtp: abhaVerify.abhaAddress.requestAbhaAddressOtp,
  verifyAbhaAddressOtp: abhaVerify.abhaAddress.verifyAbhaAddressOtp,
  requestAbhaIdOtp: abhaVerify.abhaId.requestAbhaIdOtp,
  verifyAbhaIdOtp: abhaVerify.abhaId.verifyAbhaIdOtp,
  requestAadhaarOtp: abhaVerify.aadhaar.requestAadhaarOtp,
  verifyAadhaarOtp: abhaVerify.aadhaar.verifyAadhaarOtp,
  requestMobileOtp: abhaVerify.mobile.requestMobileOtp,
  verifyMobileOtp: abhaVerify.mobile.verifyMobileOtp,

  // ABHA creation
  requestEnrollmentOtp: abhaCreate.aadhaar.requestEnrollmentOtp,
  verifyEnrollmentOtp: abhaCreate.aadhaar.verifyEnrollmentOtp,

  // ABHA card
  downloadAbhaCardRaw: abhaCard.download,

  // Consent management
  requestConsent: consent.request,
  storeConsent: consent.store,
  revokeConsent: consent.revoke,
  fetchConsentDetails: consent.fetch,
};
```

This will allow existing code to continue working while we gradually migrate to the new structure.
