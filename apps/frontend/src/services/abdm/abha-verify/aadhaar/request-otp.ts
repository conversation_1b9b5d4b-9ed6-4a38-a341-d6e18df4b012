/**
 * Request OTP for Aadhaar verification
 */

import { encryptAadhaar } from "../../utils/encryption";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";
import { validateAadhaar } from "../../utils/validation";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL =
  process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL ||
  "https://abhasbx.abdm.gov.in/abha/api";
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Request OTP for Aadhaar verification
 * @param aadhaar - Aadhaar number
 * @returns Transaction ID and message
 */
export async function requestAadhaarOtp(aadhaar: string) {
  try {
    // Validate Aadhaar number
    if (!validateAadhaar(aadhaar)) {
      throw new Error(
        "Invalid Aadhaar number format. Please provide a valid 12-digit Aadhaar number.",
      );
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the Aadhaar number
    const encryptedAadhaar = await encryptAadhaar(aadhaar);

    // Prepare the payload according to ABDM V3 API specifications
    const payload = {
      scope: ["abha-login", "aadhaar-verify"],
      loginHint: "aadhaar",
      loginId: encryptedAadhaar,
      otpSystem: "aadhaar",
    };

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/login/request/otp`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    return response;
  } catch (error) {
    throw error;
  }
}
