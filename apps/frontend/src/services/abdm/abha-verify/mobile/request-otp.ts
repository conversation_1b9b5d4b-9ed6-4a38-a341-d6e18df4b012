/**
 * Request OTP for Mobile verification
 */

import { encryptAadhaar } from "../../utils/encryption";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";
import { validateMobile } from "../../utils/validation";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Request OTP for Mobile verification
 * @param mobile - Mobile number
 * @returns Transaction ID and message
 */
export async function requestMobileOtp(mobile: string) {
  try {
    // Validate mobile number
    if (!validateMobile(mobile)) {
      throw new Error(
        "Invalid mobile number format. Please provide a valid 10-digit mobile number.",
      );
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the mobile number
    const encryptedMobile = await encryptAadhaar(mobile);

    // Prepare the payload according to ABDM V3 API specifications
    const payload = {
      scope: ["abha-login", "mobile-verify"],
      loginHint: "mobile",
      loginId: encryptedMobile,
      otpSystem: "abdm",
    };

    // Make the API request with the enhanced abdmFetch
    const data = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/login/request/otp`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Return the data with a default message if not provided
    return {
      ...data,
      message: data.message || "OTP sent successfully to your mobile number",
    };
  } catch (error) {
    throw error;
  }
}
