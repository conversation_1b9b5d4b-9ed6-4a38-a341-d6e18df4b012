/**
 * Verify OTP for ABHA ID verification
 */

import { encryptAadhaar } from "../../utils/encryption";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";
import { validateOTP } from "../../utils/validation";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Verify OTP for ABHA ID verification
 * @param otp - OTP received on mobile
 * @param txnId - Transaction ID from OTP generation
 * @param otpMethod - OTP method ("aadhaar-linked" or "abha-linked")
 * @returns ABHA details
 */
export async function verifyAbhaIdOtp(
  otp: string,
  txnId: string,
  otpMethod: "aadhaar-linked" | "abha-linked" = "aadhaar-linked",
) {
  try {
    // Validate inputs
    if (!txnId) {
      throw new Error("Transaction ID is required");
    }

    if (!validateOTP(otp)) {
      throw new Error("Invalid OTP format. Please enter a 6-digit OTP.");
    }

    // Get access token
    const accessToken = await getAccessToken();

    const encryptedOtp = await encryptAadhaar(otp);

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the payload according to ABDM V3 API specifications based on OTP method
    const payload = {
      authData: {
        authMethods: ["otp"],
        otp: {
          txnId,
          otpValue: encryptedOtp,
        },
      },
      ...(otpMethod === "aadhaar-linked"
        ? {
            scope: ["abha-login", "aadhaar-verify"],
          }
        : {
            scope: ["abha-login", "mobile-verify"],
          }),
    };

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/login/verify`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    return response;
  } catch (error) {
    throw error;
  }
}
