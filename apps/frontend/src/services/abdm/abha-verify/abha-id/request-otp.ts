/**
 * Request OTP for ABHA ID verification
 */

import { encryptAadhaar } from "../../utils/encryption";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";
import { validateAbhaNumber } from "../../utils/validation";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Request OTP for ABHA ID verification
 * @param abhaId - ABHA ID
 * @param otpMethod - OTP method ("aadhaar-linked" or "abha-linked")
 * @returns Transaction ID and message
 */
export async function requestAbhaIdOtp(
  abhaId: string,
  otpMethod: "aadhaar-linked" | "abha-linked" = "aadhaar-linked",
) {
  try {
    // Validate ABHA ID
    if (!validateAbhaNumber(abhaId)) {
      throw new Error(
        "Invalid ABHA ID format. Please provide a valid ABHA ID.",
      );
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the ABHA ID
    const encryptedAbhaId = await encryptAadhaar(abhaId);

    // Prepare the payload according to ABDM V3 API specifications based on OTP method
    const payload = {
      loginHint: "abha-number",
      loginId: encryptedAbhaId,
      ...(otpMethod === "aadhaar-linked"
        ? {
            scope: ["abha-login", "aadhaar-verify"],
            otpSystem: "aadhaar",
          }
        : {
            scope: ["abha-login", "mobile-verify"],
            otpSystem: "abdm",
          }),
    };

    // Make the API request with the enhanced abdmFetch
    const data = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/login/request/otp`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Return the data with a default message if not provided
    return {
      ...data,
      txnId: data.txnId || "",
      message:
        data.message ||
        (otpMethod === "aadhaar-linked"
          ? "OTP sent successfully to your Aadhaar-linked mobile number"
          : "OTP sent successfully to your ABHA-linked mobile number"),
    };
  } catch (error) {
    throw error;
  }
}
