/**
 * Request OTP for email verification to update ABHA profile
 *
 * This service handles the API call to request an OTP for email verification
 * when updating a patient's email in their ABHA profile.
 */

import { generateUUID } from "@/lib/utils";
import { abdmFetch } from "@/lib/abdm-fetch";
import { encryptData } from "@/services/abdm/utils/encryption";
import { getAccessToken } from "@/services/abdm/utils/auth";

// Environment variables
const ABDM_SANDBOX_BASE_URL =
  process.env.ABDM_SANDBOX_BASE_URL || "https://abhasbx.abdm.gov.in/abha/api";
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Request OTP for email verification
 * @param email - New email to be verified
 * @param xToken - X-Token from ABHA login
 * @returns Transaction ID and message
 */
export async function requestEmailUpdateOtp(
  email: string,
  xToken: string,
): Promise<{ txnId: string; message: string }> {
  try {
    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the email
    const encryptedEmail = await encryptData(email);

    // Prepare the payload
    const payload = {
      scope: ["abha-profile", "email-verify"],
      loginHint: "email",
      loginId: encryptedEmail,
      otpSystem: "abdm",
    };

    // Make the API request with the enhanced abdmFetch
    const data = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/account/request/otp`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID,
          Authorization: `Bearer ${accessToken}`,
          "X-token": xToken.startsWith("Bearer ") ? xToken : `Bearer ${xToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Return the data with a default message if not provided
    return {
      txnId: (data as any).txnId,
      message:
        data.message || "OTP sent successfully to the provided email address",
    };
  } catch (error) {
    throw error;
  }
}
