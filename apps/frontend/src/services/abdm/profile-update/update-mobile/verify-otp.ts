/**
 * Verify OTP for mobile update in ABHA profile
 *
 * This service handles the API call to verify an OTP for mobile verification
 * when updating a patient's mobile number in their ABHA profile.
 */

import { generateUUID } from "@/lib/utils";
import { abdmFetch } from "@/lib/abdm-fetch";
import { encryptData } from "@/services/abdm/utils/encryption";
import { getAccessToken } from "@/services/abdm/utils/auth";

// Environment variables
const ABDM_SANDBOX_BASE_URL =
  process.env.ABDM_SANDBOX_BASE_URL || "https://abhasbx.abdm.gov.in/abha/api";
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Verify OTP for mobile update
 * @param otp - OTP received by the user
 * @param txnId - Transaction ID from the OTP request
 * @param xToken - X-Token from ABHA login
 * @returns Verification result
 */
export async function verifyMobileUpdateOtp(
  otp: string,
  txnId: string,
  xToken: string,
): Promise<any> {
  try {
    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the OTP
    const encryptedOtp = await encryptData(otp);

    // Prepare the payload
    const payload = {
      scope: ["abha-profile", "mobile-verify"],
      authData: {
        authMethods: ["otp"],
        otp: {
          txnId: txnId,
          otpValue: encryptedOtp,
        },
      },
    };

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/account/verify`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID,
          Authorization: `Bearer ${accessToken}`,
          "X-token": xToken.startsWith("Bearer ") ? xToken : `Bearer ${xToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    return {
      ...response,
      message: response.message || "Mobile number updated successfully",
    };
  } catch (error) {
    throw error;
  }
}
