/**
 * Verify OTP for ABHA login
 */

import { abdm<PERSON><PERSON><PERSON>, AbdmResponse } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { validateOTP } from "../utils/validation";
import { encryptAadhaar } from "../utils/encryption";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Verify OTP for ABHA login
 * @param txnId - Transaction ID from OTP generation
 * @param otp - OTP received on mobile
 * @returns ABHA login details including X-token
 */
export async function verifyAbhaLoginOtp(txnId: string, otp: string) {
  try {
    // Validate inputs
    if (!txnId) {
      throw new Error("Transaction ID is required");
    }

    if (!validateOTP(otp)) {
      throw new Error("Invalid OTP format. Please enter a 6-digit OTP.");
    }

    // Get access token
    const accessToken = await getAccessToken();

    const encryptedOtp = await encryptAadhaar(otp);

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the payload according to ABDM V3 API specifications
    const payload = {
      scope: ["abha-login", "mobile-verify"],
      authData: {
        authMethods: ["otp"],
        otp: {
          txnId,
          otpValue: encryptedOtp,
        },
      },
    };

    // Define the interface for the expected response
    interface AbhaLoginResponse extends Omit<AbdmResponse, "authResult"> {
      token: string;
      refreshToken?: string;
      tokenType?: string;
      expiresIn?: number;
      accounts?: Array<{
        ABHANumber?: string;
        preferredAbhaAddress?: string;
        [key: string]: any;
      }>;
      authResult?: "Success" | "Failed" | string;
    }

    // Make the API request
    const response = await abdmFetch<AbhaLoginResponse>(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/login/verify`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    return response;
  } catch (error) {
    throw error;
  }
}
