/**
 * Request OTP for ABHA login
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { validateAbhaNumber } from "../utils/validation";
import { encryptAadhaar } from "../utils/encryption";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Request OTP for ABHA login
 * @param abhaNumber - ABHA number
 * @returns Transaction ID and message
 */
export async function requestAbhaLoginOtp(abhaNumber: string) {
  try {
    // Validate ABHA number
    if (!validateAbhaNumber(abhaNumber)) {
      throw new Error(
        "Invalid ABHA number format. Please provide a valid ABHA number.",
      );
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the mobile number
    const encryptedMobile = await encryptAadhaar(abhaNumber);

    const payload = {
      scope: ["abha-login", "mobile-verify"],
      loginHint: "abha-number",
      loginId: encryptedMobile,
      otpSystem: "abdm",
    };

    // Make the API request with the enhanced abdmFetch
    const data = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/login/request/otp`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Return the data with a default message if not provided
    return {
      txnId: data.txnId,
      message: data.message || "OTP sent successfully",
    };
  } catch (error) {
    throw error;
  }
}
