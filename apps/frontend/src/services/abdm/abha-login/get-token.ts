/**
 * Get ABHA token from database
 */

import { db } from "@/lib/db";

/**
 * Get ABHA token from database
 * @param patientId - Patient ID
 * @param organizationId - Organization ID
 * @returns ABHA token details
 */
export async function getAbhaToken(patientId: string, organizationId: string) {
  try {
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    if (!organizationId) {
      throw new Error("Organization ID is required");
    }

    // Get the ABHA profile for the patient
    const abhaProfile = await db.abhaProfile.findUnique({
      where: {
        patientId,
        organizationId,
      },
    });

    if (!abhaProfile) {
      throw new Error("ABHA profile not found for this patient");
    }

    // Check if the X-token exists
    if (!abhaProfile.xToken) {
      throw new Error("No ABHA X-token found. Please login to ABHA first.");
    }

    // Check if the X-token is expired
    if (
      abhaProfile.xTokenExpiresAt &&
      new Date(abhaProfile.xTokenExpiresAt) < new Date()
    ) {
      throw new Error("ABHA X-token has expired. Please login again.");
    }

    // Return the X-token and related information
    return {
      xToken: abhaProfile.xToken,
      abhaNumber: abhaProfile.abhaNumber,
      expiresAt: abhaProfile.xTokenExpiresAt,
    };
  } catch (error) {
    console.error("Error retrieving ABHA token:", error);
    throw error;
  }
}
