/**
 * Create ABHA address for ABHA creation
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";

/**
 * Create ABHA address for ABHA creation
 * @param txnId - Transaction ID from OTP generation
 * @param abhaAddress - Selected ABHA address
 * @returns ABHA creation result
 */
export async function createAbhaAddress(txnId: string, abhaAddress: string) {
  try {
    // Validate inputs
    if (!txnId) {
      throw new Error("Transaction ID is required");
    }
    if (!abhaAddress) {
      throw new Error("ABHA address is required");
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the payload for ABHA address creation
    // Make sure we're only sending the username part without @sbx
    const abhaUsername = abhaAddress.includes("@")
      ? abhaAddress.split("@")[0]
      : abhaAddress;

    const payload = {
      txnId,
      abhaAddress: abhaUsername,
      preferred: 1,
    };

    console.log(
      "Creating ABHA address with payload:",
      JSON.stringify(payload, null, 2),
    );

    // Make the API request with the enhanced abdmFetch
    // Use the correct API endpoint for creating ABHA addresses
    const data = await abdmFetch(
      `https://abhasbx.abdm.gov.in/abha/api/v3/enrollment/enrol/abha-address`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    console.log(
      "ABHA address creation response:",
      JSON.stringify(data, null, 2),
    );

    // Return the data
    return {
      success: true,
      abhaAddress: data.abhaAddress || abhaAddress,
      abhaNumber: data.abhaNumber || data.ABHANumber,
      profile: data.ABHAProfile || data.profile,
      message: data.message || "ABHA address created successfully",
    };
  } catch (error) {
    console.error("Error creating ABHA address:", error);
    throw error;
  }
}
