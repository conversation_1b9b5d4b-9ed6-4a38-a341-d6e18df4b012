/**
 * Request OTP for ABHA creation using <PERSON><PERSON><PERSON><PERSON>
 */

import { encryptAadhaar } from "../../utils/encryption";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";
import { validateAadhaar } from "../../utils/validation";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Request OTP for ABHA creation using Aadhaar
 * @param aadhaar - Aadhaar number
 * @returns Transaction ID and message
 */
export async function requestEnrollmentOtp(aadhaar: string) {
  try {
    // Validate Aadhaar number
    if (!validateAadhaar(aadhaar)) {
      throw new Error(
        "Invalid Aadhaar number format. Please provide a valid 12-digit Aadhaar number.",
      );
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the Aadhaar number
    const encryptedAadhaar = await encryptAadhaar(aadhaar);

    // Prepare the payload according to ABDM V3 API specifications
    const payload = {
      txnId: "", // Empty transaction ID for initial request
      scope: ["abha-enrol"],
      loginHint: "aadhaar",
      loginId: encryptedAadhaar,
      otpSystem: "aadhaar",
    };

    // Make the API request with the enhanced abdmFetch
    const data = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/enrollment/request/otp`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Return the data with a default message if not provided
    return {
      txnId: data.txnId,
      message:
        data.message ||
        "OTP sent successfully to your Aadhaar-linked mobile number",
    };
  } catch (error) {
    throw error;
  }
}
