/**
 * Get ABHA address suggestions for ABHA creation
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";

// ABDM API endpoints

/**
 * Get ABHA address suggestions for ABHA creation
 * @param txnId - Transaction ID from OTP generation
 * @returns Array of ABHA address suggestions
 */
export async function getAbhaAddressSuggestions(txnId: string) {
  try {
    // Validate inputs
    if (!txnId) {
      throw new Error("Transaction ID is required");
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Make the API request with the enhanced abdmFetch
    // Use the correct ABDM API endpoint for suggestions
    const data = await abdmFetch(
      `https://abhasbx.abdm.gov.in/abha/api/v3/enrollment/enrol/suggestion`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          Transaction_Id: txnId,
          Authorization: `Bearer ${accessToken}`,
        },
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Log the response for debugging
    console.log(
      "ABDM API response for suggestions:",
      JSON.stringify(data, null, 2),
    );

    // Handle different response formats
    // The API returns suggestions in 'abhaAddressList' field
    const suggestions = data.abhaAddressList || data.suggestions || [];

    console.log("Extracted suggestions:", suggestions);

    // Return the suggestions
    return {
      suggestions: suggestions,
      txnId: data.txnId,
      message:
        data.message || "ABHA address suggestions retrieved successfully",
    };
  } catch (error) {
    console.error("Error getting ABHA address suggestions:", error);
    throw error;
  }
}
