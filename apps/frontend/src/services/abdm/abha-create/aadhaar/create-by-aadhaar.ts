/**
 * Create ABHA by <PERSON><PERSON><PERSON><PERSON> verification
 */

import { encryptAadhaar } from "../../utils/encryption";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";
import { validateOTP, validateMobile } from "../../utils/validation";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;

/**
 * Create ABHA by A<PERSON><PERSON>ar verification
 * @param otp - OTP received on mobile
 * @param txnId - Transaction ID from OTP generation
 * @param mobile - Mobile number to link with ABHA
 * @returns ABHA details
 */
export async function createByAadhaar(
  otp: string,
  txnId: string,
  mobile: string,
) {
  try {
    // Validate inputs
    if (!txnId) {
      throw new Error("Transaction ID is required");
    }

    if (!validateOTP(otp)) {
      throw new Error("Invalid OTP format. Please enter a 6-digit OTP.");
    }

    if (!validateMobile(mobile)) {
      throw new Error(
        "Invalid mobile number format. Please enter a 10-digit mobile number.",
      );
    }

    // Get access token
    const accessToken = await getAccessToken();

    const encryptedOtp = await encryptAadhaar(otp);

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the payload according to ABDM V3 API specifications
    const payload = {
      txnId,
      authData: {
        authMethods: ["otp"],
        otp: {
          txnId,
          otpValue: encryptedOtp,
          mobile,
        },
      },
      consent: {
        code: "abha-enrollment",
        version: "1.4",
      },
    };

    // Make the API request with the enhanced abdmFetch
    const data = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/enrollment/enrol/byAadhaar`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    return data;
  } catch (error) {
    throw error;
  }
}
