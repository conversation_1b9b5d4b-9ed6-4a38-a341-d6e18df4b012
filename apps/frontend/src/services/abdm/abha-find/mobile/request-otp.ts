/**
 * Request OTP for ABHA verification using index
 */

import { encryptA<PERSON>haar } from "../../utils/encryption";
import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Request OTP for ABHA verification using index
 * @param index - Index from search response
 * @param searchTxnId - Transaction ID from search response
 * @returns Transaction ID and message
 */
export async function requestOtpWithIndex(index: string, searchTxnId: string) {
  try {
    if (!index) {
      throw new Error("Index is required");
    }

    if (!searchTxnId) {
      throw new Error("Search transaction ID is required");
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the index
    // Make sure we have a valid index value
    if (index === undefined || index === null) {
      throw new Error("Index is required");
    }

    // Convert to string for encryption
    const indexStr = index.toString();
    console.log("Index to encrypt:", indexStr, "Original type:", typeof index);
    const encryptedIndex = await encryptAadhaar(indexStr);

    // Prepare the payload according to ABDM V3 API specifications
    const payload = {
      scope: ["abha-login", "search-abha", "mobile-verify"],
      loginHint: "index",
      loginId: encryptedIndex,
      otpSystem: "abdm",
      txnId: searchTxnId,
    };

    // Make the API request with the enhanced abdmFetch
    const data = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/login/request/otp`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Return the data with a default message if not provided
    return {
      ...data,
      message: data.message || "OTP sent successfully to your mobile number",
    };
  } catch (error) {
    console.error("Error requesting OTP with index:", error);
    throw error;
  }
}
