/**
 * Get ABHA profile details
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../../utils/auth";
import { generateUUID } from "../../utils/request";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL = process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Get ABHA profile details
 * @param token - Token from verification response
 * @returns ABHA profile details
 */
export async function getProfileDetails(token: string) {
  try {
    // Validate inputs
    if (!token) {
      throw new Error("Token is required");
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/account`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          "X-Token": `Bearer ${token}`,
          Authorization: `Bearer ${accessToken}`,
        },
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    return response;
  } catch (error) {
    console.error("Error getting profile details:", error);
    throw error;
  }
}
