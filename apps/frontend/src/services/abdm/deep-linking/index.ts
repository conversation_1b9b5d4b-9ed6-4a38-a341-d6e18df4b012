/**
 * ABDM Deep Linking Service
 *
 * This service handles sending SMS notifications for PHR app download links
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "@/lib/abdm-auth";

// ABDM API base URL
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Send a PHR app download link via SMS
 *
 * @param payload The payload containing phone number and HIP details
 * @returns The response from the ABDM API
 */
export async function sendPhrAppLink(payload: {
  requestId: string;
  timestamp: string;
  notification: {
    phoneNo: string;
    hip: {
      name: string;
      id: string;
    };
  };
}) {
  try {
    if (!ABDM_BASE_URL) {
      throw new Error("ABDM_BASE_URL is not configured");
    }

    // Get access token
    const { accessToken } = await getAccessToken();

    console.log("Sending PHR app link with access token");

    // Create the request payload according to ABDM API requirements
    const requestPayload = {
      requestId: payload.requestId,
      timestamp: payload.timestamp,
      notification: payload.notification,
    };

    console.log(
      "Sending PHR app link with payload:",
      JSON.stringify(requestPayload, null, 2),
    );

    const response = await abdmFetch(
      `${ABDM_BASE_URL}/hiecm/hip/v3/link/patient/links/sms/notify2`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": payload.requestId,
          TIMESTAMP: payload.timestamp,
          "X-CM-ID": ABDM_CM_ID,
          Authorization: `Bearer ${accessToken}`,
          "X-HIP-ID": payload.notification.hip.id,
        },
        body: JSON.stringify(requestPayload),
      },
    );

    return response;
  } catch (error) {
    console.error("Error sending PHR app link:", error);
    throw error;
  }
}
