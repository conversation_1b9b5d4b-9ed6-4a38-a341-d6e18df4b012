/**
 * Consent management service
 *
 * This module provides functions for managing consent:
 * - Request consent
 * - Store consent
 * - Fetch consent details
 * - Check consent status
 * - Revoke consent
 * - Handle consent notifications
 */

import { requestConsent } from "./request";
import { storeConsent } from "./store";
import { fetchConsentDetails, fetchConsentFromDatabase } from "./fetch";
import { revokeConsent, updateConsentStatus } from "./revoke";
import { checkConsentStatus } from "./status";
import {
  linkCareContextToConsent,
  getCareContextsForConsent,
  getConsentsForCareContext,
} from "./link-care-context";
import {
  storeConsentNotification,
  getConsentNotification,
  getConsentNotificationsByPatient,
} from "./notify";

export {
  requestConsent,
  storeConsent,
  fetchConsentDetails,
  fetchConsentFromDatabase,
  revokeConsent,
  updateConsentStatus,
  checkConsentStatus,
  linkCareContextToConsent,
  getCareContextsForConsent,
  getConsentsForCareContext,
  storeConsentNotification,
  getConsentNotification,
  getConsentNotificationsByPatient,
};
