/**
 * Check consent status
 */

import { db } from "@/lib/db";

/**
 * Check consent status by ID
 * This function only checks the database status
 * @param consentId - Consent ID
 * @returns Status information
 */
export async function checkConsentStatus(consentId: string) {
  try {
    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    // Get the consent from the database
    const dbConsent = await db.consent.findUnique({
      where: { id: consentId },
    });

    if (!dbConsent) {
      throw new Error("Consent not found");
    }

    // Return the database status without calling ABDM API
    return {
      status: dbConsent.status,
      consentId,
      consentRequestId: dbConsent.consentRequestId,
    };
  } catch (error) {
    console.error("Error checking consent status:", error);
    throw error;
  }
}
