/**
 * Link care contexts to consents
 */

import { db } from "@/lib/db";

/**
 * Link a care context to a consent
 * @param consentId - Consent ID
 * @param careContextId - Care context ID
 * @returns The created link
 */
export async function linkCareContextToConsent(
  consentId: string,
  careContextId: string,
) {
  try {
    // Validate inputs
    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    if (!careContextId) {
      throw new Error("Care context ID is required");
    }

    // Check if the consent exists
    const consent = await db.consent.findUnique({
      where: {
        id: consentId,
      },
    });

    if (!consent) {
      throw new Error(`Consent with ID ${consentId} not found`);
    }

    // Check if the care context exists
    const careContext = await db.careContext.findUnique({
      where: {
        id: careContextId,
      },
    });

    if (!careContext) {
      throw new Error(`Care context with ID ${careContextId} not found`);
    }

    // Check if the care context belongs to the same patient as the consent
    if (careContext.patientId !== consent.patientId) {
      throw new Error(
        "Care context and consent must belong to the same patient",
      );
    }

    // Check if the link already exists
    const existingLink = await db.consentCareContext.findFirst({
      where: {
        consentId,
        careContextId,
      },
    });

    if (existingLink) {
      return existingLink;
    }

    // Create the link
    const link = await db.consentCareContext.create({
      data: {
        consentId,
        careContextId,
      },
    });

    return link;
  } catch (error) {
    console.error("Error linking care context to consent:", error);
    throw error;
  }
}

/**
 * Get care contexts linked to a consent
 * @param consentId - Consent ID
 * @returns Array of care contexts
 */
export async function getCareContextsForConsent(consentId: string) {
  try {
    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    // Get the links
    const links = await db.consentCareContext.findMany({
      where: {
        consentId,
      },
      include: {
        careContext: true,
      },
    });

    // Return the care contexts
    return links.map((link) => link.careContext);
  } catch (error) {
    console.error("Error getting care contexts for consent:", error);
    throw error;
  }
}

/**
 * Get consents linked to a care context
 * @param careContextId - Care context ID
 * @returns Array of consents
 */
export async function getConsentsForCareContext(careContextId: string) {
  try {
    if (!careContextId) {
      throw new Error("Care context ID is required");
    }

    // Get the links
    const links = await db.consentCareContext.findMany({
      where: {
        careContextId,
      },
      include: {
        consent: true,
      },
    });

    // Return the consents
    return links.map((link) => link.consent);
  } catch (error) {
    console.error("Error getting consents for care context:", error);
    throw error;
  }
}
