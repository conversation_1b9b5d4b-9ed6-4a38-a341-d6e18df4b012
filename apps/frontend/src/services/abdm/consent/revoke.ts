/**
 * Revoke consent
 */

import { abdm<PERSON><PERSON><PERSON>, AbdmResponse } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Revoke consent in ABDM
 * @param consentId - Consent ID
 * @returns Revocation status
 */
export async function revokeConsent(consentId: string) {
  try {
    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the payload according to ABDM V3 API specifications
    const payload = {
      requestId: generateUUID(),
      timestamp: new Date().toISOString(),
    };

    // Define the interface for the expected response
    interface ConsentRevokeResponse extends AbdmResponse {
      status?: string;
    }

    // Make the API request
    const response = await abdmFetch<ConsentRevokeResponse>(
      `${ABDM_BASE_URL}/hiecm/consent/v3/${consentId}/revoke`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Update consent status in database
    await updateConsentStatus(consentId, "REVOKED");

    // Return the response
    return {
      status: response.status || "REVOKED",
    };
  } catch (error) {
    console.error("Error revoking consent:", error);
    throw error;
  }
}

/**
 * Update consent status in database
 * @param consentId - Consent ID
 * @param status - New status
 * @returns Updated consent
 */
export async function updateConsentStatus(consentId: string, status: string) {
  try {
    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    if (!status) {
      throw new Error("Status is required");
    }

    // Update consent status in database
    const consent = await db.consent.update({
      where: { id: consentId },
      data: { status },
    });

    return consent;
  } catch (error) {
    console.error("Error updating consent status:", error);
    throw error;
  }
}
