/**
 * Store consent details
 */

import { db } from "@/lib/db";
/**
 * Store consent details in the database
 * @param patientId - Patient ID
 * @param purpose - Purpose of consent
 * @param hiTypes - Health information types
 * @param dateRange - Date range for consent
 * @param expiry - Expiry date for consent
 * @param organizationId - Organization ID
 * @returns Stored consent details
 */
export async function storeConsent(
  patientId: string,
  purpose: string,
  hiTypes: string[],
  dateRange: { from: string; to: string },
  expiry: string,
  organizationId: string,
) {
  try {
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    if (!purpose) {
      throw new Error("Purpose is required");
    }

    if (!hiTypes || hiTypes.length === 0) {
      throw new Error("Health information types are required");
    }

    if (!dateRange || !dateRange.from || !dateRange.to) {
      throw new Error("Date range is required");
    }

    if (!expiry) {
      throw new Error("Expiry date is required");
    }

    if (!organizationId) {
      throw new Error("Organization ID is required");
    }

    // Store consent details in the database with a unique consentRequestId
    // This will be updated when we receive the callback from ABDM
    const consent = await db.consent.create({
      data: {
        consentRequestId: "pending", // Will be updated when webhook receives actual ID from ABDM
        patientId,
        purpose,
        hiTypes: hiTypes,
        permission: {
          dateRange: {
            from: new Date(dateRange.from),
            to: new Date(dateRange.to), // Keep original dateRange.to as it represents data access period
          },
          dataEraseAt: new Date(expiry),
        },
        expiryDate: new Date(expiry),
        status: "REQUESTED",
        organizationId,
      },
    });

    return consent;
  } catch (error) {
    console.error("Error storing consent:", error);
    throw error;
  }
}
