/**
 * Request consent from a patient
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";
import { cookies } from "next/headers";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";
const ABDM_HIP_ID = process.env.NEXT_PUBLIC_ABDM_HIP_ID;

/**
 * Get the current branch ID from cookies
 * @returns The branch ID or null if not found
 */
async function getCurrentBranchId(): Promise<string | null> {
  try {
    const cookieStore = cookies();
    const branchCookie = cookieStore.get("current-branch")?.value;

    if (branchCookie) {
      const branchInfo = JSON.parse(decodeURIComponent(branchCookie));
      return branchInfo.id;
    }
    return null;
  } catch (error) {
    console.error("Error getting current branch ID:", error);
    return null;
  }
}

/**
 * Get facility ID (hipId) from branch
 * @param branchId - Branch ID to get facility ID for
 * @returns Facility ID or null if not found
 */
async function getFacilityIdFromBranch(
  branchId: string,
): Promise<string | null> {
  try {
    if (!branchId) return null;

    const branch = await db.branch.findUnique({
      where: { id: branchId },
    });

    return branch?.hipId || null;
  } catch (error) {
    console.error("Error getting facility ID from branch:", error);
    return null;
  }
}

/**
 * Request consent from a patient
 * @param patientId - Patient ID (ABHA number)
 * @param purpose - Purpose of consent
 * @param hiTypes - Health information types
 * @param dateRange - Date range for consent
 * @param expiry - Expiry date for consent
 * @param requester - Requester details
 * @returns Consent request ID
 */
export async function requestConsent(
  patientId: string,
  purpose: string,
  hiTypes: string[],
  dateRange: { from: string; to: string },
  expiry: string,
  requester?: {
    name: string;
    identifier?: {
      type: string;
      value: string;
      system?: string;
    };
  },
) {
  try {
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    if (!purpose) {
      throw new Error("Purpose is required");
    }

    if (!hiTypes || hiTypes.length === 0) {
      throw new Error("Health information types are required");
    }

    if (!dateRange || !dateRange.from || !dateRange.to) {
      throw new Error("Date range is required");
    }

    if (!expiry) {
      throw new Error("Expiry date is required");
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Default requester if not provided
    const defaultRequester = {
      name: "Aran Care",
      identifier: {
        type: "HIP",
        value: ABDM_HIP_ID || "default-hip",
      },
    };

    // Map purpose text to ABDM purpose code
    const purposeCodeMap: Record<string, string> = {
      "Care Management": "CAREMGT",
      "Break the Glass": "BREAKGLASS",
      "Public Health": "PUBHLTH",
      "Healthcare Payment": "HPAYMT",
      "Disease Specific Healthcare Research": "DSRCH",
      "Self Requested": "PATRQT",
    };

    // Get the purpose code from the map or use a default
    const purposeCode = purposeCodeMap[purpose] || "CAREMGT";

    // Get facility ID from branch
    let facilityId = null;
    const branchId = await getCurrentBranchId();
    if (branchId) {
      facilityId = await getFacilityIdFromBranch(branchId);
    }

    // Prepare the payload according to ABDM V3 API specifications
    const payload = {
      consent: {
        purpose: {
          text: purpose,
          code: purposeCode,
          refUri: "www.abhasbx.gov.in",
        },
        patient: {
          id: patientId,
        },
        hiu: {
          id: facilityId, // Use branch facility ID if available, otherwise fallback
        },
        careContexts: null,
        requester: {
          name: requester?.name || defaultRequester.name,
          identifier: {
            type:
              requester?.identifier?.type || defaultRequester.identifier.type,
            value:
              requester?.identifier?.value || defaultRequester.identifier.value,
            system: requester?.identifier?.system || "https://www.mciindia.org",
          },
        },
        hiTypes,
        permission: {
          accessMode: "VIEW",
          dateRange,
          dataEraseAt: expiry,
          frequency: {
            unit: "HOUR",
            value: 0,
            repeats: 0,
          },
        },
      },
    };

    // Log the full URL for debugging
    const apiUrl = `${ABDM_BASE_URL}/hiecm/consent/v3/request/init`;

    // Make the API request
    const response = await abdmFetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "REQUEST-ID": requestId,
        TIMESTAMP: timestamp,
        "X-CM-ID": ABDM_CM_ID || "sbx",
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(payload),
      signal: AbortSignal.timeout(120000), // 120 seconds timeout
    });

    // If we get an empty response but status is 202 (Accepted), create a mock response
    if (Object.keys(response).length === 0) {
      return {
        consentRequestId: `consent-req-${requestId}`,
        message: "Consent request accepted",
      };
    }

    return response;
  } catch (error) {
    console.error("Error requesting consent:", error);
    throw error;
  }
}
