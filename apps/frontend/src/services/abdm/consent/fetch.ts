/**
 * Fetch consent details
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Fetch consent details from ABDM
 * @param consentId - Consent ID
 * @param organizationId - Organization ID to get HIU ID from branch
 * @returns Consent details
 */
export async function fetchConsentDetails(consentId: string, organizationId?: string) {
  try {
    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    // First try to get consent from database
    try {
      const dbConsent = await fetchConsentFromDatabase(consentId);
      if (dbConsent) {
        return dbConsent;
      }
    } catch (dbError) {
      console.log("Consent not found in database, trying ABDM API");
    }

    // Get HIU ID from branch (use HIP ID as HIU ID)
    let hiuId = "";
    if (organizationId) {
      try {
        const branch = await db.branch.findFirst({
          where: { organizationId },
          select: { hipId: true },
        });
        hiuId = branch?.hipId || "";
        console.log(`📋 Using HIU ID from branch: ${hiuId}`);
      } catch (error) {
        console.warn("Failed to get HIU ID from branch:", error);
      }
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    console.log(`📞 CONSENT FETCH: Calling ABDM API for consentId: ${consentId}`);
    console.log(`📞 CALLBACK EXPECTED: This will trigger a callback to /api/webhook/api/v3/hiu/consent/on-fetch`);

    // Make the API request - using the correct endpoint and method
    const response = await abdmFetch(
      `${ABDM_BASE_URL}/hiecm/consent/v3/fetch`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          "X-HIU-ID": hiuId, // Use HIU ID from branch
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          consentId: consentId,
        }),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    return response;
  } catch (error) {
    console.error("Error fetching consent details:", error);
    throw error;
  }
}

/**
 * Fetch consent details from database
 * @param consentId - Consent ID
 * @returns Consent details
 */
export async function fetchConsentFromDatabase(consentId: string) {
  try {
    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    // Fetch consent details from database with linked care contexts
    const consent = await db.consent.findUnique({
      where: { id: consentId },
      include: {
        patient: true,
        careContextLinks: {
          include: {
            careContext: {
              include: {
                consultation: true,
              },
            },
          },
        },
      },
    });

    if (!consent) {
      throw new Error("Consent not found");
    }

    // Transform the response to include linked care contexts
    const transformedConsent = {
      ...consent,
      linkedCareContexts: consent.careContextLinks.map(
        (link) => link.careContext,
      ),
      careContextLinks: undefined, // Remove the raw links from the response
    };
    return transformedConsent;
  } catch (error) {
    console.error("Error fetching consent from database:", error);
    throw error;
  }
}
