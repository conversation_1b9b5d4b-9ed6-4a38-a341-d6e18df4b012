/**
 * Consent notification service
 *
 * This module provides functions for handling consent notifications:
 * - Store consent notification
 * - Get consent notification by ID
 * - Get consent notifications by patient ID
 */

import { db } from "@/lib/db";

/**
 * Store consent notification from webhook
 * @param requestId - Request ID from headers
 * @param notification - Notification data from webhook
 * @returns Created consent notification
 */
export async function storeConsentNotification(
  requestId: string,
  notification: any,
) {
  try {
    if (!requestId) {
      throw new Error("Request ID is required");
    }

    if (!notification) {
      throw new Error("Notification data is required");
    }

    const {
      status,
      consentId,
      consentDetail,
      signature,
      grantAcknowledgement,
    } = notification;

    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    if (!consentDetail) {
      throw new Error("Consent detail is required");
    }

    // Check if consent notification already exists
    // Note: consentId is not a unique field, we need to use findFirst instead
    const existingNotification = await db.consentNotify.findFirst({
      where: {
        consentId,
      },
    });

    if (existingNotification) {
      // Update existing notification
      return await db.consentNotify.update({
        where: {
          id: existingNotification.id,
        },
        data: {
          status,
          signature,
          grantAcknowledgement,
          updatedAt: new Date(),
        },
      });
    }

    // Extract data from consent detail
    const {
      // schemaVersion,
      // createdAt is no longer needed since we removed notificationDate
      patient,
      // careContexts,
      // purpose,
      // hip,
      // consentManager,
      // hiTypes,
      // permission,
    } = consentDetail;

    // Find the patient to get the organizationId
    const patientRecord = await db.patient.findUnique({
      where: { id: patient.id },
      select: { organizationId: true },
    });

    if (!patientRecord) {
      throw new Error(`Patient not found with ID: ${patient.id}`);
    }

    // Create new consent notification
    // return await db.consentNotify.create({
    //   data: {
    //     requestId,
    //     consentId,
    //     status,
    //     schemaVersion,
    //     patientId: patient.id,
    //     hipId: hip.id,
    //     consentManagerId: consentManager.id,
    //     careContexts: careContexts,
    //     purpose: purpose,
    //     hiTypes: hiTypes,
    //     permission: permission,
    //     signature,
    //     grantAcknowledgement,
    //   },
    // });
  } catch (error) {
    console.error("Error storing consent notification:", error);
    throw error;
  }
}

/**
 * Get consent notification by ID
 * @param consentId - Consent ID
 * @returns Consent notification
 */
export async function getConsentNotification(consentId: string) {
  try {
    if (!consentId) {
      throw new Error("Consent ID is required");
    }

    const notification = await db.consentNotify.findFirst({
      where: {
        consentId,
      },
    });

    if (!notification) {
      throw new Error("Consent notification not found");
    }

    return notification;
  } catch (error) {
    console.error("Error getting consent notification:", error);
    throw error;
  }
}

/**
 * Get consent notifications by patient ID
 * @param patientId - Patient ID
 * @returns Consent notifications
 */
export async function getConsentNotificationsByPatient(patientId: string) {
  try {
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    const notifications = await db.consentNotify.findMany({
      where: {
        // ConsentNotify has patientId field directly
        patientId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return notifications;
  } catch (error) {
    console.error("Error getting consent notifications by patient:", error);
    throw error;
  }
}
