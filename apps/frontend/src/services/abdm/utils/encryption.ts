/**
 * Encryption utilities for ABDM
 *
 * This module provides encryption utilities for ABDM API requests.
 */

const PUBLIC_KEY =
  "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAstWB95C5pHLXiYW59qyO4Xb+59KYVm9Hywbo77qETZVAyc6VIsxU+UWhd/k/YtjZibCznB+HaXWX9TVTFs9Nwgv7LRGq5uLczpZQDrU7dnGkl/urRA8p0Jv/f8T0MZdFWQgks91uFffeBmJOb58u68ZRxSYGMPe4hb9XXKDVsgoSJaRNYviH7RgAI2QhTCwLEiMqIaUX3p1SAc178ZlN8qHXSSGXvhDR1GKM+y2DIyJqlzfik7lD14mDY/I4lcbftib8cv7llkybtjX1AayfZp4XpmIXKWv8nRM488/jOAF81Bi13paKgpjQUUuwq9tb5Qd/DChytYgBTBTJFe7irDFCmTIcqPr8+IMB7tXA3YXPp3z605Z6cGoYxezUm2Nz2o6oUmarDUntDhq/PnkNergmSeSvS8gD9DHBuJkJWZweG3xOPXiKQAUBr92mdFhJGm6fitO5jsBxgpmulxpG0oKDy9lAOLWSqK92JMcbMNHn4wRikdI9HSiXrrI7fLhJYTbyU3I4v5ESdEsayHXuiwO/1C8y56egzKSw44GAtEpbAkTNEEfK5H5R0QnVBIXOvfeF4tzGvmkfOO6nNXU3o/WAdOyV3xSQ9dqLY5MEL4sJCGY1iJBIAQ452s8v0ynJG5Yq+8hNhsCVnklCzAlsIzQpnSVDUVEzv17grVAw078CAwEAAQ==";

import { webcrypto } from "crypto";

/**
 * Encrypt data using ABDM public key
 * @param data - Data to encrypt
 * @returns Encrypted data in base64 format
 */
export async function encryptData(data: string): Promise<string> {
  try {
    // Convert base64 public key to binary
    const keyData = Buffer.from(PUBLIC_KEY, "base64");

    // Import the key for RSA-OAEP encryption
    const cryptoKey = await webcrypto.subtle.importKey(
      "spki",
      keyData,
      {
        name: "RSA-OAEP",
        hash: "SHA-1", // Using SHA-1 as specified
      },
      false,
      ["encrypt"],
    );

    // Encrypt the data
    const encodedData = new TextEncoder().encode(data);
    const encrypted = await webcrypto.subtle.encrypt(
      {
        name: "RSA-OAEP",
      },
      cryptoKey,
      encodedData,
    );

    // Convert to base64
    return Buffer.from(encrypted).toString("base64");
  } catch (error) {
    console.error("Encryption error:", error);
    throw new Error("Failed to encrypt data");
  }
}

/**
 * Encrypt Aadhaar number using ABDM public key
 * @param aadhaar - Aadhaar number to encrypt
 * @returns Encrypted Aadhaar number in base64 format
 */
export async function encryptAadhaar(aadhaar: string): Promise<string> {
  return encryptData(aadhaar);
}
