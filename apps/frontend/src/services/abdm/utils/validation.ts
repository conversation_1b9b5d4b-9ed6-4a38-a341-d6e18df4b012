/**
 * Validation utilities for ABDM service and general application use
 *
 * This file contains comprehensive validation patterns as per application requirements:
 * - Mobile Number Validation: (\+91|0)?[1-9][0-9]{9}
 * - Date of Birth Validation: \d{4}-(0[0-9]|1[012])-(0[0-9]|[12][0-9]|3[01])$
 * - ABHA Address Validation: (^[a-zA-Z0-9]+[.]?[a-zA-Z0-9][]?[a-zA-Z0-9]+$)|(^[a-zA-Z0-9]+[]?[a-zA-Z0-9][.]?[a-zA-Z0-9]+$)
 * - ABHA Number Validation: \d{2}-\d{4}-\d{4}-\d{4}
 * - OTP Validation: [0-9]{6}
 * - Password Validation: ^(?=.[A-Z])(?=.\d)(?=.[!@#$^-])[A-Za-z\d!@#$%^&*-]{8,}$
 * - Email Validation: ^[a-zA-Z0-9_-]+(?:.[a-zA-Z0-9_-]+)@(?:[a-zA-Z0-9-]+.)+[a-zA-Z]{2,7}$
 */

// Validation regex patterns
export const VALIDATION_PATTERNS = {
  MOBILE: /(\+91|0)?[1-9][0-9]{9}/,
  DATE_OF_BIRTH: /\d{4}-(0[0-9]|1[012])-(0[0-9]|[12][0-9]|3[01])$/,
  ABHA_ADDRESS:
    /^[a-zA-Z0-9]+([._]?[a-zA-Z0-9]+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z0-9]+)*$/,
  ABHA_NUMBER: /\d{2}-\d{4}-\d{4}-\d{4}/,
  OTP: /[0-9]{6}/,
  PASSWORD: /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*\-])[A-Za-z\d!@#$%^&*\-]{8,}$/,
  EMAIL:
    /^[a-zA-Z0-9_-]+(?:\.[a-zA-Z0-9_-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,7}$/,
  AADHAAR: /^\d{12}$/,
} as const;

/**
 * Validate OTP format (6 digits)
 * @param otp - OTP to validate
 * @returns Boolean indicating if OTP is valid
 */
export function validateOTP(otp: string): boolean {
  if (!otp || typeof otp !== "string") return false;
  return VALIDATION_PATTERNS.OTP.test(otp);
}

/**
 * Validate ABHA number format (XX-XXXX-XXXX-XXXX)
 * @param abhaNumber - ABHA number to validate
 * @returns Boolean indicating if ABHA number is valid
 */
export function validateAbhaNumber(abhaNumber: string): boolean {
  if (!abhaNumber || typeof abhaNumber !== "string") return false;
  return VALIDATION_PATTERNS.ABHA_NUMBER.test(abhaNumber);
}

/**
 * Validate ABHA Address format
 * @param abhaAddress - ABHA Address to validate
 * @returns Boolean indicating if ABHA Address is valid
 */
export function validateAbhaAddress(abhaAddress: string): boolean {
  if (!abhaAddress || typeof abhaAddress !== "string") return false;
  return VALIDATION_PATTERNS.ABHA_ADDRESS.test(abhaAddress);
}

/**
 * Validate Aadhaar number format (12 digits)
 * @param aadhaar - Aadhaar number to validate
 * @returns Boolean indicating if Aadhaar number is valid
 */
export function validateAadhaar(aadhaar: string): boolean {
  if (!aadhaar || typeof aadhaar !== "string") return false;
  return VALIDATION_PATTERNS.AADHAAR.test(aadhaar);
}

/**
 * Validate mobile number format
 * Supports: 9876543210, +************, 09876543210
 * @param mobile - Mobile number to validate
 * @returns Boolean indicating if mobile number is valid
 */
export function validateMobile(mobile: string): boolean {
  if (!mobile || typeof mobile !== "string") return false;
  return VALIDATION_PATTERNS.MOBILE.test(mobile);
}

/**
 * Validate email format
 * @param email - Email to validate
 * @returns Boolean indicating if email is valid
 */
export function validateEmail(email: string): boolean {
  if (!email || typeof email !== "string") return false;
  return VALIDATION_PATTERNS.EMAIL.test(email);
}

/**
 * Validate password format
 * Must contain: at least 8 characters, 1 uppercase letter, 1 digit, 1 special character
 * @param password - Password to validate
 * @returns Boolean indicating if password is valid
 */
export function validatePassword(password: string): boolean {
  if (!password || typeof password !== "string") return false;
  return VALIDATION_PATTERNS.PASSWORD.test(password);
}

/**
 * Validate date of birth format (YYYY-MM-DD)
 * @param dateOfBirth - Date of birth to validate
 * @returns Boolean indicating if date of birth is valid
 */
export function validateDateOfBirth(dateOfBirth: string): boolean {
  if (!dateOfBirth || typeof dateOfBirth !== "string") return false;
  return VALIDATION_PATTERNS.DATE_OF_BIRTH.test(dateOfBirth);
}
