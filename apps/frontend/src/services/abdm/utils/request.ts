/**
 * Request utilities for ABDM service
 */

import {
  calculateRetryDelay,
  parseAbdmError,
  shouldRetryAbdmError,
} from "@/lib/abdm-error-utils";

/**
 * Generate a UUID (replacement for crypto.randomUUID())
 * @returns Generated UUID
 */
export function generateUUID(): string {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Enhanced helper function to implement retry logic for API calls with exponential backoff
 * @param url - URL to fetch
 * @param options - Fetch options
 * @param maxRetries - Maximum number of retries
 * @param baseRetryDelay - Base delay between retries in milliseconds
 * @param retryOn - HTTP status codes to retry on
 * @returns Fetch response
 */
export async function retryFetch(
  url: string,
  options: RequestInit,
  maxRetries = 1,
  baseRetryDelay = 1000,
  retryOn = [408, 429, 500, 502, 503, 504],
): Promise<Response> {
  let lastError: Error | null = null;
  let responseText: string | null = null;

  // Ensure we have a timeout signal if not provided
  if (!options.signal) {
    // Default timeout of 120 seconds if not specified (well below the 299 second max)
    options.signal = AbortSignal.timeout(120000);
  }

  // Circuit breaker pattern - track consecutive failures
  let consecutiveNetworkFailures = 0;
  const MAX_NETWORK_FAILURES = 2; // Break circuit after 2 consecutive network failures

  for (let attempt = 0; attempt < maxRetries + 1; attempt++) {
    try {
      // If this is a retry, log the attempt
      if (attempt > 0) {
        const delay = calculateRetryDelay(attempt - 1, baseRetryDelay);
        // Wait before retrying with exponential backoff
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      // Make the request
      const response = await fetch(url, options);

      // Reset network failure counter on any response
      consecutiveNetworkFailures = 0;

      // If the response is ok, return it immediately
      if (response.ok) {
        return response;
      }

      // Get the response text for error analysis
      responseText = await response.text();

      // If the status code is not in our retry list, throw immediately
      if (!retryOn.includes(response.status)) {
        const errorMessage = `HTTP error ${response.status}: ${response.statusText}`;
        lastError = new Error(errorMessage);

        // Try to parse the response body for more details
        try {
          if (responseText) {
            const errorData = JSON.parse(responseText);
            const parsedError = parseAbdmError(errorData);
            lastError = new Error(
              `${parsedError.code}: ${parsedError.message}`,
            );
          }
        } catch (e) {
          // If parsing fails, use the original error
        }

        throw lastError;
      }

      // If we get here, the response status is in the retry list
      lastError = new Error(
        `HTTP error ${response.status}: ${response.statusText}`,
      );

      // Try to parse the response body for more details
      try {
        if (responseText) {
          const errorData = JSON.parse(responseText);
          const parsedError = parseAbdmError(errorData);

          // Check if this error should be retried
          if (!shouldRetryAbdmError(errorData)) {
            lastError = new Error(
              `${parsedError.code}: ${parsedError.message}`,
            );
            throw lastError; // Don't retry this specific error
          }

          lastError = new Error(`${parsedError.code}: ${parsedError.message}`);
        }
      } catch (e) {
        // If parsing fails, continue with the original error
      }

      console.warn(
        `Attempt ${attempt + 1}/${maxRetries + 1} failed: ${
          lastError.message
        }. ${attempt < maxRetries ? "Will retry." : "No more retries."}`,
      );

      // If this is the last attempt, throw the error
      if (attempt === maxRetries) {
        throw lastError;
      }
    } catch (error) {
      // Check if this is an AbortError (timeout)
      if (error instanceof DOMException && error.name === "AbortError") {
        console.error(`Request to ${url} timed out`);
        throw new Error(
          `ABDM-9003: ABDM API request timed out after ${
            options.signal instanceof AbortSignal
              ? (options.signal as any).timeout / 1000
              : 120
          } seconds`,
        );
      }

      // Network error or other fetch error
      lastError = error instanceof Error ? error : new Error(String(error));

      // Increment network failure counter
      if (error instanceof TypeError && error.message.includes("fetch")) {
        consecutiveNetworkFailures++;

        // Circuit breaker - if too many consecutive network failures, break
        if (consecutiveNetworkFailures >= MAX_NETWORK_FAILURES) {
          console.error(
            `Circuit breaker triggered after ${consecutiveNetworkFailures} consecutive network failures`,
          );
          throw new Error(
            `ABDM-9001: Network connection to ABDM services is unstable. Please check your internet connection and try again later.`,
          );
        }
      }

      console.warn(
        `Attempt ${attempt + 1}/${maxRetries + 1} failed: ${
          lastError.message
        }. ${attempt < maxRetries ? "Will retry." : "No more retries."}`,
      );

      // If this is the last attempt, throw the error
      if (attempt === maxRetries) {
        throw lastError;
      }
    }
  }

  // If we've exhausted all retries, throw the last error
  throw lastError || new Error("All retry attempts failed");
}

/**
 * Get required headers for ABDM API requests
 * @param ABDM_CM_ID - ABDM CM ID
 * @param ABDM_HIP_ID - ABDM HIP ID
 * @returns Headers object
 */
export function getRequiredHeaders(
  ABDM_CM_ID?: string,
  ABDM_HIP_ID?: string,
): Record<string, string> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    "REQUEST-ID": generateUUID(),
    TIMESTAMP: new Date().toISOString(),
  };

  if (ABDM_CM_ID) {
    headers["X-CM-ID"] = ABDM_CM_ID;
  }

  if (ABDM_HIP_ID) {
    headers["X-HIP-ID"] = ABDM_HIP_ID;
  }

  return headers;
}
