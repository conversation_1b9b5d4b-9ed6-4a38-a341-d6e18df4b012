/**
 * Authentication utilities for ABDM service
 */

import { generateUUID } from "./request";
import { abd<PERSON><PERSON><PERSON><PERSON>, Abd<PERSON><PERSON><PERSON>ponse } from "@/lib/abdm-fetch";

// Cache for access token to avoid unnecessary API calls
let cachedToken: { token: string; expiresAt: number } | null = null;

/**
 * Get access token for ABDM API with caching and retry logic
 * @returns Access token
 */
export async function getAccessToken(): Promise<string> {
  // ABDM API endpoints
  const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
  const NEXT_PUBLIC_ABDM_CLIENT_ID = process.env.NEXT_PUBLIC_ABDM_CLIENT_ID;
  const ABDM_CLIENT_SECRET = process.env.NEXT_PUBLIC_ABDM_CLIENT_SECRET;
  const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

  // Check if we have a valid cached token
  if (cachedToken && cachedToken.expiresAt > Date.now()) {
    return cachedToken.token;
  }

  // Check if required environment variables are set
  if (!ABDM_BASE_URL) {
    throw new Error(
      "ABDM_BASE_URL is not configured. Please set the NEXT_PUBLIC_ABDM_BASE_URL environment variable.",
    );
  }

  if (!NEXT_PUBLIC_ABDM_CLIENT_ID) {
    throw new Error(
      "NEXT_PUBLIC_ABDM_CLIENT_ID is not configured. Please set either NEXT_PUBLIC_ABDM_CLIENT_ID or NEXT_PUBLIC_ABDM_CLIENT_ID environment variable.",
    );
  }

  if (!ABDM_CLIENT_SECRET) {
    throw new Error(
      "ABDM_CLIENT_SECRET is not configured. Please set either NEXT_PUBLIC_ABDM_CLIENT_SECRET or ABDM_CLIENT_SECRET environment variable.",
    );
  }

  try {
    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "REQUEST-ID": requestId,
      TIMESTAMP: timestamp,
    };

    if (ABDM_CM_ID) {
      headers["X-CM-ID"] = ABDM_CM_ID;
    }

    // Define the interface for the expected response
    interface AuthResponse extends AbdmResponse {
      accessToken: string;
      expiresIn: number;
      tokenType: string;
    }

    // Check if ABDM_BASE_URL already contains /api
    const baseUrl = ABDM_BASE_URL.endsWith("/api")
      ? ABDM_BASE_URL
      : ABDM_BASE_URL.includes("/api/")
        ? ABDM_BASE_URL
        : `${ABDM_BASE_URL}/api`;

    // Log the authentication URL for debugging
    console.log(`Authentication URL: ${baseUrl}/hiecm/gateway/v3/sessions`);

    const response = await abdmFetch<AuthResponse>(
      `${baseUrl}/hiecm/gateway/v3/sessions`,
      {
        method: "POST",
        headers,
        body: JSON.stringify({
          clientId: NEXT_PUBLIC_ABDM_CLIENT_ID,
          clientSecret: ABDM_CLIENT_SECRET,
          grantType: "client_credentials",
        }),
        // Add timeout to prevent hanging requests
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Cache the token with expiry time (subtract 5 minutes for safety margin)
    const expiresInMs = (response.expiresIn - 300) * 1000;
    cachedToken = {
      token: response.accessToken,
      expiresAt: Date.now() + expiresInMs,
    };

    return response.accessToken;
  } catch (error) {
    // Rethrow with a user-friendly message
    throw error instanceof Error
      ? error
      : new Error("An unexpected error occurred during authentication");
  }
}

/**
 * Clear the cached token
 * This is useful when the token is expired or invalid
 */
export function clearCachedToken(): void {
  cachedToken = null;
}
