/**
 * Get ABHA profile details
 *
 * This service handles fetching ABHA profile details from the ABDM API
 * using the X-Token obtained during ABHA verification or login.
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { generateUUID } from "../utils/request";
import { getAccessToken } from "../utils/auth";

// ABDM API endpoints
const ABDM_SANDBOX_BASE_URL =
  process.env.NEXT_PUBLIC_ABDM_SANDBOX_URL ||
  "https://abhasbx.abdm.gov.in/abha/api";

/**
 * Get ABHA profile details using X-Token
 * @param xToken - X-Token obtained during ABHA verification or login
 * @returns ABHA profile details
 */
export async function getAbhaProfileDetails(xToken: string) {
  try {
    if (!xToken) {
      throw new Error("X-Token is required to fetch ABHA profile details");
    }

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Get access token
    const accessToken = await getAccessToken();

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/profile/account`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-token": `Bearer ${xToken}`,
          Authorization: `Bearer ${accessToken}`,
        },
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    console.log(
      "ABHA profile API response:",
      JSON.stringify(response, null, 2),
    );
    return response;
  } catch (error) {
    console.error("Error fetching ABHA profile details:", error);
    throw error;
  }
}

/**
 * Get ABHA profile details for ABHA Address verification using X-Token
 * This uses the specific endpoint for ABHA address verification
 * @param xToken - X-Token obtained during ABHA address verification
 * @returns ABHA profile details
 */
export async function getAbhaAddressProfileDetails(xToken: string) {
  try {
    if (!xToken) {
      throw new Error("X-Token is required to fetch ABHA profile details");
    }

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Get access token
    const accessToken = await getAccessToken();

    // Make the API request with the specific endpoint for ABHA address
    const response = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/phr/web/login/profile/abha-profile`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-token": `Bearer ${xToken}`,
          Authorization: `Bearer ${accessToken}`,
        },
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    console.log(
      "ABHA address profile API response:",
      JSON.stringify(response, null, 2),
    );
    return response;
  } catch (error) {
    console.error("Error fetching ABHA address profile details:", error);
    throw error;
  }
}
