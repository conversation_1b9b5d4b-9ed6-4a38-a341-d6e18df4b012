/**
 * ABDM Service
 *
 * This service handles communication with the ABDM sandbox APIs for:
 * - A<PERSON><PERSON>ar OTP generation and verification
 * - ABHA number creation
 * - ABHA verification
 * - ABHA card download
 * - Consent management
 * - Profile updates (email and mobile)
 * - Health record packaging and upload
 * - Care context linking
 * - ABHA search functionality
 * - ABHA profile retrieval and synchronization
 * - ABHA find functionality
 * - User Initiated Linking (UIL) flow
 */

import * as abhaVerify from "./abha-verify";
import * as abhaCreate from "./abha-create";
import * as abhaCard from "./abha-card";
import * as abhaLogin from "./abha-login";
import * as consent from "./consent";
import * as profileUpdate from "./profile-update";
import * as healthRecord from "./health-record";
import * as careContext from "./care-context";
import * as abhaProfile from "./abha-profile";
import * as utils from "./utils/auth";
import abhaSearch from "./abha-search";
import * as abhaFind from "./abha-find";
import * as userInitiatedLinking from "./user-initiated-linking";
import * as deepLinking from "./deep-linking";

// Export all ABDM services
export {
  abhaVerify,
  abhaCreate,
  abhaCard,
  abhaLogin,
  consent,
  profileUpdate,
  healthRecord,
  careContext,
  abhaProfile,
  utils,
  abhaSearch,
  abhaFind,
  userInitiatedLinking,
  deepLinking,
};
