/**
 * Notify about linked care context
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";
const ABDM_HIP_ID = process.env.NEXT_PUBLIC_ABDM_HIP_ID;

/**
 * Notify about linked care context
 * @param careContextId - Care context ID
 * @returns Notification response
 */
export async function notifyCareContext(careContextId: string) {
  try {
    if (!careContextId) {
      throw new Error("Care context ID is required");
    }

    // Get care context details
    const careContext = await db.careContext.findUnique({
      where: { id: careContextId },
      include: {
        patient: {
          include: {
            abhaProfile: true,
          },
        },
        consultation: true,
      },
    });

    if (!careContext) {
      throw new Error("Care context not found");
    }

    if (!careContext.patient.abhaProfile?.abhaNumber) {
      throw new Error("Patient does not have an ABHA number");
    }

    // Get the branch ID from the consultation
    const branchId = careContext.consultation.branchId;

    // Get the link token for this patient and branch
    const abhaLinkToken = await db.abhaLinkToken.findFirst({
      where: {
        patientId: careContext.patientId,
        branchId: branchId,
        status: "active",
        linkTokenExpiry: {
          gt: new Date(),
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    if (!abhaLinkToken?.linkToken) {
      throw new Error(
        "Link token not found for this branch. Please generate a link token first.",
      );
    }

    // Store the link token for later use
    const linkToken = abhaLinkToken.linkToken;

    // Get access token
    const accessToken = await getAccessToken();

    // Generate request ID and timestamp
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the payload
    const payload = {
      notification: {
        patient: {
          id: careContext.patient.abhaProfile.abhaAddress,
        },
        careContext: {
          patientReference: careContext.patient.id,
          careContextReference: careContext.consultationId, // Use consultation ID as the reference
        },
        hiTypes: careContext.hiTypes,
        date: timestamp,
        hip: {
          id: ABDM_HIP_ID || "",
        },
      },
    };

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_BASE_URL}/hiecm/hip/v3/link/context/notify`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-HIP-ID": ABDM_HIP_ID || "",
          "X-LINK-TOKEN": linkToken,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Update the care context record in the database
    await db.careContext.update({
      where: { id: careContext.id },
      data: {
        status: "Success", // Update status to Success after notification
        additionalInfo: careContext.additionalInfo
          ? {
              ...(careContext.additionalInfo as object),
              notifyResponse: response,
              notifiedAt: new Date().toISOString(),
            }
          : {
              notifyResponse: response,
              notifiedAt: new Date().toISOString(),
            },
      },
    });

    return {
      message: "Care context notification sent successfully",
    };
  } catch (error) {
    throw error;
  }
}
