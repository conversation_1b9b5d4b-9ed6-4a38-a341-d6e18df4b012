/**
 * Generate link token for care context linking
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Generate link token for care context linking
 * @param patientId - Patient ID
 * @returns Link token
 */
export async function generateLinkToken(patientId: string, branchId?: string) {
  try {
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    // Get patient details with ABHA profile
    const patient = await db.patient.findUnique({
      where: { id: patientId },
      include: { abhaProfile: true },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    //SRIDHAR: *IMPORTANT*: Ptient not having ABHA Number is a valid case for non KYC cases. In this we need <PERSON><PERSON> adress alone.
    //Here we are getting 'undefined' as string, should handle this case.

    // if (!patient.abhaProfile?.abhaNumber) {
    //   throw new Error("Patient does not have an ABHA number");
    // }
    if (patient.abhaProfile?.abhaNumber === "undefined") {
      patient.abhaProfile.abhaNumber = null;
    }
    if (!patient.abhaProfile?.abhaNumber && !patient.abhaProfile?.abhaAddress) {
      throw new Error(
        "Patient must have either an ABHA number or an ABHA address.",
      );
    }

    // Get the HIP ID from the branch first
    let hipId = "";

    if (!branchId) {
      throw new Error(
        "Branch ID is required to generate a link token. Please specify a branch or ensure the patient has a primary branch assigned.",
      );
    }

    const branch = await db.branch.findUnique({
      where: { id: branchId },
    });

    if (!branch) {
      throw new Error(`Branch with ID ${branchId} not found.`);
    }

    if (branch?.hipId) {
      hipId = branch.hipId;
    } else {
      throw new Error(
        `Branch ${branchId} does not have a Facility ID (HIP ID). Please set a Facility ID for the branch before generating a link token.`,
      );
    }

    // If no HIP ID is available, throw an error
    if (!hipId) {
      throw new Error(
        "No Facility ID (HIP ID) available. Please set a Facility ID for the branch before generating a link token.",
      );
    }

    // Check if a valid link token already exists in the AbhaLinkToken model based on patientId and branchId
    const existingLinkToken = await db.abhaLinkToken.findFirst({
      where: {
        patientId: patient.id,
        branchId: branchId,
        status: "active",
        linkTokenExpiry: {
          gt: new Date(),
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    if (existingLinkToken?.linkToken) {
      // Return the existing token if it's still valid
      return {
        token: existingLinkToken.linkToken,
        message: "Existing link token retrieved successfully",
      };
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Generate request ID and timestamp
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Store the request context before making the ABDM API call
    try {
      await db.linkTokenRequest.create({
        data: {
          requestId,
          patientId: patient.id,
          organizationId: patient.organizationId,
          branchId: branchId || patient.primaryBranchId,
          hipId: hipId,
          abhaAddress: patient.abhaProfile.abhaAddress || "",
          status: "pending",
        },
      });
      console.log(
        `Stored link token request context for requestId: ${requestId}`,
      );
    } catch (contextError) {
      console.error("Error storing request context:", contextError);
      // Continue with the API call even if context storage fails
    }

    // Calculate year of birth from date of birth
    const yearOfBirth = new Date(patient.dateOfBirth).getFullYear();

    // Prepare the payload
    // Map gender to ABDM format (M, F, O, D, T, U)
    // Handle both database format (male/female/other) and ABDM format (M/F/O)
    const genderMap: Record<string, string> = {
      male: "M",
      female: "F",
      other: "O",
      M: "M",
      F: "F",
      O: "O",
      // Add other mappings if needed
    };

    // const payload = {
    //   abhaNumber: patient.abhaProfile.abhaNumber.split("-").join(""),
    //   abhaAddress: patient.abhaProfile.abhaAddress,
    //   name: `${patient.firstName} ${patient.lastName}`,
    //   gender:
    //     genderMap[patient.gender] ||
    //     genderMap[patient.gender.toLowerCase()] ||
    //     "O", // Handle both formats
    //   yearOfBirth: yearOfBirth,
    // };
    // SRIDHAR: IMPORTNAT: Optional Abhanumber for non kyc cases
    const payload = {
      // Conditionally add abhaNumber using a spread operator
      ...(patient.abhaProfile?.abhaNumber && {
        abhaNumber: patient.abhaProfile.abhaNumber.split("-").join(""),
      }),
      abhaAddress: patient.abhaProfile?.abhaAddress, // Use optional chaining for abhaAddress too for safety
      name: `${patient.firstName} ${patient.lastName}`,
      gender:
        genderMap[patient.gender] ||
        genderMap[patient.gender.toLowerCase()] ||
        "O", // Handle both formats
      yearOfBirth: yearOfBirth,
    };

    try {
      // Make the API request - this is now asynchronous and will return a request ID
      const response = await abdmFetch(
        `${ABDM_BASE_URL}/hiecm/v3/token/generate-token`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "REQUEST-ID": requestId,
            TIMESTAMP: timestamp,
            "X-HIP-ID": hipId || "",
            "X-CM-ID": ABDM_CM_ID || "sbx",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify(payload),
          signal: AbortSignal.timeout(120000), // 120 seconds timeout
        },
      );
      // Check if we have a requestId in the response, which indicates the request was accepted
      if (response && response.requestId) {
        // The actual token will be sent to the webhook endpoint
        // For now, we'll check if there's an existing token in the database
        const existingToken = await db.abhaLinkToken.findFirst({
          where: {
            patientId: patient.id,
            branchId: branchId,
            status: "active",
          },
          orderBy: {
            updatedAt: "desc",
          },
        });

        if (existingToken?.linkToken) {
          // Return the existing token
          return {
            token: existingToken.linkToken,
            message: "Using existing link token while waiting for webhook",
            requestId: response.requestId,
            async: true,
          };
        }

        // If no existing token, we need to wait for the webhook
        // For now, we'll return a temporary response
        return {
          token: null,
          message:
            "Link token generation initiated. Token will be received via webhook.",
          requestId: response.requestId,
          async: true,
        };
      }

      // If no token in response, check if we have a response object with headers
      if (!response.token && response.headers) {
        const tokenHeader =
          response.headers.get("X-Token") || response.headers.get("x-token");
        if (tokenHeader) {
          response.token = tokenHeader;
        }
      }

      // If still no token, try to get it from the database
      if (!response.token) {
        const existingToken = await db.abhaLinkToken.findFirst({
          where: {
            patientId: patient.id,
            branchId: branchId,
            status: "active",
          },
          orderBy: {
            updatedAt: "desc",
          },
        });

        if (existingToken?.linkToken) {
          response.token = existingToken.linkToken;
        } else {
          // If we have a requestId but no token, we're waiting for the webhook
          if (response.requestId) {
            return {
              token: null,
              message:
                "Link token request has been initiated. The token will be received via webhook and stored in the database.",
              requestId: response.requestId,
              async: true,
            };
          }

          throw new Error(
            "Link token request has been initiated. The token will be received via webhook and stored in the database.",
          );
        }
      }

      // We no longer create or update token records here
      // This is now handled in the API endpoint

      return {
        token: response.token,
        message: response.token
          ? "Link token generated successfully"
          : "Link token request has been initiated. The token will be received via webhook and stored in the database.",
        requestId: response.requestId,
        async: !response.token,
      };
    } catch (error) {
      // Update request context status to failed
      try {
        await db.linkTokenRequest.update({
          where: {
            requestId,
          },
          data: {
            status: "failed",
            updatedAt: new Date(),
          },
        });
        console.log(
          `Updated request context status to failed for requestId: ${requestId}`,
        );
      } catch (updateError) {
        console.error("Error updating request context status:", updateError);
      }

      // Handle duplicate link token request error - but allow for different branches
      if (
        error instanceof Error &&
        (error.message.includes("Duplicate Link token request") ||
          error.message.includes("ABDM-1092"))
      ) {
        // Check if this is for a different branch/organization combination
        console.log(
          `Duplicate request detected for requestId: ${requestId}, but this might be for a different branch/organization`,
        );
        throw new Error(
          "Duplicate link token request detected for this ABHA address. If you're trying to generate a token for a different branch, please wait a few minutes and try again.",
        );
      }

      // Re-throw other errors
      throw error;
    }
  } catch (error) {
    throw error;
  }
}
