/**
 * Link care context to ABHA number
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Link care context to ABHA number
 * @param patientId - Patient ID
 * @param appointmentId - Appointment ID (will be used to find the consultation)
 * @param hiTypes - Health information types
 * @param branchId - Branch ID (optional, will be used to get the HIP ID)
 * @returns Link response
 */
/**
 * Valid ABDM hiTypes:
 * - Prescription
 * - DiagnosticReport
 * - OPConsultation
 * - DischargeSummary
 * - ImmunizationRecord
 * - HealthDocumentRecord
 * - WellnessRecord
 * - Invoice
 */
const VALID_HI_TYPES = [
  "Prescription",
  "DiagnosticReport",
  "OPConsultation",
  "DischargeSummary",
  "ImmunizationRecord",
  "HealthDocumentRecord",
  "WellnessRecord",
  "Invoice",
];

export async function linkCareContext(
  patientId: string,
  appointmentId: string,
  hiTypes: string[] = ["Prescription", "DiagnosticReport", "OPConsultation"],
  branchId?: string,
  consultationId?: string,
) {
  try {
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    // Either consultationId or appointmentId must be provided
    if (!consultationId && !appointmentId) {
      throw new Error("Either Consultation ID or Appointment ID is required");
    }

    // Validate hiTypes
    const invalidHiTypes = hiTypes.filter(
      (type) => !VALID_HI_TYPES.includes(type),
    );
    if (invalidHiTypes.length > 0) {
      throw new Error(
        `Invalid hiTypes: ${invalidHiTypes.join(
          ", ",
        )}. Valid types are: ${VALID_HI_TYPES.join(", ")}`,
      );
    }

    // Get patient details with ABHA profile
    const patient = await db.patient.findUnique({
      where: { id: patientId },
      include: { abhaProfile: true },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    //SRIDHAR: *IMPORTANT*: Ptient not having ABHA Number is a valid case for non KYC cases. In this we need Abha adress alone.
    //Here we are getting 'undefined' as string, should handle this case.

    // if (!patient.abhaProfile?.abhaNumber) {
    //   throw new Error("Patient does not have an ABHA number");
    // }
    if (patient.abhaProfile?.abhaNumber === "undefined") {
      patient.abhaProfile.abhaNumber = null;
    }
    if (!patient.abhaProfile?.abhaNumber && !patient.abhaProfile?.abhaAddress) {
      throw new Error(
        "Patient must have either an ABHA number or an ABHA address.",
      );
    }

    // Get the branch ID from the consultation or the provided branchId
    const branchIdToUse =
      branchId ||
      (consultationId
        ? (
            await db.consultation.findUnique({
              where: { id: consultationId },
              select: { branchId: true },
            })
          )?.branchId
        : null);

    if (!branchIdToUse) {
      throw new Error("Branch ID is required to link care contexts");
    }

    // Get the link token for this patient and branch
    const abhaLinkToken = await db.abhaLinkToken.findFirst({
      where: {
        patientId: patientId,
        branchId: branchIdToUse,
        status: "active",
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    if (!abhaLinkToken?.linkToken) {
      throw new Error(
        "Link token not found for this branch. Please generate a link token first.",
      );
    }

    // Store the link token for later use
    const linkToken = abhaLinkToken.linkToken;

    // Get or find consultation
    let consultation: any;

    if (consultationId) {
      // If consultationId is provided, use it directly
      consultation = await db.consultation.findUnique({
        where: { id: consultationId },
        include: {
          doctor: {
            include: {
              user: true,
            },
          },
          branch: true,
          appointment: true,
        },
      });

      if (!consultation) {
        throw new Error("Consultation not found");
      }

      // Check if a care context already exists for this consultation
      const existingCareContext = await db.careContext.findFirst({
        where: {
          consultationId: consultationId,
        },
      });

      if (existingCareContext) {
        throw new Error(
          "A care context already exists for this consultation. Each consultation can only have one care context.",
        );
      }
    }

    // If consultationId is not provided, use appointmentId to find or create a consultation
    if (!consultation && appointmentId) {
      // Get appointment details
      const appointment = await db.appointment.findUnique({
        where: { id: appointmentId },
        include: {
          doctor: {
            include: {
              user: true,
            },
          },
          branch: true, // Include branch to get HIP ID
          consultation: true, // Include consultation
        },
      });

      if (!appointment) {
        throw new Error("Appointment not found");
      }

      // Get or create consultation for this appointment
      consultation = appointment.consultation;

      if (!consultation) {
        // If no consultation exists, create one
        consultation = await db.consultation.create({
          data: {
            appointmentId: appointment.id,
            patientId: patientId,
            doctorId: appointment.doctorId,
            branchId: appointment.branchId,
            organizationId: appointment.organizationId,
            consultationDate: appointment.appointmentDate,
            startTime: appointment.startTime,
            endTime: appointment.endTime,
            status: "in-progress",
          },
        });
      }

      // Check if a care context already exists for this consultation
      const existingCareContext = await db.careContext.findFirst({
        where: {
          consultationId: consultation.id,
        },
      });

      if (existingCareContext) {
        throw new Error(
          "A care context already exists for this consultation. Each consultation can only have one care context.",
        );
      }
    }

    // IMPORTANT: We need to use the same HIP ID that was used to generate the link token
    // Extract the HIP ID from the link token
    let hipId = "";

    try {
      // Link tokens are JWT tokens with the HIP ID in the payload
      // The token is in the format: header.payload.signature
      const tokenParts = linkToken.split(".");
      if (tokenParts.length === 3) {
        // Decode the payload (second part)
        const payload = JSON.parse(
          Buffer.from(tokenParts[1], "base64").toString(),
        );
        console.log("Link token payload:", payload);

        // Extract the HIP ID from the payload
        if (payload.hipId) {
          hipId = payload.hipId;
          console.log("Using HIP ID from link token:", hipId);
        }
      }
    } catch (error) {
      console.error("Error extracting HIP ID from link token:", error);
    }

    // If we couldn't extract the HIP ID from the token, fall back to the branch's HIP ID
    if (!hipId) {
      console.log("Falling back to branch HIP ID");

      // If branchId is provided, use it to get the branch
      if (branchId) {
        const branch = await db.branch.findUnique({
          where: { id: branchId },
        });

        // Use type assertion to access hipId
        const branchWithHipId = branch as any;
        if (branchWithHipId?.hipId) {
          hipId = branchWithHipId.hipId;
          console.log("Using branch HIP ID:", hipId);
        } else {
          throw new Error(
            `Branch ${branchId} does not have a Facility ID (HIP ID). Please set a Facility ID for the branch before linking care contexts.`,
          );
        }
      }
      // Otherwise, use the branch from the consultation
      else {
        // Use type assertion to access hipId
        const branchWithHipId = consultation.branch as any;
        if (branchWithHipId?.hipId) {
          hipId = branchWithHipId.hipId;
          console.log("Using consultation branch HIP ID:", hipId);
        } else {
          throw new Error(
            `The consultation's branch does not have a Facility ID (HIP ID). Please set a Facility ID for the branch before linking care contexts.`,
          );
        }
      }
    }

    // Check if the branch is registered as a HIP
    if (!hipId) {
      throw new Error(
        "No Facility ID (HIP ID) available. Please set a Facility ID for the branch before linking care contexts.",
      );
    }

    // Create a display name for the care context
    const consultationDate = new Date(consultation.consultationDate)
      .toISOString()
      .split("T")[0];
    const doctorName = consultation.doctor.user.name || "Doctor";

    // Include the appointment time if available
    let timeInfo = "";
    if (consultation.startTime) {
      timeInfo = ` at ${consultation.startTime}`;
    }

    const display = `Consultation with Dr. ${doctorName} on ${consultationDate}${timeInfo}`;

    // Get access token
    const accessToken = await getAccessToken();

    // Generate request ID and timestamp
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the payload with support for multiple hiTypes
    // Create separate patient entries for each hiType
    const patientEntries = hiTypes.map((hiType) => ({
      referenceNumber: patient.id,
      display: `${patient.firstName} ${patient.lastName}`,
      careContexts: [
        {
          referenceNumber: consultation.id, // Use consultation ID as the reference number
          display: display,
        },
      ],
      hiType: hiType, // Each entry has a single hiType as a string (not an array)
      count: 1,
    }));

    const payload = {
      //SRIDHAR: Made Abhanumber optional for cases where patient does not have ABHA number.
      // abhaNumber: patient.abhaProfile.abhaNumber.split("-").join(""),
      ...(patient.abhaProfile?.abhaNumber && {
        abhaNumber: patient.abhaProfile.abhaNumber.split("-").join(""),
      }),
      abhaAddress: patient.abhaProfile.abhaAddress,
      patient: patientEntries,
    };

    console.log("Care context payload:", JSON.stringify(payload, null, 2));

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_BASE_URL}/hiecm/hip/v3/link/carecontext`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-HIP-ID": hipId || "",
          "X-LINK-TOKEN": linkToken,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Create a care context record in the database
    const careContext = await db.careContext.create({
      data: {
        patientId: patient.id,
        organizationId: patient.organizationId,
        consultationId: consultation.id, // Link to consultation
        display: display,
        hiTypes: hiTypes,
        requestId: requestId, // Add the requestId for tracking
        status: "Requested", // Set initial status as Requested
        additionalInfo: {
          doctorId: consultation.doctorId,
          branchId: consultation.branchId,
          appointmentId: consultation.appointmentId,
          linkResponse: response,
          linkedAt: new Date().toISOString(),
        },
      },
    });

    return {
      careContext,
      message: "Care context linked successfully",
    };
  } catch (error) {
    throw error;
  }
}
