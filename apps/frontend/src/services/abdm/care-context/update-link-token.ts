/**
 * Update link token in the database
 */

import { db } from "@/lib/db";
import { generateUUID } from "../utils/request";

/**
 * Update link token in the database
 * @param patientId - Patient ID
 * @param branchId - Branch ID
 * @returns Success status
 */
export async function updateLinkToken(patientId: string, branchId: string) {
  try {
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    if (!branchId) {
      throw new Error("Branch ID is required");
    }

    // Get patient details with ABHA profile
    const patient = await db.patient.findUnique({
      where: { id: patientId },
      include: { abhaProfile: true },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    if (!patient.abhaProfile?.abhaNumber) {
      throw new Error("Patient does not have an ABHA number");
    }

    // Generate a new token (as a fallback when ABDM API fails)
    const fallbackToken = generateUUID();

    // Get the HIP ID from the branch
    const branch = await db.branch.findUnique({
      where: { id: branchId },
    });

    if (!branch) {
      throw new Error(`Branch with ID ${branchId} not found.`);
    }

    if (!branch.hipId) {
      throw new Error(
        `Branch ${branchId} does not have a Facility ID (HIP ID). Please set a Facility ID for the branch before updating a link token.`,
      );
    }

    const hipId = branch.hipId;

    // Check if an AbhaLinkToken already exists for this patient and branchId
    const existingToken = await db.abhaLinkToken.findFirst({
      where: {
        patientId: patient.id,
        branchId: branchId,
      },
    });

    // Store the link token in the database using Prisma client
    try {
      let updatedToken;

      if (existingToken) {
        // Update existing token
        updatedToken = await db.abhaLinkToken.update({
          where: {
            id: existingToken.id,
          },
          data: {
            linkToken: fallbackToken,
            linkTokenExpiry: new Date(
              new Date().setMonth(new Date().getMonth() + 6),
            ),
            status: "active",
            updatedAt: new Date(),
          },
        });
      } else {
        // Create new token
        updatedToken = await db.abhaLinkToken.create({
          data: {
            patientId: patient.id,
            branchId: branchId,
            hipId: hipId,
            organizationId: patient.organizationId,
            linkToken: fallbackToken,
            linkTokenExpiry: new Date(
              new Date().setMonth(new Date().getMonth() + 6),
            ),
            status: "active",
          },
        });
      }

      if (!updatedToken.linkToken) {
        console.error("Fallback link token not saved to database!");
        throw new Error("Failed to save fallback link token to database");
      }

      return {
        token: fallbackToken,
        message: "Fallback link token generated and saved to database",
      };
    } catch (dbError) {
      console.error(
        "Error updating ABHA profile with fallback link token:",
        dbError,
      );
      throw new Error(
        `Failed to save fallback link token to database: ${
          dbError instanceof Error ? dbError.message : String(dbError)
        }`,
      );
    }
  } catch (error) {
    throw error;
  }
}
