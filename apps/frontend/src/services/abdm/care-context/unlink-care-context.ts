/**
 * Unlink care context from ABHA number
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";
const ABDM_HIP_ID = process.env.NEXT_PUBLIC_ABDM_HIP_ID;

/**
 * Unlink care context from ABHA number
 * @param patientId - Patient ID
 * @param careContextId - Care context ID
 * @returns Unlink response
 */
export async function unlinkCareContext(
  patientId: string,
  careContextId: string,
) {
  try {
    if (!patientId) {
      throw new Error("Patient ID is required");
    }

    if (!careContextId) {
      throw new Error("Care context ID is required");
    }

    // Get patient details with ABHA profile
    const patient = await db.patient.findUnique({
      where: { id: patientId },
      include: { abhaProfile: true },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    if (!patient.abhaProfile?.abhaNumber) {
      throw new Error("Patient does not have an ABHA number");
    }

    // Get care context details
    const careContext = await db.careContext.findUnique({
      where: { id: careContextId },
      include: {
        consultation: true,
      },
    });

    if (!careContext) {
      throw new Error("Care context not found");
    }

    // Check if the care context belongs to the patient
    if (careContext.patientId !== patientId) {
      throw new Error("Care context does not belong to this patient");
    }

    // Get the branch ID from the consultation
    const branchId = careContext.consultation.branchId;

    // Get the link token for this patient and branch
    const abhaLinkToken = await db.abhaLinkToken.findFirst({
      where: {
        patientId: patientId,
        branchId: branchId,
        status: "active",
        linkTokenExpiry: {
          gt: new Date(),
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    if (!abhaLinkToken?.linkToken) {
      throw new Error(
        "Link token not found for this branch. Please generate a link token first.",
      );
    }

    // Store the link token for later use
    const linkToken = abhaLinkToken.linkToken;

    // Get access token
    const accessToken = await getAccessToken();

    // Generate request ID and timestamp
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the payload
    const payload = {
      abhaNumber: patient.abhaProfile.abhaNumber,
      abhaAddress: patient.abhaProfile.abhaAddress,
      patient: [
        {
          referenceNumber: patient.id,
          display: `${patient.firstName} ${patient.lastName}`,
          careContexts: [
            {
              referenceNumber: careContext.consultationId, // Use consultationId as the reference number
              display: careContext.display,
            },
          ],
        },
      ],
    };

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_BASE_URL}/hiecm/hip/v3/unlink/carecontext`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-HIP-ID": ABDM_HIP_ID || "",
          "X-LINK-TOKEN": linkToken,
          "X-CM-ID": ABDM_CM_ID || "sbx",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // Update the care context record in the database
    await db.careContext.update({
      where: { id: careContext.id },
      data: {
        status: "unlinked", // Update status to unlinked
        additionalInfo: careContext.additionalInfo
          ? {
              ...(careContext.additionalInfo as object),
              unlinkResponse: response,
              unlinkedAt: new Date().toISOString(),
            }
          : { unlinkResponse: response, unlinkedAt: new Date().toISOString() },
      },
    });

    return {
      message: "Care context unlinked successfully",
    };
  } catch (error) {
    throw error;
  }
}
