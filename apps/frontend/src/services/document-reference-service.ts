import { db } from "@/lib/db";
import { createDocumentReferenceResource } from "@/lib/fhir/resources";
import { FhirDocumentReference } from "@/lib/fhir/types";
import { DocumentReference } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";

/**
 * Create a new document reference record
 */
export async function createDocumentReference(data: {
  patientId: string;
  doctorId: string;
  organizationId: string;
  consultationId?: string;
  status: string;
  docStatus: string;
  type: string;
  typeDisplay: string;
  category: string;
  categoryDisplay: string;
  subject: string;
  date: Date;
  author?: string;
  authenticator?: string;
  custodian?: string;
  description?: string;
  securityLabel?: string;
  content: any;
  context?: any;
}): Promise<DocumentReference> {
  return db.documentReference.create({
    data: {
      patientId: data.patientId,
      doctorId: data.doctorId,
      organizationId: data.organizationId,
      consultationId: data.consultationId,
      status: data.status,
      docStatus: data.docStatus,
      type: data.type,
      typeDisplay: data.typeDisplay,
      category: data.category,
      categoryDisplay: data.categoryDisplay,
      subject: data.subject,
      date: data.date,
      author: data.author,
      authenticator: data.authenticator,
      custodian: data.custodian,
      description: data.description,
      securityLabel: data.securityLabel,
      content: data.content,
      context: data.context,
    },
  });
}

/**
 * Get a document reference record by ID
 */
export async function getDocumentReferenceById(
  id: string,
): Promise<DocumentReference | null> {
  return db.documentReference.findUnique({
    where: { id },
  });
}

/**
 * Get document reference records for a patient
 */
export async function getDocumentReferencesByPatient(
  patientId: string,
): Promise<DocumentReference[]> {
  return db.documentReference.findMany({
    where: { patientId },
    orderBy: { date: "desc" },
  });
}

/**
 * Get document reference records for a consultation
 */
export async function getDocumentReferencesByConsultation(
  consultationId: string,
): Promise<DocumentReference[]> {
  return db.documentReference.findMany({
    where: { consultationId },
    orderBy: { date: "desc" },
  });
}

/**
 * Get document reference records by category
 */
export async function getDocumentReferencesByCategory(
  patientId: string,
  category: string,
): Promise<DocumentReference[]> {
  return db.documentReference.findMany({
    where: {
      patientId,
      category,
    },
    orderBy: { date: "desc" },
  });
}

/**
 * Update a document reference record
 */
export async function updateDocumentReference(
  id: string,
  data: Partial<DocumentReference>,
): Promise<DocumentReference> {
  // Get the current record first
  const currentRecord = await db.documentReference.findUnique({
    where: { id },
  });

  if (!currentRecord) {
    throw new Error(`Document reference with ID ${id} not found`);
  }

  // Only update specific fields that are allowed to be updated
  return db.documentReference.update({
    where: { id },
    data: {
      status: data.status,
      docStatus: data.docStatus,
      type: data.type,
      typeDisplay: data.typeDisplay,
      category: data.category,
      categoryDisplay: data.categoryDisplay,
      subject: data.subject,
      date: data.date,
      author: data.author,
      authenticator: data.authenticator,
      custodian: data.custodian,
      description: data.description,
      securityLabel: data.securityLabel,
      // Handle JSON fields carefully
      ...(data.content !== undefined ? { content: data.content as any } : {}),
      ...(data.context !== undefined ? { context: data.context as any } : {}),
    },
  });
}

/**
 * Delete a document reference record
 */
export async function deleteDocumentReference(
  id: string,
): Promise<DocumentReference> {
  return db.documentReference.delete({
    where: { id },
  });
}

/**
 * Convert a document reference record to a FHIR resource
 */
export async function convertToFhirResource(
  documentReferenceId: string,
): Promise<FhirDocumentReference> {
  const documentReference = await getDocumentReferenceById(documentReferenceId);

  if (!documentReference) {
    throw new Error(
      `Document reference with ID ${documentReferenceId} not found`,
    );
  }

  return createDocumentReferenceResource(
    documentReference,
    documentReference.patientId,
    documentReference.doctorId,
  );
}

/**
 * Store a document reference record as a FHIR resource
 */
export async function storeAsFhirResource(
  documentReferenceId: string,
): Promise<string> {
  const documentReference = await getDocumentReferenceById(documentReferenceId);

  if (!documentReference) {
    throw new Error(
      `Document reference with ID ${documentReferenceId} not found`,
    );
  }

  const fhirResource = createDocumentReferenceResource(
    documentReference,
    documentReference.patientId,
    documentReference.doctorId,
  );

  // First create a FHIR bundle to contain the resource
  const bundleId = `bundle-${uuidv4()}`;
  const bundle = {
    resourceType: "Bundle",
    id: bundleId,
    type: "collection",
    entry: [
      {
        resource: fhirResource,
      },
    ],
  };

  // Store the FHIR bundle
  await db.fhirBundle.create({
    data: {
      bundleId,
      bundleType: "collection",
      bundleJson: bundle as any,
      patientId: documentReference.patientId,
      organizationId: documentReference.organizationId,
      status: "completed",
    },
  });

  // Store the FHIR resource
  const storedResource = await db.fhirResource.create({
    data: {
      resourceType: "DocumentReference",
      resourceId: fhirResource.id,
      patientId: documentReference.patientId,
      organizationId: documentReference.organizationId,
      fhirJson: fhirResource as any,
      sourceType: "documentReference",
      sourceId: documentReference.id,
      version: "4.0.1",
      bundleId, // Link to the bundle
    },
  });

  return storedResource.id;
}
