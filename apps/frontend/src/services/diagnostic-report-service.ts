import { db } from "@/lib/db";
import { createDiagnosticReportResource } from "@/lib/fhir/resources";
import { FhirDiagnosticReport } from "@/lib/fhir/types";
import { DiagnosticReport } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";

/**
 * Create a new diagnostic report
 */
export async function createDiagnosticReport(data: {
  consultationId: string;
  patientId: string;
  doctorId: string;
  organizationId: string;
  reportType: string;
  reportDate: Date;
  status: string;
  category?: string;
  code: string;
  codeDisplay: string;
  conclusion?: string;
  presentedForm?: any;
  result?: any;
  performer?: string;
  specimen?: string;
  effectiveDate: Date;
  issuedDate: Date;
}): Promise<DiagnosticReport> {
  return db.diagnosticReport.create({
    data: {
      consultationId: data.consultationId,
      patientId: data.patientId,
      doctorId: data.doctorId,
      organizationId: data.organizationId,
      reportType: data.reportType,
      reportDate: data.reportDate,
      status: data.status,
      category: data.category,
      code: data.code,
      codeDisplay: data.codeDisplay,
      conclusion: data.conclusion,
      presentedForm: data.presentedForm,
      result: data.result,
      performer: data.performer,
      specimen: data.specimen,
      effectiveDate: data.effectiveDate,
      issuedDate: data.issuedDate,
    },
  });
}

/**
 * Get a diagnostic report by ID
 */
export async function getDiagnosticReportById(
  id: string,
): Promise<DiagnosticReport | null> {
  return db.diagnosticReport.findUnique({
    where: { id },
  });
}

/**
 * Get diagnostic reports for a patient
 */
export async function getDiagnosticReportsByPatient(
  patientId: string,
): Promise<DiagnosticReport[]> {
  return db.diagnosticReport.findMany({
    where: { patientId },
    orderBy: { reportDate: "desc" },
  });
}

/**
 * Get diagnostic reports for a consultation
 */
export async function getDiagnosticReportsByConsultation(
  consultationId: string,
): Promise<DiagnosticReport[]> {
  return db.diagnosticReport.findMany({
    where: { consultationId },
    orderBy: { reportDate: "desc" },
  });
}

/**
 * Update a diagnostic report
 */
export async function updateDiagnosticReport(
  id: string,
  data: Partial<DiagnosticReport>,
): Promise<DiagnosticReport> {
  // Get the current record first
  const currentRecord = await db.diagnosticReport.findUnique({
    where: { id },
  });

  if (!currentRecord) {
    throw new Error(`Diagnostic report with ID ${id} not found`);
  }

  // Only update specific fields that are allowed to be updated
  return db.diagnosticReport.update({
    where: { id },
    data: {
      reportType: data.reportType,
      reportDate: data.reportDate,
      status: data.status,
      category: data.category,
      code: data.code,
      codeDisplay: data.codeDisplay,
      effectiveDate: data.effectiveDate,
      issuedDate: data.issuedDate,
      conclusion: data.conclusion,
      // Handle JSON fields carefully
      ...(data.result !== undefined ? { result: data.result as any } : {}),
      ...(data.specimen !== undefined
        ? { specimen: data.specimen as any }
        : {}),
      ...(data.presentedForm !== undefined
        ? { presentedForm: data.presentedForm as any }
        : {}),
    },
  });
}

/**
 * Delete a diagnostic report
 */
export async function deleteDiagnosticReport(
  id: string,
): Promise<DiagnosticReport> {
  return db.diagnosticReport.delete({
    where: { id },
  });
}

/**
 * Convert a diagnostic report to a FHIR resource
 */
export async function convertToFhirResource(
  reportId: string,
): Promise<FhirDiagnosticReport> {
  const report = await getDiagnosticReportById(reportId);

  if (!report) {
    throw new Error(`Diagnostic report with ID ${reportId} not found`);
  }

  return createDiagnosticReportResource(
    report,
    report.patientId,
    report.doctorId,
  );
}

/**
 * Store a diagnostic report as a FHIR resource
 */
export async function storeAsFhirResource(reportId: string): Promise<string> {
  const report = await getDiagnosticReportById(reportId);

  if (!report) {
    throw new Error(`Diagnostic report with ID ${reportId} not found`);
  }

  const fhirResource = createDiagnosticReportResource(
    report,
    report.patientId,
    report.doctorId,
  );

  // First create a FHIR bundle to contain the resource
  const bundleId = `bundle-${uuidv4()}`;
  const bundle = {
    resourceType: "Bundle",
    id: bundleId,
    type: "collection",
    entry: [
      {
        resource: fhirResource,
      },
    ],
  };

  // Store the FHIR bundle
  await db.fhirBundle.create({
    data: {
      bundleId,
      bundleType: "collection",
      bundleJson: bundle as any,
      patientId: report.patientId,
      organizationId: report.organizationId,
      status: "completed",
    },
  });

  // Store the FHIR resource
  const storedResource = await db.fhirResource.create({
    data: {
      resourceType: "DiagnosticReport",
      resourceId: fhirResource.id,
      patientId: report.patientId,
      organizationId: report.organizationId,
      fhirJson: fhirResource as any,
      sourceType: "diagnosticReport",
      sourceId: report.id,
      version: "4.0.1",
      bundleId, // Link to the bundle
    },
  });

  return storedResource.id;
}
