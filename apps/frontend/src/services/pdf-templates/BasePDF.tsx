import React from "react";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
} from "@react-pdf/renderer";
import { format } from "date-fns";
import {
  ConsultationData,
  PdfGenerationOptions,
} from "../pdf-generation/pdf-generator";

// import signatureImage from "../../assets/signature/sign.jpg"; // Adjust the path as needed

const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 30,
    fontSize: 10,
    fontFamily: "Helvetica",
  },
  header: {
    marginBottom: 20,
    borderBottomWidth: 2,
    borderBottomColor: "#2563eb",
    borderBottomStyle: "solid",
    paddingBottom: 10,
  },
  hospitalName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1e40af",
    marginBottom: 5,
    textAlign: "center",
  },
  hospitalInfo: {
    fontSize: 9,
    color: "#6b7280",
    textAlign: "center",
    marginBottom: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1f2937",
    textAlign: "center",
    marginTop: 10,
  },
  patientInfo: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: "#f8fafc",
    borderRadius: 5,
  },
  patientInfoTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#1e40af",
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
    borderBottomStyle: "solid",
    paddingBottom: 5,
  },
  infoRow: {
    flexDirection: "row",
    marginBottom: 5,
  },
  infoLabel: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#374151",
    width: 80,
  },
  infoValue: {
    fontSize: 9,
    color: "#6b7280",
    flex: 1,
  },
  consultationInfo: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: "#f0f9ff",
    borderRadius: 5,
  },
  consultationInfoTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#0369a1",
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#bae6fd",
    borderBottomStyle: "solid",
    paddingBottom: 5,
  },
  content: {
    flex: 1,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 10,
    marginTop: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#d1d5db",
    borderBottomStyle: "solid",
    paddingBottom: 5,
  },
  signature: {
    width: 120,
    height: 50,
    alignSelf: "flex-end",
    marginTop: 20,
    marginBottom: 5,
  },
  doctorName: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#1e293b",
    textAlign: "right",
    marginRight: 5,
  },
  footer: {
    marginTop: "auto",
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: "#e5e7eb",
    borderTopStyle: "solid",
    fontSize: 8,
    color: "#6b7280",
    textAlign: "center",
  },
  generatedInfo: {
    fontSize: 8,
    color: "#9ca3af",
    textAlign: "right",
    marginTop: 10,
  },
});

interface BasePDFProps {
  consultationData: ConsultationData;
  title: string;
  children: React.ReactNode;
  options?: PdfGenerationOptions;
}

export const BasePDF: React.FC<BasePDFProps> = ({
  consultationData,
  title,
  children,
  options = {},
}) => {
  const {
    includeHeader = true,
    includeFooter = true,
    pageSize = "A4".toUpperCase(),
    orientation = "portrait",
  } = options;

  const { patient, doctor, branch, organization, consultationDate } = consultationData;

  return (
    <Document>
      <Page
        size={pageSize as "A4" | "LETTER"}
        orientation={orientation}
        style={styles.page}
      >
        {includeHeader && (
          <View style={styles.header}>
            <Text style={styles.hospitalName}>
              {organization?.name || branch?.name || "Healthcare Facility"}
            </Text>
            {organization?.address && (
              <Text style={styles.hospitalInfo}>{organization.address}</Text>
            )}
            {(organization?.phone || organization?.email) && (
              <Text style={styles.hospitalInfo}>
                {organization?.phone && `Phone: ${organization.phone}`}
                {organization?.phone && organization?.email && " | "}
                {organization?.email && `Email: ${organization.email}`}
              </Text>
            )}
            <Text style={styles.title}>{title}</Text>
          </View>
        )}

        <View style={styles.patientInfo}>
          <Text style={styles.patientInfoTitle}>Patient Information</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Name:</Text>
            <Text style={styles.infoValue}>
              {patient.firstName} {patient.lastName}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Gender:</Text>
            <Text style={styles.infoValue}>{patient.gender}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Date of Birth:</Text>
            <Text style={styles.infoValue}>
              {format(new Date(patient.dateOfBirth), "PPP")}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Phone:</Text>
            <Text style={styles.infoValue}>{patient.phone}</Text>
          </View>
          {patient.email && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Email:</Text>
              <Text style={styles.infoValue}>{patient.email}</Text>
            </View>
          )}
          {patient.bloodGroup && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Blood Group:</Text>
              <Text style={styles.infoValue}>{patient.bloodGroup}</Text>
            </View>
          )}
          {(patient.address || patient.city || patient.state || patient.pincode) && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Address:</Text>
              <Text style={styles.infoValue}>
                {[patient.address, patient.city, patient.state, patient.pincode]
                  .filter(Boolean)
                  .join(", ")}
              </Text>
            </View>
          )}
          {patient.abhaProfile?.abhaNumber && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>ABHA Number:</Text>
              <Text style={styles.infoValue}>
                {patient.abhaProfile.abhaNumber.replace(/(\d{4})(\d{4})(\d{4})/, "XXXX XXXX $3")}
              </Text>
            </View>
          )}
          {patient.abhaProfile?.abhaAddress && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>ABHA Address:</Text>
              <Text style={styles.infoValue}>{patient.abhaProfile.abhaAddress}</Text>
            </View>
          )}
          {patient.abhaProfile?.healthIdNumber && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Health ID:</Text>
              <Text style={styles.infoValue}>{patient.abhaProfile.healthIdNumber}</Text>
            </View>
          )}
        </View>

        <View style={styles.consultationInfo}>
          <Text style={styles.consultationInfoTitle}>Consultation Details</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Date:</Text>
            <Text style={styles.infoValue}>
              {format(new Date(consultationDate), "PPP")}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Doctor:</Text>
            <Text style={styles.infoValue}>{doctor.user.name || "N/A"}</Text>
          </View>
          {doctor.specialization && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Specialization:</Text>
              <Text style={styles.infoValue}>{doctor.specialization}</Text>
            </View>
          )}
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Branch:</Text>
            <Text style={styles.infoValue}>{branch.name}</Text>
          </View>
          {consultationData.startTime && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Time:</Text>
              <Text style={styles.infoValue}>
                {consultationData.startTime} -{" "}
                {consultationData.endTime || "Ongoing"}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.content}>{children}</View>

        {/* Signature Image */}
        <Image
          style={styles.signature}
          src={`${process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"}/signature/sign.jpg`}
        ></Image>
        <Text style={styles.doctorName}>{doctor.user.name}</Text>

        {includeFooter && (
          <View style={styles.footer}>
            <Text>
              This document was generated electronically and is valid without
              signature.
            </Text>
            <Text style={styles.generatedInfo}>
              Generated on {format(new Date(), "PPP p")}
            </Text>
          </View>
        )}
      </Page>
    </Document>
  );
};

export { styles };
