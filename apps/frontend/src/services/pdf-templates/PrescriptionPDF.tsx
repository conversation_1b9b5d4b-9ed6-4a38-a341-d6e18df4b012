import React from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";
import { format } from "date-fns";
import { BasePDF } from "./BasePDF";
import {
  ConsultationData,
  PdfGenerationOptions,
} from "../pdf-generation/pdf-generator";

const styles = StyleSheet.create({
  section: { marginBottom: 20 },
  heading: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    paddingBottom: 4,
  },
  table: {
    width: "100%",
    borderWidth: 1,
    borderColor: "#000",
    borderStyle: "solid",
    marginBottom: 10,
  },
  tableRow: {
    flexDirection: "row",
  },
  tableColHeader: {
    width: "25%",
    borderStyle: "solid",
    borderBottomWidth: 1,
    borderRightWidth: 1,
    backgroundColor: "#f0f0f0",
    padding: 4,
    fontWeight: "bold",
    fontSize: 10,
  },
  tableCol: {
    width: "25%",
    borderStyle: "solid",
    borderBottomWidth: 1,
    borderRightWidth: 1,
    padding: 4,
    fontSize: 10,
  },
  notes: {
    fontSize: 10,
    marginTop: 10,
    lineHeight: 1.5,
  },
});

interface PrescriptionPDFProps {
  consultationData: ConsultationData;
  options?: PdfGenerationOptions;
}

export const PrescriptionPDF: React.FC<PrescriptionPDFProps> = ({
  consultationData,
  options,
}) => {
  const { prescriptions, patient } = consultationData;
  const prescription = prescriptions[0];
  type PrescriptionItem = ConsultationData["prescriptions"][0]["items"][0];

  return (
    <BasePDF
      consultationData={consultationData}
      title="Prescription"
      options={options}
    >
      <View style={styles.section}>
        <Text style={styles.notes}>Patient Details : </Text>
        <Text style={styles.heading}>
          {patient.firstName} {patient.lastName} | Gender: {patient.gender} |
          DOB: {format(new Date(patient.dateOfBirth), "PPP")} | Phone:{" "}
          {patient.phone}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.heading}>Medications</Text>
        <View style={styles.table}>
          <View style={styles.tableRow}>
            <Text style={styles.tableColHeader}>Name</Text>
            <Text style={styles.tableColHeader}>Dosage</Text>
            <Text style={styles.tableColHeader}>Frequency</Text>
            <Text style={styles.tableColHeader}>Duration</Text>
          </View>
          {prescription?.items?.map((item: PrescriptionItem, idx: number) => (
            <View key={idx} style={styles.tableRow}>
              <Text style={styles.tableCol}>{item.medicationName}</Text>
              <Text style={styles.tableCol}>{item.dosage || "-"}</Text>
              <Text style={styles.tableCol}>{item.frequency || "-"}</Text>
              <Text style={styles.tableCol}>{item.duration || "-"}</Text>
            </View>
          ))}
        </View>
      </View>

      {prescription?.instructions && (
        <View style={styles.section}>
          <Text style={styles.heading}>General Instructions</Text>
          <Text style={styles.notes}>{prescription.instructions}</Text>
        </View>
      )}
    </BasePDF>
  );
};
