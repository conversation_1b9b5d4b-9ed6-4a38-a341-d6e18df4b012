import React from "react";
import { Document, Page, Text, View, StyleSheet } from "@react-pdf/renderer";
import { format } from "date-fns";
import {
  ConsultationData,
  PdfGenerationOptions,
} from "../pdf-generation/pdf-generator";

const styles = StyleSheet.create({
  // Professional Header Styles
  letterhead: {
    borderBottom: 3,
    borderBottomColor: "#1e40af",
    borderBottomStyle: "solid",
    paddingBottom: 15,
    marginBottom: 20,
  },
  clinicName: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#1e40af",
    textAlign: "center",
    marginBottom: 5,
  },
  clinicDetails: {
    fontSize: 10,
    color: "#374151",
    textAlign: "center",
    marginBottom: 3,
  },
  registrationInfo: {
    fontSize: 8,
    color: "#6b7280",
    textAlign: "center",
    fontStyle: "italic",
  },

  // Doctor Information
  doctorHeader: {
    backgroundColor: "#f8fafc",
    padding: 12,
    marginBottom: 15,
    borderLeft: 4,
    borderLeftColor: "#1e40af",
    borderLeftStyle: "solid",
  },
  doctorName: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 3,
  },
  doctorCredentials: {
    fontSize: 10,
    color: "#374151",
    marginBottom: 2,
  },

  // Patient Information Box
  patientInfoBox: {
    backgroundColor: "#fafafa",
    border: 1,
    borderColor: "#d1d5db",
    borderStyle: "solid",
    padding: 12,
    marginBottom: 20,
  },
  patientInfoHeader: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 8,
    borderBottom: 1,
    borderBottomColor: "#d1d5db",
    borderBottomStyle: "solid",
    paddingBottom: 4,
  },
  patientInfoRow: {
    flexDirection: "row",
    marginBottom: 4,
  },
  patientLabel: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#374151",
    width: 80,
  },
  patientValue: {
    fontSize: 9,
    color: "#1f2937",
    flex: 1,
  },

  // Vital Signs Section
  vitalsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#1e40af",
    backgroundColor: "#eff6ff",
    padding: 6,
    marginBottom: 10,
    textAlign: "center",
    border: 1,
    borderColor: "#bfdbfe",
    borderStyle: "solid",
  },
  vitalsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    backgroundColor: "#fef2f2",
    padding: 10,
    border: 1,
    borderColor: "#fecaca",
    borderStyle: "solid",
  },
  vitalItem: {
    backgroundColor: "#ffffff",
    padding: 6,
    borderRadius: 3,
    minWidth: 70,
    textAlign: "center",
    border: 1,
    borderColor: "#e5e7eb",
    borderStyle: "solid",
  },
  vitalLabel: {
    fontSize: 7,
    color: "#6b7280",
    fontWeight: "bold",
    marginBottom: 2,
  },
  vitalValue: {
    fontSize: 10,
    color: "#1f2937",
    fontWeight: "bold",
  },

  // Professional Prescription Section
  prescriptionSection: {
    marginBottom: 20,
    minHeight: 300,
  },
  rxSymbol: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#dc2626",
    marginBottom: 10,
  },
  prescriptionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 15,
    borderBottom: 2,
    borderBottomColor: "#dc2626",
    borderBottomStyle: "solid",
    paddingBottom: 8,
  },
  prescriptionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#dc2626",
    marginLeft: 10,
  },
  medicationItem: {
    marginBottom: 15,
    padding: 12,
    backgroundColor: "#f9fafb",
    border: 1,
    borderColor: "#d1d5db",
    borderStyle: "solid",
    borderRadius: 4,
  },
  medicationNumber: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#dc2626",
    marginBottom: 5,
  },
  medicationName: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 6,
    textTransform: "uppercase",
  },
  dosageInfo: {
    backgroundColor: "#ffffff",
    padding: 8,
    border: 1,
    borderColor: "#e5e7eb",
    borderStyle: "solid",
    borderRadius: 3,
    marginBottom: 6,
  },
  dosageRow: {
    flexDirection: "row",
    marginBottom: 3,
  },
  dosageLabel: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#374151",
    width: 60,
  },
  dosageValue: {
    fontSize: 9,
    color: "#1f2937",
    flex: 1,
  },
  instructions: {
    fontSize: 9,
    color: "#374151",
    fontStyle: "italic",
    lineHeight: 1.3,
    backgroundColor: "#fffbeb",
    padding: 6,
    border: 1,
    borderColor: "#fde68a",
    borderStyle: "solid",
    borderRadius: 3,
  },

  // Clinical Notes Section
  clinicalSection: {
    marginBottom: 20,
  },
  clinicalNote: {
    backgroundColor: "#f0f9ff",
    padding: 12,
    marginBottom: 10,
    border: 1,
    borderColor: "#bae6fd",
    borderStyle: "solid",
    borderRadius: 4,
  },
  noteHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 6,
    borderBottom: 1,
    borderBottomColor: "#bae6fd",
    borderBottomStyle: "solid",
    paddingBottom: 4,
  },
  noteType: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#0369a1",
  },
  noteDate: {
    fontSize: 8,
    color: "#6b7280",
  },
  noteContent: {
    fontSize: 10,
    color: "#1f2937",
    lineHeight: 1.4,
  },
  diagnosisBox: {
    backgroundColor: "#fef3c7",
    padding: 8,
    marginTop: 8,
    border: 1,
    borderColor: "#fcd34d",
    borderStyle: "solid",
    borderRadius: 3,
  },
  diagnosisLabel: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#92400e",
    marginBottom: 4,
  },
  diagnosisText: {
    fontSize: 9,
    color: "#78350f",
    lineHeight: 1.3,
  },

  // Professional Footer
  prescriptionFooter: {
    marginTop: 30,
    borderTop: 1,
    borderTopColor: "#d1d5db",
    borderTopStyle: "solid",
    paddingTop: 15,
  },
  signatureSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    marginBottom: 15,
  },
  signatureBox: {
    width: 200,
    height: 50,
    border: 1,
    borderColor: "#9ca3af",
    borderStyle: "solid",
    padding: 5,
  },
  signatureLabel: {
    fontSize: 8,
    color: "#6b7280",
    marginBottom: 5,
  },
  dateBox: {
    width: 100,
    textAlign: "center",
  },

  // Legal and Safety Information
  legalInfo: {
    backgroundColor: "#fef7f7",
    padding: 10,
    border: 1,
    borderColor: "#fecaca",
    borderStyle: "solid",
    borderRadius: 4,
    marginTop: 15,
  },
  legalText: {
    fontSize: 7,
    color: "#7f1d1d",
    lineHeight: 1.2,
    textAlign: "center",
  },

  // Page Layout
  page: {
    padding: 30,
    fontSize: 10,
    fontFamily: "Helvetica",
    backgroundColor: "#ffffff",
  },

  // Watermark/Security
  prescriptionId: {
    position: "absolute",
    top: 10,
    right: 30,
    fontSize: 8,
    color: "#9ca3af",
    backgroundColor: "#f9fafb",
    padding: 4,
    border: 1,
    borderColor: "#d1d5db",
    borderStyle: "solid",
  },
});

interface CombinedPDFProps {
  consultationData: ConsultationData;
  options?: PdfGenerationOptions;
}

export const CombinedPDF: React.FC<CombinedPDFProps> = ({
  consultationData,
}) => {
  const {
    vitals,
    prescriptions,
    clinicalNotes,
    patient,
    doctor,
    consultationDate,
  } = consultationData;

  const hasVitals = vitals && vitals.length > 0;
  const hasPrescriptions = prescriptions && prescriptions.length > 0;
  const hasClinicalNotes = clinicalNotes && clinicalNotes.length > 0;

  const generatePrescriptionId = () => {
    const date = format(new Date(), "yyyyMMdd");
    const random = Math.random().toString(36).substr(2, 6).toUpperCase();
    return `RX-${date}-${random}`;
  };

  const renderProfessionalHeader = () => (
    <View style={styles.letterhead}>
      <Text style={styles.clinicName}>MEDICAL CENTER</Text>
      <Text style={styles.clinicDetails}>
        123 Healthcare Avenue, Medical District
      </Text>
      <Text style={styles.clinicDetails}>
        Phone: (************* | Fax: (*************
      </Text>
      <Text style={styles.clinicDetails}>Email: <EMAIL></Text>
      <Text style={styles.registrationInfo}>
        Licensed Healthcare Facility | DEA Registration: ********* | NPI:
        **********
      </Text>
    </View>
  );

  const renderDoctorInfo = () => (
    <View style={styles.doctorHeader}>
      <Text style={styles.doctorName}>
        Dr. {doctor.user.name || "Medical Professional"}
      </Text>
      <Text style={styles.doctorCredentials}>
        MD, Board Certified Internal Medicine
      </Text>
      <Text style={styles.doctorCredentials}>
        License #: MD12345 | DEA #: *********
      </Text>
      <Text style={styles.doctorCredentials}>NPI: **********</Text>
    </View>
  );

  const renderPatientInfo = () => (
    <View style={styles.patientInfoBox}>
      <Text style={styles.patientInfoHeader}>PATIENT INFORMATION</Text>
      <View style={styles.patientInfoRow}>
        <Text style={styles.patientLabel}>Name:</Text>
        <Text style={styles.patientValue}>
          {patient.firstName} {patient.lastName}
        </Text>
      </View>
      <View style={styles.patientInfoRow}>
        <Text style={styles.patientLabel}>DOB:</Text>
        <Text style={styles.patientValue}>
          {patient.dateOfBirth
            ? format(new Date(patient.dateOfBirth), "MM/dd/yyyy")
            : "Not provided"}
        </Text>
      </View>
      <View style={styles.patientInfoRow}>
        <Text style={styles.patientLabel}>Phone:</Text>
        <Text style={styles.patientValue}>
          {patient.phone || "Not provided"}
        </Text>
      </View>
      <View style={styles.patientInfoRow}>
        <Text style={styles.patientLabel}>Address:</Text>
      </View>
      <View style={styles.patientInfoRow}>
        <Text style={styles.patientLabel}>Date:</Text>
        <Text style={styles.patientValue}>
          {format(new Date(consultationDate), "MM/dd/yyyy")}
        </Text>
      </View>
    </View>
  );

  const renderVitalSigns = () => {
    if (!hasVitals) return null;

    const latestVital = vitals[0]; // Most recent vital signs

    return (
      <View style={styles.vitalsSection}>
        <Text style={styles.sectionTitle}>VITAL SIGNS</Text>
        <View style={styles.vitalsGrid}>
          {latestVital.bloodPressureSystolic && (
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>BLOOD PRESSURE</Text>
              <Text style={styles.vitalValue}>
                {latestVital.bloodPressureSystolic}/
                {latestVital.bloodPressureDiastolic}
              </Text>
            </View>
          )}
          {latestVital.pulse && (
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>PULSE</Text>
              <Text style={styles.vitalValue}>{latestVital.pulse} bpm</Text>
            </View>
          )}
          {latestVital.temperature && (
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>TEMPERATURE</Text>
              <Text style={styles.vitalValue}>{latestVital.temperature}°F</Text>
            </View>
          )}
          {latestVital.oxygenSaturation && (
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>O2 SAT</Text>
              <Text style={styles.vitalValue}>
                {latestVital.oxygenSaturation}%
              </Text>
            </View>
          )}
          {latestVital.weight && (
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>WEIGHT</Text>
              <Text style={styles.vitalValue}>{latestVital.weight} kg</Text>
            </View>
          )}
          {latestVital.height && (
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>HEIGHT</Text>
              <Text style={styles.vitalValue}>{latestVital.height} cm</Text>
            </View>
          )}
          {latestVital.bmi && (
            <View style={styles.vitalItem}>
              <Text style={styles.vitalLabel}>BMI</Text>
              <Text style={styles.vitalValue}>{latestVital.bmi}</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderPrescription = () => {
    if (!hasPrescriptions) return null;

    return (
      <View style={styles.prescriptionSection}>
        <View style={styles.prescriptionHeader}>
          <Text style={styles.rxSymbol}>℞</Text>
          <Text style={styles.prescriptionTitle}>PRESCRIPTION</Text>
        </View>

        {prescriptions.map((prescription, prescriptionIndex) =>
          prescription.items?.map((item: any, itemIndex: number) => (
            <View
              key={`${prescriptionIndex}-${itemIndex}`}
              style={styles.medicationItem}
            >
              <Text style={styles.medicationNumber}>{itemIndex + 1}.</Text>
              <Text style={styles.medicationName}>{item.medicationName}</Text>

              <View style={styles.dosageInfo}>
                <View style={styles.dosageRow}>
                  <Text style={styles.dosageLabel}>Strength:</Text>
                  <Text style={styles.dosageValue}>{item.dosage}</Text>
                </View>
                <View style={styles.dosageRow}>
                  <Text style={styles.dosageLabel}>Frequency:</Text>
                  <Text style={styles.dosageValue}>{item.frequency}</Text>
                </View>
                <View style={styles.dosageRow}>
                  <Text style={styles.dosageLabel}>Duration:</Text>
                  <Text style={styles.dosageValue}>{item.duration}</Text>
                </View>
                <View style={styles.dosageRow}>
                  <Text style={styles.dosageLabel}>Quantity:</Text>
                  <Text style={styles.dosageValue}>
                    {item.quantity || "As prescribed"} {item.unit || ""}
                  </Text>
                </View>
                <View style={styles.dosageRow}>
                  <Text style={styles.dosageLabel}>Refills:</Text>
                  <Text style={styles.dosageValue}>{item.refills || "0"}</Text>
                </View>
              </View>

              {item.instructions && (
                <Text style={styles.instructions}>
                  DIRECTIONS FOR USE: {item.instructions}
                </Text>
              )}
            </View>
          )),
        )}
      </View>
    );
  };

  const renderClinicalNotes = () => {
    if (!hasClinicalNotes) return null;

    return (
      <View style={styles.clinicalSection}>
        <Text style={styles.sectionTitle}>CLINICAL ASSESSMENT & NOTES</Text>

        {clinicalNotes.map((note, index) => (
          <View key={index} style={styles.clinicalNote}>
            <View style={styles.noteHeader}>
              <Text style={styles.noteType}>
                {note.noteType || "Clinical Assessment"}
              </Text>
              <Text style={styles.noteDate}>
                {format(new Date(note.createdAt), "MM/dd/yyyy HH:mm")}
              </Text>
            </View>

            <Text style={styles.noteContent}>{note.content}</Text>

            {note.snomedDiagnoses && (
              <View style={styles.diagnosisBox}>
                <Text style={styles.diagnosisLabel}>
                  DIAGNOSIS (ICD-10/SNOMED):
                </Text>
                <Text style={styles.diagnosisText}>{note.snomedDiagnoses}</Text>
              </View>
            )}
          </View>
        ))}
      </View>
    );
  };

  const renderProfessionalFooter = () => (
    <View style={styles.prescriptionFooter}>
      <View style={styles.signatureSection}>
        <View>
          <Text style={styles.signatureLabel}>Physician Signature:</Text>
          <View style={styles.signatureBox} />
          <Text style={[styles.signatureLabel, { marginTop: 5 }]}>
            Dr. {doctor.user.name || "Medical Professional"}
          </Text>
        </View>

        <View style={styles.dateBox}>
          <Text style={styles.signatureLabel}>Date:</Text>
          <Text style={[styles.signatureLabel, { fontSize: 10, marginTop: 5 }]}>
            {format(new Date(), "MM/dd/yyyy")}
          </Text>
        </View>
      </View>

      <View style={styles.legalInfo}>
        <Text style={styles.legalText}>
          This prescription is valid for 90 days from the date of issue. Generic
          substitution permitted unless "Dispense as Written" is indicated. For
          questions regarding this prescription, contact the prescribing
          physician.
        </Text>
        <Text style={[styles.legalText, { marginTop: 3 }]}>
          DEA Registration: ********* | State License: MD12345
        </Text>
      </View>
    </View>
  );

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.prescriptionId}>
          Prescription ID: {generatePrescriptionId()}
        </Text>

        {renderProfessionalHeader()}
        {renderDoctorInfo()}
        {renderPatientInfo()}
        {renderVitalSigns()}
        {renderPrescription()}
        {renderClinicalNotes()}
        {renderProfessionalFooter()}
      </Page>
    </Document>
  );
};
