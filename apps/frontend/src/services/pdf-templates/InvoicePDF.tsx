import React from "react";
import { Document, Page, Text, View, StyleSheet } from "@react-pdf/renderer";
import { format } from "date-fns";
import {
  InvoiceData,
  PdfGenerationOptions,
} from "../pdf-generation/pdf-generator";

const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 30,
    fontSize: 10,
    fontFamily: "Helvetica",
  },
  header: {
    marginBottom: 20,
    borderBottomWidth: 2,
    borderBottomColor: "#2563eb",
    borderBottomStyle: "solid",
    paddingBottom: 15,
  },
  hospitalName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1e40af",
    marginBottom: 5,
    textAlign: "center",
  },
  hospitalInfo: {
    fontSize: 9,
    color: "#6b7280",
    textAlign: "center",
    marginBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1f2937",
    textAlign: "center",
    marginTop: 10,
  },
  invoiceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  invoiceInfo: {
    width: "45%",
  },
  billTo: {
    width: "45%",
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#1e40af",
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
    borderBottomStyle: "solid",
    paddingBottom: 3,
  },
  infoRow: {
    flexDirection: "row",
    marginBottom: 4,
  },
  infoLabel: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#374151",
    width: 80,
  },
  infoValue: {
    fontSize: 9,
    color: "#6b7280",
    flex: 1,
  },
  table: {
    marginTop: 20,
    marginBottom: 20,
  },
  tableHeader: {
    flexDirection: "row",
    backgroundColor: "#f3f4f6",
    borderBottomWidth: 1,
    borderBottomColor: "#d1d5db",
    borderBottomStyle: "solid",
    paddingVertical: 8,
    paddingHorizontal: 5,
  },
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
    borderBottomStyle: "solid",
    paddingVertical: 6,
    paddingHorizontal: 5,
  },
  tableCell: {
    fontSize: 8,
    color: "#374151",
    paddingHorizontal: 3,
  },
  tableCellHeader: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#1f2937",
    paddingHorizontal: 3,
  },
  descriptionCell: {
    width: "30%",
  },
  qtyCell: {
    width: "8%",
    textAlign: "center",
  },
  priceCell: {
    width: "12%",
    textAlign: "right",
  },
  amountCell: {
    width: "12%",
    textAlign: "right",
  },
  totalsSection: {
    marginTop: 20,
    alignItems: "flex-end",
  },
  totalsTable: {
    width: "40%",
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 3,
    paddingHorizontal: 10,
  },
  totalLabel: {
    fontSize: 9,
    color: "#374151",
  },
  totalValue: {
    fontSize: 9,
    color: "#374151",
    fontWeight: "bold",
  },
  grandTotalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 6,
    paddingHorizontal: 10,
    backgroundColor: "#f3f4f6",
    borderTopWidth: 1,
    borderTopColor: "#d1d5db",
    borderTopStyle: "solid",
  },
  grandTotalLabel: {
    fontSize: 11,
    fontWeight: "bold",
    color: "#1f2937",
  },
  grandTotalValue: {
    fontSize: 11,
    fontWeight: "bold",
    color: "#1f2937",
  },
  notes: {
    marginTop: 20,
    padding: 10,
    backgroundColor: "#f9fafb",
    borderRadius: 5,
  },
  notesTitle: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#374151",
    marginBottom: 5,
  },
  notesText: {
    fontSize: 9,
    color: "#6b7280",
    lineHeight: 1.4,
  },
  footer: {
    marginTop: "auto",
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: "#e5e7eb",
    borderTopStyle: "solid",
    fontSize: 8,
    color: "#6b7280",
    textAlign: "center",
  },
  statusBadge: {
    backgroundColor: "#dbeafe",
    color: "#1e40af",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 3,
    fontSize: 8,
    fontWeight: "bold",
    textAlign: "center",
    marginTop: 5,
  },
});

interface InvoicePDFProps {
  invoiceData: InvoiceData;
  options?: PdfGenerationOptions;
}

export const InvoicePDF: React.FC<InvoicePDFProps> = ({
  invoiceData,
  options = {},
}) => {
  const {
    includeHeader = true,
    includeFooter = true,
    pageSize = "A4" as const,
    orientation = "portrait",
  } = options;

  const formatCurrency = (amount: number | string) => {
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    return `₹${num.toFixed(2)}`;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return { backgroundColor: "#dcfce7", color: "#166534" };
      case "issued":
        return { backgroundColor: "#dbeafe", color: "#1e40af" };
      case "overdue":
        return { backgroundColor: "#fee2e2", color: "#dc2626" };
      default:
        return { backgroundColor: "#f3f4f6", color: "#374151" };
    }
  };

  return (
    <Document>
      <Page
        size={pageSize === "Letter" ? "LETTER" : pageSize}
        orientation={orientation}
        style={styles.page}
      >
        {includeHeader && (
          <View style={styles.header}>
            <Text style={styles.hospitalName}>
              {invoiceData.organization.name}
            </Text>
            {invoiceData.branch.address && (
              <Text style={styles.hospitalInfo}>
                {invoiceData.branch.address}
                {invoiceData.branch.city && `, ${invoiceData.branch.city}`}
                {invoiceData.branch.state && `, ${invoiceData.branch.state}`}
                {invoiceData.branch.pincode &&
                  ` - ${invoiceData.branch.pincode}`}
              </Text>
            )}
            {invoiceData.branch.phone && (
              <Text style={styles.hospitalInfo}>
                Phone: {invoiceData.branch.phone}
              </Text>
            )}
            <Text style={styles.title}>INVOICE</Text>
          </View>
        )}

        <View style={styles.invoiceHeader}>
          <View style={styles.invoiceInfo}>
            <Text style={styles.sectionTitle}>Invoice Details</Text>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Invoice #:</Text>
              <Text style={styles.infoValue}>{invoiceData.invoiceNumber}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Date:</Text>
              <Text style={styles.infoValue}>
                {format(new Date(invoiceData.invoiceDate), "PPP")}
              </Text>
            </View>
            {invoiceData.dueDate && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Due Date:</Text>
                <Text style={styles.infoValue}>
                  {format(new Date(invoiceData.dueDate), "PPP")}
                </Text>
              </View>
            )}
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Status:</Text>
              <Text
                style={[styles.statusBadge, getStatusColor(invoiceData.status)]}
              >
                {invoiceData.status.toUpperCase()}
              </Text>
            </View>
            {invoiceData.paymentTerms && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Terms:</Text>
                <Text style={styles.infoValue}>{invoiceData.paymentTerms}</Text>
              </View>
            )}
          </View>

          <View style={styles.billTo}>
            <Text style={styles.sectionTitle}>Bill To</Text>
            <Text
              style={[
                styles.infoValue,
                { fontWeight: "bold", marginBottom: 3 },
              ]}
            >
              {invoiceData.patient.firstName} {invoiceData.patient.lastName}
            </Text>
            <Text style={styles.infoValue}>
              Phone: {invoiceData.patient.phone}
            </Text>
            {invoiceData.patient.email && (
              <Text style={styles.infoValue}>
                Email: {invoiceData.patient.email}
              </Text>
            )}
            {invoiceData.patient.address && (
              <Text style={styles.infoValue}>
                {invoiceData.patient.address}
                {invoiceData.patient.city && `, ${invoiceData.patient.city}`}
                {invoiceData.patient.state && `, ${invoiceData.patient.state}`}
                {invoiceData.patient.pincode &&
                  ` - ${invoiceData.patient.pincode}`}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.table}>
          <View style={styles.tableHeader}>
            <Text style={[styles.tableCellHeader, styles.descriptionCell]}>
              Description
            </Text>
            <Text style={[styles.tableCellHeader, styles.qtyCell]}>Qty</Text>
            <Text style={[styles.tableCellHeader, styles.priceCell]}>
              Unit Price
            </Text>
            <Text style={[styles.tableCellHeader, styles.priceCell]}>
              Discount
            </Text>
            <Text style={[styles.tableCellHeader, styles.priceCell]}>Tax</Text>
            <Text style={[styles.tableCellHeader, styles.amountCell]}>
              Amount
            </Text>
          </View>

          {invoiceData.items.map((item, index) => (
            <View key={index} style={styles.tableRow}>
              <View style={styles.descriptionCell}>
                <Text style={styles.tableCell}>{item.serviceDisplay}</Text>
                {item.notes && (
                  <Text
                    style={[
                      styles.tableCell,
                      { fontSize: 7, color: "#9ca3af" },
                    ]}
                  >
                    {item.notes}
                  </Text>
                )}
              </View>
              <Text style={[styles.tableCell, styles.qtyCell]}>
                {typeof item.quantity === "string"
                  ? parseFloat(item.quantity)
                  : item.quantity}
              </Text>
              <Text style={[styles.tableCell, styles.priceCell]}>
                {formatCurrency(item.unitPrice)}
              </Text>
              <Text style={[styles.tableCell, styles.priceCell]}>
                {formatCurrency(item.discountAmount)}
              </Text>
              <Text style={[styles.tableCell, styles.priceCell]}>
                {typeof item.taxRate === "string"
                  ? parseFloat(item.taxRate)
                  : item.taxRate}
                %
              </Text>
              <Text style={[styles.tableCell, styles.amountCell]}>
                {formatCurrency(item.totalAmount)}
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.totalsSection}>
          <View style={styles.totalsTable}>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Subtotal:</Text>
              <Text style={styles.totalValue}>
                {formatCurrency(invoiceData.subtotal)}
              </Text>
            </View>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Discount:</Text>
              <Text style={styles.totalValue}>
                -{formatCurrency(invoiceData.discountAmount)}
              </Text>
            </View>
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Tax:</Text>
              <Text style={styles.totalValue}>
                {formatCurrency(invoiceData.taxAmount)}
              </Text>
            </View>
            <View style={styles.grandTotalRow}>
              <Text style={styles.grandTotalLabel}>Total Amount:</Text>
              <Text style={styles.grandTotalValue}>
                {formatCurrency(invoiceData.totalAmount)}
              </Text>
            </View>
          </View>
        </View>

        {invoiceData.notes && (
          <View style={styles.notes}>
            <Text style={styles.notesTitle}>Notes:</Text>
            <Text style={styles.notesText}>{invoiceData.notes}</Text>
          </View>
        )}

        {includeFooter && (
          <View style={styles.footer}>
            <Text>Thank you for your business!</Text>
            <Text>
              This invoice was generated electronically and is valid without
              signature.
            </Text>
            <Text style={{ marginTop: 5, fontSize: 7 }}>
              Generated on {format(new Date(), "PPP p")}
            </Text>
          </View>
        )}
      </Page>
    </Document>
  );
};
