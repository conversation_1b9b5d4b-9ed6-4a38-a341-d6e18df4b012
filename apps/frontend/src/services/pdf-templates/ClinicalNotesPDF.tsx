import React from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";
import { format } from "date-fns";
import { BasePDF, styles as baseStyles } from "./BasePDF";
import {
  ConsultationData,
  PdfGenerationOptions,
} from "../pdf-generation/pdf-generator";

const styles = StyleSheet.create({
  noteContainer: {
    marginBottom: 15,
    padding: 12,
    backgroundColor: "#f0f9ff",
    borderRadius: 5,
    borderWidth: 1,
    borderColor: "#bae6fd",
    borderStyle: "solid",
  },
  noteHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: "#38bdf8",
    borderBottomStyle: "solid",
  },
  noteDate: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#0369a1",
  },
  noteType: {
    fontSize: 8,
    color: "#6b7280",
    backgroundColor: "#e0f2fe",
    padding: 3,
    borderRadius: 3,
    textTransform: "uppercase",
  },
  noteContent: {
    fontSize: 10,
    color: "#1f2937",
    lineHeight: 1.5,
    marginBottom: 10,
  },
  snomedSection: {
    marginTop: 10,
    padding: 8,
    backgroundColor: "#fef3c7",
    borderRadius: 3,
    borderWidth: 1,
    borderColor: "#fbbf24",
    borderStyle: "solid",
  },
  snomedTitle: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#92400e",
    marginBottom: 5,
  },
  snomedItem: {
    marginBottom: 5,
    padding: 5,
    backgroundColor: "#ffffff",
    borderRadius: 2,
    borderWidth: 1,
    borderColor: "#fed7aa",
    borderStyle: "solid",
  },
  snomedCode: {
    fontSize: 8,
    fontWeight: "bold",
    color: "#1f2937",
  },
  snomedTerm: {
    fontSize: 8,
    color: "#374151",
    marginTop: 2,
  },
  snomedDefinition: {
    fontSize: 7,
    color: "#6b7280",
    marginTop: 2,
    fontStyle: "italic",
  },
  tagsSection: {
    marginTop: 8,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 5,
  },
  tag: {
    fontSize: 7,
    color: "#1e40af",
    backgroundColor: "#dbeafe",
    padding: 2,
    borderRadius: 2,
    borderWidth: 1,
    borderColor: "#93c5fd",
    borderStyle: "solid",
  },
  noDataMessage: {
    textAlign: "center",
    fontSize: 12,
    color: "#6b7280",
    fontStyle: "italic",
    padding: 20,
    backgroundColor: "#f9fafb",
    borderRadius: 5,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    borderStyle: "solid",
  },
  summarySection: {
    marginTop: 20,
    padding: 15,
    backgroundColor: "#f8fafc",
    borderRadius: 5,
    borderWidth: 1,
    borderColor: "#e2e8f0",
    borderStyle: "solid",
  },
  summaryTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#1e293b",
    marginBottom: 10,
  },
  summaryItem: {
    flexDirection: "row",
    marginBottom: 5,
  },
  summaryLabel: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#374151",
    width: 100,
  },
  summaryValue: {
    fontSize: 9,
    color: "#1f2937",
    flex: 1,
  },
  diagnosisSection: {
    marginTop: 15,
    padding: 12,
    backgroundColor: "#fef2f2",
    borderRadius: 5,
    borderWidth: 1,
    borderColor: "#fecaca",
    borderStyle: "solid",
  },
  diagnosisTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#dc2626",
    marginBottom: 10,
  },
  diagnosisItem: {
    marginBottom: 8,
    padding: 8,
    backgroundColor: "#ffffff",
    borderRadius: 3,
    borderWidth: 1,
    borderColor: "#fee2e2",
    borderStyle: "solid",
  },
});

interface ClinicalNotesPDFProps {
  consultationData: ConsultationData;
  options?: PdfGenerationOptions;
}

export const ClinicalNotesPDF: React.FC<ClinicalNotesPDFProps> = ({
  consultationData,
  options,
}) => {
  const { clinicalNotes } = consultationData;

  const renderSnomedDiagnoses = (diagnoses: string) => {
    if (!diagnoses) return null;

    return (
      <View style={styles.snomedSection}>
        <Text style={styles.snomedTitle}>SNOMED CT Diagnoses:</Text>
        <View style={styles.snomedItem}>
          <Text style={styles.snomedTerm}>{diagnoses}</Text>
        </View>
      </View>
    );
  };

  const renderTags = (tags: string[]) => {
    if (!tags || tags.length === 0) return null;

    return (
      <View style={styles.tagsSection}>
        {tags.map((tag, index) => (
          <Text key={index} style={styles.tag}>
            {tag}
          </Text>
        ))}
      </View>
    );
  };

  const renderClinicalNote = (note: any, index: number) => (
    <View key={index} style={styles.noteContainer}>
      <View style={styles.noteHeader}>
        <Text style={styles.noteDate}>
          {format(new Date(note.createdAt), "PPP p")}
        </Text>
        <Text style={styles.noteType}>{note.noteType || "Clinical Note"}</Text>
      </View>

      {/* Structured Clinical Note Fields */}
      {note.chiefComplaints && (
        <View style={{ marginBottom: 8 }}>
          <Text
            style={{
              fontSize: 10,
              fontWeight: "bold",
              color: "#374151",
              marginBottom: 4,
            }}
          >
            Chief Complaints:
          </Text>
          <Text style={styles.noteContent}>{note.chiefComplaints}</Text>
        </View>
      )}

      {note.allergies && (
        <View style={{ marginBottom: 8 }}>
          <Text
            style={{
              fontSize: 10,
              fontWeight: "bold",
              color: "#dc2626",
              marginBottom: 4,
            }}
          >
            Allergies:
          </Text>
          <Text style={styles.noteContent}>{note.allergies}</Text>
        </View>
      )}

      {note.medicalHistory && (
        <View style={{ marginBottom: 8 }}>
          <Text
            style={{
              fontSize: 10,
              fontWeight: "bold",
              color: "#374151",
              marginBottom: 4,
            }}
          >
            Medical History:
          </Text>
          <Text style={styles.noteContent}>{note.medicalHistory}</Text>
        </View>
      )}

      {note.investigationAdvice && (
        <View style={{ marginBottom: 8 }}>
          <Text
            style={{
              fontSize: 10,
              fontWeight: "bold",
              color: "#2563eb",
              marginBottom: 4,
            }}
          >
            Investigation Advice:
          </Text>
          <Text style={styles.noteContent}>{note.investigationAdvice}</Text>
        </View>
      )}

      {note.procedure && (
        <View style={{ marginBottom: 8 }}>
          <Text
            style={{
              fontSize: 10,
              fontWeight: "bold",
              color: "#059669",
              marginBottom: 4,
            }}
          >
            Procedure:
          </Text>
          <Text style={styles.noteContent}>{note.procedure}</Text>
        </View>
      )}

      {note.followUp && (
        <View style={{ marginBottom: 8 }}>
          <Text
            style={{
              fontSize: 10,
              fontWeight: "bold",
              color: "#d97706",
              marginBottom: 4,
            }}
          >
            Follow Up:
          </Text>
          <Text style={styles.noteContent}>{note.followUp}</Text>
        </View>
      )}

      {note.content && (
        <View style={{ marginBottom: 8 }}>
          <Text
            style={{
              fontSize: 10,
              fontWeight: "bold",
              color: "#374151",
              marginBottom: 4,
            }}
          >
            Additional Clinical Observations:
          </Text>
          <Text style={styles.noteContent}>{note.content}</Text>
        </View>
      )}

      {note.snomedTags &&
        note.snomedTags.length > 0 &&
        renderTags(note.snomedTags)}

      {note.snomedDiagnoses && renderSnomedDiagnoses(note.snomedDiagnoses)}
    </View>
  );

  const getAllDiagnoses = () => {
    const allDiagnoses: string[] = [];
    clinicalNotes?.forEach((note) => {
      if (note.snomedDiagnoses) {
        allDiagnoses.push(note.snomedDiagnoses);
      }
    });
    return allDiagnoses;
  };

  const renderSummary = () => {
    if (!clinicalNotes || clinicalNotes.length === 0) return null;

    const totalNotes = clinicalNotes.length;
    const noteTypes = [
      ...new Set(clinicalNotes.map((note) => note.noteType).filter(Boolean)),
    ];
    const allDiagnoses = getAllDiagnoses();

    return (
      <View style={styles.summarySection}>
        <Text style={styles.summaryTitle}>Clinical Notes Summary</Text>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Total Notes:</Text>
          <Text style={styles.summaryValue}>{totalNotes}</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Note Types:</Text>
          <Text style={styles.summaryValue}>
            {noteTypes.length > 0 ? noteTypes.join(", ") : "General"}
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Diagnoses Count:</Text>
          <Text style={styles.summaryValue}>{allDiagnoses.length}</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Date Range:</Text>
          <Text style={styles.summaryValue}>
            {format(new Date(clinicalNotes[0].createdAt), "PPP")}
            {totalNotes > 1 &&
              ` - ${format(new Date(clinicalNotes[totalNotes - 1].createdAt), "PPP")}`}
          </Text>
        </View>
      </View>
    );
  };

  const renderDiagnosesSection = () => {
    const allDiagnoses = getAllDiagnoses();
    if (allDiagnoses.length === 0) return null;

    // Remove duplicates
    const uniqueDiagnoses = [...new Set(allDiagnoses)];

    return (
      <View style={styles.diagnosisSection}>
        <Text style={styles.diagnosisTitle}>All Diagnoses Summary</Text>
        {uniqueDiagnoses.map((diagnosis, index) => (
          <View key={index} style={styles.diagnosisItem}>
            <Text style={styles.snomedCode}>{diagnosis}</Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <BasePDF
      consultationData={consultationData}
      title="Clinical Notes Report"
      options={options}
    >
      <Text style={baseStyles.sectionTitle}>Clinical Observations & Notes</Text>

      {!clinicalNotes || clinicalNotes.length === 0 ? (
        <Text style={styles.noDataMessage}>
          No clinical notes recorded for this consultation.
        </Text>
      ) : (
        <>
          {clinicalNotes.map((note, index) => renderClinicalNote(note, index))}
          {renderSummary()}
          {renderDiagnosesSection()}
        </>
      )}
    </BasePDF>
  );
};
