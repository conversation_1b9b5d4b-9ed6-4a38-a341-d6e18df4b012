import React from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";
import { format } from "date-fns";
import { BasePDF } from "./BasePDF";
import {
  ConsultationData,
  PdfGenerationOptions,
} from "../pdf-generation/pdf-generator";

const styles = StyleSheet.create({
  section: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: "#f8f9fa",
    borderRadius: 4,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  heading: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#2c3e50",
  },
  subheading: {
    fontSize: 12,
    fontWeight: "bold",
    marginBottom: 6,
    color: "#34495e",
  },
  text: {
    fontSize: 10,
    marginBottom: 4,
    color: "#2c3e50",
    lineHeight: 1.4,
  },
  row: {
    flexDirection: "row",
    marginBottom: 4,
  },
  label: {
    fontSize: 10,
    fontWeight: "bold",
    width: "30%",
    color: "#2c3e50",
  },
  value: {
    fontSize: 10,
    width: "70%",
    color: "#2c3e50",
  },
  testResult: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 6,
    paddingBottom: 4,
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  testName: {
    fontSize: 10,
    fontWeight: "bold",
    width: "40%",
    color: "#2c3e50",
  },
  testValue: {
    fontSize: 10,
    width: "20%",
    textAlign: "center",
    color: "#2c3e50",
  },
  testUnit: {
    fontSize: 10,
    width: "15%",
    textAlign: "center",
    color: "#7f8c8d",
  },
  testRange: {
    fontSize: 10,
    width: "25%",
    textAlign: "right",
    color: "#7f8c8d",
  },
  abnormalValue: {
    color: "#e74c3c",
    fontWeight: "bold",
  },
  normalValue: {
    color: "#27ae60",
  },
  patientInfo: {
    backgroundColor: "#e8f4fd",
    borderColor: "#3498db",
  },
  abhaInfo: {
    backgroundColor: "#e8f5e8",
    borderColor: "#27ae60",
  },
  reportHeader: {
    backgroundColor: "#fff3cd",
    borderColor: "#ffc107",
  },
});

interface LabReportPDFProps {
  consultationData: ConsultationData;
  diagnosticReport?: any;
  options?: PdfGenerationOptions;
}

export const LabReportPDF: React.FC<LabReportPDFProps> = ({
  consultationData,
  diagnosticReport,
  options,
}) => {

  const formatValue = (value: any) => {
    if (value === null || value === undefined) return "N/A";
    return String(value);
  };

  const isAbnormal = (value: number, normalRange: string) => {
    if (!normalRange || isNaN(value)) return false;
    
    // Parse normal range (e.g., "10-20", "<5", ">100")
    const rangeMatch = normalRange.match(/(\d+\.?\d*)\s*-\s*(\d+\.?\d*)/);
    if (rangeMatch) {
      const min = parseFloat(rangeMatch[1]);
      const max = parseFloat(rangeMatch[2]);
      return value < min || value > max;
    }
    
    const lessThanMatch = normalRange.match(/<\s*(\d+\.?\d*)/);
    if (lessThanMatch) {
      const max = parseFloat(lessThanMatch[1]);
      return value >= max;
    }
    
    const greaterThanMatch = normalRange.match(/>\s*(\d+\.?\d*)/);
    if (greaterThanMatch) {
      const min = parseFloat(greaterThanMatch[1]);
      return value <= min;
    }
    
    return false;
  };

  return (
    <BasePDF
      consultationData={consultationData}
      title="Lab Report"
      options={options}
    >


      {/* Report Information */}
      {diagnosticReport && (
        <View style={[styles.section, styles.reportHeader]}>
          <Text style={styles.heading}>Report Information</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Report Type:</Text>
            <Text style={styles.value}>{diagnosticReport.reportType || "Lab Report"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Report Date:</Text>
            <Text style={styles.value}>
              {diagnosticReport.reportDate
                ? format(new Date(diagnosticReport.reportDate), "PPP")
                : "N/A"}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Test Code:</Text>
            <Text style={styles.value}>{diagnosticReport.code || "N/A"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Test Name:</Text>
            <Text style={styles.value}>{diagnosticReport.codeDisplay || "N/A"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Status:</Text>
            <Text style={styles.value}>{diagnosticReport.status || "N/A"}</Text>
          </View>
          {diagnosticReport.performer && (
            <View style={styles.row}>
              <Text style={styles.label}>Performed By:</Text>
              <Text style={styles.value}>{diagnosticReport.performer}</Text>
            </View>
          )}
          {diagnosticReport.specimen && (
            <View style={styles.row}>
              <Text style={styles.label}>Specimen:</Text>
              <Text style={styles.value}>{diagnosticReport.specimen}</Text>
            </View>
          )}
          {diagnosticReport.conclusion && (
            <View style={styles.row}>
              <Text style={styles.label}>Conclusion:</Text>
              <Text style={styles.value}>{diagnosticReport.conclusion}</Text>
            </View>
          )}
        </View>
      )}

      {/* Test Results */}
      {diagnosticReport?.result && (
        <View style={styles.section}>
          <Text style={styles.heading}>Test Results</Text>

          {/* Display result as text if it's a simple string or object */}
          {typeof diagnosticReport.result === 'string' ? (
            <Text style={styles.text}>{diagnosticReport.result}</Text>
          ) : diagnosticReport.result && typeof diagnosticReport.result === 'object' ? (
            <View>
              {diagnosticReport.result.text && (
                <Text style={styles.text}>{diagnosticReport.result.text}</Text>
              )}
              {/* If result is an array of test results */}
              {Array.isArray(diagnosticReport.result) && (
                <View>
                  <View style={styles.testResult}>
                    <Text style={[styles.testName, { fontWeight: "bold" }]}>Test Name</Text>
                    <Text style={[styles.testValue, { fontWeight: "bold" }]}>Value</Text>
                    <Text style={[styles.testUnit, { fontWeight: "bold" }]}>Unit</Text>
                    <Text style={[styles.testRange, { fontWeight: "bold" }]}>Normal Range</Text>
                  </View>

                  {diagnosticReport.result.map((result: any, index: number) => {
                    const numericValue = parseFloat(result.value);
                    const isValueAbnormal = !isNaN(numericValue) && isAbnormal(numericValue, result.normalRange);

                    return (
                      <View key={index} style={styles.testResult}>
                        <Text style={styles.testName}>{result.testName || "N/A"}</Text>
                        <Text style={[
                          styles.testValue,
                          isValueAbnormal ? styles.abnormalValue : styles.normalValue
                        ]}>
                          {formatValue(result.value)}
                        </Text>
                        <Text style={styles.testUnit}>{result.unit || "-"}</Text>
                        <Text style={styles.testRange}>{result.normalRange || "N/A"}</Text>
                      </View>
                    );
                  })}
                </View>
              )}
              {/* If result has individual properties */}
              {!Array.isArray(diagnosticReport.result) && !diagnosticReport.result.text && (
                <View>
                  {Object.entries(diagnosticReport.result).map(([key, value], index) => (
                    <View key={index} style={styles.row}>
                      <Text style={styles.label}>{key}:</Text>
                      <Text style={styles.value}>{formatValue(value)}</Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ) : (
            <Text style={styles.text}>No test results available</Text>
          )}
        </View>
      )}


    </BasePDF>
  );
};
