import { prisma } from "@/lib/prisma";
import { createConsentAuditLog } from "@/app/api/abdm/consent/audit/actions";

/**
 * Check for expired consents and update their status
 * This function should be called periodically (e.g., daily)
 */
export async function checkExpiredConsents() {
  try {
    // Get current date
    const now = new Date();

    // Find consents that might be expired (status is GRANTED and permission.dateRange.to is in the past)
    const potentiallyExpiredConsents = await prisma.consent.findMany({
      where: {
        status: "GRANTED",
      },
      include: {
        patient: true,
      },
    });

    // Check each consent
    for (const consent of potentiallyExpiredConsents) {
      try {
        // Parse permission
        const permission =
          typeof consent.permission === "string"
            ? JSON.parse(consent.permission)
            : consent.permission;

        // Check if dataEraseAt is in the past (ONLY condition for consent expiration)
        const dataEraseExpired =
          permission?.dataEraseAt && new Date(permission.dataEraseAt) < now;
        console.log("Data erase expired:", dataEraseExpired);

        if (dataEraseExpired) {
          // Update consent status in database
          await prisma.consent.update({
            where: { id: consent.id },
            data: {
              status: "EXPIRED",
              updatedAt: now,
            },
          });

          // Create audit log
          await createConsentAuditLog({
            consentId: consent.id,
            action: "STATUS_CHANGE",
            actorId: "SYSTEM",
            actorRole: "SYSTEM",
            details: {
              oldStatus: "GRANTED",
              newStatus: "EXPIRED",
              reason: "Consent expired based on dataEraseAt date",
            },
            ipAddress: "127.0.0.1",
            userAgent: "Consent Expiry Checker",
          });
        }
      } catch (error) {
        console.error(`Error processing consent ${consent.id}:`, error);
      }
    }
    return { success: true, message: "Expired consent check completed" };
  } catch (error) {
    console.error("Error checking expired consents:", error);
    return { success: false, error: "Failed to check expired consents" };
  }
}

/**
 * Check for consents that are about to expire (e.g., in the next 7 days)
 * This could be used to notify users about upcoming expirations
 */
export async function checkUpcomingExpirations(daysThreshold = 7) {
  try {
    // Get current date
    const now = new Date();

    // Calculate threshold date (e.g., 7 days from now)
    const thresholdDate = new Date();
    thresholdDate.setDate(thresholdDate.getDate() + daysThreshold);

    // Find consents that are about to expire
    const soonToExpireConsents = await prisma.consent.findMany({
      where: {
        status: "GRANTED",
      },
      include: {
        patient: true,
      },
    });

    const upcomingExpirations = [];

    // Check each consent
    for (const consent of soonToExpireConsents) {
      try {
        // Parse permission
        const permission =
          typeof consent.permission === "string"
            ? JSON.parse(consent.permission)
            : consent.permission;

        // Check if dateRange.to is between now and threshold date
        if (
          permission?.dateRange?.to &&
          new Date(permission.dateRange.to) > now &&
          new Date(permission.dateRange.to) <= thresholdDate
        ) {
          upcomingExpirations.push({
            consentId: consent.id,
            patientId: consent.patientId,
            patientName: `${consent.patient?.firstName} ${consent.patient?.lastName}`,
            expiryDate: permission.dateRange.to,
          });
        }
      } catch (error) {
        console.error(`Error processing consent ${consent.id}:`, error);
      }
    }
    return {
      success: true,
      upcomingExpirations,
      message: `Found ${upcomingExpirations.length} consents expiring in the next ${daysThreshold} days`,
    };
  } catch (error) {
    console.error("Error checking upcoming expirations:", error);
    return { success: false, error: "Failed to check upcoming expirations" };
  }
}
