/**
 * ABDM Service Compatibility Layer
 *
 * This file provides backward compatibility with the old ABDM service.
 * It re-exports the functions from the new modular ABDM service.
 *
 * @deprecated Use the new modular ABDM service instead.
 */

import {
  abhaVerify,
  abhaCreate,
  abhaCard,
  abhaLogin,
  consent,
  healthRecord,
  careContext,
  abhaSearch,
  deepLinking,
} from "./abdm";

// Create a compatibility layer for the old abdmService
const abdmService = {
  // ABHA verification
  verifyAbhaAddress: abhaVerify.abhaAddress.verifyAbhaAddressOtp,
  generateAbhaAddressOtp: abhaVerify.abhaAddress.requestAbhaAddressOtp,
  verifyAbhaNumber: abhaVerify.abhaId.verifyAbhaIdOtp,
  generateAbhaNumberOtp: abhaVerify.abhaId.requestAbhaIdOtp,
  verifyAadhaar: abhaVerify.aadhaar.verifyAadhaarOtp,
  generateAadhaarOtp: abhaVerify.aadhaar.requestAadhaarOtp,
  verifyMobile: abhaVerify.mobile.verifyMobileOtp,
  generateMobileOtp: abhaVerify.mobile.requestMobileOtp,

  // ABHA creation
  createAbha: abhaCreate.aadhaar.verifyEnrollmentOtp,
  requestEnrollmentOtp: abhaCreate.aadhaar.requestEnrollmentOtp,

  // ABHA card
  downloadAbhaCard: abhaCard.downloadAbhaCard,

  // ABHA login
  requestAbhaLoginOtp: abhaLogin.requestAbhaLoginOtp,
  verifyAbhaLoginOtp: abhaLogin.verifyAbhaLoginOtp,
  getAbhaToken: abhaLogin.getAbhaToken,

  // Consent management
  requestConsent: consent.requestConsent,
  storeConsent: consent.storeConsent,
  revokeConsent: consent.revokeConsent,
  fetchConsentDetails: consent.fetchConsentDetails,

  // Health record management
  createVitalsBundle: healthRecord.packageService.createVitalsBundle,
  createPrescriptionBundle:
    healthRecord.packageService.createPrescriptionBundle,
  createClinicalNoteBundle:
    healthRecord.packageService.createClinicalNoteBundle,
  uploadHealthRecord: healthRecord.upload.uploadHealthRecord,

  // Care context management
  generateLinkToken: (patientId: string, branchId?: string) => {
    return careContext.linkToken.generateLinkToken(patientId, branchId);
  },
  updateLinkToken: (patientId: string, branchId: string) => {
    return careContext.updateLinkToken.updateLinkToken(patientId, branchId);
  },
  linkCareContext: (
    patientId: string,
    appointmentId: string,
    hiTypes: string[],
    branchId?: string,
    consultationId?: string,
  ) => {
    return careContext.linkCareContext.linkCareContext(
      patientId,
      appointmentId,
      hiTypes,
      branchId,
      consultationId,
    );
  },
  notifyCareContext: careContext.notifyCareContext.notifyCareContext,
  unlinkCareContext: careContext.unlinkCareContext.unlinkCareContext,

  // ABHA Search
  searchAbhaByMobile: abhaSearch.mobile.searchAbhaByMobile,

  // Deep Linking
  sendPhrAppLink: deepLinking.sendPhrAppLink,
};

export { abdmService };
