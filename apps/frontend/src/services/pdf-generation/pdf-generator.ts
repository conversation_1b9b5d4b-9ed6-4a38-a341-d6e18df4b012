import { renderTo<PERSON>uffer } from "@react-pdf/renderer";
import { ReactElement } from "react";

export interface ConsultationData {
  id: string;
  consultationDate: Date | string;
  status: string;
  startTime?: string | null;
  endTime?: string | null;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    dateOfBirth: Date | string;
    gender: string;
    phone: string;
    email?: string | null;
    address?: string | null;
    city?: string | null;
    state?: string | null;
    pincode?: string | null;
    bloodGroup?: string | null;
    abhaProfile?: {
      id: string;
      abhaNumber: string | null;
      abhaAddress?: string | null;
      healthIdNumber?: string | null;
      abhaStatus?: string | null;
    } | null;
  };
  doctor: {
    id: string;
    user: {
      name: string | null;
      email: string | null;
    };
    specialization?: string | null;
  };
  branch: {
    id: string;
    name: string;
  };
  organization?: {
    id: string;
    name: string;
    address?: string;
    phone?: string;
    email?: string;
  };
  vitals: any[];
  clinicalNotes: any[];
  prescriptions: any[];
  invoices?: any[];
}

export interface InvoiceData {
  id: string;
  invoiceNumber: string;
  invoiceDate: Date | string;
  dueDate?: Date | string | null;
  status: string;
  type: string;
  subtotal: number | string;
  taxAmount: number | string;
  discountAmount: number | string;
  totalAmount: number | string;
  currency: string;
  paymentTerms?: string | null;
  paymentStatus: string;
  notes?: string | null;
  consultation: {
    id: string;
    consultationDate: Date | string;
  };
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    phone: string;
    email?: string | null;
    address?: string | null;
    city?: string | null;
    state?: string | null;
    pincode?: string | null;
  };
  doctor: {
    id: string;
    user: {
      name: string | null;
    };
    specialization?: string | null;
  };
  organization: {
    id: string;
    name: string;
  };
  branch: {
    id: string;
    name: string;
    address?: string | null;
    city?: string | null;
    state?: string | null;
    pincode?: string | null;
    phone?: string | null;
  };
  items: {
    id: string;
    sequence: number;
    serviceCode?: string | null;
    serviceDisplay: string;
    category: string;
    quantity: number | string;
    unitPrice: number | string;
    mrp?: number | string | null;
    discountAmount: number | string;
    taxRate: number | string;
    cgstAmount: number | string;
    sgstAmount: number | string;
    igstAmount: number | string;
    totalAmount: number | string;
    notes?: string | null;
  }[];
}

export interface PdfGenerationOptions {
  includeHeader?: boolean;
  includeFooter?: boolean;
  pageSize?: "A4" | "Letter";
  orientation?: "portrait" | "landscape";
}

export type PdfType =
  | "vitals"
  | "prescription"
  | "clinical-notes"
  | "combined"
  | "invoice";

/**
 * Generate PDF buffer from React PDF component
 */
export async function generatePdfBuffer(
  component: ReactElement,
  // options?: PdfGenerationOptions
): Promise<Buffer> {
  try {
    const buffer = await renderToBuffer(component);
    return buffer;
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw new Error("Failed to generate PDF");
  }
}

/**
 * Generate PDF for specific consultation document type
 */
export async function generateConsultationPdf(
  consultationData: ConsultationData,
  type: PdfType,
  options?: PdfGenerationOptions,
): Promise<Buffer> {
  try {
    let component: ReactElement;

    switch (type) {
      case "vitals":
        const { VitalsPDF } = await import("../pdf-templates/VitalsPDF");
        component = VitalsPDF({ consultationData, options }) as ReactElement;
        break;

      case "prescription":
        const { PrescriptionPDF } = await import(
          "../pdf-templates/PrescriptionPDF"
        );
        component = PrescriptionPDF({
          consultationData,
          options,
        }) as ReactElement;
        break;

      case "clinical-notes":
        const { ClinicalNotesPDF } = await import(
          "../pdf-templates/ClinicalNotesPDF"
        );
        component = ClinicalNotesPDF({
          consultationData,
          options,
        }) as ReactElement;
        break;

      case "combined":
        const { CombinedPDF } = await import("../pdf-templates/CombinedPDF");
        component = CombinedPDF({ consultationData, options }) as ReactElement;
        break;

      case "invoice":
        const { InvoicePDF } = await import("../pdf-templates/InvoicePDF");
        // Need to extract invoice data from the consultation
        const invoice = consultationData.invoices?.[0];
        if (!invoice) throw new Error("No invoice data available");
        component = InvoicePDF({
          invoiceData: invoice,
          options,
        }) as ReactElement;
        break;

      default:
        throw new Error(`Unsupported PDF type: ${type}`);
    }

    return await generatePdfBuffer(component);
  } catch (error) {
    console.error(`Error generating ${type} PDF:`, error);
    throw new Error(`Failed to generate ${type} PDF`);
  }
}

/**
 * Validate consultation data for PDF generation
 */
export function validateConsultationData(
  consultationData: ConsultationData,
  type: PdfType,
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Basic validation
  if (!consultationData) {
    errors.push("Consultation data is required");
    return { isValid: false, errors };
  }

  if (!consultationData.patient) {
    errors.push("Patient information is required");
  }

  if (!consultationData.doctor) {
    errors.push("Doctor information is required");
  }

  // Type-specific validation
  switch (type) {
    case "vitals":
      if (!consultationData.vitals || consultationData.vitals.length === 0) {
        errors.push("No vitals data available for PDF generation");
      }
      break;

    case "prescription":
      if (
        !consultationData.prescriptions ||
        consultationData.prescriptions.length === 0
      ) {
        errors.push("No prescription data available for PDF generation");
      }
      break;

    case "clinical-notes":
      if (
        !consultationData.clinicalNotes ||
        consultationData.clinicalNotes.length === 0
      ) {
        errors.push("No clinical notes available for PDF generation");
      }
      break;

    case "combined":
      const hasVitals =
        consultationData.vitals && consultationData.vitals.length > 0;
      const hasPrescriptions =
        consultationData.prescriptions &&
        consultationData.prescriptions.length > 0;
      const hasClinicalNotes =
        consultationData.clinicalNotes &&
        consultationData.clinicalNotes.length > 0;

      if (!hasVitals && !hasPrescriptions && !hasClinicalNotes) {
        errors.push(
          "No consultation data available for combined PDF generation",
        );
      }
      break;

    case "invoice":
      if (
        !consultationData.invoices ||
        consultationData.invoices.length === 0
      ) {
        errors.push("No invoice data available for PDF generation");
      }
      break;
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Get PDF filename based on type and consultation data
 */
export function getPdfFilename(
  consultationData: ConsultationData,
  type: PdfType,
): string {
  const patientName = `${consultationData.patient.firstName}_${consultationData.patient.lastName}`;
  const date = new Date(consultationData.consultationDate)
    .toISOString()
    .split("T")[0];

  switch (type) {
    case "vitals":
      return `${patientName}_Vitals_${date}.pdf`;
    case "prescription":
      return `${patientName}_Prescription_${date}.pdf`;
    case "clinical-notes":
      return `${patientName}_Clinical_Notes_${date}.pdf`;
    case "combined":
      return `${patientName}_Consultation_Report_${date}.pdf`;
    case "invoice":
      return `${patientName}_Invoice_${date}.pdf`;
    default:
      return `${patientName}_${type}_${date}.pdf`;
  }
}

/**
 * Generate PDF for invoice
 */
export async function generateInvoicePdf(
  invoiceData: InvoiceData,
  options?: PdfGenerationOptions,
): Promise<Buffer> {
  try {
    const { InvoicePDF } = await import("../pdf-templates/InvoicePDF");
    const component = InvoicePDF({ invoiceData, options }) as ReactElement;
    return await generatePdfBuffer(component);
  } catch (error) {
    console.error("Error generating invoice PDF:", error);
    throw new Error("Failed to generate invoice PDF");
  }
}

/**
 * Get invoice PDF filename
 */
export function getInvoicePdfFilename(invoiceData: InvoiceData): string {
  const patientName = `${invoiceData.patient.firstName}_${invoiceData.patient.lastName}`;
  const date = new Date(invoiceData.invoiceDate).toISOString().split("T")[0];
  return `Invoice_${invoiceData.invoiceNumber}_${patientName}_${date}.pdf`;
}
