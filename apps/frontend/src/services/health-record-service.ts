/**
 * Health Record Service
 *
 * This service provides functions for managing health records in the frontend.
 */

import { Fetch } from "./fetch";

/**
 * Package a health record for ABDM
 * @param patientId - Patient ID
 * @param recordType - Record type (vitals, prescription, clinicalNote)
 * @param recordId - Record ID
 * @param consultationId - Optional consultation ID for context
 * @returns Bundle ID
 */
export async function packageHealthRecord(
  patientId: string,
  recordType: string,
  recordId: string,
  consultationId?: string,
): Promise<{ bundleId: string; message: string }> {
  try {
    const response = await Fetch.post("/api/abdm/health-record/package", {
      patientId,
      recordType,
      recordId,
      consultationId, // Include consultationId if provided
    });

    if (!response.success) {
      // Handle error response
      if (response.status === 401) {
        throw new Error("Authentication error: Please log in again");
      } else {
        throw new Error(response.error || "Failed to package health record");
      }
    }

    return response as unknown as { bundleId: string; message: string };
  } catch (error) {
    console.error("Error packaging health record:", error);
    throw error;
  }
}

/**
 * Upload a health record to ABDM
 * @param bundleId - Bundle ID
 * @param consentId - Consent ID
 * @param careContextReference - Care context reference
 * @param recipientPublicKey - Recipient's public key
 * @returns Upload response
 */
export async function uploadHealthRecord(
  bundleId: string,
  consentId: string,
  careContextReference: string,
  recipientPublicKey: string,
): Promise<{ message: string; transactionId: string }> {
  try {
    const response = await Fetch.post("/api/abdm/health-record/upload", {
      bundleId,
      consentId,
      careContextReference,
      recipientPublicKey,
    });

    if (!response.success) {
      // Handle error response
      if (response.status === 401) {
        throw new Error("Authentication error: Please log in again");
      } else {
        throw new Error(response.error || "Failed to upload health record");
      }
    }

    return response as unknown as { message: string; transactionId: string };
  } catch (error) {
    console.error("Error uploading health record:", error);
    throw error;
  }
}

/**
 * Get health record fetch requests for a patient
 * @param patientId - Patient ID
 * @returns Health record fetch requests
 */
export async function getHealthRecordFetchRequests(
  patientId: string,
): Promise<any[]> {
  try {
    const response = await Fetch.get(
      `/api/abdm/health-record/fetch-requests?patientId=${patientId}`,
    );
    return response.fetchRequests || [];
  } catch (error) {
    console.error("Error getting health record fetch requests:", error);
    throw error;
  }
}

/**
 * Get FHIR bundles for a patient
 * @param patientId - Patient ID
 * @returns FHIR bundles
 */
export async function getPatientFhirBundles(patientId: string): Promise<any[]> {
  try {
    const response = await Fetch.get(`/api/patients/${patientId}/fhir-bundles`);

    if (!response.success) {
      // Handle error response
      if (response.status === 401) {
        throw new Error("Authentication error: Please log in again");
      } else {
        throw new Error(response.error || "Failed to get patient FHIR bundles");
      }
    }

    return response.bundles || [];
  } catch (error) {
    console.error("Error getting patient FHIR bundles:", error);
    throw error;
  }
}

/**
 * Upload a health record to a dataPushUrl
 * @param bundleId - Bundle ID
 * @param fetchId - Fetch ID
 * @returns Upload response
 */
export async function uploadHealthRecordToDataPushUrl(
  bundleId: string,
  fetchId: string,
): Promise<{ message: string; transactionId: string }> {
  try {
    const response = await Fetch.post(
      "/api/abdm/health-record/upload-to-datapush",
      {
        bundleId,
        fetchId,
      },
    );

    return response as unknown as { message: string; transactionId: string };
  } catch (error) {
    console.error("Error uploading health record to dataPushUrl:", error);
    throw error;
  }
}

/**
 * Check if a health record can be shared
 * @param patientId - Patient ID
 * @param recordType - Record type (vitals, prescription, clinicalNote)
 * @returns Whether the record can be shared
 */
export async function canShareHealthRecord(
  patientId: string,
  recordType: string,
): Promise<{ canShare: boolean; activeConsents: any[] }> {
  try {
    const response = await Fetch.get(
      `/api/patients/${patientId}/consents/active`,
    );

    const activeConsents = response.consents || [];
    if (!activeConsents.length) {
      return { canShare: false, activeConsents: [] };
    }

    // Map record types to HI types
    const hiTypeMap: Record<string, string[]> = {
      vitals: ["Vital Signs", "OPConsultation"],
      prescription: ["Prescription"],
      clinicalNote: ["Clinical Notes", "OPConsultation"],
      diagnosticReport: ["DiagnosticReport"],
      procedure: ["Procedure", "OPConsultation"],
    };

    const hiType = hiTypeMap[recordType];
    if (!hiType) {
      return { canShare: false, activeConsents };
    }

    // Check if any consent allows sharing this type of record
    const canShare = activeConsents.some((consent: { hiTypes: string[] }) =>
      // Check if any of the mapped HI types is included in the consent's hiTypes
      hiType.some((type) => consent.hiTypes.includes(type)),
    );

    return { canShare, activeConsents };
  } catch (error) {
    console.error("Error checking if health record can be shared:", error);
    throw error;
  }
}
