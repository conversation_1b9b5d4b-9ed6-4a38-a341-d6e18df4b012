import { db } from "@/lib/db";
import { createAllergyIntoleranceResource } from "@/lib/fhir/resources";
import { FhirAllergyIntolerance } from "@/lib/fhir/types";
import { AllergyIntolerance } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";

/**
 * Create a new allergy intolerance record
 */
export async function createAllergyIntolerance(data: {
  patientId: string;
  doctorId: string;
  organizationId: string;
  consultationId?: string;
  clinicalStatus: string;
  verificationStatus: string;
  type: string;
  category: string;
  criticality: string;
  code: string;
  codeDisplay: string;
  onsetDateTime?: Date;
  recordedDate: Date;
  recorder?: string;
  asserter?: string;
  lastOccurrence?: Date;
  note?: string;
  reaction?: any;
}): Promise<AllergyIntolerance> {
  return db.allergyIntolerance.create({
    data: {
      patientId: data.patientId,
      doctorId: data.doctorId,
      organizationId: data.organizationId,
      consultationId: data.consultationId,
      clinicalStatus: data.clinicalStatus,
      verificationStatus: data.verificationStatus,
      type: data.type,
      category: data.category,
      criticality: data.criticality,
      code: data.code,
      codeDisplay: data.codeDisplay,
      onsetDateTime: data.onsetDateTime,
      recordedDate: data.recordedDate,
      recorder: data.recorder,
      asserter: data.asserter,
      lastOccurrence: data.lastOccurrence,
      note: data.note,
      reaction: data.reaction,
    },
  });
}

/**
 * Get an allergy intolerance record by ID
 */
export async function getAllergyIntoleranceById(
  id: string,
): Promise<AllergyIntolerance | null> {
  return db.allergyIntolerance.findUnique({
    where: { id },
  });
}

/**
 * Get allergy intolerance records for a patient
 */
export async function getAllergyIntolerancesByPatient(
  patientId: string,
): Promise<AllergyIntolerance[]> {
  return db.allergyIntolerance.findMany({
    where: { patientId },
    orderBy: { recordedDate: "desc" },
  });
}

/**
 * Get allergy intolerance records for a consultation
 */
export async function getAllergyIntolerancesByConsultation(
  consultationId: string,
): Promise<AllergyIntolerance[]> {
  return db.allergyIntolerance.findMany({
    where: { consultationId },
    orderBy: { recordedDate: "desc" },
  });
}

/**
 * Update an allergy intolerance record
 */
export async function updateAllergyIntolerance(
  id: string,
  data: Partial<AllergyIntolerance>,
): Promise<AllergyIntolerance> {
  // Get the current record first
  const currentRecord = await db.allergyIntolerance.findUnique({
    where: { id },
  });

  if (!currentRecord) {
    throw new Error(`Allergy intolerance with ID ${id} not found`);
  }

  // Only update specific fields that are allowed to be updated
  return db.allergyIntolerance.update({
    where: { id },
    data: {
      clinicalStatus: data.clinicalStatus,
      verificationStatus: data.verificationStatus,
      type: data.type,
      category: data.category,
      criticality: data.criticality,
      code: data.code,
      codeDisplay: data.codeDisplay,
      onsetDateTime: data.onsetDateTime,
      recordedDate: data.recordedDate,
      asserter: data.asserter,
      lastOccurrence: data.lastOccurrence,
      note: data.note,
      // Handle JSON fields carefully
      ...(data.reaction !== undefined
        ? { reaction: data.reaction as any }
        : {}),
    },
  });
}

/**
 * Delete an allergy intolerance record
 */
export async function deleteAllergyIntolerance(
  id: string,
): Promise<AllergyIntolerance> {
  return db.allergyIntolerance.delete({
    where: { id },
  });
}

/**
 * Convert an allergy intolerance record to a FHIR resource
 */
export async function convertToFhirResource(
  allergyId: string,
): Promise<FhirAllergyIntolerance> {
  const allergy = await getAllergyIntoleranceById(allergyId);

  if (!allergy) {
    throw new Error(`Allergy intolerance with ID ${allergyId} not found`);
  }

  return createAllergyIntoleranceResource(
    allergy,
    allergy.patientId,
    allergy.doctorId,
  );
}

/**
 * Store an allergy intolerance record as a FHIR resource
 */
export async function storeAsFhirResource(allergyId: string): Promise<string> {
  const allergy = await getAllergyIntoleranceById(allergyId);

  if (!allergy) {
    throw new Error(`Allergy intolerance with ID ${allergyId} not found`);
  }

  const fhirResource = createAllergyIntoleranceResource(
    allergy,
    allergy.patientId,
    allergy.doctorId,
  );

  // First create a FHIR bundle to contain the resource
  const bundleId = `bundle-${uuidv4()}`;
  const bundle = {
    resourceType: "Bundle",
    id: bundleId,
    type: "collection",
    entry: [
      {
        resource: fhirResource,
      },
    ],
  };

  // Store the FHIR bundle
  await db.fhirBundle.create({
    data: {
      bundleId,
      bundleType: "collection",
      bundleJson: bundle as any,
      patientId: allergy.patientId,
      organizationId: allergy.organizationId,
      status: "completed",
    },
  });

  // Store the FHIR resource
  const storedResource = await db.fhirResource.create({
    data: {
      resourceType: "AllergyIntolerance",
      resourceId: fhirResource.id,
      patientId: allergy.patientId,
      organizationId: allergy.organizationId,
      fhirJson: fhirResource as any,
      sourceType: "allergyIntolerance",
      sourceId: allergy.id,
      version: "4.0.1",
      bundleId, // Link to the bundle
    },
  });

  return storedResource.id;
}
