import { db } from "@/lib/db";
import { createImmunizationResource } from "@/lib/fhir/resources";
import { FhirImmunization } from "@/lib/fhir/types";
import { Immunization } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";

/**
 * Create a new immunization record
 */
export async function createImmunization(data: {
  patientId: string;
  doctorId: string;
  organizationId: string;
  consultationId?: string;
  status: string;
  statusReason?: string;
  vaccineCode: string;
  vaccineDisplay: string;
  occurrenceDateTime: Date;
  recorded: Date;
  primarySource: boolean;
  reportOrigin?: string;
  location?: string;
  manufacturer?: string;
  lotNumber?: string;
  expirationDate?: Date;
  site?: string;
  route?: string;
  doseQuantity?: string;
  performer?: string;
  note?: string;
  reasonCode?: string;
  reasonDisplay?: string;
  isSubpotent?: boolean;
  subpotentReason?: string;
  programEligibility?: string;
  fundingSource?: string;
  reaction?: any;
  protocolApplied?: any;
}): Promise<Immunization> {
  return db.immunization.create({
    data: {
      patientId: data.patientId,
      doctorId: data.doctorId,
      organizationId: data.organizationId,
      consultationId: data.consultationId,
      status: data.status,
      statusReason: data.statusReason,
      vaccineCode: data.vaccineCode,
      vaccineDisplay: data.vaccineDisplay,
      occurrenceDateTime: data.occurrenceDateTime,
      recorded: data.recorded,
      primarySource: data.primarySource,
      reportOrigin: data.reportOrigin,
      location: data.location,
      manufacturer: data.manufacturer,
      lotNumber: data.lotNumber,
      expirationDate: data.expirationDate,
      site: data.site,
      route: data.route,
      doseQuantity: data.doseQuantity,
      performer: data.performer,
      note: data.note,
      reasonCode: data.reasonCode,
      reasonDisplay: data.reasonDisplay,
      isSubpotent: data.isSubpotent,
      subpotentReason: data.subpotentReason,
      programEligibility: data.programEligibility,
      fundingSource: data.fundingSource,
      reaction: data.reaction,
      protocolApplied: data.protocolApplied,
    },
  });
}

/**
 * Get an immunization record by ID
 */
export async function getImmunizationById(
  id: string,
): Promise<Immunization | null> {
  return db.immunization.findUnique({
    where: { id },
    include: {
      patient: {
        select: {
          firstName: true,
          lastName: true,
        },
      },
      doctor: {
        include: {
          user: {
            select: {
              name: true,
            },
          },
        },
      },
    },
  });
}

/**
 * Get immunization records for a patient
 */
export async function getImmunizationsByPatient(
  patientId: string,
): Promise<Immunization[]> {
  return db.immunization.findMany({
    where: { patientId },
    orderBy: { occurrenceDateTime: "desc" },
  });
}

/**
 * Get immunization records for a consultation
 */
export async function getImmunizationsByConsultation(
  consultationId: string,
): Promise<Immunization[]> {
  return db.immunization.findMany({
    where: { consultationId },
    orderBy: { occurrenceDateTime: "desc" },
  });
}

/**
 * Update an immunization record
 */
export async function updateImmunization(
  id: string,
  data: Partial<Immunization>,
): Promise<Immunization> {
  // Get the current record first
  const currentRecord = await db.immunization.findUnique({
    where: { id },
  });

  if (!currentRecord) {
    throw new Error(`Immunization with ID ${id} not found`);
  }

  // Only update specific fields that are allowed to be updated
  return db.immunization.update({
    where: { id },
    data: {
      status: data.status,
      statusReason: data.statusReason,
      vaccineCode: data.vaccineCode,
      vaccineDisplay: data.vaccineDisplay,
      occurrenceDateTime: data.occurrenceDateTime,
      recorded: data.recorded,
      primarySource: data.primarySource,
      reportOrigin: data.reportOrigin,
      location: data.location,
      manufacturer: data.manufacturer,
      lotNumber: data.lotNumber,
      expirationDate: data.expirationDate,
      site: data.site,
      route: data.route,
      doseQuantity: data.doseQuantity,
      performer: data.performer,
      note: data.note,
      reasonCode: data.reasonCode,
      reasonDisplay: data.reasonDisplay,
      isSubpotent: data.isSubpotent,
      subpotentReason: data.subpotentReason,
      programEligibility: data.programEligibility,
      fundingSource: data.fundingSource,
      // Handle JSON fields carefully
      ...(data.reaction !== undefined
        ? { reaction: data.reaction as any }
        : {}),
      ...(data.protocolApplied !== undefined
        ? { protocolApplied: data.protocolApplied as any }
        : {}),
    },
  });
}

/**
 * Delete an immunization record
 */
export async function deleteImmunization(id: string): Promise<Immunization> {
  return db.immunization.delete({
    where: { id },
  });
}

/**
 * Convert an immunization record to a FHIR resource
 */
export async function convertToFhirResource(
  immunizationId: string,
): Promise<FhirImmunization> {
  const immunization = await getImmunizationById(immunizationId);

  if (!immunization) {
    throw new Error(`Immunization with ID ${immunizationId} not found`);
  }

  return createImmunizationResource(
    immunization,
    immunization.patientId,
    immunization.doctorId,
  );
}

/**
 * Store an immunization record as a FHIR resource
 */
export async function storeAsFhirResource(
  immunizationId: string,
): Promise<string> {
  const immunization = await getImmunizationById(immunizationId);

  if (!immunization) {
    throw new Error(`Immunization with ID ${immunizationId} not found`);
  }

  const fhirResource = createImmunizationResource(
    immunization,
    immunization.patientId,
    immunization.doctorId,
  );

  // First create a FHIR bundle to contain the resource
  const bundleId = `bundle-${uuidv4()}`;
  const bundle = {
    resourceType: "Bundle",
    id: bundleId,
    type: "collection",
    entry: [
      {
        resource: fhirResource,
      },
    ],
  };

  // Store the FHIR bundle
  await db.fhirBundle.create({
    data: {
      bundleId,
      bundleType: "collection",
      bundleJson: bundle as any,
      patientId: immunization.patientId,
      organizationId: immunization.organizationId,
      status: "completed",
    },
  });

  // Store the FHIR resource
  const storedResource = await db.fhirResource.create({
    data: {
      resourceType: "Immunization",
      resourceId: fhirResource.id,
      patientId: immunization.patientId,
      organizationId: immunization.organizationId,
      fhirJson: fhirResource as any,
      sourceType: "immunization",
      sourceId: immunization.id,
      version: "4.0.1",
      bundleId, // Link to the bundle
    },
  });

  return storedResource.id;
}
