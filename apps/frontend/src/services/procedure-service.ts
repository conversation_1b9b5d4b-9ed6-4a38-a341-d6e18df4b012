import { db } from "@/lib/db";
import { createProcedureResource } from "@/lib/fhir/resources";
import { FhirProcedure } from "@/lib/fhir/types";
import { Procedure } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";

/**
 * Create a new procedure
 */
export async function createProcedure(data: {
  consultationId: string;
  patientId: string;
  doctorId: string;
  organizationId: string;
  procedureDate: Date;
  status: string;
  category?: string;
  code: string;
  codeDisplay: string;
  bodySite?: string;
  outcome?: string;
  complication?: string;
  followUp?: string;
  notes?: string;
  performer?: string;
  location?: string;
  reasonCode?: string;
  reasonDisplay?: string;
}): Promise<Procedure> {
  return db.procedure.create({
    data: {
      consultationId: data.consultationId,
      patientId: data.patientId,
      doctorId: data.doctorId,
      organizationId: data.organizationId,
      procedureDate: data.procedureDate,
      status: data.status,
      category: data.category,
      code: data.code,
      codeDisplay: data.codeDisplay,
      bodySite: data.bodySite,
      outcome: data.outcome,
      complication: data.complication,
      followUp: data.followUp,
      notes: data.notes,
      performer: data.performer,
      location: data.location,
      reasonCode: data.reasonCode,
      reasonDisplay: data.reasonDisplay,
    },
  });
}

/**
 * Get a procedure by ID
 */
export async function getProcedureById(id: string): Promise<Procedure | null> {
  return db.procedure.findUnique({
    where: { id },
  });
}

/**
 * Get procedures for a patient
 */
export async function getProceduresByPatient(
  patientId: string,
): Promise<Procedure[]> {
  return db.procedure.findMany({
    where: { patientId },
    orderBy: { procedureDate: "desc" },
  });
}

/**
 * Get procedures for a consultation
 */
export async function getProceduresByConsultation(
  consultationId: string,
): Promise<Procedure[]> {
  return db.procedure.findMany({
    where: { consultationId },
    orderBy: { procedureDate: "desc" },
  });
}

/**
 * Update a procedure
 */
export async function updateProcedure(
  id: string,
  data: Partial<Procedure>,
): Promise<Procedure> {
  return db.procedure.update({
    where: { id },
    data,
  });
}

/**
 * Delete a procedure
 */
export async function deleteProcedure(id: string): Promise<Procedure> {
  return db.procedure.delete({
    where: { id },
  });
}

/**
 * Convert a procedure to a FHIR resource
 */
export async function convertToFhirResource(
  procedureId: string,
): Promise<FhirProcedure> {
  const procedure = await getProcedureById(procedureId);

  if (!procedure) {
    throw new Error(`Procedure with ID ${procedureId} not found`);
  }

  return createProcedureResource(
    procedure,
    procedure.patientId,
    procedure.doctorId,
  );
}

/**
 * Store a procedure as a FHIR resource
 */
export async function storeAsFhirResource(
  procedureId: string,
): Promise<string> {
  const procedure = await getProcedureById(procedureId);

  if (!procedure) {
    throw new Error(`Procedure with ID ${procedureId} not found`);
  }

  const fhirResource = createProcedureResource(
    procedure,
    procedure.patientId,
    procedure.doctorId,
  );

  // First create a FHIR bundle to contain the resource
  const bundleId = `bundle-${uuidv4()}`;
  const bundle = {
    resourceType: "Bundle",
    id: bundleId,
    type: "collection",
    entry: [
      {
        resource: fhirResource,
      },
    ],
  };

  // Store the FHIR bundle
  await db.fhirBundle.create({
    data: {
      bundleId,
      bundleType: "collection",
      bundleJson: bundle as any,
      patientId: procedure.patientId,
      organizationId: procedure.organizationId,
      status: "completed",
    },
  });

  // Store the FHIR resource
  const storedResource = await db.fhirResource.create({
    data: {
      resourceType: "Procedure",
      resourceId: fhirResource.id,
      patientId: procedure.patientId,
      organizationId: procedure.organizationId,
      fhirJson: fhirResource as any,
      sourceType: "procedure",
      sourceId: procedure.id,
      version: "4.0.1",
      bundleId, // Link to the bundle
    },
  });

  return storedResource.id;
}
