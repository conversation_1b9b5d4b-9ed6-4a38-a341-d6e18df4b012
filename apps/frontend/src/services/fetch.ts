/**
 * Fetch utility for making API requests
 *
 * This utility provides a standardized way to make API requests with proper error handling,
 * authentication headers, and response parsing.
 */

interface FetchOptions extends RequestInit {
  params?: Record<string, string>;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  status?: number;
  [key: string]: any;
}

export class Fetch {
  /**
   * Make a GET request
   * @param url - The URL to fetch
   * @param options - Additional fetch options
   * @returns The response data
   */
  static async get<T = any>(
    url: string,
    options: FetchOptions = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...options, method: "GET" });
  }

  /**
   * Make a POST request
   * @param url - The URL to fetch
   * @param body - The request body
   * @param options - Additional fetch options
   * @returns The response data
   */
  static async post<T = any>(
    url: string,
    body?: any,
    options: FetchOptions = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      ...options,
      method: "POST",
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  /**
   * Make a PUT request
   * @param url - The URL to fetch
   * @param body - The request body
   * @param options - Additional fetch options
   * @returns The response data
   */
  static async put<T = any>(
    url: string,
    body?: any,
    options: FetchOptions = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      ...options,
      method: "PUT",
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  /**
   * Make a PATCH request
   * @param url - The URL to fetch
   * @param body - The request body
   * @param options - Additional fetch options
   * @returns The response data
   */
  static async patch<T = any>(
    url: string,
    body?: any,
    options: FetchOptions = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      ...options,
      method: "PATCH",
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  /**
   * Make a DELETE request
   * @param url - The URL to fetch
   * @param options - Additional fetch options
   * @returns The response data
   */
  static async delete<T = any>(
    url: string,
    options: FetchOptions = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(url, { ...options, method: "DELETE" });
  }

  /**
   * Make a request with the given options
   * @param url - The URL to fetch
   * @param options - The fetch options
   * @returns The response data
   */
  private static async request<T = any>(
    url: string,
    options: FetchOptions = {},
  ): Promise<ApiResponse<T>> {
    try {
      // Add query parameters if provided
      if (options.params) {
        const params = new URLSearchParams();
        Object.entries(options.params).forEach(([key, value]) => {
          params.append(key, value);
        });
        url = `${url}${url.includes("?") ? "&" : "?"}${params.toString()}`;
      }

      // Set default headers
      const headers = new Headers(options.headers);
      if (
        !headers.has("Content-Type") &&
        options.method !== "GET" &&
        options.body
      ) {
        headers.set("Content-Type", "application/json");
      }

      // Make the request
      const response = await fetch(url, {
        ...options,
        headers,
        credentials: "include", // Include cookies
      });

      // Parse the response
      let data: any;
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      // Return a standardized response
      if (response.ok) {
        return {
          success: true,
          ...data,
          status: response.status,
        };
      } else {
        return {
          success: false,
          error: data.error || response.statusText,
          status: response.status,
          ...data,
        };
      }
    } catch (error) {
      console.error(`Fetch error for ${url}:`, error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "An unknown error occurred",
        status: 0,
      };
    }
  }
}

export default Fetch;
