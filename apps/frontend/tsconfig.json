{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "useDefineForClassFields": true, "moduleDetection": "force", "baseUrl": ".", "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "pages/*": ["./src/pages/*"], "@lib/*": ["./src/lib/*"], "(components)/*": ["./src/(components)/*"], "shared": ["../../packages/shared/src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}