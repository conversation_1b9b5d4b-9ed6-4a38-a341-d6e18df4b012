import React from "react";
import { DocsThemeConfig } from "nextra-theme-docs";

const config: DocsThemeConfig = {
  useNextSeoProps() {
    return {
      titleTemplate: "Aran Care| Docs | %s",
    };
  },
  logo: (
    <div className="flex items-center">
      <span className="font-bold text-xl mr-2">Aran Care</span>
    </div>
  ),
  navigation: {
    prev: true,
    next: true,
  },
  primaryHue: 210,
  search: {
    placeholder: "Search documentation...",
  },
  head: (
    <>
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta
        name="description"
        content="Aran Care Hospital Information Management System Documentation"
      />
      <meta name="og:title" content="Aran Care HIMS Documentation" />
    </>
  ),
  footer: {
    text: (
      <div className="flex w-full justify-between items-center">
        <div>© {new Date().getFullYear()} Aran Care HIMS</div>
      </div>
    ),
  },
  sidebar: {
    defaultMenuCollapseLevel: 1,
    titleComponent: ({ title, type }) => {
      if (type === "separator") {
        return <div className="cursor-default">{title}</div>;
      }
      return <>{title}</>;
    },
  },
  editLink: {
    component: null,
  },
  feedback: {
    content: null,
  },
};

export default config;
