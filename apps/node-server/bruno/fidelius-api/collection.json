{"name": "Fidelius API", "version": "1", "description": "API collection for Fidelius API", "variables": {"baseUrl": "http://localhost:8000", "apiKey": "your-secure-api-key"}, "folders": [{"name": "Health", "items": [{"name": "Basic Health Check", "request": {"method": "GET", "url": "{{baseUrl}}/fidelius-api/", "headers": [{"name": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}"}]}}, {"name": "Detailed Health Check", "request": {"method": "GET", "url": "{{baseUrl}}/fidelius-api/health", "headers": [{"name": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}"}]}}]}, {"name": "Cryptography", "items": [{"name": "Generate Key Material", "request": {"method": "POST", "url": "{{baseUrl}}/fidelius-api/generate-key-material", "headers": [{"name": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}"}, {"name": "Content-Type", "value": "application/json"}]}}, {"name": "Encrypt Data", "request": {"method": "POST", "url": "{{baseUrl}}/fidelius-api/encrypt", "headers": [{"name": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}"}, {"name": "Content-Type", "value": "application/json"}], "body": {"mode": "json", "json": {"string_to_encrypt": "Hello, <PERSON>!", "sender_nonce": "1234567890abcdef", "requester_nonce": "abcdef1234567890", "sender_private_key": "AQiHPRXYr47YKqSSwkeSsLFb0sDx2bhzIxewMljPeG8=", "requester_public_key": "BGZNLrq5YDC0MRAHyRPvA9Nqud1v3Zm8WpEPQn6MYLV3NTsz3r1KIXuPCICs2xNVK3pnbzC2sjmSBvSMqjaPA1w="}}}}, {"name": "Decrypt Data", "request": {"method": "POST", "url": "{{baseUrl}}/fidelius-api/decrypt", "headers": [{"name": "X-API-Key", "value": "{{a<PERSON><PERSON><PERSON>}}"}, {"name": "Content-Type", "value": "application/json"}], "body": {"mode": "json", "json": {"encrypted_data": "eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiRUNESC1FUyIsImVwayI6eyJrdHkiOiJFQyIsImNydiI6IlAtMjU2Iiwi...", "requester_nonce": "abcdef1234567890", "sender_nonce": "1234567890abcdef", "requester_private_key": "AQiHPRXYr47YKqSSwkeSsLFb0sDx2bhzIxewMljPeG8=", "sender_public_key": "BGZNLrq5YDC0MRAHyRPvA9Nqud1v3Zm8WpEPQn6MYLV3NTsz3r1KIXuPCICs2xNVK3pnbzC2sjmSBvSMqjaPA1w="}}}}]}]}