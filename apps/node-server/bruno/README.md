# Bruno API Collections

This directory contains [<PERSON>](https://www.usebruno.com/) API collections for testing the Node Server APIs.

## Getting Started

1. Install Bruno: https://www.usebruno.com/downloads
2. Open Bruno and click "Open Collection"
3. Navigate to this directory and select the collection you want to open

## Collections

### Fidelius API

The Fidelius API collection contains requests for testing the Fidelius API endpoints:

- **Health**: Basic and detailed health check endpoints
- **Cryptography**: Generate key material, encrypt data, and decrypt data

## Environments

The following environments are available:

- **Local**: For testing against a local development server
- **Production**: For testing against the production server

## Usage

1. Select the environment you want to use from the dropdown in the top right corner
2. Update the environment variables as needed (e.g., API key)
3. Select a request from the collection
4. Click "Send" to execute the request

## Variables

The following variables are available in the environments:

- **baseUrl**: The base URL of the API server
- **apiKey**: The API key for authentication

## Example Workflow

1. Generate key material using the "Generate Key Material" request
2. Copy the generated key material values (private<PERSON>ey, publicKey, nonce)
3. Update the "Encrypt Data" request with the key material values
4. Execute the "Encrypt Data" request
5. <PERSON>py the encrypted data from the response
6. Update the "Decrypt Data" request with the encrypted data and key material values
7. Execute the "Decrypt Data" request to verify the decryption works
