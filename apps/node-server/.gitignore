# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/
*/node_modules/

# dotenv environment variable files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*/.env
*/.env.*

# Coverage directory used by tools like istanbul
coverage
*/coverage

# nyc test coverage
.nyc_output
*/.nyc_output

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache
*/.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# OS specific files
.DS_Store
Thumbs.db
