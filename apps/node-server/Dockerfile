FROM node:18-slim

# Install JRE (default version)
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    default-jre-headless \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy Fidelius CLI
COPY ./fidelius-cli/ ./fidelius-cli/

# Make Fidelius CLI executable
RUN chmod +x ./fidelius-cli/bin/fidelius-cli

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY ./src/ ./src/

# Create logs directory
RUN mkdir -p logs

# Set environment variables
ENV NODE_ENV=production \
    PORT=8000 \
    HOST=0.0.0.0 \
    FIDELIUS_PATH=/app/fidelius-cli/bin/fidelius-cli

# Expose port
EXPOSE 8000

# Start the application
CMD ["node", "src/index.js"]
