/**
 * Socket.io event handlers
 * 
 * @param {SocketIO.Server} io - Socket.io server instance
 * @param {Object} logger - Winston logger instance
 */
const socketHandlers = (io, logger) => {
  // Add middleware to track connections
  let connectionCount = 0;
  let activeConnections = new Map();

  io.use((socket, next) => {
    const clientIp =
      socket.handshake.headers["x-forwarded-for"] || socket.handshake.address;

    logger.info(`New connection attempt from ${clientIp} (${socket.id})`);
    next();
  });

  // Socket.io event handlers
  io.on("connection", (socket) => {
    // Track connection
    connectionCount++;
    activeConnections.set(socket.id, {
      id: socket.id,
      connectedAt: new Date(),
      ip: socket.handshake.headers["x-forwarded-for"] || socket.handshake.address,
      userAgent: socket.handshake.headers["user-agent"],
      rooms: [],
    });

    logger.info(`Client connected: ${socket.id} (Total: ${connectionCount})`);

    // Join organization and branch rooms for scoped updates
    socket.on("join-organization", (organizationId) => {
      try {
        if (!organizationId) {
          logger.warn(
            `Socket ${socket.id} attempted to join organization with invalid ID`
          );
          return;
        }

        const roomName = `org:${organizationId}`;
        socket.join(roomName);

        // Update connection tracking
        const connection = activeConnections.get(socket.id);
        if (connection) {
          connection.rooms.push(roomName);
          activeConnections.set(socket.id, connection);
        }

        logger.info(`Socket ${socket.id} joined organization ${organizationId}`);
      } catch (error) {
        logger.error(`Error joining organization room: ${error.message}`, error);
      }
    });

    socket.on("join-branch", (branchId) => {
      try {
        if (!branchId) {
          logger.warn(
            `Socket ${socket.id} attempted to join branch with invalid ID`
          );
          return;
        }

        const roomName = `branch:${branchId}`;
        socket.join(roomName);

        // Update connection tracking
        const connection = activeConnections.get(socket.id);
        if (connection) {
          connection.rooms.push(roomName);
          activeConnections.set(socket.id, connection);
        }

        logger.info(`Socket ${socket.id} joined branch ${branchId}`);
      } catch (error) {
        logger.error(`Error joining branch room: ${error.message}`, error);
      }
    });

    socket.on("join-doctor", (doctorId) => {
      try {
        if (!doctorId) {
          logger.warn(
            `Socket ${socket.id} attempted to join doctor with invalid ID`
          );
          return;
        }

        const roomName = `doctor:${doctorId}`;
        socket.join(roomName);

        // Update connection tracking
        const connection = activeConnections.get(socket.id);
        if (connection) {
          connection.rooms.push(roomName);
          activeConnections.set(socket.id, connection);
        }

        logger.info(`Socket ${socket.id} joined doctor ${doctorId}`);
      } catch (error) {
        logger.error(`Error joining doctor room: ${error.message}`, error);
      }
    });

    // Handle queue status updates
    socket.on("queue-status-update", (data) => {
      try {
        // Validate data
        if (!data || !data.organizationId || !data.branchId) {
          logger.warn(
            `Socket ${
              socket.id
            } sent invalid queue status update: ${JSON.stringify(data)}`
          );
          return;
        }

        // Add timestamp for tracking latency
        const timestamp = Date.now();
        const enhancedData = {
          ...data,
          _meta: {
            timestamp,
            socketId: socket.id,
          },
        };

        // Broadcast to specific rooms for targeted updates
        // 1. Organization-wide broadcast
        io.to(`org:${data.organizationId}`).emit(
          "queue-status-updated",
          enhancedData
        );

        // 2. Branch-specific broadcast
        io.to(`branch:${data.branchId}`).emit(
          "queue-status-updated",
          enhancedData
        );

        // 3. Doctor-specific broadcast (if doctorId is provided)
        if (data.doctorId) {
          io.to(`doctor:${data.doctorId}`).emit(
            "queue-status-updated",
            enhancedData
          );
        }

        logger.info(
          `Queue status update from ${socket.id} for org:${data.organizationId}, branch:${data.branchId}${
            data.doctorId ? `, doctor:${data.doctorId}` : ""
          }`
        );
      } catch (error) {
        logger.error(`Error handling queue status update: ${error.message}`, error);
      }
    });

    socket.on("disconnect", (reason) => {
      // Update connection tracking
      connectionCount = Math.max(0, connectionCount - 1);
      activeConnections.delete(socket.id);

      logger.info(
        `Client disconnected: ${socket.id} (Reason: ${reason}) (Total: ${connectionCount})`
      );
    });
  });

  // Return functions to access socket server state
  return {
    getConnectionCount: () => connectionCount,
    getActiveConnections: () => Array.from(activeConnections.values()),
    getRooms: () => Array.from(io.sockets.adapter.rooms.keys())
      .filter((room) => !room.includes("/"))
      .map((room) => ({
        name: room,
        clients: io.sockets.adapter.rooms.get(room)?.size || 0,
      })),
  };
};

module.exports = socketHandlers;
