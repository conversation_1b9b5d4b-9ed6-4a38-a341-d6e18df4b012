/**
 * @swagger
 * tags:
 *   name: Socket.io
 *   description: Socket.io event documentation
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     QueueStatusUpdate:
 *       type: object
 *       required:
 *         - organizationId
 *         - branchId
 *       properties:
 *         organizationId:
 *           type: string
 *           description: Organization ID
 *           example: org_123456
 *         branchId:
 *           type: string
 *           description: Branch ID
 *           example: branch_123456
 *         doctorId:
 *           type: string
 *           description: Doctor ID (optional)
 *           example: doctor_123456
 *         status:
 *           type: string
 *           description: Queue status
 *           enum: [waiting, in-progress, completed, cancelled]
 *           example: waiting
 *         position:
 *           type: integer
 *           description: Position in queue
 *           example: 1
 *     QueueStatusUpdated:
 *       type: object
 *       properties:
 *         organizationId:
 *           type: string
 *           description: Organization ID
 *           example: org_123456
 *         branchId:
 *           type: string
 *           description: Branch ID
 *           example: branch_123456
 *         doctorId:
 *           type: string
 *           description: Doctor ID (optional)
 *           example: doctor_123456
 *         status:
 *           type: string
 *           description: Queue status
 *           enum: [waiting, in-progress, completed, cancelled]
 *           example: waiting
 *         position:
 *           type: integer
 *           description: Position in queue
 *           example: 1
 *         _meta:
 *           type: object
 *           properties:
 *             timestamp:
 *               type: integer
 *               description: Timestamp of the update
 *               example: 1620000000000
 *             socketId:
 *               type: string
 *               description: Socket ID of the sender
 *               example: 1234567890
 */

/**
 * @swagger
 * /socket.io:
 *   get:
 *     summary: Socket.io endpoint
 *     description: |
 *       This is the Socket.io endpoint. You can connect to this endpoint using a Socket.io client.
 *       
 *       ## Client Events (emit)
 *       
 *       ### join-organization
 *       Join an organization room to receive organization-wide updates.
 *       ```javascript
 *       socket.emit('join-organization', 'organizationId');
 *       ```
 *       
 *       ### join-branch
 *       Join a branch room to receive branch-specific updates.
 *       ```javascript
 *       socket.emit('join-branch', 'branchId');
 *       ```
 *       
 *       ### join-doctor
 *       Join a doctor room to receive doctor-specific updates.
 *       ```javascript
 *       socket.emit('join-doctor', 'doctorId');
 *       ```
 *       
 *       ### queue-status-update
 *       Send a queue status update.
 *       ```javascript
 *       socket.emit('queue-status-update', {
 *         organizationId: 'organizationId',
 *         branchId: 'branchId',
 *         doctorId: 'doctorId',
 *         status: 'waiting',
 *         position: 1
 *       });
 *       ```
 *       
 *       ## Server Events (on)
 *       
 *       ### queue-status-updated
 *       Listen for queue status updates.
 *       ```javascript
 *       socket.on('queue-status-updated', (data) => {
 *         console.log('Queue status updated:', data);
 *       });
 *       ```
 *     tags: [Socket.io]
 *     responses:
 *       200:
 *         description: Socket.io endpoint
 */

// This file is for documentation purposes only
module.exports = {};
