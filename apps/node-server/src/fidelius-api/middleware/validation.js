const Joi = require('joi');
const { logger } = require('../../utils/logger');

// Validation schemas
const schemas = {
  encrypt: Joi.object({
    string_to_encrypt: Joi.string().max(50 * 1024 * 1024).required(), // Allow up to 50MB strings
    sender_nonce: Joi.string().required(),
    requester_nonce: Joi.string().required(),
    sender_private_key: Joi.string().required(),
    requester_public_key: Joi.string().required()
  }),

  decrypt: Joi.object({
    encrypted_data: Joi.string().max(50 * 1024 * 1024).required(), // Allow up to 50MB strings
    requester_nonce: Joi.string().required(),
    sender_nonce: Joi.string().required(),
    requester_private_key: Joi.string().required(),
    sender_public_key: Joi.string().required()
  })
};

/**
 * Validate request body against schema
 * 
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} - Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      logger.error(`Validation schema '${schemaName}' not found`);
      return res.status(500).json({
        success: false,
        error: 'Server configuration error'
      });
    }
    
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      logger.warn(`Validation error: ${error.message}`, { error });
      return res.status(400).json({
        success: false,
        error: error.details.map(x => x.message).join(', ')
      });
    }
    
    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = { validateRequest };
