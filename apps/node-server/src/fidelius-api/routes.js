const express = require('express');
const router = express.Router();
const { validateRequest } = require('./middleware/validation');
const {
  healthCheck,
  generateKeyMaterial,
  encrypt,
  decrypt
} = require('./controllers');

/**
 * @swagger
 * /fidelius-api:
 *   get:
 *     summary: Basic health check for Fidelius API
 *     tags: [Health]
 *     security:
 *       - apiKey: []
 *     responses:
 *       200:
 *         description: API is running
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: Fidelius API is running
 */
router.get('/', (req, res) => {
  res.json({ status: 'Fidelius API is running' });
});

/**
 * @swagger
 * /fidelius-api/health:
 *   get:
 *     summary: Detailed health check for Fidelius API
 *     tags: [Health]
 *     security:
 *       - apiKey: []
 *     responses:
 *       200:
 *         description: Detailed health status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy]
 *                   example: healthy
 *                 fidelius_cli:
 *                   type: object
 *                   properties:
 *                     path:
 *                       type: string
 *                       example: /app/fidelius-cli/bin/fidelius-cli
 *                     exists:
 *                       type: boolean
 *                       example: true
 *                     executable:
 *                       type: boolean
 *                       example: true
 *                     version:
 *                       type: object
 *                       example: null
 *                     error:
 *                       type: string
 *                       example: null
 */
router.get('/health', healthCheck);

/**
 * @swagger
 * /fidelius-api/generate-key-material:
 *   post:
 *     summary: Generate cryptographic key material
 *     description: Generates an ECDH key pair and a random nonce for cryptographic operations
 *     tags: [Fidelius]
 *     security:
 *       - apiKey: []
 *     responses:
 *       200:
 *         description: Key material generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     privateKey:
 *                       type: string
 *                       example: AQiHPRXYr47YKqSSwkeSsLFb0sDx2bhzIxewMljPeG8=
 *                     publicKey:
 *                       type: string
 *                       example: BGZNLrq5YDC0MRAHyRPvA9Nqud1v3Zm8WpEPQn6MYLV3NTsz3r1KIXuPCICs2xNVK3pnbzC2sjmSBvSMqjaPA1w=
 *                     x509PublicKey:
 *                       type: string
 *                       example: MIIBMTCB6gYHKoZIzj0CATCB3gIBATArBgcqhkjOPQEBAiB...
 *                     nonce:
 *                       type: string
 *                       example: 1234567890abcdef
 *                 error:
 *                   type: string
 *                   nullable: true
 *                   example: null
 */
router.post('/generate-key-material', generateKeyMaterial);

/**
 * @swagger
 * /fidelius-api/encrypt:
 *   post:
 *     summary: Encrypt data
 *     description: Encrypts data using the Fidelius CLI
 *     tags: [Fidelius]
 *     security:
 *       - apiKey: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - string_to_encrypt
 *               - sender_nonce
 *               - requester_nonce
 *               - sender_private_key
 *               - requester_public_key
 *             properties:
 *               string_to_encrypt:
 *                 type: string
 *                 example: Hello, World!
 *               sender_nonce:
 *                 type: string
 *                 example: 1234567890abcdef
 *               requester_nonce:
 *                 type: string
 *                 example: abcdef1234567890
 *               sender_private_key:
 *                 type: string
 *                 example: AQiHPRXYr47YKqSSwkeSsLFb0sDx2bhzIxewMljPeG8=
 *               requester_public_key:
 *                 type: string
 *                 example: BGZNLrq5YDC0MRAHyRPvA9Nqud1v3Zm8WpEPQn6MYLV3NTsz3r1KIXuPCICs2xNVK3pnbzC2sjmSBvSMqjaPA1w=
 *     responses:
 *       200:
 *         description: Data encrypted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     encryptedData:
 *                       type: string
 *                       example: eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiRUNESC1FUyIsImVwayI6eyJrdHkiOiJFQyIsImNydiI6IlAtMjU2Iiwi...
 *                 error:
 *                   type: string
 *                   nullable: true
 *                   example: null
 */
router.post('/encrypt', validateRequest('encrypt'), encrypt);

/**
 * @swagger
 * /fidelius-api/decrypt:
 *   post:
 *     summary: Decrypt data
 *     description: Decrypts data using the Fidelius CLI
 *     tags: [Fidelius]
 *     security:
 *       - apiKey: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - encrypted_data
 *               - requester_nonce
 *               - sender_nonce
 *               - requester_private_key
 *               - sender_public_key
 *             properties:
 *               encrypted_data:
 *                 type: string
 *                 example: eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiRUNESC1FUyIsImVwayI6eyJrdHkiOiJFQyIsImNydiI6IlAtMjU2Iiwi...
 *               requester_nonce:
 *                 type: string
 *                 example: abcdef1234567890
 *               sender_nonce:
 *                 type: string
 *                 example: 1234567890abcdef
 *               requester_private_key:
 *                 type: string
 *                 example: AQiHPRXYr47YKqSSwkeSsLFb0sDx2bhzIxewMljPeG8=
 *               sender_public_key:
 *                 type: string
 *                 example: BGZNLrq5YDC0MRAHyRPvA9Nqud1v3Zm8WpEPQn6MYLV3NTsz3r1KIXuPCICs2xNVK3pnbzC2sjmSBvSMqjaPA1w=
 *     responses:
 *       200:
 *         description: Data decrypted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     decryptedData:
 *                       type: string
 *                       example: Hello, World!
 *                 error:
 *                   type: string
 *                   nullable: true
 *                   example: null
 */
router.post('/decrypt', validateRequest('decrypt'), decrypt);

module.exports = router;
