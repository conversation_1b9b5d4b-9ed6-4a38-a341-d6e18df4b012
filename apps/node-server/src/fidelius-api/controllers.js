const fs = require('fs');
const path = require('path');
const { logger } = require('../utils/logger');
const { runFideliusCommand } = require('./utils/fidelius');

/**
 * Health check endpoint
 */
const healthCheck = async (req, res) => {
  try {
    // Get Fidelius CLI path from environment or use default
    const fideliusPath = process.env.FIDELIUS_PATH || 
      path.join(__dirname, '../../fidelius-cli/bin/fidelius-cli');
    
    // Check if Fidelius CLI exists and is executable
    const exists = fs.existsSync(fideliusPath);
    let executable = false;
    
    try {
      fs.accessSync(fideliusPath, fs.constants.X_OK);
      executable = true;
    } catch (err) {
      executable = false;
    }
    
    // Try to execute a simple command to verify the CLI works
    const { success, data, error } = await runFideliusCommand('--version');
    
    return res.json({
      status: success ? 'healthy' : 'degraded',
      fidelius_cli: {
        path: fideliusPath,
        exists,
        executable,
        version: success ? data : null,
        error: error
      }
    });
  } catch (error) {
    logger.error(`Health check failed: ${error.message}`, { error });
    return res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
};

/**
 * Generate key material using Fidelius CLI
 */
const generateKeyMaterial = async (req, res) => {
  try {
    const { success, data, error } = await runFideliusCommand('gkm');
    
    return res.json({
      success,
      data,
      error
    });
  } catch (error) {
    logger.error(`Failed to generate key material: ${error.message}`, { error });
    return res.status(500).json({
      success: false,
      data: null,
      error: error.message
    });
  }
};

/**
 * Encrypt data using Fidelius CLI
 */
const encrypt = async (req, res) => {
  try {
    const {
      string_to_encrypt,
      sender_nonce,
      requester_nonce,
      sender_private_key,
      requester_public_key
    } = req.body;
    
    const args = [
      string_to_encrypt,
      sender_nonce,
      requester_nonce,
      sender_private_key,
      requester_public_key
    ];
    
    const { success, data, error } = await runFideliusCommand('e', args);
    
    return res.json({
      success,
      data,
      error
    });
  } catch (error) {
    logger.error(`Failed to encrypt data: ${error.message}`, { error });
    return res.status(500).json({
      success: false,
      data: null,
      error: error.message
    });
  }
};

/**
 * Decrypt data using Fidelius CLI
 */
const decrypt = async (req, res) => {
  try {
    const {
      encrypted_data,
      requester_nonce,
      sender_nonce,
      requester_private_key,
      sender_public_key
    } = req.body;
    
    const args = [
      encrypted_data,
      requester_nonce,
      sender_nonce,
      requester_private_key,
      sender_public_key
    ];
    
    const { success, data, error } = await runFideliusCommand('d', args);
    
    return res.json({
      success,
      data,
      error
    });
  } catch (error) {
    logger.error(`Failed to decrypt data: ${error.message}`, { error });
    return res.status(500).json({
      success: false,
      data: null,
      error: error.message
    });
  }
};

module.exports = {
  healthCheck,
  generateKeyMaterial,
  encrypt,
  decrypt
};
