const { execFile } = require('child_process');
const path = require('path');
const { logger } = require('../../utils/logger');

/**
 * Run a Fidelius CLI command and return the result
 * 
 * @param {string} command - Command to run (e.g., 'gkm', 'e', 'd')
 * @param {Array} args - List of arguments to pass to the command
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise<Object>} - Promise resolving to { success, data, error }
 */
const runFideliusCommand = async (command, args = [], timeout = 60000) => {
  // Get Fidelius CLI path from environment or use default
  const fideliusPath = process.env.FIDELIUS_PATH ||
    path.join(__dirname, '../../../fidelius-cli/bin/fidelius-cli');

  // Prepare command arguments
  const cmdArgs = [command, ...(args || [])];

  logger.info(`Running Fidelius command: ${fideliusPath} ${cmdArgs.join(' ')}`);

  return new Promise((resolve) => {
    // Increase buffer sizes for large JSON payloads
    const options = {
      timeout,
      maxBuffer: 50 * 1024 * 1024, // 50MB buffer for stdout
      killSignal: 'SIGKILL'
    };

    execFile(fideliusPath, cmdArgs, options, (error, stdout, stderr) => {
      if (error) {
        logger.error(`Fidelius CLI error: ${error.message}`, {
          error,
          stderr: stderr?.substring(0, 1000) // Log first 1000 chars of stderr
        });
        return resolve({
          success: false,
          data: null,
          error: stderr || error.message
        });
      }

      try {
        // Parse the JSON output
        const data = JSON.parse(stdout);
        logger.info(`Fidelius command ${command} executed successfully`, {
          outputSize: stdout.length
        });

        return resolve({
          success: true,
          data,
          error: null
        });
      } catch (parseError) {
        logger.error(`Failed to parse Fidelius CLI output: ${parseError.message}`, {
          parseError,
          stdoutLength: stdout?.length,
          stdoutPreview: stdout?.substring(0, 500) // Log first 500 chars
        });

        return resolve({
          success: false,
          data: null,
          error: `Failed to parse Fidelius CLI output: ${parseError.message}`
        });
      }
    });
  });
};

/**
 * Check if Fidelius CLI is available and executable
 * 
 * @returns {Promise<boolean>} - Promise resolving to true if Fidelius CLI is available
 */
const checkFideliusCli = async () => {
  try {
    const fideliusPath = process.env.FIDELIUS_PATH || 
      path.join(__dirname, '../../../fidelius-cli/bin/fidelius-cli');
    
    const { success } = await runFideliusCommand('--version');
    return success;
  } catch (error) {
    logger.error(`Failed to check Fidelius CLI: ${error.message}`, { error });
    return false;
  }
};

module.exports = {
  runFideliusCommand,
  checkFideliusCli
};
