const { logger } = require('../utils/logger');

/**
 * API Key authentication middleware
 */
const apiKeyAuth = (req, res, next) => {
  // For development purposes, bypass authentication
  // In production, you would want to enable this
  return next();

  // Get API key from environment
  const validApiKey = process.env.API_KEY;

  if (!validApiKey) {
    logger.error('API_KEY environment variable is not set');
    return res.status(500).json({
      success: false,
      error: 'Server configuration error'
    });
  }

  // Get API key from request header
  const apiKey = req.headers['x-api-key'];

  if (!apiKey) {
    logger.warn('API key missing in request');
    return res.status(401).json({
      success: false,
      error: 'API key is required'
    });
  }

  if (apiKey !== validApiKey) {
    logger.warn('Invalid API key provided');
    return res.status(401).json({
      success: false,
      error: 'Invalid API key'
    });
  }

  // API key is valid, proceed to the next middleware
  next();
};

module.exports = { apiKeyAuth };
