require('dotenv').config();
const express = require('express');
const http = require('http');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { Server } = require('socket.io');
const rateLimit = require('express-rate-limit');
const { logger } = require('./utils/logger');
const { apiKeyAuth } = require('./middleware/auth');
const { setupSwagger } = require('./utils/swagger');
const fideliusRoutes = require('./fidelius-api/routes');
const socketHandlers = require('./socket-server/handlers');

// Create Express app
const app = express();

// Create HTTP server
const server = http.createServer(app);

// Create Socket.io server
const io = new Server(server, {
  cors: {
    origin: "*", // Allow all origins for testing
    methods: ["GET", "POST"],
    credentials: true,
  },
  pingTimeout: 60000, // 60 seconds
  maxHttpBufferSize: 1e6, // 1MB
});

// Get configuration from environment variables
const PORT = process.env.PORT || 8000;
const HOST = process.env.HOST || '0.0.0.0';
const CORS_ORIGINS = process.env.CORS_ORIGINS
  ? process.env.CORS_ORIGINS.split(',')
  : ['http://localhost:3000', 'http://localhost:3005'];
const NODE_ENV = process.env.NODE_ENV || 'development';
const ENABLE_API_KEY_AUTH = process.env.ENABLE_API_KEY_AUTH === 'true';

// Log server configuration
logger.info('Server starting with configuration:');
logger.info(`- Environment: ${NODE_ENV}`);
logger.info(`- Port: ${PORT}`);
logger.info(`- Host: ${HOST}`);
logger.info(`- Allowed Origins: ${CORS_ORIGINS.join(', ')}`);
logger.info(`- API Key Auth: ${ENABLE_API_KEY_AUTH ? 'Enabled' : 'Disabled'}`);

// Apply middleware
app.use(helmet()); // Security headers
app.use(cors({
  origin: '*', // Allow all origins for testing
  credentials: true,
}));
app.use(express.json({ limit: '50mb' })); // Parse JSON request body with increased limit for FHIR bundles
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } })); // HTTP request logging

// Apply rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Setup Swagger documentation first (before authentication)
setupSwagger(app);

// Apply API key authentication for Fidelius API routes if enabled
if (ENABLE_API_KEY_AUTH) {
  logger.info('API Key authentication is enabled for Fidelius API routes');
  app.use('/fidelius-api', apiKeyAuth);
} else {
  logger.warn('API Key authentication is disabled for Fidelius API routes');
}

// Register routes
app.use('/fidelius-api', fideliusRoutes);

// Home route
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>Node Server</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1 { color: #333; }
          h2 { color: #666; margin-top: 30px; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
          .endpoint { background: #e9f7fe; padding: 10px; border-radius: 5px; margin-bottom: 10px; }
        </style>
      </head>
      <body>
        <h1>Node Server</h1>
        <p>Combined Node.js server with Fidelius API and Socket.io functionality</p>

        <h2>API Documentation</h2>
        <p><a href="/api-docs" style="display: inline-block; background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">Swagger API Documentation</a></p>

        <h2>Fidelius API Endpoints</h2>
        <div class="endpoint">GET /fidelius-api/health - Health check endpoint</div>
        <div class="endpoint">POST /fidelius-api/generate-key-material - Generate cryptographic key material</div>
        <div class="endpoint">POST /fidelius-api/encrypt - Encrypt data</div>
        <div class="endpoint">POST /fidelius-api/decrypt - Decrypt data</div>

        <h2>Socket.io Events</h2>
        <pre>
// Connect to the socket server
const socket = io('http://${req.headers.host}');

// Join rooms
socket.emit('join-organization', 'organizationId');
socket.emit('join-branch', 'branchId');
socket.emit('join-doctor', 'doctorId');

// Send queue updates
socket.emit('queue-status-update', {
  organizationId: 'organizationId',
  branchId: 'branchId',
  doctorId: 'doctorId',
  status: 'waiting',
  position: 1
});

// Listen for queue updates
socket.on('queue-status-updated', (data) => {
  console.log('Queue status updated:', data);
});
        </pre>
      </body>
    </html>
  `);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    services: {
      fidelius: true,
      socket: true
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`Error: ${err.message}`, { error: err });

  res.status(err.status || 500).json({
    success: false,
    error: NODE_ENV === 'production' ? 'Internal Server Error' : err.message
  });
});

// Initialize Socket.io handlers
socketHandlers(io, logger);

// Start server
server.listen(PORT, HOST, () => {
  logger.info(`Node Server running at http://${HOST}:${PORT}`);
  logger.info(`Fidelius API available at http://${HOST}:${PORT}/fidelius-api`);
  logger.info(`Socket.io server available at http://${HOST}:${PORT}`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

// Export app for testing
module.exports = { app, server, io };
