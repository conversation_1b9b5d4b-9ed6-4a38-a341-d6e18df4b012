const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const { version } = require('../../package.json');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Node Server API Documentation',
      version,
      description: 'API documentation for the Node Server, including Fidelius API and Socket.io functionality',
      license: {
        name: 'Private',
      },
      contact: {
        name: 'Aran Care',
        url: 'https://arancare.com',
      },
    },
    servers: [
      {
        url: 'http://localhost:8000',
        description: 'Development server',
      },
      {
        url: 'https://api.healthcare.flinkk.io',
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API key for authentication',
        },
      },
    },
    security: [
      {
        apiKey: [],
      },
    ],
    tags: [
      {
        name: 'Health',
        description: 'Health check endpoints',
      },
      {
        name: 'Fidelius',
        description: 'Fidelius API endpoints for cryptographic operations',
      },
      {
        name: 'Socket.io',
        description: 'Socket.io event documentation',
      },
    ],
  },
  apis: ['./src/**/*.js'], // Path to the API docs
};

const specs = swaggerJsdoc(options);

/**
 * Configure Swagger UI for Express
 *
 * @param {Express} app - Express application
 */
const setupSwagger = (app) => {
  // Get API key from environment
  const apiKey = process.env.API_KEY || 'your-secure-api-key';

  // Serve Swagger UI
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Node Server API Documentation',
    swaggerOptions: {
      persistAuthorization: true,
      initOAuth: {
        clientId: "your-client-id",
        clientSecret: "your-client-secret",
        realm: "your-realms",
        appName: "Fidelius API",
        scopeSeparator: " ",
        scopes: "openid profile email",
        usePkceWithAuthorizationCodeGrant: true
      },
      onComplete: function() {
        // Pre-authorize with API key
        this.authActions.authorize({
          apiKey: {
            value: apiKey
          }
        });
      }
    }
  }));

  // Serve Swagger JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });
};

module.exports = { setupSwagger };
