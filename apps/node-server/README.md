# Node Server

A combined Node.js server application that includes both Fidelius API and Socket.io functionality.

## Features

- **Fidelius API**: RESTful API for Fidelius CLI cryptography operations
- **Socket.io Server**: Real-time communication for queue updates
- Secure API key authentication
- Docker containerization
- Comprehensive logging
- Health check endpoints
- Swagger API documentation
- Bruno API collections

## API Endpoints

### Fidelius API

- `GET /fidelius-api/`: Health check endpoint
- `GET /fidelius-api/health`: Detailed health check endpoint
- `POST /fidelius-api/generate-key-material`: Generate cryptographic key material
- `POST /fidelius-api/encrypt`: Encrypt data
- `POST /fidelius-api/decrypt`: Decrypt data

### Socket.io Events

- `join-organization`: Join an organization room
- `join-branch`: Join a branch room
- `join-doctor`: Join a doctor room
- `queue-status-update`: Update queue status
- `queue-status-updated`: Notifies clients of queue status updates

## Local Development

### Prerequisites

- Node.js 18 or higher
- JRE 1.8 or higher (for Fidelius CLI)
- Docker (optional, for containerized development)

### Setup

1. Clone the repository
2. Install dependencies:

```bash
cd apps/node-server
npm install
```

3. Make sure Fidelius CLI is available and executable:

```bash
chmod +x fidelius-cli/bin/fidelius-cli
```

4. Create a `.env` file with the following variables:

```
HOST=0.0.0.0
PORT=8000
FIDELIUS_PATH=./fidelius-cli/bin/fidelius-cli
CORS_ORIGINS=http://localhost:3000,http://localhost:3005
API_KEY=your-secure-api-key
ENABLE_API_KEY_AUTH=true
LOG_LEVEL=info
NODE_ENV=development
```

5. Run the application:

```bash
npm run dev
```

The server will be available at [http://localhost:8000](http://localhost:8000).

### Using Docker

1. Build the Docker image:

```bash
docker build -t node-server .
```

2. Run the Docker container:

```bash
docker run -p 8000:8000 -e API_KEY=your-secure-api-key -e ENABLE_API_KEY_AUTH=true node-server
```

## Deployment to VM

### Prerequisites

- VM with Node.js 18 or higher installed
- JRE 1.8 or higher installed
- Git (for cloning the repository)

### Deployment Steps

1. Clone the repository on the VM:

```bash
git clone <repository-url>
cd <repository-directory>/apps/node-server
```

2. Install dependencies:

```bash
npm ci --only=production
```

3. Create a `.env` file with your production configuration:

```
HOST=0.0.0.0
PORT=8000
FIDELIUS_PATH=./fidelius-cli/bin/fidelius-cli
CORS_ORIGINS=https://your-frontend-domain.com
API_KEY=your-secure-api-key
ENABLE_API_KEY_AUTH=true
LOG_LEVEL=info
NODE_ENV=production
```

4. Make sure Fidelius CLI is executable:

```bash
chmod +x fidelius-cli/bin/fidelius-cli
```

5. Start the application:

```bash
npm start
```

6. (Optional) Set up a process manager like PM2:

```bash
npm install -g pm2
pm2 start src/index.js --name node-server
pm2 save
pm2 startup
```

## API Documentation

### Swagger UI

The Node Server includes Swagger UI for API documentation. Once the server is running, you can access the Swagger UI at:

```
http://localhost:8000/api-docs
```

The Swagger UI provides interactive documentation for all API endpoints, including:

- Request and response schemas
- Example requests and responses
- Authentication requirements
- Try-it-out functionality

#### Authentication

For development purposes, API key authentication is disabled by default. This allows you to test the API endpoints directly from the Swagger UI without providing an API key.

For production, you should enable API key authentication by:

1. Setting `ENABLE_API_KEY_AUTH=true` in the `.env` file
2. Setting a secure API key in the `.env` file
3. Uncommenting the authentication code in `src/middleware/auth.js`

When authentication is enabled, you need to include the API key in the `X-API-Key` header for all requests to the Fidelius API endpoints.

### Bruno Collections

The repository includes [Bruno](https://www.usebruno.com/) API collections for testing the APIs. Bruno is an open-source API client that makes it easy to test and debug APIs.

To use the Bruno collections:

1. Install Bruno from [the Bruno website](https://www.usebruno.com/downloads)
2. Open Bruno and click "Open Collection"
3. Navigate to the `apps/node-server/bruno` directory
4. Select the collection you want to open

For more information, see the [Bruno README](./bruno/README.md).

## API Usage Examples

### Using the Fidelius API

#### Generate Key Material

```bash
curl -X POST "http://localhost:8000/fidelius-api/generate-key-material" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json"
```

#### Encrypt Data

```bash
curl -X POST "http://localhost:8000/fidelius-api/encrypt" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "string_to_encrypt": "Hello, World!",
    "sender_nonce": "your-sender-nonce",
    "requester_nonce": "your-requester-nonce",
    "sender_private_key": "your-sender-private-key",
    "requester_public_key": "your-requester-public-key"
  }'
```

#### Decrypt Data

```bash
curl -X POST "http://localhost:8000/fidelius-api/decrypt" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "encrypted_data": "your-encrypted-data",
    "requester_nonce": "your-requester-nonce",
    "sender_nonce": "your-sender-nonce",
    "requester_private_key": "your-requester-private-key",
    "sender_public_key": "your-sender-public-key"
  }'
```

### Socket.io

```javascript
// Connect to the socket server
const socket = io("http://localhost:8000");

// Join rooms
socket.emit("join-organization", "organizationId");
socket.emit("join-branch", "branchId");
socket.emit("join-doctor", "doctorId");

// Send queue updates
socket.emit("queue-status-update", {
  organizationId: "organizationId",
  branchId: "branchId",
  doctorId: "doctorId",
  status: "waiting",
  position: 1,
});

// Listen for queue updates
socket.on("queue-status-updated", (data) => {
  console.log("Queue status updated:", data);
});
```

## Security Considerations

- Always use HTTPS in production
- Set a strong API key
- Implement proper authentication for your API endpoints
- Secure handling of cryptographic keys
- Set up appropriate logging while ensuring sensitive data isn't logged
