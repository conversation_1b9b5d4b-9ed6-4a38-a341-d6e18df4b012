import Script from "next/script";

const CLIENT_TOKEN = process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN;
const APPLICATION_ID = process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID;
const SITE = process.env.NEXT_PUBLIC_DATADOG_SITE || "ap1.datadoghq.com";
const SERVICE = process.env.NEXT_PUBLIC_DATADOG_SERVICE || "aran-care";
const ENVIRONMENT = process.env.NEXT_PUBLIC_ENVIRONMENT || "LOCAL";

export const DataDogScript = () => {
  if (!CLIENT_TOKEN) {
    return null;
  }
  return (
    <Script id="datadog-rum" strategy={"afterInteractive"}>
      {`
      (function(h,o,u,n,d) {
        h=h[d]=h[d]||{q:[],onReady:function(c){h.q.push(c)}}
        d=o.createElement(u);d.async=1;d.src=n
        n=o.getElementsByTagName(u)[0];n.parentNode.insertBefore(d,n)
      })(window,document,'script','https://www.datadoghq-browser-agent.com/us1/v5/datadog-rum.js','DD_RUM');
      window.DD_RUM.onReady(function() {
        window.DD_RUM.init({
          clientToken: '${CLIENT_TOKEN}',
          applicationId: '${APPLICATION_ID}',
          site: '${SITE}',
          service: '${SERVICE}',
          env: '${ENVIRONMENT}',
          sessionSampleRate: 100,
          sessionReplaySampleRate: 20,
          trackUserInteractions: true,
          trackResources: true,
          trackLongTasks: true,
          defaultPrivacyLevel: 'mask-user-input',
        });
      })
    `}
    </Script>
  );
};
